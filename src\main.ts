import './assets/main.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus, { ElMessage, type MessageHandler } from 'element-plus'
import SpecificUI from '@specificlab/ui'
import '@specificlab/ui/styles.css'
import App from './App.vue'
import router, { loadGuards } from './router'
import { getI18nInstance, elementPlusLocale } from './lang/i18n'
import Directives from './directives'
import Plugins from './vue-plugins'
import.meta.glob('@/plugins/*.ts', { eager: true })
import { useFonts } from './hooks/usePdf/fonts/useFonts'
import VueVirtualScroller from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

(async () => {
  await loadGuards()
  const i18n = getI18nInstance()
  const { loadFonts } = useFonts()
  loadFonts()
  const app = createApp(App)

  app.use(i18n)
  app.use(createPinia())
  app.use(router)
  app.use(Directives)
  app.use(Plugins)
  app.use(ElementPlus, {
    locale: elementPlusLocale.value
  })
  app.use(SpecificUI)
  app.use(VueVirtualScroller)

  app.mount('#app')

  let msg: MessageHandler
  let message: string
  window.addEventListener('vite:preloadError', () => {
    if (msg) {
      return
    }
    // #if BUILD_REG == 'INTL'
    message = 'Oops! The page failed to load. <a href="javascript:void(0);" style="text-decoration: underline; margin-right: 2px;" onclick="location.reload()">Refresh</a> to try again.',
    // #else
    message = '哎呀！页面加载失败。<a href="javascript:void(0);" style="text-decoration: underline; margin-right: 2px;" onclick="location.reload()">刷新</a>重试。'
    // #endif
    msg = ElMessage.warning({
      dangerouslyUseHTMLString: true,
      message,
      duration: 0,
      showClose: false,
    })
  })
})()
