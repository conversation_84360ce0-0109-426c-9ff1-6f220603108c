// Message Types
export const enum SSEMessageType {
  // Search related
  SEARCH_RESULT = 'search_result',
  SEARCH_START = 'SEARCH_START',
  SEARCH_DONE = 'SEARCH_DONE',
  SEARCH_ALL_DATA_START = 'SEARCH_ALL_DATA_START',
  SEARCH_ALL_DATA_DONE = 'SEARCH_ALL_DATA_DONE',
  SEARCH_SECTION_DATA_START = 'SEARCH_SECTION_DATA_START',
  SEARCH_SECTION_DATA_DONE = 'SEARCH_SECTION_DATA_DONE',
  INSIDE_DATABASE_SOURCE_START = 'INSIDE_DATABASE_SOURCE_START',
  INSIDE_DATABASE_SOURCE_DONE = 'INSIDE_DATABASE_SOURCE_DONE',
  INSIDE_SEARCH = 'INSIDE_SEARCH',
  INSIDE_SEARCH_DONE = 'INSIDE_SEARCH_DONE',
  // Thread related
  THREAD_START = 'THREAD_START',
  THREAD_DONE = 'THREAD_DONE',

  //LEGAL
  LEGAL_DATA = 'LEGAL_DATA',
  LEGAL_DATA_ANALYZE_START = 'LEGAL_DATA_ANALYZE_START',
  LEGAL_DATA_ANALYZE_DONE = 'LEGAL_DATA_ANALYZE_DONE',
  // Filter related
  FILTER_DATA_START = 'FILTER_DATA_START',
  FILTER_DATA_DONE = 'FILTER_DATA_DONE',

  // Rate related
  RATE_DATA_START = 'RATE_DATA_START',
  RATE_DATA_DONE = 'RATE_DATA_DONE',

  // Section related
  SECTION_START = 'SECTION_START',
  SECTION_DONE = 'SECTION_DONE',

  // Chart related
  REPORT_CHARTS = 'REPORT_CHARTS',

  // TIMELINE
  TIMELINE = 'TIME_LINE',

  // Summary related
  SUMMARY_DATA_START = 'SUMMARY_DATA_START',
  SUMMARY_DATA_DONE = 'SUMMARY_DATA_DONE',

  // News API 
  SEARCH_NEWSAPI_DATA_START = 'SEARCH_NEWSAPI_DATA_START',
  SEARCH_NEWSAPI_DATA_DONE = 'SEARCH_NEWSAPI_DATA_DONE',

  // Content related
  APPEND_TEXT = 'append-text',
}

// Message Type Groups
export const SEARCH_TYPES = [
  SSEMessageType.SEARCH_START,
  SSEMessageType.SEARCH_DONE,
] as const

export const LOADING_START_TYPES = [
  SSEMessageType.THREAD_START,
  SSEMessageType.FILTER_DATA_START,
  SSEMessageType.SEARCH_START,
  SSEMessageType.RATE_DATA_START,
  SSEMessageType.LEGAL_DATA_ANALYZE_START,
  SSEMessageType.SEARCH_NEWSAPI_DATA_START,
] as const

export const LOADING_DONE_TYPES = [
  SSEMessageType.THREAD_DONE,
  SSEMessageType.FILTER_DATA_DONE,
  SSEMessageType.SEARCH_DONE,
  SSEMessageType.RATE_DATA_DONE,
  SSEMessageType.LEGAL_DATA_ANALYZE_DONE,
  SSEMessageType.SEARCH_NEWSAPI_DATA_DONE,
] as const

// Type Guards
export const isSearchMessage = (type: string): boolean => {
  return SEARCH_TYPES.includes(
    type as SSEMessageType.SEARCH_START | SSEMessageType.SEARCH_DONE
  )
}

export const isLoadingStartMessage = (type: string): boolean => {
  return LOADING_START_TYPES.includes(
    type as
      | SSEMessageType.THREAD_START
      | SSEMessageType.FILTER_DATA_START
      | SSEMessageType.SEARCH_START
      | SSEMessageType.RATE_DATA_START
      | SSEMessageType.LEGAL_DATA_ANALYZE_START
      | SSEMessageType.SEARCH_NEWSAPI_DATA_START
  )
}

export const isLoadingDoneMessage = (type: string): boolean => {
  return LOADING_DONE_TYPES.includes(
    type as
      | SSEMessageType.THREAD_DONE
      | SSEMessageType.FILTER_DATA_DONE
      | SSEMessageType.SEARCH_DONE
      | SSEMessageType.RATE_DATA_DONE
      | SSEMessageType.LEGAL_DATA_ANALYZE_DONE
      | SSEMessageType.SEARCH_NEWSAPI_DATA_DONE
  )
}

// Message Interfaces
export interface BaseSSEMessage {
  type: SSEMessageType;
  section_name?: string;
  message?: string;
}

export interface SearchResultMessage extends BaseSSEMessage {
  type: SSEMessageType.SEARCH_RESULT;
  data: any[];
}

export interface AppendTextMessage extends BaseSSEMessage {
  type: SSEMessageType.APPEND_TEXT;
  message: string;
  section_name?: string;
}

export type SSEMessage =
  | BaseSSEMessage
  | SearchResultMessage
  | AppendTextMessage;
