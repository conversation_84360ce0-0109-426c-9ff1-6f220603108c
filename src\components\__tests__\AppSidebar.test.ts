import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHistory } from 'vue-router'
import AppSidebar from '../AppSidebar.vue'

// Mock stores and composables
const mockUserStore = {
  userInfo: {
    user_name: 'Test User',
    email: '<EMAIL>',
    language: 'english'
  },
  menuConfig: [
    { id: 'information', status: true },
    { id: 'dashboard', status: true },
    { id: 'format_report', status: false },
    { id: 'daily_report', status: true },
    { id: 'procurement', status: false },
    { id: 'chat_bot', status: true }
  ],
  changeUserLanguage: vi.fn()
}

const mockPermissions = {
  hasPermission: vi.fn(),
  canAccess: {
    manageUsers: { value: true },
    companyInfo: { value: false },
    personalInfo: { value: true }
  },
  getAvailableMenuItems: vi.fn(),
  getSettingsMenuItems: vi.fn()
}

const mockSidebar = {
  state: { value: 'expanded' }
}

const mockLanguageSwitch = {
  currentLocale: { value: 'english' },
  switchLanguage: vi.fn()
}

const mockNetwork = {
  isOnline: { value: true },
  downlink: { value: 10.5 }
}

// Mock modules
vi.mock('@/stores/user', () => ({
  useUserStore: () => mockUserStore
}))

vi.mock('@/composables/usePermissions', () => ({
  usePermissions: () => mockPermissions
}))

vi.mock('@/components/ui/sidebar', () => ({
  Sidebar: { template: '<div><slot /></div>' },
  SidebarContent: { template: '<div><slot /></div>' },
  SidebarFooter: { template: '<div><slot /></div>' },
  SidebarHeader: { template: '<div><slot /></div>' },
  SidebarRail: { template: '<div></div>' },
  SidebarMenu: { template: '<div><slot /></div>' },
  SidebarMenuItem: { template: '<div><slot /></div>' },
  SidebarMenuButton: { template: '<div><slot /></div>' },
  SidebarGroup: { template: '<div><slot /></div>' },
  SidebarGroupLabel: { template: '<div><slot /></div>' },
  useSidebar: () => mockSidebar,
  SidebarTrigger: { template: '<div></div>' }
}))

vi.mock('@/composables/useLanguageSwitch', () => ({
  useLanguageSwitch: () => mockLanguageSwitch
}))

vi.mock('@vueuse/core', () => ({
  useNetwork: () => mockNetwork
}))

vi.mock('@/config/menu.config', () => ({
  menuItems: [
    {
      menuId: 'information',
      labelKey: 'global.information',
      route: '/information',
      priority: 1
    },
    {
      menuId: 'dashboard',
      labelKey: 'global.dashboard',
      route: '/dashboard',
      priority: 2
    }
  ]
}))

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/information', component: { template: '<div>Information</div>' } },
    { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
    { path: '/settings', component: { template: '<div>Settings</div>' } }
  ]
})

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      global: {
        mainMenu: 'Main Menu',
        settingMenu: 'Settings',
        agentMarket: 'Agent Market',
        information: 'Information',
        dashboard: 'Dashboard',
        networkOffline: 'Offline',
        networkSlow: 'Slow',
        networkMedium: 'Medium',
        networkFast: 'Fast',
        networkUnknown: 'Unknown'
      },
      setting: {
        menuTitle: 'Settings',
        peopleManage: 'User Management',
        companyInfo: 'Company Info',
        personalInfo: 'Personal Info'
      }
    }
  }
})

describe('AppSidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock returns
    mockPermissions.getAvailableMenuItems.mockReturnValue([
      {
        menuId: 'information',
        labelKey: 'global.information',
        route: '/information',
        priority: 1
      }
    ])
    
    mockPermissions.getSettingsMenuItems.mockReturnValue([
      {
        title: 'User Management',
        url: '/settings',
        permission: 'settings.manage_users'
      },
      {
        title: 'Personal Info',
        url: '/settings/personal-info',
        permission: 'settings.personal_info'
      }
    ])
  })

  it('renders sidebar with unified permission management', async () => {
    const wrapper = mount(AppSidebar, {
      global: {
        plugins: [i18n, router]
      }
    })

    expect(mockPermissions.getAvailableMenuItems).toHaveBeenCalled()
    expect(mockPermissions.getSettingsMenuItems).toHaveBeenCalled()
    expect(wrapper.exists()).toBe(true)
  })

  it('filters menu items based on permissions', () => {
    const menuItems = [
      { menuId: 'information', labelKey: 'global.information', route: '/information' },
      { menuId: 'dashboard', labelKey: 'global.dashboard', route: '/dashboard' }
    ]

    mockPermissions.getAvailableMenuItems.mockReturnValue([menuItems[0]]) // Only information allowed

    const wrapper = mount(AppSidebar, {
      global: {
        plugins: [i18n, router]
      }
    })

    expect(mockPermissions.getAvailableMenuItems).toHaveBeenCalledWith(expect.any(Array))
  })

  it('filters settings items based on permissions', () => {
    const settingsItems = [
      { title: 'Personal Info', url: '/settings/personal-info', permission: 'settings.personal_info' }
    ]

    mockPermissions.getSettingsMenuItems.mockReturnValue(settingsItems)

    const wrapper = mount(AppSidebar, {
      global: {
        plugins: [i18n, router]
      }
    })

    expect(mockPermissions.getSettingsMenuItems).toHaveBeenCalled()
  })

  it('handles network status display', () => {
    const wrapper = mount(AppSidebar, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Network status should be displayed
    expect(wrapper.exists()).toBe(true)
  })

  it('handles language switching', async () => {
    const wrapper = mount(AppSidebar, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Language options should be available
    expect(wrapper.exists()).toBe(true)
  })
})
