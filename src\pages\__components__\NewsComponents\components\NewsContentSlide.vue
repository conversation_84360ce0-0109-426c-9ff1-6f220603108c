<template>
  <div
    class="w-full py-[20px] flex justify-center items-center gap-[15px]">
    <div class="w-[80%]">
      <!-- Title -->
      <span class="text-[12px]">{{
        formatCalendarDateTime(news.publish_time)
      }}</span>
      <NewsItemHeader :news="news"/>

      <!-- Content -->
      <div class="mt-[16px] space-y-[20px]">
        <!-- Short Summary -->
        <div :class="$style.summary" v-if="news.brief_summary">
          <MarkdownContainer style="padding: 0 !important" :content="news.brief_summary || ''"/>
        </div>

        <!-- Long Summary -->
        <div :class="$style.longSummaryWrapper" v-if="news.long_summary">
          <div :class="$style.longSummary">
            <MarkdownContainer style="padding: 0 !important" :content="news.long_summary || ''"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type PropType } from 'vue'
import type { NewsDetail } from '@/types'
import NewsItemHeader from './NewsItemHeader.vue'
import { formatCalendarDateTime } from '@/utils/filter'
import MarkdownContainer from '@/components/MarkdownComponents/MarkdownContainer.vue'


const props = defineProps({
  news: {
    type: Object as PropType<NewsDetail>,
    required: true,
  },
})
</script>

<style lang="scss" module>
.title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  cursor: pointer;

  &:hover {
    color: #2563eb;
  }
}

.summary {
  color: #4b4b4b;
  font-size: 16px;
  line-height: 1.6;
}

.longSummaryWrapper {
  /* background-color: #f3f4f6;  // 稍微加深背景色 / Slightly darker background */
  border-radius: 4px;
}

.longSummary {
  color: #374151; // 加深文字颜色 / Darker text color
  font-size: 16px;
  line-height: 1.7;
  position: relative;
  padding-left: 12px;
  border-left: 2px solid #94a3b8; // 更柔和的边框颜色 / Softer border color
}
</style>
