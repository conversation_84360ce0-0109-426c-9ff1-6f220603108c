<template>
  <div :class="$style['section-skeleton']">
    <!-- 当前任务状态提示 -->
    <div v-if="currentTask" :class="$style['task-status']">
      <Icon 
        icon="mdi:loading" 
        :class="$style['loading-icon']"/>
      <span>{{ currentTask.label }}</span>
    </div>
    
    <!-- 骨架屏内容 -->
    <div :class="$style['skeleton-section']">
      <div :class="$style['skeleton-container']">
        <div class="text-sm text-gray-500 mb-2" v-if="props.sectionKey === theLastWritingSection">
          {{ t("global.thinking", { label: sectionKey }) }}
        </div>
        <div :class="$style['skeleton-title']"></div>
        <div :class="$style['skeleton-line']"></div>
        <div :class="$style['skeleton-line']"></div>
        <div :class="$style['skeleton-line-short']"></div>
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
  
const { t } = useI18n()
  
interface PreTask {
  type: string
  status: 'pending' | 'active' | 'done'
  sectionName: string
  label: string
}
  
const props = defineProps<{
  sectionKey: string | number
  status: string
  theLastWritingSection: string
}>()
  
const preTasks = ref(new Map<string, PreTask>())

// 获取当前活动的任务
const currentTask = computed(() => {
  const tasks = Array.from(preTasks.value.values())
  return tasks.find(task => 
    task.sectionName === props.sectionKey && 
    task.status === 'active'
  )
})
  
// 处理前置任务消息
const handlePreTask = (message: any) => {
  const { type, message: msg, section_name } = message
  const taskKey = msg.replace('start ', '').replace('end ', '')
  
  if (type === 'SEARCH_START') {
    preTasks.value.set(taskKey, {
      type: 'search',
      status: 'active',
      sectionName: section_name,
      label: t('global.searching', { label: section_name })
    })
  }
  
  if (type === 'SEARCH_DONE') {
    const task = preTasks.value.get(taskKey)
    if (task) {
      task.status = 'done'
    }
  }
  
  if (type === 'SECTION_START') {
    preTasks.value.clear()
  }
}
  
defineExpose({
  handlePreTask
})
</script>
  
<style lang="scss" module>
.section-skeleton {
  margin-top: 0.875rem;
  margin-bottom: 0.875rem;
}

.task-status {
  margin-left: auto;
  margin-right: auto;
  max-width: 1150px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  color: #4466BC;
  font-size: 0.875rem;
  line-height: 1.25rem;
  margin-bottom: 0.75rem;
}

.loading-icon {
  animation: spin 1s linear infinite;
}
  
.skeleton-section {
  padding: 1.25rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 1150px;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
  
.skeleton-container {
  animation: skeleton-loading 1.5s infinite;
}
  
.skeleton-title {
  height: 1.5rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  width: 40%;
}
  
.skeleton-line {
  height: 1rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  margin-bottom: 0.75rem;
  width: 100%;
}
  
.skeleton-line-short {
  composes: skeleton-line;
  width: 60%;
}
  
@keyframes skeleton-loading {
  0% { opacity: 0.6; }
  50% { opacity: 0.8; }
  100% { opacity: 0.6; }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>