import MarkdownIt from 'markdown-it'
// @ts-ignore
import container from 'markdown-it-container'
import externalLinks from 'markdown-it-external-links'
import multimdTable from 'markdown-it-multimd-table'
import mermaid from 'mermaid'
import { mermaidConfig, renderMermaid<PERSON><PERSON> } from './mermaid.config'

// 初始化 mermaid
mermaid.initialize(mermaidConfig)

// 配置 markdown-it 实例
const md = new MarkdownIt({
  html: true,
  breaks: true,
  typographer: true,
  linkify: true,
})

// 1. 定义内联替换规则
interface InlineReplacement {
  pattern: RegExp
  template: string | ((match: string) => string)
}

const inlineReplacements: InlineReplacement[] = [
  {
    // 高亮语法
    pattern: /==([^=]+)==/g,
    template: '<mark class="bg-red-400 rounded-sm px-1">$1</mark>'
  },
  {
    // 引用语法 - 优化后的紧凑格式
    pattern: /\[\[(.*?)\]\]/g,
    template: (id: string) => {
      const uniqueId = generateUniqueId(id)
      return `<span id="${uniqueId}" data-reference-id="${id}" class="inline-flex items-center justify-center cursor-pointer px-2 py-0.5 text-xs font-mono rounded-[25px] bg-gray-300 mx-1 hover:bg-gray-400 transition-colors duration-300">${id}</span>`
    }
  }
]

// 2. 定义块级容器
interface BlockContainer {
  name: string
  validate: (params: string) => boolean | RegExpMatchArray | null
  render: (tokens: any[], idx: number) => string
}

const blockContainers: BlockContainer[] = [
  {
    name: 'details',
    validate: (params: string) => params.trim().match(/^details\s+(.*)$/),
    render: (tokens: any[], idx: number) => {
      const m = tokens[idx].info.trim().match(/^details\s+(.*)$/)
      
      if (tokens[idx].nesting === 1) {
        return `<details open class="markdown-details">
                <summary class="markdown-summary">${m[1]}</summary>
                <div class="markdown-details-content">`
      } 
      return '</div></details>\n'
      
    }
  }
]

// 3. 处理表格中的特殊语法
const processTableContent = (markdown: string): string => {
  const processedTables = new Set<string>()
  
  return markdown.replace(/^\|.*\|.*$/gm, (tableLine) => {
    if (processedTables.has(tableLine)) {
      return tableLine
    }

    // 处理单元格内容
    const cells = tableLine.split('|').filter(Boolean)
    const processedCells = cells.map(cell => {
      let processedCell = cell.trim()
      // 确保HTML标签完整性的处理
      inlineReplacements.forEach(({ pattern, template }) => {
        processedCell = processedCell.replace(pattern, (match, ...args) => {
          const replacement = typeof template === 'string' 
            ? template.replace(/\$1/g, args[0])
            : template(args[0])
          // 移除所有换行和多余空格
          return replacement.replace(/\s+/g, ' ').trim()
        })
      })
      return processedCell
    })

    const processedLine = `|${processedCells.join('|')}|`
    processedTables.add(processedLine)
    return processedLine
  })
}

// 4. 生成唯一ID的工具函数
const generateUniqueId = (() => {
  let counter = 0
  const prefix = 'ref'
  
  return (referenceId: string) => {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000)
    counter = (counter + 1) % Number.MAX_SAFE_INTEGER
    
    const uniqueString = `${timestamp.toString(36)}${counter.toString(36)}${random.toString(36)}`
    return `${prefix}-${uniqueString}-${referenceId}`
  }
})()

// 5. 注册所有扩展
const registerMarkdownExtensions = (md: MarkdownIt) => {
  // 注册内联替换规则
  md.core.ruler.before('normalize', 'custom_inline', state => {
    // 先处理表格
    state.src = processTableContent(state.src)
    
    // 处理非表格内容
    state.src = state.src.replace(/^(?!\|.*\|).*$/gm, line => {
      return inlineReplacements.reduce((processedLine, { pattern, template }) => {
        return processedLine.replace(pattern, (match, ...args) => {
          return typeof template === 'string'
            ? template.replace(/\$1/g, args[0])
            : template(args[0])
        })
      }, line)
    })
  })

  // 注册块级容器
  blockContainers.forEach(({ name, validate, render }) => {
    md.use(container, name, {
      validate,
      render
    })
  })

  // 注册其他插件
  md.use(multimdTable, {
    multiline: true,
    rowspan: true,
    headerless: true,
    multibody: true,
    aotolabel: true
  })

  md.use(externalLinks, {
    externalTarget: '_blank',
    internalTarget: '_blank'
  })

  // 添加 mermaid 支持
  md.renderer.rules.fence = (tokens, idx, options, env, self) => {
    const token = tokens[idx]
    const code = token.content.trim()
    const firstLine = code.split(/\n/)[0].trim()

    // 处理 mermaid 图表
    if (token.info === 'mermaid' || firstLine === 'mermaid') {
      try {
        return renderMermaidChart(code)
      } catch (err) {
        console.error('Mermaid rendering error:', err)
        return `<pre class="error">Mermaid rendering error: ${err}</pre>`
      }
    }

    // 对于非 mermaid 代码块，使用默认渲染
    return self.renderToken(tokens, idx, options)
  }
}

// 6. 主渲染函数
export const markdownToHtml = (markdown: string): string => {
  // 确保插件和扩展已注册
  registerMarkdownExtensions(md)
  
  // 渲染 markdown
  return md.render(markdown)
}

/**
 * @function removeMarkdown
 *
 * @description
 * Parse the markdown and returns a string
 *
 * @param markdown - The markdown string to parse
 * @param options - The options for the function
 *
 * @returns The parsed plain text
 */
type Options = {
  stripListLeaders?: boolean;
  listUnicodeChar: string | boolean;
  gfm?: boolean;
  useImgAltText?: boolean;
  preserveLinks?: boolean;
};

export const removeMarkdown = (
  markdown: string,
  {
    stripListLeaders = true,
    listUnicodeChar = false,
    gfm = true,
    useImgAltText = true,
    preserveLinks = false,
  }: Options = { listUnicodeChar: '' }
) => {
  let output = markdown || ''

  // Remove horizontal rules
  output = output.replace(/^(-\s*?|\*\s*?|_\s*?){3,}\s*$/gm, '')

  try {
    if (stripListLeaders) {
      const listPattern = listUnicodeChar ? `${listUnicodeChar} $1` : '$1'
      output = output.replace(/^([\s\t]*)([\*\-\+]|\d+\.)\s+/gm, listPattern)
    }

    if (gfm) {
      output = output
        .replace(/\n={2,}/g, '\n') // Header
        .replace(/~{3}.*\n/g, '') // Fenced codeblocks
        .replace(/~~/g, '') // Strikethrough
        .replace(/`{3}.*\n/g, '') // Fenced codeblocks
    }

    if (preserveLinks) {
      output = output.replace(/\[(.*?)\][\[\(](.*?)[\]\)]/g, '$1 ($2)')
    }

    output = output
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/^[=\-]{2,}\s*$/g, '') // Remove setext-style headers
      .replace(/\[\^.+?\](\: .*?$)?/g, '') // Remove footnotes
      .replace(/\s{0,2}\[.*?\]: .*?$/g, '') // Remove footnotes
      .replace(/\!\[(.*?)\][\[\(].*?[\]\)]/g, useImgAltText ? '$1' : '') // Remove images
      .replace(/\[(.*?)\][\[\(].*?[\]\)]/g, '$1') // Remove inline links
      .replace(/^\s{0,3}>\s?/g, '') // Remove blockquotes
      .replace(/(^|\n)\s{0,3}>\s?/g, '\n\n') // Remove blockquotes
      .replace(/^\s{1,2}\[(.*?)\]: (\S+)( ".*?")?\s*$/g, '') // Remove reference-style links
      .replace(
        /^(\n)?\s{0,}#{1,6}\s+| {0,}(\n)?\s{0,}#{0,} {0,}(\n)?\s{0,}$/gm,
        '$1$2$3'
      ) // Remove atx-style headers
      .replace(/([\*_]{1,3})(\S.*?\S{0,1})\1/g, '$2') // Remove emphasis
      .replace(/([\*_]{1,3})(\S.*?\S{0,1})\1/g, '$2') // Remove emphasis
      .replace(/(`{3,})(.*?)\1/gm, '$2') // Remove code blocks
      .replace(/`(.+?)`/g, '$1') // Remove inline code
      .replace(/\n{2,}/g, '\n\n') // Replace two or more newlines with exactly two
  } catch (e) {
    console.error(e)
    return markdown
  }
  return output
}
