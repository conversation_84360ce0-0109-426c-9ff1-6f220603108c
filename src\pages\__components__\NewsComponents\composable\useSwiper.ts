import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import type { Swiper as SwiperType } from 'swiper'
import { useTimeoutFn } from '@vueuse/shared'
import type { SwiperEvents, AutoplayMethods } from 'swiper/types'
import { useTrack } from '@/hooks/useTrack'
interface UseSwiperOptions {
  onSlideChange?: (index: number) => void;
}

// 精简 PauseReason 类型，只保留实际使用的值
type PauseReason = 'mouse' | 'click' | 'manual' | null;


export function useSwiper(options: UseSwiperOptions = {}) {

  const { trackEvent } = useTrack()
  // Core refs
  const swiperInstance = ref<SwiperType | null>(null)
  const pauseReason = ref<PauseReason>(null)
  
  // State management
  const isAutoplayActive = ref(true)
  const manualPaused = ref(false)
  const lastAutoplayEvent = ref<'start' | 'stop' | null>(null)
  // time left and percentage
  const autoplayTimeLeft = ref(0)
  const autoplayPercentage = ref(0)
  // Constants
  const PAUSE_DURATION = 7000
  // 常量定义
  const AUTOPLAY_DELAY = 3000

  // 添加手动控制状态
  const isManualControl = ref(false)

  // Computed states
  const isPaused = computed(() => {
    return manualPaused.value || lastAutoplayEvent.value === 'stop'
  })

  // 状态同步函数
  const syncAutoplayState = () => {
    const swiper = swiperInstance.value
    if (swiper?.autoplay) {
      lastAutoplayEvent.value = swiper.autoplay.running ? 'start' : 'stop'
    }
  }

  // Timeout handlers
  const {
    start: startPauseTimeout,
    stop: stopPauseTimeout,
    isPending: isClickPausePending,
  } = useTimeoutFn(() => {
    if (pauseReason.value === 'click') {
      resumeAutoplay('timeout')
    }
  }, PAUSE_DURATION, { immediate: false })

  // Event handlers for Swiper instance
  const setupSwiperEvents = (swiper: SwiperType) => {
    if (swiper.autoplay) {
      const events = [
        {
          event: 'autoplayStart',
          handler: () => {
            lastAutoplayEvent.value = 'start'
            pauseReason.value = null
            syncAutoplayState()
          }
        },
        {
          event: 'autoplayStop',
          handler: () => {
            lastAutoplayEvent.value = 'stop'
            autoplayTimeLeft.value = 0
            autoplayPercentage.value = 0
            syncAutoplayState()
          }
        },
        {
          event: 'autoplayTimeLeft',
          handler: (_: any, time: number, percentage: number) => {
            autoplayTimeLeft.value = time
            autoplayPercentage.value = percentage * 100
          }
        },
        {
          event: 'slideChange',
          handler: () => handleSlideChange()
        }
      ]

      events.forEach(({ event, handler }) => {
        swiper.on(event as keyof SwiperEvents, handler)
      })
    }
  }

  // Core initialization
  const onSwiperInit = (swiper: SwiperType) => {
    try {
      console.log('Swiper Init State:', {
        autoplay: swiper.autoplay,
        running: swiper.autoplay?.running,
        params: swiper.params,
        isAutoplayActive: isAutoplayActive.value,
        manualPaused: manualPaused.value,
        lastAutoplayEvent: lastAutoplayEvent.value,
        isManualControl: isManualControl.value
      })
      
      swiperInstance.value = swiper
      setupSwiperEvents(swiper)
      
      // Enable observer for dynamic content
      swiper.params.observer = true
      swiper.params.observeParents = true

      // Initial state sync
      if (swiper.autoplay) {
        syncAutoplayState()
      }
    } catch (error) {
      console.error('Swiper initialization error:', error)
    }
  }

  // Navigation methods
  const slideToIndex = (index: number) => {
    try {
      swiperInstance.value?.slideToLoop(index, 1000)
    } catch (error) {
      console.error('Slide to index error:', error)
    }
  }

  // Autoplay control methods
  const pauseAutoplay = (reason: PauseReason = null) => {
    if (isClickPausePending.value) {return}
    
    try {
      if (swiperInstance.value?.autoplay) {
        swiperInstance.value.autoplay.stop()
        lastAutoplayEvent.value = 'stop'
      }

      manualPaused.value = true
      pauseReason.value = reason

      if (reason === 'click') {
        startPauseTimeout()
      }
    } catch (error) {
      console.error('Pause autoplay error:', error)
      manualPaused.value = true
    }
  }

  const resumeAutoplay = (trigger: 'manual' | 'timeout' = 'manual') => {
    try {
      if (pauseReason.value !== 'mouse' || trigger === 'manual') {
        if (swiperInstance.value?.autoplay) {
          swiperInstance.value.autoplay.stop()
          swiperInstance.value.autoplay.start()
          
          manualPaused.value = false
          pauseReason.value = null
          stopPauseTimeout()
          syncAutoplayState()
          
          lastAutoplayEvent.value = 'start'
        }
      }
    } catch (error) {
      console.error('Resume autoplay error:', error)
      manualPaused.value = false
    }
  }

  // Event handlers
  const handleContentMouseEnter = () => {
    trackEvent('rolling_hover', {}, {
      debounce:1000
    })
    if (!isManualControl.value) {
      pauseAutoplay('mouse')
    }
  }

  const handleContentMouseLeave = () => {
    if (!isManualControl.value) {
      resumeAutoplay()
    }
  }

  const handleSlideChange = () => {
    try {
      options.onSlideChange?.(swiperInstance.value?.realIndex || 0)
    } catch (error) {
      console.error('Slide change error:', error)
    }
  }

  // Reset autoplay
  const resetAutoplay = () => {
    try {
      const swiper = swiperInstance.value
      if (swiper?.autoplay) {
        swiper.autoplay.stop()
        swiper.autoplay.start()
        syncAutoplayState()
      }
    } catch (error) {
      console.error('Reset autoplay error:', error)
    }
  }

  // 添加手动控制方法
  const toggleManualControl = () => {
    isManualControl.value = !isManualControl.value
    if (isManualControl.value) {
      // 手动暂停
      pauseAutoplay('manual')
    } else {
      // 手动恢复
      resumeAutoplay('manual')
    }
  }

  // 添加重置方法
  const resetAllState = () => {
    isManualControl.value = false
    manualPaused.value = false
    pauseReason.value = null
    lastAutoplayEvent.value = null
    
    // 重新启动自动播放
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.autoplay.stop()
      swiperInstance.value.autoplay.start()
    }
  }

  // Lifecycle hooks
  onMounted(() => {
    isAutoplayActive.value = true
    resetAllState()
  })

  onBeforeUnmount(() => {
    isAutoplayActive.value = false
    stopPauseTimeout()
    
    // Clear all event listeners
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.off('autoplayStart')
      swiperInstance.value.off('autoplayStop')
      swiperInstance.value.off('autoplayTimeLeft')
      swiperInstance.value.off('slideChange')
    }
  })

  return {
    // State
    isPaused,
    pauseReason,
    PAUSE_DURATION,
    AUTOPLAY_DELAY,
    autoplayTimeLeft,
    autoplayPercentage,
    isManualControl,
    swiperInstance,
    lastAutoplayEvent,
    manualPaused,

    // Methods
    onSwiperInit,
    slideToIndex,
    handleContentMouseEnter,
    handleContentMouseLeave,
    handleSlideChange,
    resetAutoplay,
    pauseAutoplay,
    resumeAutoplay,
    startPauseTimeout,
    toggleManualControl,
  }
}
