<template>
  <div :class="$style['container']">
    <div class="min-h-[350px] h-full w-full">
      <ChartDisplay :charts="[chartConfig]" :title="$t('formatReport.legalChartTitle')" @charts-rendered="onChartsRendered"/>
    </div>
    <div class="w-full mt-4">
      <div class="w-full flex items-center justify-start text-2xl font-bold text-main-color mb-2">
        {{ $t('formatReport.legalTimelineTitle') }}
      </div>
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in timeline"
          :key="index"
          :timestamp="formatTime(item.time)"
          type="primary"
          size="normal"
          :hollow="true"
          placement="top">
          <template #dot v-if="$slots.dot">
            <slot name="dot" :item="item"/>
          </template>

          <template v-if="$slots.default">
            <slot :item="item"/>
          </template>
          <template v-else>
            <div
              :class="$style.content"
              v-html="markdownToHtml(item.content)"></div>
          </template>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script setup lang="ts">
import { markdownToHtml } from '@/utils/markdown'
import { computed } from 'vue'
import ChartDisplay from './ChartDisplay.vue'
import type { ChartData as DisplayChartData } from './ChartDisplay.vue'
import { useI18n } from 'vue-i18n'

const emit = defineEmits<{
  'charts-rendered': [images: Record<string, string>];
}>()

const { t } = useI18n()
interface TimelineItem {
  content: string
  time: string | number | Date
}

interface TimelineProps {
  items: TimelineItem[]
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'normal' | 'large'
  placement?: 'top' | 'bottom'
  hollow?: boolean
  timeFormat?: string
}

interface ChartData {
  x: string
  y: number
  detail?: {
    type: string
    count: number
  }[]
}

interface Props {
  timeline: TimelineProps['items']
  chartData: {
    data: ChartData[]
  }
}

const props = withDefaults(defineProps<Props>(), {
  timeline: () => [],
  chartData: () => ({
    data: [],
    title: ''
  })
})

const COLORS = ['#3B82F6', '#B22222', '#10B981', '#EF4444']

const aggregateByQuarter = (data: ChartData[]): ChartData[] => {
  const quarterMap = new Map<string, ChartData>()
  
  data.forEach(item => {
    const date = new Date(item.x)
    if (isNaN(date.getTime())) {return} // Skip invalid dates
    
    const quarterKey = `${date.getFullYear()}-Q${Math.floor(date.getMonth() / 3) + 1}`
    
    if (!quarterMap.has(quarterKey)) {
      quarterMap.set(quarterKey, {
        x: quarterKey,
        y: 0,
        detail: []
      })
    }
    
    const quarterData = quarterMap.get(quarterKey)!
    item.detail?.forEach(d => {
      const existingType = quarterData.detail?.find(qd => qd.type === d.type)
      if (existingType) {
        existingType.count += d.count
      } else {
        quarterData.detail = quarterData.detail || []
        quarterData.detail.push({ ...d })
      }
    })
  })
  
  return Array.from(quarterMap.values())
    .sort((a, b) => a.x.localeCompare(b.x))
    .filter(item => item.detail?.some(d => d.count > 0)) // Filter out empty quarters
}

const chartConfig = computed<DisplayChartData>(() => {
  // Aggregate data by quarter
  const aggregatedData = aggregateByQuarter(props.chartData.data)
  
  const types = Array.from(new Set(
    aggregatedData.flatMap(item => 
      item.detail?.map(d => d.type) || []
    )
  ))

  return {
    id: 'timeline-chart',
    type: 'bar',
    gridSpan: 3,
    data: {
      title: t('formatReport.legalChartTitle'),
      xData: aggregatedData.map(item => item.x),
      yData: types.map(type => ({
        name: type,
        data: aggregatedData.map(item => 
          item.detail?.find(d => d.type === type)?.count || 0
        )
      }))
    },
    config: {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#E5E7EB',
        borderWidth: 1,
        padding: [8, 12],
        textStyle: {
          color: '#374151',
          fontSize: 13
        },
        formatter: (params: any[]) => {
          let tooltipContent = '<div class="tooltip-content">'
          tooltipContent += `<div style="font-weight:500;margin-bottom:8px;">${params[0].axisValue}</div>`
          
          let total = 0
          params.forEach(param => {
            if (param.value > 0) {
              tooltipContent += `<div style="display:flex;align-items:center;padding:3px 0;">
                <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${param.color};margin-right:8px;"></span>
                <span style="flex:1;color:#666;">${param.seriesName}</span>
                <span style="font-weight:500;margin-left:24px;">${param.value}</span>
              </div>`
              total += param.value
            }
          })
          tooltipContent += `<div style="margin-top:6px;padding-top:6px;border-top:1px dashed #E5E7EB;">
            <span style="color:#666;">总计</span>
            <span style="float:right;font-weight:500;">${total}</span>
          </div>`
          tooltipContent += '</div>'
          return tooltipContent
        }
      },
      legend: {
        show: true,
        bottom: 0,
        left: 'center',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 24,
        textStyle: {
          color: '#666',
          fontSize: 13
        }
      },
      grid: {
        top: '18%',
        left: '3%',
        right: '3%',
        bottom: '12%',
        containLabel: true
      },
      title: {
        text: t('formatReport.legalChartTitle'),
        top: '2%',
        textStyle: {
          fontSize: 20,
          fontWeight: 600,
          color: '#374151'
        }
      },
      xAxis: {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#E5E7EB'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#374151',
          fontSize: 12,
          margin: 16,
          rotate: 30, // Slightly reduced rotation
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#E5E7EB',
            type: 'dashed'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
          margin: 12
        }
      },
      series: types.map((type, index) => {
        const color = COLORS[index % COLORS.length]
        return {
          name: type,
          type: 'bar',
          stack: 'total',
          barWidth: '50%',
          barGap: '30%',
          itemStyle: {
            color: color,
            borderRadius: 0
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              color: color,
              opacity: 0.9,
              borderRadius: 0
            }
          },
          states: {
            normal: {
              itemStyle: {
                color: color,
                borderRadius: 0
              }
            },
            emphasis: {
              itemStyle: {
                color: color,
                borderRadius: 0
              }
            },
            hover: {
              itemStyle: {
                color: color,
                borderRadius: 0
              }
            }
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => params.value > 0 ? params.value : '',
            fontSize: 11,
            color: '#fff',
            distance: 0
          }
        }
      })
    }
  }
})

const formatTime = (time: string | number | Date): string => {
  if (time instanceof Date) {
    return time.toLocaleString()
  }
  if (typeof time === 'number') {
    return new Date(time).toLocaleString()
  }
  return time
}

const onChartsRendered = (images: Record<string, string>) => {
  emit('charts-rendered', images)
}
</script>

<style lang="scss" module>
.container {
  width: 100%;
  max-width: 1150px;
  margin: 20px auto;
  padding: 20px;
  background: var(--main-bg-color);
  border-radius: var(--radius-default);
  box-shadow: var(--shadow-normal);
}

.content {
}
</style>
