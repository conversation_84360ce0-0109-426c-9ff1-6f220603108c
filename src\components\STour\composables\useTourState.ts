import { ref, computed, watch } from 'vue'
import type { ModuleTourConfig } from '../types'
import { tourConfig as SystemTourConfig } from '../config'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'
import { useRoute } from 'vue-router'
import { usePost } from '@/hooks/useHttp'

export function useTourState() {
  const { execute:requestTourConfig, isLoading:requestTourConfigLoading } = usePost(
    '/user/completion',
    undefined,
    {},
    {
      immediate: false,
    }
  )
  const route = useRoute()
  const userStore = useUserStore()
  const { tourConfig } = storeToRefs(userStore)
  
  // 状态管理
  const isVisible = ref(false)
  const currentModuleId = ref<string | undefined>(undefined)
  
  // 路由映射表
  const routeToMenuMap: Record<string, string> = {
    '/': 'information',
    '/intelligence': 'daily_report',
    '/format-report': 'format_report',
    '/robot': 'chat_bot'
  }
  
  // 获取模块ID
  const getModuleId = (path: string): string | undefined => {
    // 精确匹配优先
    const exactMatch = routeToMenuMap[path]
    if (exactMatch) {
      console.log('Tour: Exact route match -', path, '=>', exactMatch)
      return exactMatch
    }
    
    // 前缀匹配
    for (const [prefix, moduleId] of Object.entries({
      '/intelligence/': 'daily_report',
      '/format-report/': 'format_report',
      '/robot/': 'chat_bot'
    })) {
      if (path.startsWith(prefix)) {
        console.log(`Tour: ${prefix} route match =>`, moduleId)
        return moduleId
      }
    }
    
    console.log('Tour: No route match for', path)
    return undefined
  }
  
  // 计算当前配置
  const currentConfig = computed(() => {
    const moduleId = currentModuleId.value
    if (!moduleId) {
      console.log('Tour: No current module ID')
      return undefined
    }
    
    const config = SystemTourConfig[moduleId]
    console.log('Tour: Current config for module', moduleId, config ? 'found' : 'not found')
    return config
  })
  
  // 检查是否应该显示tour
  const shouldShowTour = computed(() => {
    const moduleId = currentModuleId.value
    if (!moduleId) {
      console.log('Tour: No module ID, should not show')
      return false
    }
    
    const moduleConfig = tourConfig.value.find(menu => menu.id === moduleId)
    const shouldShow = Boolean(moduleConfig && !moduleConfig.tour_completed)
    console.log('Tour: Should show for module', moduleId, ':', shouldShow)
    return shouldShow
  })
  
  // 更新模块完成状态
  const markModuleCompleted = async() => {
    const moduleId = currentModuleId.value
    if (!moduleId) {return}
    
    const menuIndex = tourConfig.value.findIndex(menu => menu.id === moduleId)
    if (menuIndex !== -1) {
      userStore.tourConfig[menuIndex].tour_completed = true
      console.log('Tour: Marked module completed:', moduleId)
    }
    await requestTourConfig(0, {
      tour_config:{
        id: moduleId,
      }
    })
  }
  
  // 重置tour状态
  const resetTourState = () => {
    isVisible.value = false
    console.log('Tour: Reset tour state')
  }
  
  // 初始化tour
  const initTour = (path: string) => {
    console.log('Tour: Initializing for path:', path)
    
    // 1. 重置状态
    resetTourState()
    
    // 2. 获取并设置新的模块ID
    const newModuleId = getModuleId(path)
    if (currentModuleId.value !== newModuleId) {
      console.log('Tour: Updating module ID from', currentModuleId.value, 'to', newModuleId)
      currentModuleId.value = newModuleId
    }
  }
  
  // 完成处理
  const handleFinish = async () => {
    console.log('Tour: Finishing tour for module', currentModuleId.value)

    await markModuleCompleted()
    resetTourState()
  }
  
  // 监听路由变化
  watch(
    () => route.path,
    (newPath, oldPath) => {
      console.log('Tour: Route changed from', oldPath, 'to', newPath)
      initTour(newPath)
    },
    { immediate: true }
  )
  
  // 监听是否应该显示tour
  watch(
    shouldShowTour,
    (show) => {
      console.log('Tour: ShouldShowTour changed to:', show)
      if (show && currentConfig.value) {
        console.log('Tour: Starting tour for module:', currentModuleId.value)
        isVisible.value = true
      } else if (!show) {
        console.log('Tour: Hiding tour')
        isVisible.value = false
      }
    },
    { immediate: true }
  )
  
  return {
    isVisible,
    currentConfig,
    handleFinish
  }
} 