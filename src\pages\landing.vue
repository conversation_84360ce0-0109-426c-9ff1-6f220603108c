<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, computed } from 'vue'
import { definePage } from 'vue-router/auto'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router/auto'
import { Icon } from '@iconify/vue'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/hooks/useToken'

// Shadcn/Vue 组件
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import MagicText from '@/components/Hero/MagicText.vue'

// 现有组件
import AuthDialog from './__components__/LoginComponents/AuthDialog.vue'
import type { LoginMode } from '@/types/login'
import { useGet, usePost } from '@/hooks/useHttp'
import { ElMessage } from 'element-plus'
import { Languages } from 'lucide-vue-next'
import type { Language } from '@/types/language'
import { useLanguageSwitch } from '@/composables/useLanguageSwitch'

// Page Configuration
definePage({
  meta: {
    layout: false,
  },
})

// 状态管理
const email = ref('')
const showDialog = ref(false)
const dialogMode = ref<LoginMode>('login')
const isScrolled = ref(false)
const mobileMenuOpen = ref(false)
const containerRef = ref<HTMLElement | null>(null)
const activeSection = ref<string>('hero')
const pricingTabsFixed = ref(false)
const pricingTabsRef = ref<HTMLElement | null>(null)

// 导航相关
const route = useRoute()
const router = useRouter()
const { t, locale } = useI18n()
const { currentLocale, switchLanguage } = useLanguageSwitch()

// 语言切换相关状态
const isDetectingLanguage = ref(false)

// 语言选项配置，包含图标
const languageOptions = [
  {
    value: 'chinese' as Language,
    label: '简体中文',
    icon: 'emojione:flag-for-china'
  },
  {
    value: 'english' as Language,
    label: 'English',
    icon: 'emojione:flag-for-united-states'
  },
  {
    value: 'japanese' as Language,
    label: '日本語',
    icon: 'emojione:flag-for-japan'
  }
]

// 当前选中的语言
const currentLanguage = computed<Language>(() => 
  currentLocale.value as Language
)

// 获取当前语言的图标
const getCurrentLanguageIcon = computed(() => {
  return languageOptions.find(lang => lang.value === currentLanguage.value)?.icon
})

// 获取当前语言标签
const getCurrentLanguageLabel = computed(() => {
  const lang = languageOptions.find(l => l.value === currentLanguage.value)
  return lang?.label || currentLanguage.value
})

// IP地址检测和自动语言设置（每次都重新检测）
const detectAndSetLanguage = async () => {
  // 移除缓存检查，每次都重新检测
  if (isDetectingLanguage.value) {
    return
  }
  
  isDetectingLanguage.value = true
  
  try {
    // 使用AbortController实现超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)
    
    // 使用 ip-api.com API 检测国家信息
    const response = await fetch('https://ipinfo.io/json', {
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    console.log('IP detection result:', data)
    
    // 检查API响应状态
    if (data.status !== 'success') {
      throw new Error(`IP detection failed: ${data.status}`)
    }
    
    // 根据国家代码确定目标语言
    let targetLanguage: Language = 'english' // 默认英文
    
    switch (data.country) {
    case 'CN':
      targetLanguage = 'chinese'
      console.log('Detected China (CN), setting language to Chinese')
      break
    case 'JP':
      targetLanguage = 'japanese'
      console.log('Detected Japan (JP), setting language to Japanese')
      break
    default:
      targetLanguage = 'english'
      console.log(`Detected ${data.country || 'unknown'} (${data.country || 'unknown'}), setting language to English`)
      break
    }
    
    // 只有当当前语言与目标语言不同时才切换
    if (currentLanguage.value !== targetLanguage) {
      console.log(`Auto-switching from ${currentLanguage.value} to ${targetLanguage}`)
      await changeLanguage(targetLanguage, true) // 自动切换时跳过确认弹窗
    } else {
      console.log(`Current language ${currentLanguage.value} is already correct for ${data.country}`)
    }
    
  } catch (error) {
    console.warn('Failed to detect IP location:', error)
    // 检测失败时不进行任何语言切换，保持当前状态
  } finally {
    // 移除localStorage缓存逻辑
    isDetectingLanguage.value = false
  }
}

// 切换语言
const changeLanguage = async (lang: Language, skipConfirmation = false) => {
  if (lang === currentLanguage.value) {
    return
  }
  
  try {
    if (skipConfirmation) {
      // 自动切换时直接修改语言，不显示确认弹窗
      locale.value = lang
      // 保存到localStorage
      localStorage.setItem('language', lang)
      console.log('Language auto-switched to:', lang)
    } else {
      // 手动切换时使用原有逻辑（可能包含确认弹窗）
      await switchLanguage(lang)
      console.log('Language manually switched to:', lang)
    }
  } catch (error) {
    console.error('Language switch failed:', error)
    ElMessage.error(t('landing.messages.languageSwitchFailed'))
  }
}

// 在开发模式下暴露到window对象，方便调试
if (import.meta.env.DEV) {
  (window as any).detectLandingLanguage = detectAndSetLanguage
  console.log('Dev tools: Use detectLandingLanguage() to manually trigger IP detection')
}

// 产品特性 - 使用computed属性来获取国际化文案
const features = computed(() => [
  {
    title: t('landing.features.items.strategy.title'),
    icon: 'carbon:chart-relationship',
    description: t('landing.features.items.strategy.description')
  },
  {
    title: t('landing.features.items.insights.title'),
    icon: 'carbon:radar',
    description: t('landing.features.items.insights.description')
  },
  {
    title: t('landing.features.items.bidding.title'),
    icon: 'lucide:file-text',
    description: t('landing.features.items.bidding.description')
  },
  {
    title: t('landing.features.items.projects.title'),
    icon: 'lucide:globe',
    description: t('landing.features.items.projects.description')
  },
  {
    title: t('landing.features.items.monitoring.title'),
    icon: 'carbon:security',
    description: t('landing.features.items.monitoring.description')
  }
])

// 统计数据 - 使用computed属性来获取国际化文案
const stats = computed(() => [
  { value: '100+', label: t('landing.stats.countries') },
  { value: '50+', label: t('landing.stats.languages') },
  { value: '10K+', label: t('landing.stats.dailyAnalysis') },
  { value: '85%', label: t('landing.stats.accuracy') }
])

// API调用
const { execute: addContact } = usePost(
  '/contact/add',
  {},
  {},
  {
    immediate: false,
  }
)

// 事件处理
const handleSubscribe = async () => {
  if (!email.value) {
    ElMessage.warning(t('landing.contact.emailRequired'))
    return
  }
  
  await addContact(0, { contact: email.value })
  ElMessage.success(t('landing.contact.successMessage'))
  email.value = ''
}

const handleLoginClick = () => {
  dialogMode.value = 'login'
  showDialog.value = true
}

const handleRegisterClick = () => {
  dialogMode.value = 'register'
  showDialog.value = true
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
  
  if (mobileMenuOpen.value) {
    mobileMenuOpen.value = false
  }
}

// 导航到步骤页面或主页
const toStep = () => {
  router.push('/poll')
}

const toHome = () => {
  router.push('/')
}

// 用户登录状态检查
const userStore = useUserStore()

const checkLoginState = async () => {
  const token = getToken()
  if (token) {
    const result = await userStore.silentRetryLoadAccount()
    if (result) {
      toHome()
    }
  }
}


// 动画状态跟踪
const sectionsVisible = ref<Record<string, boolean>>({
  features: false,
  clients: false,
  pricing: false,
  contact: false
})

// 计算当前Tabs应该使用的背景色类
const tabsBackgroundClass = computed(() => {
  if (activeSection.value === 'hero') {
    return 'bg-transparent'
  }
  if (activeSection.value === 'stats') {
    return 'bg-gradient-to-r from-muted/50 via-background to-muted/50'
  }
  if (activeSection.value === 'features') {
    return 'bg-background'
  }
  if (activeSection.value === 'clients') {
    return 'bg-muted/50'
  }
  if (activeSection.value === 'pricing') {
    return 'bg-background'
  }
  if (activeSection.value === 'contact') {
    return 'bg-muted'
  }
  return 'bg-background'
})

// 添加此函数来确保滚动监听在容器内也能工作
const handleContainerScroll = () => {
  if (containerRef.value) {
    isScrolled.value = containerRef.value.scrollTop > 50
    
    // 检查定价选项卡是否应该固定
    if (pricingTabsRef.value) {
      const pricingSection = document.getElementById('pricing')
      if (pricingSection) {
        const pricingSectionRect = pricingSection.getBoundingClientRect()
        const pricingTabsTop = pricingTabsRef.value.offsetTop
        
        pricingTabsFixed.value = (pricingSectionRect.top <= 0) && 
                                (pricingSectionRect.bottom >= pricingTabsRef.value.offsetHeight)
      }
    }
  }
}

// 生命周期钩子
onMounted(() => {
  // 在文档上添加滚动事件监听，这样可以确保在任何滚动区域都能触发
  document.addEventListener('scroll', handleContainerScroll, { passive: true })
  
  // 添加对容器的滚动监听
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', handleContainerScroll, { passive: true })
  }
  
  const inviteCode = route.query.invite_code as string
  const create_company_code = route.query.create_company_code as string
  if (inviteCode || create_company_code) {
    dialogMode.value = 'register'
    showDialog.value = true
  }
  
  // 检查登录状态
  checkLoginState()

  // 自动检测语言设置
  detectAndSetLanguage()

  // 修改观察器逻辑，持续监控所有section
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      const sectionId = entry.target.id
      
      // 记录section的可见性状态（用于动画）
      if (entry.isIntersecting && sectionId && sectionId in sectionsVisible.value) {
        sectionsVisible.value[sectionId] = true
      }
      
      // 更新当前活跃的section（用于背景色变化）
      if (entry.isIntersecting && entry.intersectionRatio >= 0.3) {
        activeSection.value = sectionId || 'hero'
      }
    })
  }, { threshold: [0.1, 0.3, 0.5, 0.7], rootMargin: '-100px 0px -200px 0px' })
  
  // 观察所有sections（包括hero section）
  document.querySelectorAll('section').forEach(section => {
    observer.observe(section)
  })
  
  // 添加hero section的ID以便观察
  const heroSection = document.querySelector('section.pt-32.pb-20')
  if (heroSection && !heroSection.id) {
    heroSection.id = 'hero'
  }
  
  // 清理
  onUnmounted(() => {
    observer.disconnect()
    document.removeEventListener('scroll', handleContainerScroll)
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleContainerScroll)
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('scroll', handleContainerScroll)
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', handleContainerScroll)
  }
})
</script>

<template>
  <!-- 确保页面内容可以滚动，使用full-page-container类 -->
  <div ref="containerRef" class="landing-container">
    
    <!-- 导航栏 -->
    <header 
      :class="[
        'fixed top-0 w-full z-[50] transition-all duration-300',
        isScrolled ? 'bg-background/85 backdrop-blur-lg shadow-sm border-b border-primary/5' : 'bg-background/75 backdrop-blur-md'
      ]" 
      style="position: fixed !important; pointer-events: auto;">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16 md:h-20">
          <!-- Logo -->
          <div class="flex-shrink-0">
            <a href="#" class="flex items-center">
              <img src="@/assets/logo.svg" :alt="t('global.appName')" class="h-8 md:h-10"/>
            </a>
          </div>
          
          <!-- 桌面导航 -->
          <!-- <nav class="hidden md:flex space-x-8">
            <a @click="scrollToSection('features')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.features') }}</a>
            <a @click="scrollToSection('clients')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.clients') }}</a>
            <a @click="scrollToSection('pricing')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.pricing') }}</a>
            <a @click="scrollToSection('contact')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.contact') }}</a>
          </nav> -->
          
          <!-- 登录/注册按钮 -->
          <div class="hidden md:flex items-center space-x-4">
            <!-- 语言切换组件 -->
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" 
                        size="sm" 
                        :class="[
                          'h-9 px-3 hover:bg-primary/5 border border-primary/10 language-trigger',
                          { 'language-detecting': isDetectingLanguage }
                        ]"
                        :disabled="isDetectingLanguage">
                  <div class="flex items-center gap-2">
                    <Icon v-if="getCurrentLanguageIcon" 
                          :icon="getCurrentLanguageIcon" 
                          class="w-4 h-4"/>
                    <Languages v-else class="w-4 h-4"/>
                    <span class="text-sm">{{ getCurrentLanguageLabel }}</span>
                    <Icon v-if="isDetectingLanguage" 
                          icon="lucide:loader-2" 
                          class="w-3 h-3 opacity-50 animate-spin"/>
                    <Icon v-else 
                          icon="lucide:chevron-down" 
                          class="w-3 h-3 opacity-50"/>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-48">
                <DropdownMenuItem 
                  v-for="lang in languageOptions" 
                  :key="lang.value"
                  @click="changeLanguage(lang.value,true)"
                  :class="[
                    'cursor-pointer language-dropdown-item',
                    { 'bg-primary/10 text-primary font-medium': lang.value === currentLanguage }
                  ]"
                  :data-active="lang.value === currentLanguage">
                  <div class="flex items-center gap-2">
                    <Icon :icon="lang.icon" class="w-4 h-4"/>
                    <span>{{ lang.label }}</span>
                    <Icon v-if="lang.value === currentLanguage" 
                          icon="lucide:check" 
                          class="w-3 h-3 ml-auto"/>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Button @click="handleLoginClick"
                    variant="outline"
                    size="sm">{{ t('landing.navigation.login') }}</Button>
            <!-- <Button @click="handleRegisterClick" size="sm">{{ t('landing.navigation.register') }}</Button> -->
          </div>
          
          <!-- 移动端菜单按钮 -->
          <Button @click="toggleMobileMenu"
                  variant="ghost"
                  size="icon"
                  class="md:hidden">
            <Icon v-if="!mobileMenuOpen" icon="lucide:menu" class="h-6 w-6"/>
            <Icon v-else icon="lucide:x" class="h-6 w-6"/>
          </Button>
        </div>
      </div>
    </header>

    <!-- 移动端导航菜单 -->
    <div v-if="mobileMenuOpen" class="md:hidden bg-background/95 backdrop-blur-md border-b border-primary/5 shadow-md z-[999] fixed top-16 left-0 right-0" style="pointer-events: auto;">
      <div class="container mx-auto px-4 py-4 space-y-2">
        <a @click="scrollToSection('features')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.features') }}</a>
        <a @click="scrollToSection('clients')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.clients') }}</a>
        <a @click="scrollToSection('pricing')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.pricing') }}</a>
        <a @click="scrollToSection('contact')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.contact') }}</a>
        
        <!-- 移动端语言切换 -->
        <div class="pt-2 border-t border-primary/10 mobile-language-section">
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" 
                      size="sm" 
                      :class="[
                        'w-full justify-start h-9 px-3 hover:bg-primary/5 border border-primary/10 language-trigger',
                        { 'language-detecting': isDetectingLanguage }
                      ]"
                      :disabled="isDetectingLanguage">
                <div class="flex items-center gap-2">
                  <Icon v-if="getCurrentLanguageIcon" 
                        :icon="getCurrentLanguageIcon" 
                        class="w-4 h-4"/>
                  <Languages v-else class="w-4 h-4"/>
                  <span class="text-sm">{{ getCurrentLanguageLabel }}</span>
                  <Icon v-if="isDetectingLanguage" 
                        icon="lucide:loader-2" 
                        class="w-3 h-3 opacity-50 animate-spin ml-auto"/>
                  <Icon v-else 
                        icon="lucide:chevron-down" 
                        class="w-3 h-3 opacity-50 ml-auto"/>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" class="w-48">
              <DropdownMenuItem 
                v-for="lang in languageOptions" 
                :key="lang.value"
                @click="changeLanguage(lang.value,true)"
                :class="[
                  'cursor-pointer language-dropdown-item',
                  { 'bg-primary/10 text-primary font-medium': lang.value === currentLanguage }
                ]"
                :data-active="lang.value === currentLanguage">
                <div class="flex items-center gap-2">
                  <Icon :icon="lang.icon" class="w-4 h-4"/>
                  <span>{{ lang.label }}</span>
                  <Icon v-if="lang.value === currentLanguage" 
                        icon="lucide:check" 
                        class="w-3 h-3 ml-auto"/>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <div class="pt-2 flex space-x-4">
          <Button @click="handleLoginClick"
                  variant="outline"
                  size="sm"
                  class="w-full">{{ t('landing.navigation.login') }}</Button>
          <!-- <Button @click="handleRegisterClick" size="sm" class="w-full">{{ t('landing.navigation.register') }}</Button> -->
        </div>
      </div>
    </div>
    <section id="intro" class="pt-32 pb-20 md:pt-40 md:pb-32 relative overflow-hidden">
      <MagicText/>
    </section>
    
    <!-- Hero部分改进 - 全屏背景 -->
    <section id="hero" class="pt-32 pb-20 md:pt-40 md:pb-32 relative overflow-hidden">
      <div class="container mx-auto px-4 relative z-10">
        <div class="flex flex-col lg:flex-row items-center justify-between">
          <div class="lg:w-1/2 lg:pr-8 mb-10 lg:mb-0">
            <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary to-[#7490ff]">
              {{ t('landing.hero.title') }}<br/>
              <span class="relative text-primary">
                {{ t('landing.hero.titleAccent') }}
                <span class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-primary to-transparent rounded-full"></span>
              </span>
            </h1>
            <p class="text-xl md:text-2xl text-muted-foreground mb-10 font-light">
              {{ t('landing.hero.subtitle', { countries: '100+', ai: 'AI' }) }}
            </p>
            <!-- <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6">
              <Button @click="handleRegisterClick" size="lg" class="px-8 py-6 text-lg rounded-full bg-gradient-to-r from-primary to-[#6384ff] hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
                {{ t('landing.hero.cta.primary') }}
                <Icon icon="lucide:arrow-right" class="ml-2 h-5 w-5"/>
              </Button>
              <Button @click="scrollToSection('features')"
                      variant="outline"
                      size="lg"
                      class="px-8 py-6 text-lg rounded-full border-primary/30 hover:bg-primary/5">
                {{ t('landing.hero.cta.secondary') }}
              </Button>
            </div> -->
          </div>
          <div class="lg:w-1/2 relative">
            <div class="relative z-10 rounded-2xl overflow-hidden shadow-2xl shadow-primary/10 border border-primary/10 backdrop-blur-sm">
              <div class="p-1 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20">
                <div class="bg-background/80 backdrop-blur-sm rounded-xl p-4">
                  <!-- 仪表盘模拟界面 -->
                  <div class="aspect-video w-full bg-gradient-to-br from-background to-muted rounded-lg p-4 shadow-inner relative">
                    <!-- 顶部导航条 -->
                    <div class="flex items-center justify-between mb-4">
                      <div class="flex items-center gap-3">
                        <div class="w-3 h-3 rounded-full bg-red-400"></div>
                        <div class="w-3 h-3 rounded-full bg-yellow-400"></div>
                        <div class="w-3 h-3 rounded-full bg-green-400"></div>
                      </div>
                      <div class="flex items-center bg-card/40 rounded-md px-2 py-1 text-xs text-muted-foreground">
                        <div class="w-3 h-3 rounded-full bg-primary/50 mr-2"></div>
                        <span>{{ t('landing.footer.realTimeData') }}</span>
                      </div>
                    </div>
                    
                    <!-- 主体内容区 -->
                    <div class="grid grid-cols-4 gap-3 h-[calc(100%-2.5rem)]">
                      <!-- 左侧导航栏 -->
                      <div class="col-span-1 bg-card/40 backdrop-blur-sm rounded-md p-2 flex flex-col gap-2">
                        <div class="flex items-center p-1 bg-primary/10 rounded-md">
                          <div class="w-2 h-2 bg-primary rounded-full mr-2"></div>
                          <div class="h-2 w-20 bg-primary/20 rounded-full"></div>
                        </div>
                        <div class="flex items-center p-1">
                          <div class="w-2 h-2 bg-muted-foreground/30 rounded-full mr-2"></div>
                          <div class="h-2 w-16 bg-muted-foreground/10 rounded-full"></div>
                        </div>
                        <div class="flex items-center p-1">
                          <div class="w-2 h-2 bg-muted-foreground/30 rounded-full mr-2"></div>
                          <div class="h-2 w-18 bg-muted-foreground/10 rounded-full"></div>
                        </div>
                        <div class="flex items-center p-1">
                          <div class="w-2 h-2 bg-muted-foreground/30 rounded-full mr-2"></div>
                          <div class="h-2 w-14 bg-muted-foreground/10 rounded-full"></div>
                        </div>
                        <div class="flex-1"></div>
                        <div class="h-8 w-full bg-primary/5 rounded-md flex items-center justify-center">
                          <div class="h-2 w-3/4 bg-primary/20 rounded-full"></div>
                        </div>
                      </div>
                      
                      <!-- 右侧内容区 -->
                      <div class="col-span-3 flex flex-col gap-3">
                        <!-- 顶部卡片 -->
                        <div class="grid grid-cols-3 gap-3 h-1/3">
                          <div class="bg-card/40 backdrop-blur-sm rounded-md p-2 flex flex-col justify-between">
                            <div class="h-2 w-12 bg-muted-foreground/20 rounded-full"></div>
                            <div class="text-lg font-bold text-primary">84%</div>
                            <div class="h-1 w-full bg-muted-foreground/10 rounded-full overflow-hidden">
                              <div class="h-full w-5/6 bg-primary/40 rounded-full"></div>
                            </div>
                          </div>
                          <div class="bg-card/40 backdrop-blur-sm rounded-md p-2 flex flex-col justify-between">
                            <div class="h-2 w-14 bg-muted-foreground/20 rounded-full"></div>
                            <div class="text-lg font-bold text-primary">76%</div>
                            <div class="h-1 w-full bg-muted-foreground/10 rounded-full overflow-hidden">
                              <div class="h-full w-3/4 bg-primary/40 rounded-full"></div>
                            </div>
                          </div>
                          <div class="bg-card/40 backdrop-blur-sm rounded-md p-2 flex flex-col justify-between">
                            <div class="h-2 w-10 bg-muted-foreground/20 rounded-full"></div>
                            <div class="text-lg font-bold text-primary">92%</div>
                            <div class="h-1 w-full bg-muted-foreground/10 rounded-full overflow-hidden">
                              <div class="h-full w-11/12 bg-primary/40 rounded-full"></div>
                            </div>
                          </div>
                        </div>
                        
                        <!-- 中间图表 -->
                        <div class="bg-card/40 backdrop-blur-sm rounded-md p-2 h-1/3 flex flex-col">
                          <div class="flex justify-between items-center mb-1">
                            <div class="h-2 w-20 bg-muted-foreground/20 rounded-full"></div>
                            <div class="flex gap-1">
                              <div class="h-2 w-4 bg-primary/30 rounded-full"></div>
                              <div class="h-2 w-4 bg-primary/20 rounded-full"></div>
                              <div class="h-2 w-4 bg-primary/10 rounded-full"></div>
                            </div>
                          </div>
                          <div class="flex-1 flex items-end">
                            <!-- 柱状图 -->
                            <div class="flex-1 flex items-end justify-around">
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 40%"></div>
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 65%"></div>
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 50%"></div>
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 80%"></div>
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 60%"></div>
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 75%"></div>
                              <div class="w-4 bg-primary/60 rounded-t-sm" style="height: 45%"></div>
                            </div>
                          </div>
                        </div>
                        
                        <!-- 底部图表和数据 -->
                        <div class="grid grid-cols-2 gap-3 h-1/3">
                          <div class="bg-card/40 backdrop-blur-sm rounded-md p-2 flex flex-col">
                            <!-- 饼图 -->
                            <div class="h-2 w-16 bg-muted-foreground/20 rounded-full mb-2"></div>
                            <div class="flex-1 relative">
                              <div class="absolute inset-0 rounded-full border-4 border-primary/10"></div>
                              <div class="absolute inset-0 rounded-full border-4 border-transparent border-t-primary/60 border-r-primary/60" style="clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%, 50% 50%)"></div>
                              <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-xs font-semibold text-primary">65%</div>
                              </div>
                            </div>
                          </div>
                          <div class="bg-card/40 backdrop-blur-sm rounded-md p-2">
                            <div class="h-2 w-12 bg-muted-foreground/20 rounded-full mb-2"></div>
                            <!-- 折线图 -->
                            <div class="h-full w-full relative pt-2">
                              <svg class="w-full h-[calc(100%-8px)]" viewBox="0 0 100 40">
                                <path 
                                  d="M0,35 L10,30 L20,32 L30,25 L40,28 L50,15 L60,20 L70,18 L80,10 L90,15 L100,5" 
                                  fill="none" 
                                  stroke="rgba(68, 102, 188, 0.3)" 
                                  stroke-width="1"></path>
                                <path 
                                  d="M0,35 L10,30 L20,32 L30,25 L40,28 L50,15 L60,20 L70,18 L80,10 L90,15 L100,5" 
                                  fill="none" 
                                  stroke="rgba(68, 102, 188, 0.7)" 
                                  stroke-width="2" 
                                  stroke-dasharray="2"></path>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 装饰元素 -->
            <div class="absolute -bottom-6 -right-6 w-40 h-40 bg-primary/5 rounded-full filter blur-3xl z-0"></div>
            <div class="absolute -top-10 -left-10 w-40 h-40 bg-secondary/20 rounded-full filter blur-3xl z-0"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据部分改进 -->
    <section id="stats" class="py-20 relative overflow-hidden bg-gradient-to-r from-muted/50 via-background to-muted/50">
      <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGZpbGw9IiM0NDY2QkMwNSIgZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0yaDF2NGgtMXYtNHptMi0yaDF2MWgtMXYtMXptLTItMmgxdjFoLTF2LTF6bS0yLTJoMXYxaC0xdi0xem0tMi0yaDF2MWgtMXYtMXptLTQgMGgxdjFoLTF2LTF6bS0yIDBoMXYxaC0xdi0xem0tMiAyaDF2MWgtMXYtMXptLTIgMmgxdjFoLTF2LTF6bS0yIDJoMXYxaC0xdi0xeiIvPjwvZz48L3N2Zz4=')] opacity-10 z-0"></div>
      
      <div class="container mx-auto px-4 relative z-10">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
          <div v-for="(stat, index) in stats"
               :key="index" 
               class="relative rounded-2xl p-6 h-full min-h-[160px] backdrop-blur-sm bg-background/30 border border-primary/10 group transition-all duration-500 hover:shadow-lg hover:shadow-primary/5 hover:-translate-y-1">
            <div class="absolute -inset-0.5 bg-gradient-to-r from-primary/10 to-transparent opacity-0 group-hover:opacity-100 rounded-2xl blur transition duration-500"></div>
            <div class="absolute top-0 right-0 -mt-2 -mr-2 w-12 h-12 bg-primary/5 rounded-full filter blur-md"></div>
            
            <div class="relative flex flex-col justify-center h-full">
              <div class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-primary to-primary-foreground mb-3 tracking-tight whitespace-nowrap">
                {{ stat.value }}
              </div>
              <div class="text-sm font-medium uppercase tracking-wider text-muted-foreground">{{ stat.label }}</div>
              
              <!-- 装饰元素 -->
              <div class="h-0.5 w-1/4 bg-gradient-to-r from-primary/50 to-transparent rounded-full mt-3"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特性部分 -->
    <section 
      id="features" 
      ref="featuresRef"
      :class="['py-24 md:py-32 relative overflow-hidden', sectionsVisible.features ? 'fade-up' : 'opacity-0']">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-gradient-to-br from-background to-muted/30 z-0"></div>
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
        <div class="absolute -left-20 bottom-40 w-80 h-80 bg-primary/5 rounded-full filter blur-3xl"></div>
      </div>
      
      <div ref="animateFeatures" class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-20">
          <Badge variant="outline" class="mb-4 px-4 py-1.5 border-primary/20 text-primary font-medium">{{ t('landing.features.badge') }}</Badge>
          <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground to-primary/80">{{ t('landing.features.title') }}</h2>
          <p class="text-xl text-muted-foreground max-w-3xl mx-auto font-light">
            {{ t('landing.features.subtitle') }}
          </p>
        </div>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(feature, index) in features"
               :key="index" 
               class="group relative p-1 rounded-2xl bg-gradient-to-br from-primary/10 via-transparent to-transparent transition-all duration-300 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1">
            <div class="bg-background/80 backdrop-blur-sm p-6 rounded-xl h-full flex flex-col">
              <div class="w-14 h-14 flex items-center justify-center rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 text-primary mb-6 group-hover:scale-110 transition-transform duration-300">
                <Icon :icon="feature.icon" class="h-7 w-7"/>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-foreground group-hover:text-primary transition-colors duration-300">{{ feature.title }}</h3>
              <p class="text-muted-foreground text-sm leading-relaxed flex-grow">{{ feature.description }}</p>
              
              <!-- 装饰底部线条 -->
              <div class="h-0.5 w-1/3 bg-gradient-to-r from-primary/30 to-transparent rounded-full mt-6 group-hover:w-2/3 transition-all duration-500"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 客户案例 -->
    <!-- <section 
      id="clients" 
      ref="clientsRef"
      :class="['py-20 md:py-32 bg-muted/50', sectionsVisible.clients ? 'fade-in' : 'opacity-0']">
      <div ref="animateClients" class="container mx-auto px-4">
        <div class="text-center mb-16">
          <Badge variant="outline" class="mb-4">客户案例</Badge>
          <h2 class="text-3xl md:text-4xl font-bold mb-4">值得信赖的行业合作伙伴</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            出海易已广泛服务于众多政府机构与龙头企业，持续推动中国企业高质量"走出去"
          </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          <div v-for="(client, index) in clients"
               :key="index" 
               class="flex items-center justify-center p-4 bg-background rounded-lg shadow-sm hover:shadow-md transition-all">
            <img :src="client.logo" :alt="client.name" class="max-h-12 grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all"/>
          </div>
        </div>
      </div>
    </section> -->

    <!-- 定价部分 -->
    <!-- <section 
      id="pricing" 
      ref="pricingRef"
      :class="['py-20 md:py-32', sectionsVisible.pricing ? 'slide-in-left' : 'opacity-0']">
      <div ref="animatePricing" class="container mx-auto px-4">
        <div class="text-center mb-16">
          <Badge variant="outline" class="mb-4">定价方案</Badge>
          <h2 class="text-3xl md:text-4xl font-bold mb-4">选择适合您企业的方案</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            我们提供灵活的定价方案，满足不同规模企业的需求
          </p>
        </div>
        
        <div :class="{'sticky top-16 md:top-20 z-40 pt-4 pb-2 -mt-4 transition-all duration-300': pricingTabsFixed}" :style="pricingTabsFixed ? 'margin-bottom: 6px;' : ''">
          <div :class="[pricingTabsFixed ? tabsBackgroundClass + ' shadow-sm backdrop-blur-md border-b border-primary/5 px-4 py-2' : '']">
            <Tabs defaultValue="monthly" class="w-full max-w-4xl mx-auto" ref="pricingTabsRef">
              <div class="flex justify-center mb-8">
                <TabsList class="bg-background/70 backdrop-blur-md border border-primary/10 shadow-sm">
                  <TabsTrigger value="monthly">月付</TabsTrigger>
                  <TabsTrigger value="yearly">年付（省10%）</TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="monthly">
                <div class="grid md:grid-cols-3 gap-8">
                  <Card v-for="(plan, index) in pricingPlans"
                        :key="index" 
                        :class="{'border-primary': plan.popular, 'relative': plan.popular}">
                    <div v-if="plan.popular" class="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4">
                      <Badge variant="default" class="bg-primary">最受欢迎</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle>{{ plan.name }}</CardTitle>
                      <CardDescription>{{ plan.description }}</CardDescription>
                      <div class="mt-4">
                        <span class="text-3xl font-bold">{{ plan.price }}</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul class="space-y-2">
                        <li v-for="(feature, i) in plan.features" :key="i" class="flex items-center">
                          <Icon icon="lucide:check" class="mr-2 h-4 w-4 text-primary"/>
                          <span>{{ feature }}</span>
                        </li>
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button @click="handleRegisterClick" :variant="plan.popular ? 'default' : 'outline'" class="w-full">
                        {{ plan.cta }}
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="yearly">
                <div class="grid md:grid-cols-3 gap-8">
                  <Card v-for="(plan, index) in pricingPlans"
                        :key="index" 
                        :class="{'border-primary': plan.popular, 'relative': plan.popular}">
                    <div v-if="plan.popular" class="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4">
                      <Badge variant="default" class="bg-primary">最受欢迎</Badge>
                    </div>
                    <CardHeader>
                      <CardTitle>{{ plan.name }}</CardTitle>
                      <CardDescription>{{ plan.description }}</CardDescription>
                      <div class="mt-4">
                        <span class="text-3xl font-bold">{{ plan.yearlyPrice || plan.price }}</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul class="space-y-2">
                        <li v-for="(feature, i) in plan.features" :key="i" class="flex items-center">
                          <Icon icon="lucide:check" class="mr-2 h-4 w-4 text-primary"/>
                          <span>{{ feature }}</span>
                        </li>
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button @click="handleRegisterClick" :variant="plan.popular ? 'default' : 'outline'" class="w-full">
                        {{ plan.cta }}
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
        
        <div class="text-center mt-8">
          <p class="text-muted-foreground">需要更多定制化服务？ <a href="#contact" class="text-primary font-medium">联系我们</a> 获取企业级解决方案</p>
        </div>
      </div>
    </section> -->

    <!-- 联系我们 -->
    <section 
      id="contact" 
      ref="contactRef"
      :class="['py-20 md:py-32 relative overflow-hidden', sectionsVisible.contact ? 'zoom-in' : 'opacity-0']">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-gradient-to-br from-background/80 to-primary/5"></div>
      <div class="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGZpbGw9IiM0NDY2QkMwNSIgZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0yaDF2NGgtMXYtNHptMi0yaDF2MWgtMXYtMXptLTItMmgxdjFoLTF2LTF6bS0yLTJoMXYxaC0xdi0xem0tMi0yaDF2MWgtMXYtMXptLTQgMGgxdjFoLTF2LTF6bS0yIDBoMXYxaC0xdi0xem0tMiAyaDF2MWgtMXYtMXptLTIgMmgxdjFoLTF2LTF6bS0yIDJoMXYxaC0xdi0xeiIvPjwvZz48L3N2Zz4=')]"></div>
      <!-- 装饰光晕 -->
      <div class="absolute bottom-0 right-0 w-96 h-96 bg-primary/5 rounded-full filter blur-3xl opacity-60"></div>
      <div class="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full filter blur-3xl opacity-40"></div>
      
      <div ref="animateContact" class="container mx-auto px-4 relative z-10">
        <div class="max-w-3xl mx-auto text-center relative bg-background/50 backdrop-blur-sm p-8 rounded-2xl shadow-sm border border-primary/10">
          <Badge variant="outline" class="mb-4 border-primary/20 text-primary">{{ t('landing.contact.badge') }}</Badge>
          <h2 class="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground to-primary/80">{{ t('landing.contact.title') }}</h2>
          <p class="text-lg text-muted-foreground mb-8">
            {{ t('landing.contact.subtitle') }}
          </p>
          
          <div class="flex flex-col sm:flex-row items-center gap-3 max-w-md mx-auto">
            <Input v-model="email"
                   :placeholder="t('landing.contact.emailPlaceholder')"
                   type="email"
                   class="flex-grow"/>
            <Button @click="handleSubscribe" class="sm:flex-shrink-0 w-full sm:w-auto bg-primary hover:bg-primary/90">
              {{ t('landing.contact.cta') }}
            </Button>
          </div>
          
          <p class="text-xs text-muted-foreground mt-4">
            {{ t('landing.contact.privacy') }}
          </p>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="py-12 bg-background border-t">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 class="font-medium mb-4">{{ t('landing.footer.sections.product.title') }}</h3>
            <ul class="space-y-2">
              <li><a href="#features" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.features') }}</a></li>
              <li><a href="#pricing" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.pricing') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.tutorials') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.api') }}</a></li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-4">{{ t('landing.footer.sections.company.title') }}</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.about') }}</a></li>
              <li><a href="#clients" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.clients') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.careers') }}</a></li>
              <li><a href="#contact" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.contact') }}</a></li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-4">{{ t('landing.footer.sections.resources.title') }}</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.guides') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.reports') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.cases') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.blog') }}</a></li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-4">{{ t('landing.footer.sections.legal.title') }}</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.privacy') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.terms') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.data') }}</a></li>
              <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.cookies') }}</a></li>
            </ul>
          </div>
        </div>
        
        <Separator class="my-8"/>
        
        <div class="flex flex-col md:flex-row items-center justify-between">
          <div class="flex items-center mb-4 md:mb-0">
            <a href="#" class="flex items-center mr-3">
              <img src="@/assets/logo.svg" :alt="t('global.appName')" class="h-8"/>
            </a>
            <span class="text-sm text-muted-foreground">{{ t('landing.footer.copyright', { year: new Date().getFullYear() }) }}</span>
          </div>
          <!-- <div class="flex space-x-4">
            <a href="#" class="text-muted-foreground hover:text-foreground">
              <Icon icon="lucide:linkedin" class="h-5 w-5"/>
            </a>
            <a href="#" class="text-muted-foreground hover:text-foreground">
              <Icon icon="lucide:twitter" class="h-5 w-5"/>
            </a>
            <a href="#" class="text-muted-foreground hover:text-foreground">
              <Icon icon="lucide:facebook" class="h-5 w-5"/>
            </a>
            <a href="#" class="text-muted-foreground hover:text-foreground">
              <Icon icon="lucide:instagram" class="h-5 w-5"/>
            </a>
          </div> -->
        </div>
      </div>
    </footer>

    <!-- 认证对话框 -->
    <AuthDialog
      v-model="showDialog"
      v-model:mode="dialogMode"
      @toStepPage="toStep"
      @toHome="toHome"/>
  </div>
</template>

<style scoped>
.landing-container {
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 直接在容器上添加背景 */
  background: linear-gradient(135deg, #ffffff 0%, #e4ebff 100%);
  color: var(--foreground);
  position: relative;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 添加网格背景效果作为伪元素 */
.landing-container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%234466BC' fill-opacity='0.06' d='M36 34h4v1h-4v-1zm0-2h1v4h-1v-4zm2-2h1v1h-1v-1zm-2-2h1v1h-1v-1zm-2-2h1v1h-1v-1zm-2-2h1v1h-1v-1zm-4 0h1v1h-1v-1zm-2 0h1v1h-1v-1zm-2 2h1v1h-1v-1zm-2 2h1v1h-1v-1zm-2 2h1v1h-1v-1z'/%3E%3C/svg%3E");
  pointer-events: none;
}

/* 添加光晕效果作为另一个伪元素 */
.landing-container::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 30%, rgba(68, 102, 188, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, rgba(68, 102, 188, 0.08) 0%, transparent 35%);
  pointer-events: none;
}

/* 所有内容应该有更高的z-index，但低于header */
.landing-container > * {
  position: relative;
}

/* 确保header和相关元素具有最高z-index和正确的事件处理 */
header, 
.md:hidden[v-if="mobileMenuOpen"] {
  z-index: 999 !important;
  pointer-events: auto !important;
}

/* 给header内的所有可交互元素确保事件传递 */
header a, 
header button,
.md:hidden[v-if="mobileMenuOpen"] a,
.md:hidden[v-if="mobileMenuOpen"] button {
  pointer-events: auto !important;
  position: relative;
  z-index: 1000;
}

/* 确保section内容永远不会覆盖header */
section {
  z-index: 10;
  position: relative;
}

/* 修复任何可能的section内容覆盖header的问题 */
section > .container {
  z-index: 10;
}

/* 所有section背景元素的z-index统一设置 */
section > .absolute {
  z-index: 0;
}

/* 限制其他元素的z-index */
.landing-container > *:not(header):not(.md:hidden[v-if="mobileMenuOpen"]) {
  z-index: auto;
}

/* 给TabsList添加过渡效果 */
:deep(.TabsList) {
  transition: background-color 0.3s ease, backdrop-filter 0.3s ease, box-shadow 0.3s ease;
}

/* 毛玻璃效果加强 */
:deep(.TabsList) {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(68, 102, 188, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

/* 深色模式支持 */
:deep(.dark .TabsList) {
  background-color: rgba(20, 20, 30, 0.7);
  border: 1px solid rgba(68, 102, 188, 0.2);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 各种动画变体 */
.animate-in {
  animation: fadeUp 0.8s ease forwards;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.8s ease forwards;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease forwards;
}

.slide-in-right {
  animation: slideInRight 0.8s ease forwards;
}

.zoom-in {
  animation: zoomIn 0.8s ease forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 语言切换组件样式 */
:deep(.language-trigger) {
  transition: all 0.2s ease;
}

:deep(.language-trigger:hover) {
  background-color: rgba(68, 102, 188, 0.05);
  border-color: rgba(68, 102, 188, 0.2);
}

:deep(.language-dropdown-item[data-active="true"]) {
  background-color: rgba(68, 102, 188, 0.1) !important;
  color: rgb(68, 102, 188) !important;
  font-weight: 500;
}

:deep(.language-dropdown-item) {
  transition: all 0.15s ease;
}

:deep(.language-dropdown-item:hover) {
  background-color: rgba(68, 102, 188, 0.05);
}

/* 移动端语言切换特殊样式 */
.mobile-language-section {
  border-top: 1px solid rgba(68, 102, 188, 0.1);
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

/* 加载状态样式 */
.language-detecting {
  opacity: 0.7;
  pointer-events: none;
}

.language-detecting .language-trigger {
  cursor: wait;
}
</style> 