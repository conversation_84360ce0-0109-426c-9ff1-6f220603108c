{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "types.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", "typed-router.d.ts", "*.d.ts", "src/**/__components__/*", "src/**/**/*.vue", "src/**/**/**/*.vue", "src/vue-plugins/cdnFallback.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "moduleResolution": "<PERSON><PERSON><PERSON>", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "preserveValueImports": false}}