<template>
  <el-header :class="$style.header" class="py-[20px]">
    <div class="flex items-center gap-4 justify-center w-full">
      <img :src="logo" class="w-[200px] h-full"/>
      <span class="text-2xl font-bold">
        {{ t('global.appName') }}
      </span>
    </div>
  </el-header>
</template>

<script lang="ts" setup>
import logo from '@/assets/logo.svg'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

defineProps({
  isShowMenu: {
    type: Boolean,
    default: true,
  },
})
</script>

<style scoped lang="scss" module>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f8f8;
  min-height: 55px;
}
</style>
