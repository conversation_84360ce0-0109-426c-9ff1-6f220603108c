import router from '@/router'
import { redirectToPath } from '@/utils/router'
import { useUserStore } from '@/stores/user'

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const accessParams = to.meta.access || {}

  if (accessParams.fallback?.length ||
    accessParams.features?.length ||
    accessParams.roles?.length ||
    accessParams.permissions?.length ||
    accessParams.rightSchemeType?.length) {
    userStore.validateToken()
  }
  const hasAccess = userStore.checkAccess(accessParams)
  if (!hasAccess) {
    const redirectPath = to.meta.redirectIfNoAccess as string || '/'
    window.console.warn(`No access to ${to.path}, redirect to ${redirectPath}`)
    redirectToPath(redirectPath, to, from, next)
    return
  }
  next()
})
