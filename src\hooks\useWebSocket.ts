import { ref, onMounted, onUnmounted, readonly, type Ref, type UnwrapRef, type DeepReadonly, computed } from 'vue'
import { io, Socket } from 'socket.io-client'
import type { ManagerOptions, SocketOptions, } from 'socket.io-client'
import emitter from '@/utils/events'
import { resolveUrl } from '@/utils'
import { getToken } from './useToken'
import { getI18nInstance } from '@/lang/i18n'
import { useConfig } from './useConfig'
export interface SocketIOMessage<T = unknown> {
  data: T
  timestamp: number
  type: 'received' | 'sent'
  event: string
}

export interface SocketIOOptions extends Partial<Omit<ManagerOptions & SocketOptions, 'url'>> {
  url?: string | (() => string | Promise<string>)
  autoConnect?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
  reconnectionDelayMax?: number
  timeout?: number
  path?: string
  transports?: string[]
  auth?: Record<string, any>
  query?: Record<string, any>
}

export interface SocketIOInstance<T = unknown> {
  socket: Socket | undefined
  isConnected: DeepReadonly<Ref<boolean>>
  isConnecting: DeepReadonly<Ref<boolean>>
  messageHistory: DeepReadonly<Ref<SocketIOMessage<T>[]>>
  connect: () => Promise<void>
  disconnect: () => void
  emit: <TData = any>(event: string, data?: any) => Promise<TData>
  on: <TData = any>(event: string, callback: (data: TData) => void) => void
  off: (event: string) => void
  getStatus: () => 'connected' | 'connecting' | 'disconnected'
  startHeartbeat: () => void
  stopHeartbeat: () => void
  heartbeatStatus: DeepReadonly<Ref<HeartbeatStatus>>
}

interface HeartbeatConfig {
  enabled: boolean
  interval: number // 心跳间隔
  timeout: number // 心跳超时
  maxReconnectAttempts: number
}

const SOCKET_CONFIG = {
  MAX_MESSAGE_HISTORY: 100,
  DEFAULT_OPTIONS: {
    autoConnect: false,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    transports: ['websocket'] as any,
    path: '/socket.io',
    forceNew: true,
    reconnection: true,
    rejectUnauthorized: false,
    timeout: 5000,
    reconnectionDelayMax: 1000 * 60 * 5,
  },
  HEARTBEAT: {
    enabled: true,
    interval: 1000 * 60 * 1, // 1 min
    timeout: 5000, // 5s
    maxReconnectAttempts: 3
  } as HeartbeatConfig,
  RECONNECT: {
    maxAttempts: 10,
    initialDelay: 1000,
    maxDelay: 30000,
    factor: 1.5 // 指数退避因子
  }
} as const

const getWsDomain = () => {
  return `${window.location.protocol.replace('http', 'ws')}//${window.location.host}`
}

// 全局单例
let globalSocket: Socket | undefined
let globalInstance: SocketIOInstance<any> | undefined
let isConnecting = false

// 心跳状态类型
type HeartbeatStatus = 'normal' | 'warning' | 'error'

// 心跳相关的状态
let heartbeatTimer: NodeJS.Timeout | undefined
let pingTimeoutTimer: NodeJS.Timeout | undefined
const lastHeartbeat = ref<number>(Date.now())
const heartbeatStatus = ref<HeartbeatStatus>('normal')
let reconnectAttempts = 0

export function useSocketIO<T = unknown>(options: SocketIOOptions = {}): SocketIOInstance<T> {
  // 如果已经有全局实例，直接返回
  if (globalInstance) {
    return globalInstance as SocketIOInstance<T>
  }

  const isConnected = ref(false)
  const messageHistory = ref<SocketIOMessage<T>[]>([])

  const getSocketUrl = async () => {
    const { VITE_WS_SERVER_URL_DEV } = useConfig()
    if (VITE_WS_SERVER_URL_DEV) {
      return `${resolveUrl(VITE_WS_SERVER_URL_DEV)}/specific_ai`
    }
    if (options.url) {
      const customUrl = typeof options.url === 'function' ? await options.url() : options.url
      return import.meta.env.DEV ? customUrl.replace('wss://', 'ws://') : customUrl
    }
    return `${resolveUrl(getWsDomain())}/specific_ai`
  }

  const connect = async () => {
    // 如果已经连接或正在连接，直接返回
    if (globalSocket?.connected || isConnecting) {
      return
    }

    try {
      isConnecting = true
      const url = await getSocketUrl()
      
      // 确保清理旧连接
      if (globalSocket) {
        globalSocket.disconnect()
        globalSocket.removeAllListeners()
      }

      const i18nInstance = getI18nInstance()
      const language = i18nInstance.global.locale.value
      const socketOptions: Partial<ManagerOptions & SocketOptions> = {
        ...SOCKET_CONFIG.DEFAULT_OPTIONS,
        ...options,
        autoConnect: true,
        auth: {
          authorization: getToken(),
          language,
          timezoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone
        }
      }

      globalSocket = io(url, {
        ...socketOptions,
        forceNew: true,
        reconnection: false // 禁用自动重连，由我们自己控制
      })

      bindSocketEvents()
    } catch (error) {
      handleSocketError(error as Error)
    } finally {
      isConnecting = false
    }
  }

  const disconnect = () => {
    // stopHeartbeat() // 确保停止心跳
    
    if (!globalSocket) {return}
    
    globalSocket.removeAllListeners()
    globalSocket.disconnect()
    globalSocket = undefined
    isConnected.value = false
    
    // 清理全局实例
    globalInstance = undefined
  }

  const handleSocketConnect = () => {
    console.log('[Socket.IO] Connected')
    isConnected.value = true
    emitter.emit('socket:connected')
    startHeartbeat()
  }

  const handleSocketDisconnect = (reason: Socket.DisconnectReason) => {
    console.log('[Socket.IO] Disconnected:', reason)
    isConnected.value = false
    // stopHeartbeat()
    
    // 只在非正常断开时尝试重连
    if (reason !== 'io client disconnect') {
      setTimeout(() => {
        connect()
      }, SOCKET_CONFIG.RECONNECT.initialDelay)
    }
  }

  const handleSocketError = (error: Error) => {
    console.error('[Socket.IO] Connection error:', error)
    emitter.emit('socket:error', error)
    
    if (error.message.includes('auth')) {
      emitter.emit('socket:auth_error', error)
    }
  }

  const bindSocketEvents = () => {
    if (!globalSocket) {return}

    // 移除所有现有监听器
    globalSocket.removeAllListeners()

    // 核心事件
    globalSocket.on('connect', () => {
      handleSocketConnect()
      startHeartbeat()
    })
    
    globalSocket.on('disconnect', (reason) => {
      handleSocketDisconnect(reason)
      // stopHeartbeat()
    })
    
    globalSocket.on('connect_error', (error) => {
      handleSocketError(error)
      // stopHeartbeat()
    })

    // 心跳响应处理
    globalSocket.on('pong', handlePongResponse)

    if (import.meta.env.DEV) {
      globalSocket.onAny((event, ...args) => {
        console.log(`[Socket.IO] Event: ${event}`, args)
      })
    }
  }

  const emit = async <TData = any>(event: string, data?: any): Promise<TData> => {
    if (!globalSocket?.connected) {
      throw new Error('Socket is not connected')
    }

    return new Promise((resolve, reject) => {
      try {
        const timeoutDuration = options.timeout || SOCKET_CONFIG.DEFAULT_OPTIONS.timeout
        
        globalSocket!.timeout(timeoutDuration).emit(event, data, (err: Error | null, response: TData) => {
          if (err) {
            reject(err)
            return
          }
          resolve(response)
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  const on = <TData = any>(event: string, callback: (data: TData) => void) => {
    if (!globalSocket) {return}
    globalSocket.on(event, callback)
  }

  const off = (event: string) => {
    globalSocket?.off(event)
  }

  const startHeartbeat = () => {
    if (!globalSocket || !SOCKET_CONFIG.HEARTBEAT.enabled) {
      return
    }

    // 清理已存在的心跳定时器
    stopHeartbeat()
    
    // 重置状态
    heartbeatStatus.value = 'normal'
    reconnectAttempts = 0

    // 设置新的心跳定时器
    heartbeatTimer = setInterval(() => {
      if (!globalSocket?.connected) {
        stopHeartbeat()
        return
      }

      const now = Date.now()
      const timeSinceLastHeartbeat = now - lastHeartbeat.value

      // 检查心跳状态
      if (timeSinceLastHeartbeat > SOCKET_CONFIG.HEARTBEAT.timeout) {
        heartbeatStatus.value = 'error'
        console.warn('[Socket.IO] Heartbeat timeout detected')
        
        // 超过重试次数限制时，停止心跳并触发事件
        if (reconnectAttempts >= SOCKET_CONFIG.HEARTBEAT.maxReconnectAttempts) {
          console.error('[Socket.IO] Max reconnection attempts reached')
          stopHeartbeat()
          emitter.emit('socket:heartbeat_failed')
          return
        }

        // 增加重连次数并尝试重连
        reconnectAttempts++
        console.log(`[Socket.IO] Attempting to reconnect (${reconnectAttempts}/${SOCKET_CONFIG.HEARTBEAT.maxReconnectAttempts})`)
        disconnect()
        connect()
      } else if (timeSinceLastHeartbeat > (SOCKET_CONFIG.HEARTBEAT.timeout / 2)) {
        heartbeatStatus.value = 'warning'
      }

      // 发送 ping 并设置超时检测
      sendPing()

    }, SOCKET_CONFIG.HEARTBEAT.interval)

    // 立即发送第一次 ping
    sendPing()
  }

  const sendPing = () => {
    if (!globalSocket?.connected) {return}

    // 发送 ping
    globalSocket.emit('ping')
    
    // 设置 ping 超时定时器
    clearTimeout(pingTimeoutTimer)
    pingTimeoutTimer = setTimeout(() => {
      console.warn('[Socket.IO] Ping timeout, no pong received')
      heartbeatStatus.value = 'warning'
      lastHeartbeat.value = 0 // 强制下次检查时触发重连
    }, SOCKET_CONFIG.HEARTBEAT.timeout)
  }

  const stopHeartbeat = () => {
    heartbeatStatus.value = 'normal'
    
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = undefined
    }
    if (pingTimeoutTimer) {
      clearTimeout(pingTimeoutTimer)
      pingTimeoutTimer = undefined
    }
  }

  const handlePongResponse = (serverTimestamp: number) => {
    clearTimeout(pingTimeoutTimer)
    lastHeartbeat.value = serverTimestamp
    
    const clientTime = Date.now()
    const latency = clientTime - serverTimestamp
    
    // 更新心跳状态
    if (latency > 1000) {
      heartbeatStatus.value = 'warning'
      console.warn(`[Socket.IO] High latency detected: ${latency}ms`)
      emitter.emit('socket:high_latency', latency)
    } else {
      heartbeatStatus.value = 'normal'
      reconnectAttempts = 0 // 重置重连计数
    }
  }

  const instance: SocketIOInstance<T> = {
    socket: globalSocket,
    isConnected: readonly(isConnected),
    isConnecting: computed(() => isConnecting),
    messageHistory: computed(() => messageHistory.value) as DeepReadonly<Ref<SocketIOMessage<T>[]>>,
    connect,
    disconnect,
    emit,
    on,
    off,
    getStatus: () => isConnected.value ? 'connected' : isConnecting ? 'connecting' : 'disconnected',
    heartbeatStatus: readonly(heartbeatStatus),
    startHeartbeat,
    stopHeartbeat
  }

  // 设置全局实例
  globalInstance = instance

  return instance
}

export const useWebSocket = <T = unknown>(): SocketIOInstance<T> => {
  try {
    return useSocketIO<T>({
      autoConnect: true,
      timeout: 40000,
      transports: ['websocket'],
      path: '/socket.io'
    })
  } catch (error) {
    console.error('Failed to create WebSocket instance:', error)
    throw error
  }
}