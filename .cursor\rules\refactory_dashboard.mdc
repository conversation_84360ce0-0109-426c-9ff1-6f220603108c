---
description: 
globs: 
alwaysApply: false
---
---
description: Here’s the updated **cursor task description**, explicitly specifying that the reference UI prototype is `report_chat_view.vue`
globs: 
alwaysApply: false
---

### Updated Cursor Task Description

#### Objective:
We are tasked with integrating the interface logic from the two `index.vue` dashboard pages into the existing `report_chat_view.vue` component. The integration must ensure that the behavior and data flow from the two `index.vue` components are merged into `report_chat_view.vue`, while maintaining **100% consistency** with its existing UI prototype.

#### Key Requirements:
1. **UI Fidelity**:
   - The UI of the merged logic should **remain identical** to `report_chat_view.vue`, with no changes to its layout, color scheme, typography, spacing, or interactive elements.
   - The **interface logic** from the two `index.vue` files should be carefully transferred to `report_chat_view.vue`, ensuring the data, state management, and event handlers align with the existing design.

2. **Integration of `index.vue` Logic**:
   - **Combine the logic** from both `index.vue` files, ensuring their respective API calls, data handling, and interactive behaviors are incorporated into `report_chat_view.vue`.
   - Ensure that **state management** for both left and right sections of the dashboard is handled correctly. The functionality of each part of the dashboard must operate seamlessly together in the single component.

3. **Component Sourcing**:
   - **Do not reference components imported from `@specificlab/ui` or other external sources.**
   - **Strictly use components from `src/components/ui`**, especially **ShadCN UI components** from this folder, for consistency and reusability in the UI design.
   - Integrate the existing components into `report_chat_view.vue` carefully to match the UI and behavior.

4. **Process Compliance**:
   - Each modification must be checked against the `debugger/refacotry_todolist.md` file to ensure the **integration follows the development steps** and no critical steps are missed.
   - The updated logic must be **debugged** and cross-checked after each change to verify proper integration of both `index.vue` logics.

#### Steps to Follow:
1. **Analyze and Extract Logic**:
   - Extract the **real interface logic** from both `index.vue` components, including API calls, event handling, and state management.
   - Ensure that data flow, event handling, and lifecycle management align with the current behavior in `report_chat_view.vue`.

2. **Integrate the Logic**:
   - **Merge the logic** from the two `index.vue` files into `report_chat_view.vue`, ensuring there is no conflict between their states, data, and interactions.
   - Ensure that the **left and right sections** of the dashboard, including any interactive features, continue to work as expected after the integration.

3. **UI and Logic Consistency**:
   - Ensure the **UI layout, fonts, colors, and interaction behavior** remain unchanged from `report_chat_view.vue`.
   - Match the **logic behavior** with the original expectations in `index.vue`, ensuring that all interactions (such as button clicks, API responses, data binding, etc.) are seamlessly integrated into the existing UI.

4. **Testing and Validation**:
   - After integration, thoroughly test the dashboard to ensure all functionalities work together and that **no UI or logic discrepancies** exist.
   - Use the **debugger** to verify that the combined logic does not introduce any issues, and make sure everything is aligned with the prototype.

5. **Ongoing Debugging**:
   - After each integration step, validate the changes using the **debugger**, ensuring that the UI and interface logic are behaving correctly.
   - Update `refacotry_todolist.md` with each step to track the progress of the integration.

---

