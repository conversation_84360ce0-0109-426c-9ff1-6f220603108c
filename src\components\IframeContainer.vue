<template>
  <div 
    class="iframe-container w-full h-full relative bg-white rounded-lg shadow-sm"
    :class="containerClass"
    :style="{ 
      width: '100%', 
      height: '100%',
      maxWidth: '100%',
      overflow: 'hidden',
      position: 'relative'
    }">
    
    <!-- 加载状态 -->
    <div 
      v-if="loading" 
      class="absolute inset-0 flex items-center justify-center bg-white/90 backdrop-blur-sm z-10">
      <div class="flex flex-col items-center gap-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <p class="text-sm text-gray-600">{{ t('iframe.loading') }}</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div 
      v-if="error" 
      class="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
      <div class="flex flex-col items-center gap-4 p-6 text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <Icon icon="mdi:alert-circle" class="w-8 h-8 text-red-600"/>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-red-800 mb-2">{{ t('iframe.error.title') }}</h3>
          <p class="text-sm text-red-600 mb-4">{{ error }}</p>
          <Button 
            @click="retryLoad" 
            variant="outline" 
            size="sm"
            class="text-red-600 border-red-300 hover:bg-red-50">
            {{ t('iframe.retry') }}
          </Button>
        </div>
      </div>
    </div>

    <!-- iframe 主体 -->
    <iframe
      v-show="!loading && !error"
      ref="iframeRef"
      :src="iframeSrc"
      :title="title"
      class="w-full h-full border-0 transition-all duration-300"
      :style="{ 
        width: '100%',
        height: `${iframeHeight}px`,
        maxWidth: '100%',
        overflow: 'hidden'
      }"
      :sandbox="sandboxAttributes"
      @load="handleIframeLoad"
      @error="handleIframeLoadError"
      allow="fullscreen"
      scrolling="auto">
    </iframe>

    <!-- 调试信息 (仅开发环境) -->
    <div 
      v-if="debug && isDevelopment" 
      class="absolute top-2 right-2 bg-gray-900/80 text-white text-xs p-2 rounded z-20 font-mono">
      <div>Status: {{ status }}</div>
      <div>Height: {{ iframeHeight }}px</div>
      <div>Messages: {{ messageCount }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import { useDebounceFn } from '@vueuse/core'

// 类型定义
interface IframeMessage {
  type: string
  data?: any
  source?: string
  timestamp: number
}

// 错误类型定义
interface IframeError {
  type: 'NETWORK_ERROR' | 'TIMEOUT_ERROR' | 'AUTH_ERROR' | 'LOAD_ERROR' | 'UNKNOWN_ERROR'
  message: string
  code?: string | number
  url?: string
  timestamp: number
  retryCount: number
  details?: any
}


interface Props {
  // 基础配置
  src: string
  title?: string
  token?: string
  
  // 尺寸控制
  width?: string | number
  height?: string | number
  minHeight?: number
  maxHeight?: number
  autoHeight?: boolean
  
  // 安全配置
  sandbox?: string[]
  allowedOrigins?: string[]
  requireReferer?: boolean
  
  // 功能配置
  enableCommunication?: boolean
  debug?: boolean
  retryAttempts?: number
  loadTimeout?: number
  
  // 样式
  containerClass?: string
}

// Props 和 Emits
const props = withDefaults(defineProps<Props>(), {
  title: 'Embedded Content',
  width: '100%',
  height: 600,
  minHeight: 400,
  maxHeight: 2000,
  autoHeight: true,
  sandbox: () => [
    'allow-scripts',
    'allow-same-origin',
    'allow-forms',
    'allow-popups',
    'allow-orientation-lock',
    'allow-pointer-lock',
    'allow-presentation'
  ],
  allowedOrigins: () => [
    'https://dev.goglobalsp.com',
    'https://www.goglobalsp.com',
    'http://localhost:3000'
  ],
  requireReferer: true,
  enableCommunication: true,
  debug: false,
  retryAttempts: 3,
  loadTimeout: 30000,
  containerClass: ''
})

const emit = defineEmits<{
  loaded: []
  error: [error: IframeError | string]
  message: [message: IframeMessage]
  heightChanged: [height: number]
  retry: [attempt: number]
}>()

// Composables
const { t } = useI18n()

// 响应式状态
const iframeRef = ref<HTMLIFrameElement | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)
const status = ref<'idle' | 'loading' | 'loaded' | 'error'>('idle')
const iframeHeight = ref(props.height as number)
const messageCount = ref(0)
const retryCount = ref(0)
const loadTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const staticTimestamp = ref(Date.now())

// 计算属性
const isDevelopment = computed(() => import.meta.env.DEV)

const iframeSrc = computed(() => {
  const url = new URL(props.src)
  
  // 添加 token 参数
  if (props.token) {
    url.searchParams.set('token', props.token)
    url.searchParams.set('timestamp', staticTimestamp.value.toString())
  }
  
  // 添加调试参数 (开发环境) - 使用静态时间戳
  if (props.debug && isDevelopment.value) {
    url.searchParams.set('debug', 'true')
    url.searchParams.set('timestamp', staticTimestamp.value.toString())
  }
  
  return url.toString()
})

const sandboxAttributes = computed(() => {
  return props.sandbox.join(' ')
})

// 防抖处理高度变化
const debouncedHeightChange = useDebounceFn((height: number) => {
  emit('heightChanged', height)
}, 100)

// 消息处理
const handlePostMessage = (event: MessageEvent) => {
  // 验证来源
  if (!props.allowedOrigins.includes(event.origin)) {
    console.warn(`[IframeContainer] 拒绝来自未授权域名的消息: ${event.origin}`)
    return
  }

  // 验证iframe来源
  if (iframeRef.value && event.source !== iframeRef.value.contentWindow) {
    console.warn('[IframeContainer] 消息来源不匹配iframe窗口')
    return
  }

  try {
    const message: IframeMessage = {
      type: event.data.type || 'unknown',
      data: event.data.data,
      source: event.origin,
      timestamp: Date.now()
    }

    messageCount.value++
    
    // 处理高度调整消息
    if (message.type === 'resize' && props.autoHeight) {
      const newHeight = Math.max(
        props.minHeight,
        Math.min(props.maxHeight, message.data?.height || props.height as number)
      )
      iframeHeight.value = newHeight
      debouncedHeightChange(newHeight)
    }
    
    // 处理加载完成消息
    if (message.type === 'loaded') {
      handleLoadSuccess()
    }
    
    // 处理错误消息
    if (message.type === 'error') {
      let errorType: IframeError['type'] = 'UNKNOWN_ERROR'
      let errorCode = 'IFRAME_INTERNAL_ERROR'
      
      // 根据错误内容判断错误类型
      const errorData = message.data?.error || message.data?.message || ''
      if (typeof errorData === 'string') {
        if (errorData.includes('auth') || errorData.includes('token') || errorData.includes('unauthorized')) {
          errorType = 'AUTH_ERROR'
          errorCode = 'AUTH_FAILED'
        } else if (errorData.includes('network') || errorData.includes('connection')) {
          errorType = 'NETWORK_ERROR'
          errorCode = 'NETWORK_ERROR'
        } else if (errorData.includes('timeout')) {
          errorType = 'TIMEOUT_ERROR'
          errorCode = 'REQUEST_TIMEOUT'
        }
      }
      
      handleIframeError(
        message.data?.error || message.data?.message || 'iframe内部错误',
        errorType,
        errorCode,
        {
          originalMessage: message,
          source: message.source
        }
      )
    }

    // 发射消息事件
    emit('message', message)
    
    if (props.debug) {
      console.log('[IframeContainer] 收到消息:', message)
    }
  } catch (err) {
    console.error('[IframeContainer] 消息处理错误:', err)
  }
}

// 向iframe发送消息
const sendMessage = (type: string, data?: any) => {
  if (!iframeRef.value?.contentWindow || !props.enableCommunication) {
    console.warn('[IframeContainer] 无法发送消息: iframe未就绪或通信已禁用')
    return false
  }

  try {
    const message = {
      type,
      data,
      timestamp: Date.now(),
      source: 'parent'
    }
    
    iframeRef.value.contentWindow.postMessage(message, '*')
    
    if (props.debug) {
      console.log('[IframeContainer] 发送消息:', message)
    }
    
    return true
  } catch (err) {
    console.error('[IframeContainer] 发送消息失败:', err)
    return false
  }
}

// iframe 加载处理
const handleIframeLoad = () => {
  if (loadTimer.value) {
    clearTimeout(loadTimer.value)
    loadTimer.value = null
  }
  
  handleLoadSuccess()
}

const handleLoadSuccess = () => {
  loading.value = false
  error.value = null
  status.value = 'loaded'
  retryCount.value = 0
  
  // 发送初始化消息
  nextTick(() => {
    if (props.enableCommunication) {
      sendMessage('parent-ready', {
        autoHeight: props.autoHeight,
        debug: props.debug
      })
      
      // 发送样式设置消息，确保 iframe 内容自适应宽度
      sendMessage('set-styles', {
        width: '100%',
        maxWidth: '100%',
        overflow: 'hidden',
        boxSizing: 'border-box'
      })
    }
  })
  
  emit('loaded')
  
  if (props.debug) {
    console.log('[IframeContainer] iframe加载成功')
  }
}

// 处理iframe加载错误
const handleIframeLoadError = (event?: Event) => {
  console.error('[IframeContainer] iframe加载错误事件:', event)
  
  // 分析错误类型
  let errorType: IframeError['type'] = 'LOAD_ERROR'
  let errorCode = 'IFRAME_LOAD_ERROR'
  let errorMessage = t('iframe.error.load_failed')
  
  // 尝试检测具体错误类型
  if (props.src) {
    // 检查是否是网络错误
    fetch(props.src, { method: 'HEAD', mode: 'no-cors' })
      .catch((networkError) => {
        console.error('[IframeContainer] 网络连接检查失败:', networkError)
        errorType = 'NETWORK_ERROR'
        errorCode = 'NETWORK_UNREACHABLE'
        errorMessage = '网络连接失败，无法访问目标页面'
      })
      .finally(() => {
        handleIframeError(
          errorMessage,
          errorType,
          errorCode,
          { 
            originalEvent: event,
            url: props.src,
            userAgent: navigator.userAgent
          }
        )
      })
  } else {
    handleIframeError(
      '无效的URL地址',
      'LOAD_ERROR',
      'INVALID_URL',
      { url: props.src }
    )
  }
}

// 创建详细错误对象
const createErrorObject = (
  type: IframeError['type'],
  message: string,
  code?: string | number,
  details?: any
): IframeError => {
  return {
    type,
    message,
    code,
    url: props.src,
    timestamp: Date.now(),
    retryCount: retryCount.value,
    details
  }
}

const handleIframeError = (errorMessage?: string, errorType?: IframeError['type'], errorCode?: string | number, errorDetails?: any) => {
  if (loadTimer.value) {
    clearTimeout(loadTimer.value)
    loadTimer.value = null
  }
  
  loading.value = false
  const finalErrorMessage = errorMessage || t('iframe.error.load_failed')
  const finalErrorType = errorType || 'UNKNOWN_ERROR'
  
  // 创建详细错误对象
  const errorObject = createErrorObject(finalErrorType, finalErrorMessage, errorCode, errorDetails)
  
  error.value = finalErrorMessage
  status.value = 'error'
  
  // 发送详细错误信息到外部 (新版本)
  emit('error', errorObject)
  
  console.error('[IframeContainer] iframe加载失败:', errorObject)
}

// 重试加载
const retryLoad = () => {
  if (retryCount.value >= props.retryAttempts) {
    const maxRetriesError = createErrorObject(
      'LOAD_ERROR',
      t('iframe.error.max_retries_exceeded'),
      'MAX_RETRIES',
      { maxRetries: props.retryAttempts }
    )
    error.value = maxRetriesError.message
    emit('error', maxRetriesError)
    return
  }
  
  retryCount.value++
  loading.value = true
  error.value = null
  status.value = 'loading'
  
  emit('retry', retryCount.value)
  
  // 更新时间戳强制重新加载
  staticTimestamp.value = Date.now()
  
  // 强制重新加载iframe
  if (iframeRef.value) {
    iframeRef.value.src = iframeSrc.value
  }
  
  // 设置加载超时
  setupLoadTimeout()
  
  if (props.debug) {
    console.log(`[IframeContainer] 重试加载 (${retryCount.value}/${props.retryAttempts})`)
  }
}

// 设置加载超时
const setupLoadTimeout = () => {
  if (loadTimer.value) {
    clearTimeout(loadTimer.value)
  }
  
  loadTimer.value = setTimeout(() => {
    if (loading.value) {
      handleIframeError(
        t('iframe.error.load_timeout'),
        'TIMEOUT_ERROR',
        'LOAD_TIMEOUT',
        { timeout: props.loadTimeout, url: props.src }
      )
    }
  }, props.loadTimeout)
}

// 生命周期
onMounted(() => {
  // 监听postMessage
  if (props.enableCommunication) {
    window.addEventListener('message', handlePostMessage)
  }
  
  // 设置初始状态
  status.value = 'loading'
  setupLoadTimeout()
  
  if (props.debug) {
    console.log('[IframeContainer] 组件已挂载')
  }
})

onUnmounted(() => {
  // 清理事件监听器
  if (props.enableCommunication) {
    window.removeEventListener('message', handlePostMessage)
  }
  
  // 清理定时器
  if (loadTimer.value) {
    clearTimeout(loadTimer.value)
  }
  
  if (props.debug) {
    console.log('[IframeContainer] 组件已卸载')
  }
})

// 监听src变化
watch(() => props.src, () => {
  loading.value = true
  error.value = null
  status.value = 'loading'
  retryCount.value = 0
  // 更新时间戳以确保URL变化
  staticTimestamp.value = Date.now()
  setupLoadTimeout()
}, { immediate: false })

// 暴露方法给父组件
defineExpose({
  sendMessage,
  retryLoad,
  iframeRef,
  status,
  loading,
  error
})
</script>

<style lang="scss" module>
.iframe-container {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  
  &:hover {
    .debug-info {
      opacity: 1;
    }
  }
}

.debug-info {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

// iframe 强制适应容器
iframe {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  border: none !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  
  // 自定义滚动条
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.4);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.6);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}
</style> 