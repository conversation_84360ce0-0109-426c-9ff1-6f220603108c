<template>
  <div :class="$style['warper']" v-loading="memberLoading">
    <div class="mb-4 text-xl font-semibold">
      {{ $t("setting.peopleManage") }}
    </div>
    <div :class="$style.content">
      <el-tabs v-model="tab" :class="$style.tabs">
        <el-tab-pane :label="$t('setting.peopleManage')" name="member-list">
          <div :class="$style.header">
            <div class="flex items-center gap-2">
              <el-input
                v-model="searchKey"
                :class="[$style.searchInputFilter, $style.focus]"
                :placeholder="$t('setting.memberMgr.placeholder.search')"
                @input="debounceSearch()"/>
              <div :class="$style.batchIcons" v-if="selectedRows.length">
                <Icon icon="mdi:delete" class="text-[red] text-[16px]" @click="handleBatchDelete"/>
              </div>
            </div>

            <div :class="$style.batchActions">
              <el-button
                type="primary"
                :loading="inviteMemberLoading"
                @click="handleInvite">
                <Icon icon="mdi:content-copy" class="text-[#fff] text-[16px]"/>
                <span class="ml-1">{{ $t("setting.memberMgr.invite") }}</span>
              </el-button>
            </div>
          </div>
          <el-table
            :data="memberList"
            :class="$style.table"
            border
            @selection-change="handleSelectionChange"
            :row-class-name="$style.tableRow">
            <el-table-column
              type="selection"
              width="55"
              :selectable="selectable"/>
            <el-table-column
              prop="user_name"
              :label="$t('setting.memberMgr.userName')">
              <template #default="{ row }">
                <div :class="$style.userName" class="overflow-hidden">
                  <div
                    v-overflow-tooltip
                    class="overflow-hidden text-ellipsis whitespace-nowrap">
                    {{ personalNameEmail(row) }}
                  </div>
                  <div v-if="isSelf(row)" :class="$style.mySelf">
                    {{ $t("button.myself") }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="email"
              :label="$t('setting.memberMgr.email')"/>
            <el-table-column
              prop="created_at"
              :label="$t('setting.memberMgr.createdAt')">
              <template #default="{ row }">
                <div style="display: flex; align-items: center">
                  <span style="margin-left: 10px">{{
                    formatCalendarDateTime(row.company.created_at)
                  }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="user_type"
              :label="$t('setting.memberMgr.role')">
              <template #default="{ row }">
                <el-dropdown
                  trigger="click"
                  :disabled="!canEditRole(row)"
                  @command="(type: UserType) => handleRoleChange(type, row)">
                  <span class="flex" style="line-height: 23px">
                    <el-link
                      :underline="false"
                      :class="$style.roleColumnReference">
                      <span>{{ t(USER_TYPE_MAP[row.user_type as UserType].label) }}</span>
                      <Icon v-if="canEditRole(row)" icon="mdi:chevron-down" class="ml-1 text-xs"/>
                    </el-link>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item 
                        v-for="role in availableRoles" 
                        :key="role.value"
                        :command="role.value">
                        {{ role.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
            <el-table-column :label="$t('setting.memberMgr.action')">
              <template #default="{ row }">
                <div
                  v-if="canDeleteUser(row)"
                  :class="[$style.actionCell]"
                  ref="actionCell">
                  <Icon icon="mdi:dots-horizontal" class="text-[#333] text-[16px]" :class="$style.iconMore"/>
                  <div :class="$style.iconWarper">
                    <Icon icon="mdi:delete" class="text-[red] text-[16px]" @click="onCommand('remove', row)"/>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div :class="$style['pagination-box']" style="height: 46px">
            <el-pagination
              v-if="total"
              background
              v-model:current-page="page.pageNum"
              v-model:page-size="page.pageSize"
              :class="$style.pagination"
              layout="total, jumper, prev, pager, next"
              :total="total"
              @current-change="handleCurrentChange"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from 'vue'
import { useGet, usePost } from '@/hooks/useHttp'
import type { Member } from './types'
import { ElMessage, ElMessageBox } from 'element-plus'
import { definePage } from 'vue-router/auto'
import { formatCalendarDateTime } from '@/utils/filter'
import { debounce, isNil } from 'lodash-es'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import { UserType, USER_TYPE_MAP } from '@/stores/user'

definePage({
  meta: {
    requiresAuth: true
  }
})

const { t } = useI18n()

const { execute: getMemberList, isLoading: memberLoading } = usePost<Member>(
  '/company/employees',
  {},
  {},
  {
    immediate: false,
  }
)

const { execute: deleteMember } = usePost(
  '/company/delete_employees',
  {},
  {},
  {
    immediate: false,
  }
)

const { execute: updateMember, isLoading: updateMemberLoading } = usePost(
  '/company/update_employee',
  undefined,
  {},
  {
    immediate: false,
  }
)

const { execute: getInviteCode, isLoading: inviteMemberLoading } = useGet<{ invite_code: string }>(
  '/company/get_invite_code',
  undefined,
  {
    immediate: false,
  }
)
const page = reactive({ pageNum: 1, pageSize: 25 })
const total = ref(0)
const searchKey = ref('')
const tab = ref<'member-list' | 'invite-list'>('member-list')
const selectedRows = ref<Member['items']>([])
const selectable = (row: Member['items'][number]) => !isSelf(row)
function handleCurrentChange(val: number) {
  page.pageNum = val
  onSearch()
}
async function onSearch(clear = false) {
  if (memberLoading.value) {
    return
  }
  if (clear) {
    page.pageNum = 1
  }
  const params = {
    user_name: searchKey.value,
    page_size: page.pageSize,
    page: page.pageNum,
  }
  const data = await getMemberList(0, params)
  if (!isNil(data)) {
    const items = data.items || []
    memberList.value = items.sort((a, b) => {
      if (isSelf(a)) {
        return -1
      }
      if (isSelf(b)) {
        return 1
      }
      return 0
    })
    total.value = data.meta.total
  }
}
const debounceSearch = debounce(onSearch, 500)

const memberList = ref<Member['items']>([])
const userStore = useUserStore()
const { userId, userInfo } = storeToRefs(userStore)

function isSelf(row: Member['items'][number]) {
  return userId.value === row.id
}

function personalNameEmail(user: Member['items'][number]) {
  console.log('user', user)
  const name = user.user_name || user.email?.split('@')[0] || '-'
  return name
}

onMounted(() => {
  onSearch()
})

const availableRoles = computed(() => {
  const roles = []
  if (userStore.hasPermission('canManageUsers')) {
    roles.push({ value: UserType.CompanyAdmin, label: t(USER_TYPE_MAP[UserType.CompanyAdmin].label) })
  }
  if (userStore.hasPermission('canManageRoles')) {
    roles.push({ value: UserType.CompanyUser, label: t(USER_TYPE_MAP[UserType.CompanyUser].label) })
  }
  return roles
})

function canEditRole(row: Member['items'][number]) {
  if (isSelf(row)) {
    return false
  }
  const userType = row.user_type as UserType
  return userType !== UserType.CompanyCreator || userStore.hasPermission('canManageBilling')
}

function canDeleteUser(row: Member['items'][number]) {
  if (isSelf(row)) {
    return false
  }
  const userType = row.user_type as UserType
  return userType !== UserType.CompanyCreator || userStore.hasPermission('canManageBilling')
}

async function handleRoleChange(type: UserType, row: Member['items'][number]) {
  if (type === row.user_type) {
    return
  }
  
  try {
    await updateMember(0, {
      employee_id: row.id,
      user_type: type,
    })
    ElMessage.success(t('setting.memberMgr.updateSuccess'))
    onSearch()
  } catch (error) {
    console.error(error)
    ElMessage.error(t('setting.memberMgr.updateFailed'))
  }
}

async function onCommand(command: string, row: any) {
  if (command === 'remove') {
    const options = {
      title: t('setting.memberMgr.msgboxRemove.title'),
      message: t('setting.memberMgr.removeUser', { name: `<span style="font-weight: 600">${row.user_name}</span>` }),
      confirmButtonText: t('setting.memberMgr.remove'),
      cancelButtonText: t('button.cancel'),
      showCancelButton: true,
      confirmButtonClass: 'el-button--danger',
      dangerouslyUseHTMLString: true,
    }
    
    try {
      await ElMessageBox.confirm(options.message, options.title, options)
      await deleteMember(0, {
        employee_ids: [row.id],
      })
      ElMessage.success(t('setting.memberMgr.removeSuccess'))
      onSearch()
    } catch (error) {
      if (error !== 'cancel') {
        console.error(error)
        ElMessage.error(t('setting.memberMgr.removeFailed'))
      }
    }
  }
}

function handleSelectionChange(val: Member['items']) {
  selectedRows.value = val
}

async function handleBatchDelete() {
  if (!selectedRows.value.length) {
    return
  }
  
  const options = {
    title: t('setting.memberMgr.msgboxRemove.title'),
    message: t('setting.memberMgr.batchRemove', { count: selectedRows.value.length }),
    confirmButtonText: t('setting.memberMgr.remove'),
    cancelButtonText: t('button.cancel'),
    showCancelButton: true,
    confirmButtonClass: 'el-button--danger',
  }
  
  try {
    await ElMessageBox.confirm(options.message, options.title, options)
    await deleteMember(0, {
      employee_ids: selectedRows.value.map(item => item.id),
    })
    ElMessage.success(t('setting.memberMgr.removeSuccess'))
    onSearch()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
      ElMessage.error(t('setting.memberMgr.removeFailed'))
    }
  }
}

async function handleInvite() {
  try {
    const data = await getInviteCode()
    if (!data) { return }
    
    const host = window.location.origin
    const url = `${host}/invite?code=${data.invite_code}`
    await navigator.clipboard.writeText(url)
    ElMessage.success(t('setting.memberMgr.inviteUrlCopied'))
  } catch (error) {
    console.error(error)
    ElMessage.error(t('setting.memberMgr.inviteFailed'))
  }
}
</script>

<style lang="scss" module>
.warper {
  padding: 20px;
  background-color: #fff;
  min-height: 100%;
}

.content {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
}

.tabs {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.searchInputFilter {
  width: 200px;
}

.batchActions {
  display: flex;
  gap: 8px;
}

.table {
  margin-bottom: 16px;
}

.batchIcons {
  display: flex;
  gap: 12px;
  align-items: center;
  cursor: pointer;
}

.tableRow {
  height: 70px;
}

.pagination-box {
  display: flex;
  justify-content: flex-end;
}

.pagination {
  margin-top: 16px;
}

.userName {
  display: flex;
  align-items: center;
}

.mySelf {
  margin-left: 8px;
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--el-color-info-light-9);
  border-radius: 4px;
  font-size: 12px;
  color: var(--el-color-info);
}

.actionCell {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.iconWarper {
  display: flex;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  width: 100%;
  height: 100%;
  justify-content: center;
}

.actionCell:hover {
  .iconMore {
    opacity: 0;
  }
  .iconWarper {
    opacity: 1;
    pointer-events: auto;
  }
}

.roleColumnReference {
  color: inherit;
  &:hover {
    color: var(--el-color-primary);
  }
}
</style>
