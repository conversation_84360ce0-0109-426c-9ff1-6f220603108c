<template>
  <el-form :model="form" label-width="auto" label-position="top">
    <!-- 基础设置 -->
    <FormSection :fields="basicFields" :form="form"/>

    <!-- 边距设置 -->
    <el-form-item :label="$t('intelligence.markdown.margins')">
      <div :class="$style.marginsContainer">
        <FormMarginItem
          v-for="field in marginFields"
          :key="field.key"
          :field="field"
          v-model="margins[field.key as keyof typeof margins]"
          @change="updateMargins"/>
      </div>
    </el-form-item>

    <!-- 标题设置 -->
    <FormSection :fields="titleFields" :form="form.titleSection"/>

    <!-- 页眉设置 -->
    <el-divider>{{ $t("intelligence.markdown.headerSettings") }}</el-divider>
    <div :class="$style.headerSettings">
      <FormSection :fields="headerFields" :form="form.headerConfig"/>
    </div>

    <!-- 页脚设置 -->
    <el-divider>{{ $t("intelligence.markdown.footerSettings") }}</el-divider>
    <FormSection 
      :fields="footerFields" 
      :form="form.footerConfig" 
      input-type="textarea" 
      :rows="2"/>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import type { PdfOptions } from '@/hooks/usePdf/types'
import FormSection from './FormSection.vue'
import FormMarginItem from './FormMarginItem.vue'

// 类型定义
interface FormField {
  key: string
  label: string
  type?: 'select' | 'input' | 'textarea'
  options?: { label: string; value: string }[]
  placeholder?: string
}

// 表单字段配置
const basicFields: FormField[] = [
  {
    key: 'pageSize',
    label: 'intelligence.markdown.pageSize',
    type: 'select',
    options: [
      { label: 'A4', value: 'A4' },
      { label: 'A3', value: 'A3' },
      { label: 'Letter', value: 'LETTER' }
    ]
  },
  {
    key: 'pageOrientation',
    label: 'intelligence.markdown.orientation',
    type: 'select',
    options: [
      { label: 'intelligence.markdown.portrait', value: 'portrait' },
      { label: 'intelligence.markdown.landscape', value: 'landscape' }
    ]
  }
]

const marginFields: FormField[] = [
  { key: 'top', label: 'intelligence.markdown.marginTop' },
  { key: 'right', label: 'intelligence.markdown.marginRight' },
  { key: 'bottom', label: 'intelligence.markdown.marginBottom' },
  { key: 'left', label: 'intelligence.markdown.marginLeft' }
]

const titleFields: FormField[] = [
  { key: 'title', label: 'intelligence.markdown.title' },
  { key: 'subtitle', label: 'intelligence.markdown.subtitle' },
  { key: 'contact.company', label: 'intelligence.markdown.company' },
  { key: 'contact.website', label: 'intelligence.markdown.website' },
  { key: 'contact.email', label: 'intelligence.markdown.email' }
]

const headerFields: FormField[] = [
  { key: 'left', label: 'intelligence.markdown.headerLeft' },
  { key: 'center', label: 'intelligence.markdown.headerCenter' },
  { key: 'right', label: 'intelligence.markdown.headerRight' }
]

const footerFields: FormField[] = [
  { key: 'copyright', label: 'intelligence.markdown.footerCopyright' },
  { key: 'confidential', label: 'intelligence.markdown.footerConfidential' }
]

// Props & Emits
const props = defineProps<{
  modelValue: PdfOptions
}>()

const emit = defineEmits<{
  'update:modelValue': [value: PdfOptions]
}>()

// 响应式数据
const DEFAULT_MARGIN = 40

const margins = reactive({
  left: (props.modelValue.pageMargins as number[])?.[0] ?? DEFAULT_MARGIN,
  top: (props.modelValue.pageMargins as number[])?.[1] ?? DEFAULT_MARGIN,
  right: (props.modelValue.pageMargins as number[])?.[2] ?? DEFAULT_MARGIN,
  bottom: (props.modelValue.pageMargins as number[])?.[3] ?? DEFAULT_MARGIN,
})

const form = reactive<PdfOptions>({
  ...props.modelValue,
})

// 方法
const updateMargins = () => {
  form.pageMargins = [margins.left, margins.top, margins.right, margins.bottom]
  emit('update:modelValue', { ...form })
}

// 监听
watch(
  () => props.modelValue,
  (newValue) => {
    Object.assign(form, newValue)
    if (Array.isArray(newValue.pageMargins)) {
      margins.left = newValue.pageMargins[0]
      margins.top = newValue.pageMargins[1]
      margins.right = newValue.pageMargins[2] as number
      margins.bottom = newValue.pageMargins[3] as number
    }
  },
  { deep: true }
)

watch(
  () => ({ ...form }),
  (newValue) => {
    emit('update:modelValue', newValue)
  },
  { deep: true }
)
</script>

<style lang="scss" module>
.marginsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.headerSettings {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

:global(.el-divider) {
  margin: 24px 0 16px;
}

:global(.el-form-item) {
  margin-bottom: 18px;
}
</style>
