<!-- Generated by SpecStory -->

# CompositeAgent Report Generation Logic (2025-03-12 17:03:40)

_**User**_

在 @[id].vue 中的handleGenerateDetailReport，如果singleReportData.value?.report_type === CompositeAgent，需要同时生产RecentNewsAndSocialOpinionTrackingReport、CompanyLegalAnalysisReport两个报告

---

_**Assistant**_

```thinking
让我思考一下这个需求...

首先需要理解当前的代码逻辑:
1. 目前handleGenerateDetailReport只会根据report_type生成一个报告
2. 如果是SocialAgent生成RecentNewsAndSocialOpinionTrackingReport
3. 如果是其他类型生成CompanyLegalAnalysisReport

需要修改的地方:
1. 需要判断report_type是否为CompositeAgent
2. 如果是CompositeAgent,需要同时生成两个报告
3. 需要处理两个API请求的情况
4. 需要考虑错误处理

实现思路:
1. 在handleGenerateDetailReport中添加CompositeAgent的判断
2. 如果是CompositeAgent,使用Promise.all同时发送两个请求
3. 如果有任一请求失败,需要给出错误提示
4. 成功后统一更新UI

让我看看具体如何修改代码...

需要注意:
1. 保持现有的错误处理逻辑
2. 确保UI反馈的连贯性
3. 避免重复发送请求
4. 保持代码的可维护性

这个改动相对简单,主要是添加一个条件分支和并行请求处理。
```

我建议对`[id].vue`做如下修改:

```typescript:[id].vue
// ... existing code ...

const handleGenerateDetailReport = async (step: AgentStep) => {
  console.log('Generating detail report...', step, singleReportData.value)
  
  try {
    if (singleReportData.value?.report_type === 'CompositeAgent') {
      // 同时生成两个报告
      const requests = [
        {
          report_type: 'RecentNewsAndSocialOpinionTrackingReport',
          company_name: step.title,
        },
        {
          report_type: 'CompanyLegalAnalysisReport', 
          company_name: step.title,
        }
      ]
      
      await Promise.all(requests.map(data => delayReport(0, data)))
      ElMessage.success(t('common.success'))
    } else {
      // 原有逻辑
      const reportId = singleReportData.value?.report_type === 'SocialAgent' 
        ? 'RecentNewsAndSocialOpinionTrackingReport' 
        : 'CompanyLegalAnalysisReport'
      
      const requestData = {
        report_type: reportId,
        company_name: step.title,
      }
      
      await delayReport(0, requestData)
      ElMessage.success(t('common.success'))
    }
    
    emitter.emit('async:update')
  } catch (error: any) {
    console.error('API Error:', error)
    ElMessage.error(error?.message || t('common.error'))
  }
}

// ... existing code ...
```

主要改动:
1. 添加了对CompositeAgent类型的判断
2. 如果是CompositeAgent,使用Promise.all同时发送两个报告生成请求
3. 保持了原有的错误处理和成功提示逻辑
4. 维持了现有的事件通知机制

这样修改后,当report_type为CompositeAgent时会同时生成两个报告,其他情况保持不变。

---

_**User**_

在 @[id].vue 的handleGenerateDetailReport函数中，分支太复杂，需要你重新整理以下逻辑优化

---

_**Assistant**_

```thinking
让我重新思考一下handleGenerateDetailReport函数的逻辑...

当前的问题:
1. 代码结构嵌套太深,有多层if-else
2. 重复的代码片段(ElMessage.success和emitter.emit)
3. 请求数据的构造逻辑分散

优化思路:
1. 将报告类型判断逻辑提取出来
2. 统一处理请求数据的构造
3. 减少代码重复
4. 让逻辑更加清晰直观

可以这样重构:
1. 先根据report_type确定需要生成的报告类型列表
2. 统一构造请求数据
3. 统一发送请求
4. 统一处理结果

这样可以让代码更加清晰,更容易维护。让我写一个优化版本...
```

建议这样修改handleGenerateDetailReport函数:

```typescript:src/pages/dashboard/[id].vue
const handleGenerateDetailReport = async (step: AgentStep) => {
  console.log('Generating detail report...', step, singleReportData.value)
  
  try {
    // 确定需要生成的报告类型
    const reportTypes = singleReportData.value?.report_type === 'CompositeAgent'
      ? ['RecentNewsAndSocialOpinionTrackingReport', 'CompanyLegalAnalysisReport']
      : [singleReportData.value?.report_type === 'SocialAgent' 
          ? 'RecentNewsAndSocialOpinionTrackingReport'
          : 'CompanyLegalAnalysisReport']

    // 构造并发送请求
    await Promise.all(
      reportTypes.map(reportType => 
        delayReport(0, {
          report_type: reportType,
          company_name: step.title
        })
      )
    )

    ElMessage.success(t('common.success'))
    emitter.emit('async:update')
  } catch (error: any) {
    console.error('API Error:', error)
    ElMessage.error(error?.message || t('common.error'))
  }
}
```

主要优化点:
1. 将报告类型判断逻辑集中,使用数组统一管理
2. 使用map统一构造请求数据
3. 统一使用Promise.all处理请求
4. 减少了代码重复
5. 逻辑更加清晰直观

这样修改后代码更简洁,逻辑更清晰,同时保持了原有的功能。

---

_**User**_

在 @index.vue 中agentList的返回结构变成了：[
    {
        "id": "67d1454e8f6312422e6585da",
        "updated_at": "2025-03-12T19:23:05.755000",
        "summary": [
            "深圳市五株科技股份有限公司",
            "广州广合科技股份有限公司",
            "健鼎(无锡)电子有限公司"
        ],
        "report_name": "CompositeAgent",
        "report_type": "CompositeAgent",
        "status": "success",
        "risk_status": "Danger",
        "agent_name": "综合风险预警专家",
        "source": "agent"
    },
    {
        "id": "67d017dd10ec2640c899226a",
        "updated_at": "2025-03-12T19:16:06.130000",
        "summary": [
            "四川睿杰鑫电子股份有限公司"
        ],
        "report_name": "LegalAgent",
        "report_type": "LegalAgent",
        "status": "success",
        "risk_status": "Danger",
        "agent_name": "法律风险预警专家",
        "source": "agent"
    }
]

请你修改逻辑适配这个数据结构

---

_**Assistant**_

```thinking
我需要分析一下当前的问题和需要修改的地方：

1. 数据结构变化：
   - 原来的agentList.value可能是一个对象，包含items和meta属性
   - 现在agentList.value直接是一个数组，包含agent对象

2. 错误信息：
   - 第337行：Parameter 'agent' implicitly has an 'any' type - 需要为agent参数添加类型
   - 第337行：Property 'map' does not exist on type '{ items: CompletedAgent[]; meta: { total_pages: number; agent_last_id: string; report_last_id: string; }; }' - 因为数据结构变了，现在直接是数组
   - 第407行：Parameter 'a' implicitly has an 'any' type - 需要为a参数添加类型

3. 需要修改的地方：
   - completedAgents计算属性中的数据处理逻辑
   - 可能需要更新CompletedAgent类型定义
   - 处理summary字段，现在是数组而不是字符串

让我思考一下如何修改：

1. 首先需要更新CompletedAgent类型定义，添加新的字段并修改summary类型
2. 修改completedAgents计算属性，直接处理agentList.value作为数组
3. 处理summary字段，将数组转换为字符串或适当显示
4. 修改其他相关逻辑，确保与新数据结构兼容

这个修改相对简单，主要是适配新的数据结构和添加类型定义。
```

我需要修改`src/pages/dashboard/index.vue`文件以适配新的数据结构：

```typescript:src/pages/dashboard/index.vue
// ... existing code ...

// 更新API返回类型定义
const {
  state: agentList,
  execute: getAgentList,
  isLoading: isAgentListLoading,
} = usePost<CompletedAgent[]>(
  '/formatted_report/report_list',
  {},
  {},
  {
    immediate: false,
  }
)

// 修改completedAgents计算属性
const completedAgents = computed(() => {
  // Memoize the transformation using Map for better performance
  const memoizedAgents = new Map()

  return agentList.value?.map((agent: CompletedAgent) => {
    const cachedAgent = memoizedAgents.get(agent.id)
    if (cachedAgent) {return cachedAgent}

    // 处理summary字段，如果是数组则转换为字符串
    const summaryText = Array.isArray(agent.summary) 
      ? agent.summary.join('\n') 
      : agent.summary

    const transformedAgent = {
      id: agent.id,
      name: agent.agent_name || t('agent.no_agent_name', { name: agent.report_name }),
      duty: agent.report_name,
      report_type: agent.report_type,
      summary: summaryText,
      status: agent.risk_status,
      avatar: AVATAR_MAP[agent.report_type as keyof typeof AVATAR_MAP],
      updated_at: agent.updated_at,
    }

    memoizedAgents.set(agent.id, transformedAgent)
    return transformedAgent
  }) || []
})

// ... existing code ...

// 修改displayAgents相关的watch
watch(completedAgents, (newAgents) => {
  if (currentPage.value === 1) {
    displayAgents.value = newAgents
  } else {
    // Only append new agents that don't already exist
    const existingIds = new Set(displayAgents.value.map((a: Agent) => a.id))
    const newUniqueAgents = newAgents.filter((a: Agent) => !existingIds.has(a.id))
    displayAgents.value.push(...newUniqueAgents)
  }
}, { deep: true })

// ... rest of the code remains unchanged
```

主要修改点：
1. 更新了API返回类型为`CompletedAgent[]`数组
2. 在completedAgents计算属性中添加了类型注解`(agent: CompletedAgent)`
3. 添加了对summary字段的处理，如果是数组则转换为字符串
4. 在watch中为map和filter函数的参数添加了类型注解`(a: Agent)`

这些修改解决了TypeScript类型错误，并确保代码能够正确处理新的数据结构。

---

