<template>
  <el-popover
    placement="bottom"
    trigger="click">
    <div class="flex justify-center items-start flex-col">
      <el-button class="ml-[8px]" type="text" @click="toSettings">{{ $t('setting.menuTitle') }}</el-button>
      <el-button type="text" @click="logout">{{ $t('login.logout') }}</el-button>
    </div>
    <template #reference>
      <div class="flex justify-center items-center mr-[40px]" v-if="isLogin">
        <UserAvatar/>
        <div class="flex flex-col">
          <span class="font-bold text-[18px]">{{userInfo?.user_name}}</span>
          <span v-if="userInfo?.email" class="text-gray-400">{{ userInfo?.email }}</span>
        </div>
      </div>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'
import UserAvatar from './UserAvatar.vue'
import router from '@/router'
const userStore = useUserStore()
const { userInfo, userId } = storeToRefs(userStore)
const isLogin = computed(()=> !!userId.value)
const logout = () => {
  // 这里添加退出登录的逻辑
  userStore.logout()
}
const toSettings = () => {
  router.push(router.resolve('/settings/personal-info'))
}
</script>

<style lang="scss" module>
/* 添加你的样式 */
</style>
