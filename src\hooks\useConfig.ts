let config: ImportMetaEnv | null = null

function initializeConfig() {
  const newConfig = {} as ImportMetaEnv

  // 加载环境变量
  Object.keys(import.meta.env).forEach((key: keyof ImportMetaEnv) => {
    if ((key as string).startsWith('VITE_')) {
      newConfig[key] = import.meta.env[key]
    }
  })

  // 如果存在全局配置，覆盖相应的值
  const globalConfig = (window as any).$config as ImportMetaEnv | undefined
  if (globalConfig) {
    Object.keys(globalConfig).forEach((key) => {
      newConfig[key] = globalConfig[key]
    })
  }

  return newConfig
}

export function useConfig() {
  if (config === null) {
    config = initializeConfig()
  }
  return config
}
