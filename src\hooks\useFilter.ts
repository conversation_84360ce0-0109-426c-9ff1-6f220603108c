/**
 * useFilter Hook
 * 
 * 通用的高性能过滤器，提供基础的搜索、过滤和缓存功能。
 * 
 * 核心功能：
 * 1. 搜索机制
 *    - 多字段搜索：支持同时搜索多个字段
 *    - 智能分词：自动处理搜索词的分割和组合
 *    - 模糊匹配：支持部分词匹配和完整词匹配
 *    - 评分系统：基于匹配度的结果排序
 * 
 * 2. 缓存系统
 *    - 搜索结果缓存：避免重复计算
 *    - LRU 策略：自动清理过期缓存
 *    - 可配置容量：灵活控制缓存大小
 * 
 * 3. 性能优化
 *    - 计算缓存：使用 computed 属性
 *    - 防抖处理：避免频繁搜索
 *    - 惰性评估：按需计算结果
 * 
 * 4. 自定义能力
 *    - 评分函数：支持自定义评分逻辑
 *    - 过滤条件：支持自定义过滤规则
 *    - 排序方式：支持自定义排序方法
 *    - 字段获取：灵活配置搜索字段
 * 
 * 配置接口：
 * ```ts
 * interface FilterConfig<T> {
 *   // 获取要搜索的字段值
 *   getSearchFields: (item: T) => string[]
 *   // 自定义评分函数（可选）
 *   calculateScore?: (item: T, searchTerms: string[]) => number
 *   // 自定义过滤条件（可选）
 *   filterCondition?: (item: T) => boolean
 *   // 自定义排序函数（可选）
 *   sortFn?: (a: MatchResult<T>, b: MatchResult<T>) => number
 * }
 * ```
 * 
 * 使用示例：
 * ```ts
 * interface DataItem {
 *   id: string
 *   title: string
 *   description: string
 * }
 * 
 * const { 
 *   filteredItems,    // 过滤后的数据
 *   handleSearch,     // 搜索处理函数
 *   clearCache,       // 清除缓存
 *   trimCache,        // 清理过期缓存
 *   config           // 当前配置
 * } = useFilter<DataItem>(items, {
 *   getSearchFields: (item) => [
 *     item.title,
 *     item.description
 *   ],
 *   calculateScore: (item, terms) => {
 *     // 自定义评分逻辑
 *     return 1
 *   }
 * }, {
 *   debounceTime: 300,     // 防抖时间
 *   maxCacheSize: 100,     // 最大缓存数
 *   caseSensitive: false,  // 是否区分大小写
 *   exactMatch: false      // 是否完整匹配
 * })
 * ```
 * 
 * 评分规则：
 * - 完整匹配：2分
 * - 部分匹配：1分
 * - 不匹配：0分
 * - 最终分数：所有匹配分数之和
 * 
 * 性能特征：
 * - 时间复杂度：O(N * M * K)
 *   - N: 数据项数量
 *   - M: 搜索词数量
 *   - K: 每项数据的字段数量
 * - 空间复杂度：O(C * N)
 *   - C: 缓存容量
 *   - N: 平均结果集大小
 * 
 * @template T - 数据项类型
 * @param items - 数据源的响应式引用
 * @param config - 过滤器配置
 * @param options - 过滤器选项
 * @returns 过滤器实例
 */

import { computed, ref, type Ref } from 'vue'
import { useDebounceFn } from '@vueuse/core'

export interface FilterOptions {
  debounceTime?: number
  maxCacheSize?: number
  caseSensitive?: boolean
  exactMatch?: boolean
}

export interface SearchOptions {
  caseSensitive?: boolean
  exactMatch?: boolean
}

export interface MatchResult<T> {
  item: T
  score: number
}

export interface FilterConfig<T> {
  // 获取要搜索的字段值
  getSearchFields: (item: T) => string[]
  // 自定义评分函数（可选）
  calculateScore?: (item: T, searchTerms: string[]) => number
  // 自定义过滤条件（可选）
  filterCondition?: (item: T) => boolean
  // 自定义排序函数（可选）
  sortFn?: (a: MatchResult<T>, b: MatchResult<T>) => number
}

export function useFilter<T>(
  items: Ref<T[] | null | undefined>,
  config: FilterConfig<T>,
  options: FilterOptions = {}
) {
  const {
    debounceTime = 300,
    maxCacheSize = 100,
    caseSensitive: defaultCaseSensitive = false,
    exactMatch: defaultExactMatch = false
  } = options

  const searchTerm = ref('')
  const currentCaseSensitive = ref(defaultCaseSensitive)
  const currentExactMatch = ref(defaultExactMatch)
  const searchCache = new Map<string, T[]>()

  // 清理过期缓存
  const trimCache = () => {
    if (searchCache.size > maxCacheSize) {
      const keysToDelete = Array.from(searchCache.keys())
        .slice(0, searchCache.size - maxCacheSize)
      keysToDelete.forEach(key => searchCache.delete(key))
    }
  }

  // 添加缓存
  const addToCache = (key: string, value: T[]) => {
    searchCache.set(key, value)
    trimCache()
  }

  // 清除缓存
  const clearCache = () => searchCache.clear()

  // 规范化文本
  const normalizeText = (text: string | undefined | null): string[] => {
    if (!text) {return []}
    const processedText = currentCaseSensitive.value ? text : text.toLowerCase()
    return processedText
      .split(/[,，、\s]+/)
      .map(t => t.trim())
      .filter(Boolean)
  }

  // 检查字符串匹配
  const isMatch = (source: string, target: string): boolean => {
    if (!source || !target) {return false}
    const normalizedSource = currentCaseSensitive.value ? source : source.toLowerCase()
    const normalizedTarget = currentCaseSensitive.value ? target : target.toLowerCase()
    
    return currentExactMatch.value 
      ? normalizedSource === normalizedTarget
      : normalizedSource.includes(normalizedTarget) || normalizedTarget.includes(normalizedSource)
  }

  // 过滤计算
  const filteredItems = computed(() => {
    if (!items.value || !searchTerm.value) {
      return items.value || []
    }

    const searchKey = `${searchTerm.value}:${currentCaseSensitive.value}:${currentExactMatch.value}`
    
    if (searchCache.has(searchKey)) {
      return searchCache.get(searchKey)
    }

    const searchTerms = normalizeText(searchTerm.value)
    if (!searchTerms.length) {return items.value}

    const results = items.value
      .filter(config.filterCondition || (() => true))
      .map(item => {
        const score = config.calculateScore?.(item, searchTerms) ?? 0
        return { item, score }
      })
      .filter(result => result.score > 0)

    const sortedResults = results.sort(config.sortFn || 
      ((a, b) => b.score - a.score))

    const filtered = sortedResults.map(result => result.item)
    addToCache(searchKey, filtered)
    return filtered
  })

  // 搜索处理函数
  const handleSearch = useDebounceFn((
    term: string,
    searchOptions?: SearchOptions
  ) => {
    searchTerm.value = term
    if (searchOptions) {
      currentCaseSensitive.value = searchOptions.caseSensitive ?? defaultCaseSensitive
      currentExactMatch.value = searchOptions.exactMatch ?? defaultExactMatch
      clearCache()
    }
  }, debounceTime)

  return {
    searchTerm,
    filteredItems,
    handleSearch,
    clearCache,
    trimCache,
    config: {
      get caseSensitive() { return currentCaseSensitive.value },
      get exactMatch() { return currentExactMatch.value },
      maxCacheSize
    }
  }
} 