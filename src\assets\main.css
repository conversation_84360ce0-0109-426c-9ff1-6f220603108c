@import './base.css' layer(base);
@import 'element-plus/dist/index.css' layer(base);
@import './var.scss' layer(base);
@import './element.scss' layer(base);
@import './shadcn-theme.css' layer(base);
@import 'tailwindcss';
@import "tw-animate-css";
/*
  ---break---
*/
@custom-variant dark (&:is(.dark *));

@config '../../tailwind.config.js';

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}
/*
  ---break---
*/
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}
/*
  ---break---
*/
:root {
  /* 基础色调 */
  --background: #ffffff;
  --foreground: #1f2134; /* 使用--font-color-1 */
  --card: #ffffff;
  --card-foreground: #1f2134;
  --popover: #ffffff;
  --popover-foreground: #1f2134;

  /* 主色调 - 基于#4466BC */
  --primary: #4466BC; /* 系统主色 */
  --primary-foreground: #ffffff;
  
  /* 次要色调 - 基于品牌色系列 */
  --secondary: #f0f2ff; /* --brand-color-1 */
  --secondary-foreground: #35518f; /* --brand-color-8 */
  
  /* 柔和色调 */
  --muted: #f2f1f5; /* --purpleGray-2 */
  --muted-foreground: #797a85; /* --font-color-2 */
  
  /* 强调色 */
  --accent: #b2c0ff; /* --brand-color-3 */
  --accent-foreground: #263c62; /* --brand-color-9 */
  
  /* 危险色 */
  --destructive: #cc3c39; /* --danger-color */
  --destructive-foreground: #ffffff;
  
  /* 边框和输入框 */
  --border: #d1d8ff; /* --brand-color-2 */
  --input: #e8e7f0; /* --purpleGray-3 */
  --ring: #5578ff; /* --brand-color-6 */
  
  /* 图表颜色 - 蓝色渐变系列 */
  --chart-1: #5578ff; /* --brand-color-6 */
  --chart-2: #4466BC; /* --primary-color */
  --chart-3: #35518f; /* --brand-color-8 */
  --chart-4: #263c62; /* --brand-color-9 */
  --chart-5: #131523; /* --purpleGray-11 */
  
  /* 侧边栏 */
  --sidebar: #fbfbfc; /* --purpleGray-1 */
  --sidebar-foreground: #1f2134; /* --font-color-1 */
  --sidebar-primary: #4466BC; /* --primary-color */
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #d1d8ff; /* --brand-color-2 */
  --sidebar-accent-foreground: var(--brand-color-6); /* --brand-color-3 */
  --sidebar-border: #e8e7f0; /* --purpleGray-3 */
  --sidebar-ring: #4466BC; /* --primary-color */
  
  /* 保持原有的字体和圆角设置 */
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem; /* 8px */
  
  /* 阴影保持不变 */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}
/*
  ---break---
*/
.dark {
  /* 基础色调 */
  --background: #131523; /* --purpleGray-11 */
  --foreground: #ffffff;
  --card: #1f2134; /* --font-color-1 */
  --card-foreground: #ffffff;
  --popover: #1f2134;
  --popover-foreground: #ffffff;
  
  /* 主色调 */
  --primary: #5578ff; /* --brand-color-6，稍亮的主色 */
  --primary-foreground: #ffffff;
  
  /* 次要色调 */
  --secondary: #263c62; /* --brand-color-9 */
  --secondary-foreground: #ffffff;
  
  /* 柔和色调 */
  --muted: #33344b; /* --purpleGray-9 */
  --muted-foreground: #a5a6ae; /* --font-color-3 */
  
  /* 强调色 */
  --accent: #35518f; /* --brand-color-8 */
  --accent-foreground: #d1d8ff; /* --brand-color-2 */
  
  /* 危险色 */
  --destructive: #eb4542; /* --state-danger-icon */
  --destructive-foreground: #ffffff;
  
  /* 边框和输入框 */
  --border: #33344b; /* --purpleGray-9 */
  --input: #33344b; /* --purpleGray-9 */
  --ring: #5578ff; /* --brand-color-6 */
  
  /* 图表颜色 - 蓝色系列但更亮 */
  --chart-1: #93a8ff; /* --brand-color-4 */
  --chart-2: #7490ff; /* --brand-color-5 */
  --chart-3: #5578ff; /* --brand-color-6 */
  --chart-4: #4466BC; /* --primary-color */
  --chart-5: #35518f; /* --brand-color-8 */
  
  /* 侧边栏 */
  --sidebar: #1f2134; /* --font-color-1 */
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #5578ff; /* --brand-color-6 */
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #35518f; /* --brand-color-8 */
  --sidebar-accent-foreground: #d1d8ff; /* --brand-color-2 */
  --sidebar-border: #33344b; /* --purpleGray-9 */
  --sidebar-ring: #5578ff; /* --brand-color-6 */
  
  /* 保持字体和圆角设置不变 */
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  
  /* 阴影保持不变 */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}
/*
  ---break---
*/
@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
