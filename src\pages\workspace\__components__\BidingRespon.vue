<template>
  <div class="relative h-full flex flex-col">
    <!-- 加载状态 -->
    <!-- <div v-if="isLoading || apiLoading" class="flex flex-col items-center justify-center p-6 space-y-2">
      <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
      <p class="text-sm text-slate-600">{{ t('agent.loading') }}</p>
    </div> -->
    
    <!-- 情报官头像和信息显示 (放在 Card 外面) -->
    <!-- <div class="flex gap-3 slide-in-from-bottom-5 duration-300 mb-3">
      <div class="relative flex-shrink-0">
        <Avatar class="ring-2 ring-white shadow-sm h-10 w-10">
          <AvatarFallback v-if="officerType === 'notify'" class="bg-gradient-to-br from-amber-400 to-orange-400 text-white">
            <BellRingIcon class="h-5 w-5"/>
          </AvatarFallback>
          <AvatarFallback v-else-if="officerType === 'binding'" class="bg-gradient-to-br from-blue-400 to-indigo-400 text-white">
            <LayersIcon class="h-5 w-5"/>
          </AvatarFallback>
          <AvatarFallback v-else class="bg-gradient-to-br from-violet-400 to-indigo-400 text-white">
            <UsersIcon class="h-5 w-5"/>
          </AvatarFallback>
        </Avatar>
      </div>
      
      <div class="flex-1 space-y-2 max-w-[calc(100%-60px)]">
        <div class="flex justify-between items-start">
          <div class="flex flex-col">
            <div class="flex items-center gap-1.5">
              <span class="font-medium text-sm text-slate-800">
                {{ officerName }}
              </span>
              <span v-if="props.isAgentWorking" class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-sky-100 text-sky-700">
                <span class="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-sky-400 opacity-75 mr-1"></span>
                {{ t('biding.working') }}
              </span>
            </div>

            <div class="flex flex-col gap-1 mt-1">
              <div class="flex items-center gap-1">
                <span class="text-xs font-medium text-slate-600">
                  {{ displayReportName }}
                </span>
                <el-tooltip
                  v-if="agentDescription"
                  :content="agentDescription"
                  placement="top"
                  effect="light"
                  popper-class="report-description-tooltip">
                  <Icon icon="mdi:information-outline" class="h-3.5 w-3.5 text-slate-400 hover:text-blue-500 cursor-help"/>
                </el-tooltip>
              </div>
              
              <div v-if="props.isAgentWorking" class="flex items-center text-xs text-sky-600 mt-0.5">
                <span class="relative flex h-2 w-2 mr-1.5">
                  <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
                  <span class="relative inline-flex rounded-full h-2 w-2 bg-sky-500"></span>
                </span>
                <span>{{ t('biding.agent_working_message') }}</span>
              </div>
              <p v-if="resultsData.length > 0 && !isLoading && !apiLoading" class="text-xs text-slate-500 mt-0.5">{{ t('global.bidding_filter_desc', { count: totalItems, country_num: report.query?.nations?.length }) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 表格视图 -->
    <Transition
      name="fade"
      mode="out-in">
      <div :key="showDetail ? 'detail' : 'table'" class="h-full flex flex-col overflow-hidden">
        <div v-if="!showDetail" class="flex flex-col flex-1 h-full">
          <Card class="p-6 flex flex-col flex-1 h-full" :class="{ 'agent-working-border': props.isAgentWorking }">
            <CardHeader class="pb-3">
              <!-- 筛选工具区域 -->
              <div class="flex justify-between items-center">
                <div class="flex items-center gap-4">
                  <!-- 分页器固定在红框位置 -->
                  <div v-if="resultsData.length > 0 && !isLoading && !apiLoading">
                    <el-pagination
                      v-model:current-page="currentPage"
                      v-model:page-size="pageSize"
                      :page-sizes="[10, 20, 30, 50]"
                      :layout="'total, sizes, prev, pager, next'"
                      :total="totalItems"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      background
                      :small="true"
                      class="text-xs"/>
                  </div>
                  
                  <!-- 批量操作控件 -->
                  <Transition
                    enter-active-class="transition-all duration-300 ease-out"
                    enter-from-class="opacity-0 scale-95"
                    enter-to-class="opacity-100 scale-100"
                    leave-active-class="transition-all duration-200 ease-in"
                    leave-from-class="opacity-100 scale-100"
                    leave-to-class="opacity-0 scale-95">
                    <div v-if="selectionState.selectedCount > 1" class="flex items-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/60 px-3 py-1.5 rounded-lg shadow-sm">
                      <div class="flex items-center gap-2">
                        <div class="flex items-center justify-center w-4 h-4 bg-blue-100 rounded-full">
                          <span class="text-xs font-medium text-blue-600">{{ selectionState.selectedCount }}</span>
                        </div>
                        <span class="text-xs font-medium text-blue-700">
                          {{ t('biding.selected_count', { count: selectionState.selectedCount }) }}
                        </span>
                      </div>
                      <div class="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          @click="clearSelection"
                          :disabled="selectionState.isUnliking"
                          class="text-slate-600 hover:text-slate-800 hover:bg-white/60 h-6 px-2 text-xs">
                          {{ t('common.cancel') }}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          @click="executeBatchUnlike"
                          :disabled="selectionState.isUnliking"
                          class="bg-red-500 hover:bg-red-600 text-white h-6 px-2 text-xs">
                          <div v-if="selectionState.isUnliking" class="animate-spin rounded-full h-3 w-3 border-b border-white mr-1"></div>
                          {{ t('biding.batch_unlike') }}
                        </Button>
                      </div>
                    </div>
                  </Transition>
                </div>
                <div class="flex items-center gap-2 relative" v-if="!isLoading && !apiLoading">
                  <!-- 显示/隐藏筛选条件按钮 -->
                  <Button
                    v-if="hasActiveFilters"
                    variant="ghost"
                    size="sm"
                    @click="showActiveFilters = !showActiveFilters"
                    class="h-7 text-xs flex items-center gap-1 text-blue-700">
                    <Icon icon="mdi:eye" class="h-3.5 w-3.5"/>
                    {{ showActiveFilters ? t('biding.hide_filters') : t('biding.show_filters') }}
                    <ChevronDownIcon 
                      :class="{'rotate-180': showActiveFilters}" 
                      class="h-3.5 w-3.5"/>
                  </Button>
                  
                  <div ref="filterButtonRef"> <!-- Ref for onClickOutside ignore -->
                    <Button
                      variant="outline"
                      size="sm"
                      @click="handleToggleFilters"
                      class="h-7 text-xs flex items-center gap-1"
                      :class="{ 'bg-blue-50 border-blue-200 text-blue-700': hasActiveFilters }">
                      <DatabaseIcon class="h-3.5 w-3.5"/>
                      {{ t('biding.filter_data') }}
                      <span v-if="hasActiveFilters" class="ml-1 bg-blue-500 text-white rounded-full px-1.5 py-0.5 text-xs font-medium min-w-[16px] h-4 flex items-center justify-center">
                        {{ activeFiltersCount }}
                      </span>
                      <ChevronDownIcon 
                        :class="{'rotate-180': showFilters}" 
                        class="h-3.5 w-3.5"/>
                    </Button>
                  </div>
                  
                  <BidingFilterPopover
                    v-model="showFilters"
                    :trigger-element="filterButtonRef"
                    :nation-options-for-filter="nationOptionsForFilter"
                    :is-loading-filter-nations="isLoadingFilterNations"
                    :initial-nations="appliedNations"
                    @apply-filters="handleApplyFromPopover"
                    @reset-filters-and-fetch="handleResetFromPopover"/>
                </div>
              </div>
              
              <!-- 当前筛选条件显示区域 - 默认折叠 -->
              <Transition
                enter-active-class="transition-all duration-300 ease-out"
                enter-from-class="opacity-0 -translate-y-2"
                enter-to-class="opacity-100 translate-y-0"
                leave-active-class="transition-all duration-200 ease-in"
                leave-from-class="opacity-100 translate-y-0"
                leave-to-class="opacity-0 -translate-y-2">
                <div v-if="hasActiveFilters && showActiveFilters" class="mt-3 p-3 bg-blue-50/40 rounded border border-blue-100/50">
                  <div class="flex items-center justify-between gap-3">
                    <div class="flex items-center gap-2 min-w-0 flex-1">
                      <span class="text-xs font-medium text-blue-700 flex-shrink-0">{{ t('biding.current_filters') }}:</span>
                      <div class="flex items-center gap-1.5 flex-wrap">
                        <!-- 国家筛选标签 -->
                        <template v-if="appliedNations.length > 0">
                          <div 
                            v-for="nation in appliedNations" 
                            :key="nation"
                            class="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs font-medium hover:bg-blue-200 transition-colors">
                            <Icon icon="mdi:flag" class="h-3 w-3"/>
                            <span>{{ nation }}</span>
                            <button 
                              @click="removeNationFilter(nation)"
                              class="ml-0.5 hover:bg-blue-300 rounded-full p-0.5 transition-colors"
                              :title="t('biding.remove_filter', { name: nation })">
                              <Icon icon="mdi:close" class="h-3 w-3"/>
                            </button>
                          </div>
                        </template>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="clearAllFilters"
                      class="text-slate-600 hover:text-slate-800 hover:bg-white/60 h-6 px-2 text-xs flex-shrink-0">
                      <Icon icon="mdi:filter-off" class="h-3 w-3 mr-1"/>
                      {{ t('biding.clear_all_filters') }}
                    </Button>
                  </div>
                </div>
              </Transition>
            </CardHeader>
            <CardContent class="flex-1 h-full flex flex-col p-0 overflow-hidden">
              <BidingTableView
                ref="tableRef"
                :items="paginatedResults as any"
                :is-loading="isLoading"
                :api-loading="apiLoading"
                :total-original-items="resultsData.length"
                :get-score-class="getScoreClass"
                :format-date-string="formatDateString"
                :sort-key="currentSortKey"
                :sort-order="currentSortOrder"
                @sort="handleTableSort"
                @view-item-detail="viewDetail"
                @reset-filters="handleResetFromPopover"
                @batch-unlike="handleBatchUnlike"
                @selection-change="handleSelectionChange"
                @ai-processing="handleAiProcessing"/>
            </CardContent>
          </Card>
        </div>

        <BidingDetailView
          v-else
          :selected-item="selectedItem"
          :selected-item-basic-info="selectedItemBasicInfo"
          :detail-loading-state="detailLoadingState"
          :all-items="filteredAndSortedResults"
          :current-index="currentItemIndex"
          :get-score-class="getScoreClass"
          :format-date-string="formatDateString"
          :t="t" 
          :hex-to-rgb="hexToRgb"
          @close-detail="closeDetail"
          @navigate-to-item="navigateToItem"
          @navigate-previous="navigateToPrevious"
          @navigate-next="navigateToNext"/>
      </div>
    </Transition>

    <!-- AI Dashboard Sheet -->
    <AiDashboardSheet
      v-model:open="showAiDashboard"
      :project-id="aiProcessingData.projectId"
      :project-title="aiProcessingData.projectTitle"
      @message="handleAiMessage"/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, onMounted, watch } from 'vue'

import { useGet } from '@/hooks/useHttp'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronDownIcon, DatabaseIcon, BellRingIcon, UsersIcon, LayersIcon } from 'lucide-vue-next'
import { useI18n } from 'vue-i18n'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import type { Report, BidingAnalysisResult, BidingAnalysisItem, BidingResultItem } from '../types'
import BidingFilterPopover from './BidingFilterPopover.vue'
import BidingTableView from './BidingTableView.vue'
import BidingDetailView from './BidingDetailView.vue'
import AiDashboardSheet from '@/components/AiDashboardSheet.vue'
import { Icon } from '@iconify/vue'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import dayjs from 'dayjs'

// Define a type for individual scoring dimensions
interface ScoringDimension {
  score?: number;
  name?: string;
  reasoning?: string;
}

// Define a type for the overall scoring object, allowing specific keys we expect
// and a general index signature for flexibility if other scores might exist.
interface ScoringData {
  client_authority?: ScoringDimension;
  vendor_bias?: ScoringDimension;
  project_amount?: ScoringDimension;
  information_completeness?: ScoringDimension;
  future_collaboration?: ScoringDimension;
  comprehensive?: ScoringDimension & { breakdown?: Record<string, number> };
  language?: string;
  [key: string]: any; // Allows other keys, but we primarily care about the typed ones
}

// Define the Props interface for the component
interface Props {
  report: BidingAnalysisResult;
  viewReport?: (report: Report) => void;
  apiLoading: boolean;
  isAgentWorking?: boolean;
  agentDescriptions?: Record<string, any>;
  officerName?: string;
  officerType?: 'notify' | 'normal' | 'binding';
  loadBidingDetail?: (itemId: string) => Promise<BidingAnalysisItem | null>;
  currentBidingDetail?: BidingAnalysisItem | null;
  isDetailLoading?: boolean;
  clearDetailCache?: () => void;
}

const props = defineProps<Props>()

const { t } = useI18n()
const emit = defineEmits(['apply-filters', 'refresh-data'])

// 从 props 中获取情报官相关信息
const officerType = computed(() => props.officerType || 'binding')
const officerName = computed(() => props.officerName || t('agent.filter.binding'))

// 获取 agent 描述信息
const agentDescription = computed(() => {
  // if (!props.agentDescriptions || !props.report) {
  //   return ''
  // }
  // const reportType = props.report.report_type
  // return reportType && props.agentDescriptions[reportType] || ''
  return props.agentDescriptions?.find((item: any) => item.report_type === props.report.id)?.description || ''
})

// 显示名称
const displayReportName = computed(() => {
  return props.agentDescriptions?.find((item: any) => item.report_type === props.report.id)?.report_name || ''
})

// UI状态
const showFilters = ref(false) // This will now fully control the custom popover
const showActiveFilters = ref(false) // 控制筛选条件显示/隐藏状态，默认折叠
const filterButtonRef = ref<HTMLElement | null>(null)
const queryCollapsed = ref(true) // 默认折叠查询条件

// 使用vueuse的useBreakpoints判断屏幕尺寸
// const breakpoints = useBreakpoints({
//   sm: 640,
//   md: 768,
//   lg: 1024
// })

// const isMobileView = computed(() => breakpoints.smaller('md'))

// 分页状态
const currentPage = ref(1)
const pageSize = ref(50)

// 批量选择状态
const selectionState = ref({
  selectedItems: [] as string[],
  isAllSelected: false,
  isIndeterminate: false,
  selectedCount: 0,
  isUnliking: false
})

// Refs for currently APPLIED filters
const appliedNations = ref<string[]>([])

// 表格组件引用
const tableRef = ref<any>(null)

// API-fetched filter options data
const { execute: fetchFilterNations, state: rawNationsForFilter, isLoading: isLoadingFilterNations } = useGet<string[]>('/bid_agent/nations', {}, { immediate: false })

// --- 排序状态 ---
type SortOrder = 'asc' | 'desc' | null
const currentSortKey = ref<string | null>('update_time') // 默认按推荐评分排序
const currentSortOrder = ref<SortOrder>('desc') // 默认降序

// --- 排序事件处理 ---
const handleTableSort = ({ key, order }: { key: string; order: SortOrder }) => {
  currentSortKey.value = key
  currentSortOrder.value = order
}

// --- Helper: 获取嵌套对象的值 ---
const getValue = (obj: any, path: string | null): any => {
  if (!path || typeof path !== 'string') {return undefined}
  try {
    return path.split('.').reduce((o, k) => (o || {})[k], obj)
  } catch (e) {
    console.error(`Error accessing path ${path} in object:`, obj, e)
    return undefined
  }
}
// --------------------

const loadFilterOptionsIfNeeded = async () => {
  if (!rawNationsForFilter.value && !isLoadingFilterNations.value) {
    await fetchFilterNations()
  }
}

const handleToggleFilters = () => {
  showFilters.value = !showFilters.value
  if (showFilters.value) {
    loadFilterOptionsIfNeeded()
  }
}

// 视图状态控制
const showDetail = ref(false)
const selectedItem = ref<BidingAnalysisItem | null>(null)

// AI Dashboard Sheet 状态
const showAiDashboard = ref(false)
const aiProcessingData = ref({
  projectId: '',
  projectTitle: ''
})

// 新增：详情加载状态管理
const selectedItemBasicInfo = ref<BidingResultItem | null>(null)
const detailLoadingState = ref<'idle' | 'loading' | 'success' | 'error'>('idle')

// 导航相关状态
const currentItemIndex = computed(() => {
  // 优先使用详情数据计算索引
  if (selectedItem.value && filteredAndSortedResults.value.length) {
    return filteredAndSortedResults.value.findIndex(item => item._id === selectedItem.value?._id)
  }
  // 如果没有详情数据，但有基本信息（加载状态），使用基本信息计算索引
  if (selectedItemBasicInfo.value && filteredAndSortedResults.value.length) {
    return filteredAndSortedResults.value.findIndex(item => item._id === selectedItemBasicInfo.value?._id)
  }
  return -1
})

// 导航函数 - 修复：需要重新加载详情数据
const navigateToItem = async (index: number) => {
  if (index >= 0 && index < filteredAndSortedResults.value.length) {
    const targetItem = filteredAndSortedResults.value[index]
    
    // 立即更新基本信息和状态
    selectedItemBasicInfo.value = targetItem
    selectedItem.value = null // 清空之前的详情数据
    detailLoadingState.value = 'loading'
    
    // 异步加载新的详情数据
    if (props.loadBidingDetail && typeof props.loadBidingDetail === 'function') {
      try {
        const detailData = await props.loadBidingDetail(targetItem._id)
        if (detailData) {
          selectedItem.value = detailData
          detailLoadingState.value = 'success'
        } else {
          console.warn('Failed to load detail data for item:', targetItem._id)
          detailLoadingState.value = 'error'
          // 2秒后自动返回列表
          setTimeout(() => {
            closeDetail()
          }, 2000)
        }
      } catch (error) {
        console.error('Error loading detail data:', error)
        detailLoadingState.value = 'error'
        // 2秒后自动返回列表
        setTimeout(() => {
          closeDetail()
        }, 2000)
      }
    }
  }
}

const navigateToPrevious = async () => {
  const currentIndex = currentItemIndex.value
  if (currentIndex > 0) {
    await navigateToItem(currentIndex - 1)
  }
}

const navigateToNext = async () => {
  const currentIndex = currentItemIndex.value
  if (currentIndex < filteredAndSortedResults.value.length - 1) {
    await navigateToItem(currentIndex + 1)
  }
}

// 获取原始招标结果数据
const resultsData = computed((): BidingResultItem[] => {
  // Check if props.report exists and has the 'results' property
  if (props.report && Array.isArray(props.report.results)) {
    return props.report.results
  }
  return []
})

// Computed options for new filters using API data
const nationOptionsForFilter = computed(() => {
  return (rawNationsForFilter.value || []).map(nation => ({ label: nation, value: nation }))
})

// --- 修改 filteredAndSortedResults 以包含排序逻辑 ---
const filteredAndSortedResults = computed(() => {
  let items = [...resultsData.value] // Start with original data

  // Apply sorting based on currentSortKey and currentSortOrder
  const key = currentSortKey.value
  const order = currentSortOrder.value

  if (key && order) {
    items.sort((a, b) => {
      const aValue = getValue(a, key)
      const bValue = getValue(b, key)

      // Handle null/undefined consistently (push to bottom regardless of order)
      if (aValue === null && bValue === null) {return 0}
      if (aValue === null) {return 1} // a is null/undefined, goes after b
      if (bValue === null) {return -1} // b is null/undefined, goes after a

      // Specific comparison logic based on key
      if (key === 'scoring.comprehensive.score' || key === 'recommendation.recommend_score') {
        // Numerical comparison
        const numA = Number(aValue)
        const numB = Number(bValue)
        if (isNaN(numA) && isNaN(numB)) {return 0}
        if (isNaN(numA)) {return 1}
        if (isNaN(numB)) {return -1}
        return order === 'asc' ? numA - numB : numB - numA
      } if (key === 'extraction.basic_info.submission_deadline' || key === 'update_time') {
        // Date comparison using dayjs
        // Try parsing with '$date' first if it's an object
        const dateStrA = (typeof aValue === 'object' && aValue !== null && '$date' in aValue) ? aValue.$date : aValue
        const dateStrB = (typeof bValue === 'object' && bValue !== null && '$date' in bValue) ? bValue.$date : bValue

        const dateA = dayjs(dateStrA)
        const dateB = dayjs(dateStrB)

        if (!dateA.isValid() && !dateB.isValid()) {return 0}
        if (!dateA.isValid()) {return 1} // Invalid dates go last
        if (!dateB.isValid()) {return -1}

        return order === 'asc' ? dateA.diff(dateB) : dateB.diff(dateA)
      } 
      // Default string comparison (case-insensitive)
      const strA = String(aValue).toLowerCase()
      const strB = String(bValue).toLowerCase()
      if (strA < strB) {return order === 'asc' ? -1 : 1}
      if (strA > strB) {return order === 'asc' ? 1 : -1}
      return 0
      
    })
  }

  return items as BidingAnalysisItem[]
})

// 新增：前端分页后的数据计算属性
const paginatedResults = computed(() => {
  const sortedItems = filteredAndSortedResults.value
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return sortedItems.slice(start, end)
})

// 修改：总数计算 - 使用排序后的数据长度
const totalItems = computed(() => filteredAndSortedResults.value.length)

// 判断是否正在加载
const isLoading = computed(() => {
  // Use lowercase status for comparison
  return !!props.report.is_loading || props.report.status === 'running'
})

// 格式化日期为YYYY-MM-DD
function formatDateString(dateInput: string | Date | { $date: string } | undefined | null): string {
  if (!dateInput) {return '-'}

  let dateString: string | undefined

  if (typeof dateInput === 'string') {
    dateString = dateInput
  } else if (dateInput instanceof Date) {
    dateString = dateInput.toISOString()
  } else if (typeof dateInput === 'object' && dateInput !== null && '$date' in dateInput) {
    dateString = dateInput.$date
  } else {
    // Try converting potential numeric timestamp (assuming milliseconds)
    if (typeof dateInput === 'number' && dateInput > 0) {
      try {
        dateString = new Date(dateInput).toISOString()
      } catch (e) {
        return String(dateInput) // Fallback if number conversion fails
      }
    } else {
      return String(dateInput) // Fallback for other unexpected types
    }
  }


  if (!dateString) {return '-'}

  try {
    // 尝试转换为日期对象
    const date = dayjs(dateString) // Use dayjs for parsing robustness
    // 检查是否为有效日期
    if (!date.isValid()) {
      // 如果不是有效日期，尝试提取日期格式 (Less reliable)
      const match = String(dateString).match(/(\d{4}-\d{2}-\d{2})/)
      return match ? match[1] : (String(dateString).substring(0, 10) || '-') // Fallback to first 10 chars or '-'
    }
    // 返回YYYY-MM-DD格式
    return date.format('YYYY-MM-DD')
  } catch (e) {
    return String(dateString).substring(0, 10) || '-' // Fallback if parsing fails
  }
}


// 根据分数获取Badge样式
function getScoreClass(score: number | undefined): string {
  if (score === undefined || score === null || isNaN(Number(score))) { return 'bg-gray-100 text-gray-600' } // More robust check for invalid score

  if (score >= 8) {return 'bg-green-100 text-green-800'}
  if (score >= 6) {return 'bg-blue-100 text-blue-800'}
  if (score >= 4) {return 'bg-yellow-100 text-yellow-800'}
  return 'bg-red-100 text-red-800'
}

// 获取边框颜色
function getBorderClass(score: number | undefined): string {
  if (score === undefined || score === null || isNaN(Number(score))) { return 'border-gray-200' }

  if (score >= 8) {return 'border-green-200'}
  if (score >= 6) {return 'border-blue-200'}
  if (score >= 4) {return 'border-yellow-200'}
  return 'border-red-200'
}

// 获取所有评分数据
function getAllScoring(item: any): Record<string, any> {
  if (!item || !item.scoring) {return {}}

  return Object.entries(item.scoring)
    .filter(([key, value]) =>
      typeof value === 'object' &&
      value !== null &&
      !Array.isArray(value) &&
      key !== 'comprehensive' &&
      'score' in (value as any)
    )
    .reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {})
}

// 显示详情视图 - 适配新的拆分接口，立即跳转并异步加载
async function viewDetail(item: BidingResultItem) {
  // 1. 立即切换到详情页面
  selectedItemBasicInfo.value = item
  selectedItem.value = null // 清空之前的详情数据
  detailLoadingState.value = 'loading'
  showDetail.value = true
  showFilters.value = false

  // 2. 异步加载详情数据
  if (props.loadBidingDetail && typeof props.loadBidingDetail === 'function') {
    try {
      const detailData = await props.loadBidingDetail(item._id)
      if (detailData) {
        selectedItem.value = detailData
        detailLoadingState.value = 'success'
      } else {
        console.warn('Failed to load detail data for item:', item._id)
        detailLoadingState.value = 'error'
        // 2秒后自动回退到列表页
        setTimeout(() => {
          closeDetail()
        }, 2000)
      }
    } catch (error) {
      console.error('Failed to load detail data:', error)
      detailLoadingState.value = 'error'
      // 2秒后自动回退到列表页
      setTimeout(() => {
        closeDetail()
      }, 2000)
    }
  } else {
    console.warn('loadBidingDetail function not provided, cannot show detail view')
    detailLoadingState.value = 'error'
    setTimeout(() => {
      closeDetail()
    }, 2000)
  }
}

// 关闭详情视图
function closeDetail() {
  showDetail.value = false
  selectedItem.value = null
  selectedItemBasicInfo.value = null
  detailLoadingState.value = 'idle'
}

// Helper function to convert HEX to RGB
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    }
    : null
}

// --- 处理分页器事件 ---
function handleSizeChange(val: number) {
  pageSize.value = val
  // 重置到第一页，避免页面大小变化后出现空白页面
  currentPage.value = 1
  // 前端分页无需重新获取数据
}

function handleCurrentChange(val: number) {
  currentPage.value = val
  // 前端分页无需重新获取数据
}

// 应用筛选条件（移除分页参数，后端不再需要分页信息）
function handleFetchWithFilters() {
  emit('apply-filters', {
    nations: appliedNations.value
  })
}

// New handlers for events from BidingFilterPopover
function handleApplyFromPopover(filtersFromPopover: Record<string, any>) {
  appliedNations.value = filtersFromPopover.nations || []

  // 重置到第一页
  currentPage.value = 1

  // 应用筛选条件并获取数据
  handleFetchWithFilters()

  showFilters.value = false // Close popover after applying
}

function handleResetFromPopover() {
  appliedNations.value = []

  // 重置到第一页
  currentPage.value = 1

  // 应用空筛选条件并获取数据
  handleFetchWithFilters()

  showFilters.value = false // Close popover after resetting
}

// 处理批量不喜欢操作
const handleBatchUnlike = (unlikedIds: string[]) => {
  console.log('Batch unlike completed for IDs:', unlikedIds)
  // 通知父组件刷新数据
  emit('refresh-data')
}

// 处理表格选择变化
const handleSelectionChange = (selection: any) => {
  console.log('🎯 BidingRespon: handleSelectionChange called:', selection)
  console.log('🎯 BidingRespon: before update, selectionState.value:', selectionState.value)
  
  selectionState.value = selection
  
  console.log('🎯 BidingRespon: after update, selectionState.value:', selectionState.value)
  console.log('🎯 BidingRespon: selectedCount for UI:', selectionState.value.selectedCount)
}

// 清除选择
const clearSelection = () => {
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 执行批量不喜欢
const executeBatchUnlike = () => {
  if (tableRef.value) {
    tableRef.value.handleBatchUnlike()
  }
}

// New computed properties
const hasActiveFilters = computed(() => appliedNations.value.length > 0)
const activeFiltersCount = computed(() => appliedNations.value.length)

// New methods
function removeNationFilter(nation: string) {
  appliedNations.value = appliedNations.value.filter(n => n !== nation)
  // 重置到第一页
  currentPage.value = 1
  handleFetchWithFilters()
}

function clearAllFilters() {
  appliedNations.value = []
  // 重置到第一页
  currentPage.value = 1
  handleFetchWithFilters()
}

// AI Dashboard 相关处理函数
const handleAiProcessing = (data: { projectId: string; projectTitle: string; item: BidingAnalysisItem }) => {
  console.log('🤖 BidingRespon: handleAiProcessing called:', data)
  
  // 设置 AI 处理数据
  aiProcessingData.value = {
    projectId: data.projectId,
    projectTitle: data.projectTitle
  }
  
  // 打开 AI Dashboard Sheet
  showAiDashboard.value = true
}

const handleAiMessage = (message: any) => {
  console.log('🤖 BidingRespon: received AI message:', message)
  // 可以在这里处理来自 AI Dashboard 的消息
}
</script>

<style scoped>
/* 添加文本自动换行的样式 */
.text-wrap-cell {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* URL特殊处理 */
.break-all {
  word-break: break-all;
}

/* 表格相关样式 */
:deep(table) {
  width: 100%;
}

:deep(th),
:deep(td) {
  padding: 8px 12px;
}

/* 保留原有动画样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.card {
  transition: all 0.3s ease;
}

/* Add this if you need specific styles for ElSelectV2Normal or TagsInput */
.filterSelect {
  width: 100%;
  margin-bottom: 12px;
}

.filterKeywordsInput {
 margin-bottom: 12px;
}

/* 新增：Agent工作状态辉光效果 */
.agent-working-border {
  animation: pulse-glow 2s infinite ease-in-out;
  /* Card组件本身应有其边框样式，此类添加辉光效果 */
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 4px 0px rgba(14, 165, 233, 0.3); /* 初始细微辉光 (sky-500) */
  }
  50% {
    box-shadow: 0 0 12px 4px rgba(14, 165, 233, 0.6); /* 更宽、更明显辉光 (sky-500) */
  }
}

.report-description-tooltip {
  max-width: 300px;
  line-height: 1.4;
  word-break: break-word;
}
</style>
  