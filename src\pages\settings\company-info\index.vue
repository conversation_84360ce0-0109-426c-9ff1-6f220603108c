<template>
  <div class="p-6 flex justify-start">
    <Card class="shadow-sm border-[rgba(68,102,188,0.1)] max-w-3xl w-full">
      <CardHeader class="pb-2">
        <CardTitle class="text-xl font-semibold text-[#1f2134]">{{ $t("setting.companyMgr.title") }}</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="grid grid-cols-[120px_1fr] items-center py-4 border-b border-[rgba(68,102,188,0.1)]">
            <Label class="text-sm font-medium text-[--font-color-2] text-left">{{ $t('setting.companyMgr.name') }}</Label>
            <span class="text-[#1f2134] text-xl font-bold">{{ companyInfo?.name?.toUpperCase() || '' }}</span>
          </div>
          
          <div class="grid grid-cols-[120px_1fr] items-center py-4 border-b border-[rgba(68,102,188,0.1)]">
            <Label class="text-sm font-medium text-[--font-color-2] text-left">{{ $t('setting.companyMgr.role') }}</Label>
            <span 
              class="px-3 py-1 rounded-full text-sm font-medium inline-flex items-center justify-center"
              :class="getRoleClass(userInfo?.user_type)"
              style="width: fit-content; min-width: 80px;">
              {{ userRole ? t(userRole.label) : '' }}
            </span>
          </div>
          
          <div class="grid grid-cols-[120px_1fr] items-center py-4">
            <Label class="text-sm font-medium text-[--font-color-2] text-left">{{ $t('setting.companyMgr.createdAt') }}</Label>
            <span class="text-[#1f2134] text-base">{{ dayjs(companyInfo?.created_at).format('YYYY-MM-DD') }}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { definePage } from 'vue-router/auto'
import { useUserStore, UserType } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent 
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'

const { t } = useI18n()
const userStore = useUserStore()
const { userInfo, userRole } = storeToRefs(userStore)
const companyInfo = computed(() => userInfo.value?.company)

const getRoleClass = (userType?: UserType) => {
  switch (userType) {
  case UserType.CompanyCreator:
    return 'bg-[rgba(220,38,38,0.1)] text-[rgb(220,38,38)]'
  case UserType.CompanyAdmin:
    return 'bg-[rgba(34,197,94,0.1)] text-[rgb(34,197,94)]'
  case UserType.CompanyUser:
    return 'bg-[rgba(100,116,139,0.1)] text-[rgb(100,116,139)]'
  default:
    return 'bg-[rgba(100,116,139,0.1)] text-[rgb(100,116,139)]'
  }
}

definePage({
  meta: {
    requiresAuth: true,
  },
})
</script>

<style lang="scss" module>
.container {
  .title {
    color: #1f2134;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
  }
}
</style>
