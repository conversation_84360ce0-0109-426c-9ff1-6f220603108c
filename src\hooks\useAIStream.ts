import { ref, computed, shallowRef } from 'vue'
import { useTimeoutFn, useIntervalFn } from '@vueuse/core'
import type { Ref } from 'vue'

// Types
export type TransportMode = 'fetch' | 'sse' | 'auto'

export interface AIStreamTransport<T> {
  connect: (url: string, options: ConnectionOptions) => Promise<void>
  disconnect: () => void
  send: (data: unknown) => Promise<void>
  onMessage: (handler: (message: T) => void) => void
  onError: (handler: (error: AIStreamError) => void) => void
  onClose: (handler: () => void) => void
}

export interface ConnectionOptions {
  headers?: Record<string, string>
  method?: 'GET' | 'POST'
  body?: unknown
  timeout?: number
}

export class AIStreamError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly retriable: boolean = true,
    public readonly original?: unknown
  ) {
    super(message)
    this.name = 'AIStreamError'
  }

  static isAIStreamError(error: unknown): error is AIStreamError {
    return error instanceof AIStreamError
  }
}

// 状态机定义
export type StreamState = 
  | { status: 'idle' }
  | { status: 'connecting' }
  | { status: 'streaming', progress: number }
  | { status: 'error', error: AIStreamError }
  | { status: 'completed' }

// 智能重试策略
class RetryStrategy {
  private attempts = 0
  private readonly maxAttempts: number
  private readonly baseDelay: number
  private readonly maxDelay: number

  constructor(maxAttempts = 3, baseDelay = 1000, maxDelay = 10000) {
    this.maxAttempts = maxAttempts
    this.baseDelay = baseDelay
    this.maxDelay = maxDelay
  }

  async retry(fn: () => Promise<void>, error: AIStreamError): Promise<void> {
    if (!error.retriable || this.attempts >= this.maxAttempts) {
      throw error
    }

    const delay = Math.min(
      this.baseDelay * Math.pow(2, this.attempts),
      this.maxDelay
    )

    this.attempts++
    await new Promise(resolve => setTimeout(resolve, delay))
    return fn()
  }

  reset(): void {
    this.attempts = 0
  }
}

// 增强的消息解析器
class EnhancedJSONParser<T> {
  private buffer = ''
  private decoder = new TextDecoder()
  private messageCount = 0
  private readonly delimiter: string
  
  constructor(delimiter = '\n') {
    this.delimiter = delimiter
  }

  feed(chunk: Uint8Array): T[] {
    const text = this.decoder.decode(chunk, { stream: true })
    this.buffer += text
    
    const messages: T[] = []
    let delimiterIndex: number
    
    while ((delimiterIndex = this.buffer.indexOf(this.delimiter)) !== -1) {
      const line = this.buffer.slice(0, delimiterIndex).trim()
      this.buffer = this.buffer.slice(delimiterIndex + this.delimiter.length)
      
      if (line) {
        try {
          const parsed = JSON.parse(line)
          messages.push(parsed)
          this.messageCount++
        } catch (e) {
          console.warn('Failed to parse JSON:', line, e)
        }
      }
    }
    
    // 如果缓冲区过大，可能表示数据格式问题
    if (this.buffer.length > 10000) {
      this.buffer = ''
      throw new AIStreamError(
        'Message buffer overflow',
        'PARSER_ERROR',
        false
      )
    }
    
    return messages
  }

  reset(): void {
    this.buffer = ''
    this.messageCount = 0
  }
}

// Transport实现
class FetchTransport<T> implements AIStreamTransport<T> {
  private controller: AbortController | null = null
  private parser: EnhancedJSONParser<T>
  private messageHandler: ((message: T) => void) | null = null
  private errorHandler: ((error: AIStreamError) => void) | null = null
  private closeHandler: (() => void) | null = null

  constructor() {
    this.parser = new EnhancedJSONParser()
  }

  async connect(url: string, options: ConnectionOptions): Promise<void> {
    this.controller = new AbortController()

    try {
      const response = await fetch(url, {
        method: options.method || 'POST',
        headers: options.headers,
        body: options.body ? JSON.stringify(options.body) : undefined,
        signal: this.controller.signal
      })

      if (!response.ok) {
        throw new AIStreamError(
          `HTTP error ${response.status}`,
          'HTTP_ERROR',
          response.status >= 500
        )
      }

      if (!response.body) {
        throw new AIStreamError(
          'No response body',
          'STREAM_ERROR',
          false
        )
      }

      const reader = response.body.getReader()

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          this.closeHandler?.()
          break
        }

        const messages = this.parser.feed(value)
        messages.forEach(msg => this.messageHandler?.(msg))
      }
    } catch (e) {
      const error = AIStreamError.isAIStreamError(e) ? e :
        new AIStreamError(
          'Stream error',
          'STREAM_ERROR',
          true,
          e
        )
      this.errorHandler?.(error)
    }
  }

  disconnect(): void {
    this.controller?.abort()
    this.controller = null
    this.parser.reset()
  }

  async send(data: unknown): Promise<void> {
    throw new AIStreamError(
      'Send not supported in fetch mode',
      'OPERATION_ERROR',
      false
    )
  }

  onMessage(handler: (message: T) => void): void {
    this.messageHandler = handler
  }

  onError(handler: (error: AIStreamError) => void): void {
    this.errorHandler = handler
  }

  onClose(handler: () => void): void {
    this.closeHandler = handler
  }
}

// Hook实现
export interface UseAIStreamOptions<T> {
  url: string
  mode?: TransportMode
  headers?: Record<string, string>
  timeout?: number
  retries?: number
  parser?: EnhancedJSONParser<T>
  onMessage?: (message: T) => void | Promise<void>
  onError?: (error: AIStreamError) => void
  onComplete?: () => void
}

export function useAIStream<T>(options: UseAIStreamOptions<T>) {
  // State
  const state = shallowRef<StreamState>({ status: 'idle' })
  const messages = ref<T[]>([]) as Ref<T[]>
  const transport = ref<AIStreamTransport<T> | null>(null)
  const retryStrategy = new RetryStrategy(options.retries)
  
  // Computed
  const isStreaming = computed(() => 
    state.value.status === 'streaming'
  )
  
  const progress = computed(() => 
    state.value.status === 'streaming' ? state.value.progress : 0
  )

  const error = computed(() => 
    state.value.status === 'error' ? state.value.error : null
  )

  // Timeout handler
  const { start: startTimeout, stop: stopTimeout } = useTimeoutFn(() => {
    if (isStreaming.value) {
      handleError(new AIStreamError(
        'Stream timeout',
        'TIMEOUT_ERROR',
        true
      ))
    }
  }, options.timeout || 60000)

  // Progress tracking
  const { pause: pauseProgress } = useIntervalFn(() => {
    if (state.value.status === 'streaming') {
      state.value = {
        status: 'streaming',
        progress: Math.min(100, state.value.progress + 1)
      }
    }
  }, 1000, { immediate: false })

  const handleError = async (error: AIStreamError) => {
    try {
      if (error.retriable) {
        await retryStrategy.retry(connect, error)
        return
      }
    } catch (e) {
      const finalError = AIStreamError.isAIStreamError(e) ? e : error
      state.value = { status: 'error', error: finalError }
      options.onError?.(finalError)
    }
  }

  const connect = async () => {
    if (state.value.status === 'connecting' || state.value.status === 'streaming') {
      return
    }

    state.value = { status: 'connecting' }
    messages.value = []
    retryStrategy.reset()

    try {
      transport.value = new FetchTransport()

      transport.value.onMessage(message => {
        messages.value.push(message)
        options.onMessage?.(message)
        
        if (state.value.status === 'streaming') {
          state.value = {
            status: 'streaming',
            progress: state.value.progress
          }
        } else {
          state.value = { status: 'streaming', progress: 0 }
        }
      })

      transport.value.onError(handleError)
      
      transport.value.onClose(() => {
        state.value = { status: 'completed' }
        options.onComplete?.()
        stopTimeout()
        pauseProgress()
      })

      await transport.value.connect(options.url, {
        headers: options.headers,
        method: 'POST'
      })

      startTimeout()
    } catch (e) {
      handleError(
        AIStreamError.isAIStreamError(e) ? e :
          new AIStreamError('Connection failed', 'CONNECTION_ERROR', true, e)
      )
    }
  }

  const disconnect = () => {
    transport.value?.disconnect()
    transport.value = null
    state.value = { status: 'idle' }
    stopTimeout()
    pauseProgress()
  }

  return {
    // State
    state: state as Ref<Readonly<StreamState>>,
    messages: messages as Ref<readonly T[]>,
    isStreaming,
    progress,
    error,

    // Methods
    connect,
    disconnect
  }
} 