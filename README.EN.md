# Specific AI

An enterprise-level AI application framework based on Vue 3 + TypeScript.

## 🚀 Tech Stack

- **Framework**: Vue 3
- **Language**: TypeScript
- **Build Tool**: Vite
- **CSS Framework**: TailwindCSS
- **UI Library**: Element Plus
- **Utilities**: VueUse
- **Icons**: Iconify
- **Charts**: ECharts

## 📁 Project Structure

```
src/
├── assets/          # Static assets
│   ├── images/      # Image resources
│   └── styles/      # Style files
├── components/      # Common components
├── const/          # Constants
├── directives/     # Custom directives
├── hooks/          # Composable functions
├── pages/          # Page components
│   ├── __components__/     # Page-level components (excluded from routing)
│   ├── demo/              # Development demo pages (excluded in production)
│   └── [id].vue          # Dynamic route pages
├── utils/          # Utility functions
└── App.vue         # Root component
```

## ✨ Features

- 🎨 Integrated TailwindCSS for atomic CSS
- 📦 Vite-based build for optimal DX
- 🔑 Built-in user authentication
- 📊 Integrated ECharts components
- 🎯 TypeScript support
- 🌐 SVG icon support
- 📱 Responsive design

## 🛠️ Development Guide

### Requirements

- Node.js >= 18
- pnpm >= 8

### IDE Recommendation

- [Cursor](https://cursor.sh/) - AI-powered next-generation IDE
  - Built-in project-level AI coding standards (via `.cursorrules`)
  - Vue 3 + TypeScript support
  - Integrated Git
  - Smart code completion and refactoring
  - Project context awareness

> 💡 This project includes `.cursorrules` configuration for:
> - Tech stack standards (Vue 3, TypeScript, TailwindCSS, etc.)
> - Vue 3 Composition API best practices
> - TypeScript type specifications
> - Styling conventions
> - Project structure conventions
> 
> Using Cursor IDE provides intelligent suggestions and code quality checks based on these standards.

### Installation

```sh
pnpm install
```

### Development Server

```sh
pnpm dev
```

### Production Build

```sh
pnpm build
```

### Router System

#### 1. File System Based Routing

```ts
// vite.config.ts
VueRouter({
  exclude: [
    '**/__components__/**',   // Exclude page components directory
    '**/composables/**',      // Exclude composables directory
    '**/demo/**'              // Exclude demo pages in production
  ]
})
```

#### 2. Route Guards

The project implements multiple route guards in the following order:

1. **Authentication Guard** (auth.guard.ts):
```ts
meta: {
  requiresAuth: boolean     // Requires login
  requiresGuest: boolean    // Guest only
  tryAuth: boolean          // Try authentication but not force
  redirectIfUnauthorized: string  // Redirect path when unauthorized
}
```

2. **Access Control Guard** (access.guard.ts):
```ts
meta: {
  access: {
    features: string[]      // Required features
    roles: string[]         // Required roles
    permissions: string[]   // Required permissions
    rightSchemeType: string[] // Permission scheme types
    fallback: string        // Fallback plan when no permission
  }
  redirectIfNoAccess: string // Redirect path when no access
}
```

3. **Page Cache Guard** (keepAlive.guard.ts):
```ts
meta: {
  keepAlive: boolean       // Enable page caching
}
```

#### 3. Best Practices

1. **Route Naming**:
   - Use kebab-case for files and paths
   - Use `[param]` for dynamic parameters
   - Use `[[param]]` for optional parameters

2. **Access Control**:
   - Prefer `access` config for fine-grained control
   - Set appropriate redirect paths
   - Use `fallback` for degradation plans

### Pinia Store Standards

#### 1. Store Definition

```ts
export const useExampleStore = defineStore('example', () => {
  // 1. State definitions
  const count = ref(0)
  const user = reactive({
    name: '',
    age: 0
  })

  // 2. Getters
  const doubleCount = computed(() => count.value * 2)

  // 3. Actions
  const increment = () => {
    count.value++
  }

  return {
    count,
    user,
    doubleCount,
    increment
  }
})
```

#### 2. Best Practices

1. **State Management**:
   ```ts
   // Use ref for simple types
   const count = ref(0)
   
   // Use reactive for complex objects
   const profileFields = reactive<ProfileFields>(defaults({}, emptyFields))
   ```

2. **API Integration**:
   ```ts
   const { execute, isLoading } = useGet<ProfileFields>(
     '/api/endpoint',
     {},
     { immediate: false }
   )
   ```

3. **Type Definitions**:
   ```ts
   interface ProfileFields {
     industry: string[]
     area: string[]
   }
   ```

4. **Store Composition**:
   ```ts
   const userStore = useUserStore()
   
   const submitAndRefresh = async () => {
     await submitData()
     await userStore.loadAccount()
   }
   ```

#### 3. Naming Conventions

1. **Store Names**:
   - Use `use` prefix
   - Use `Store` suffix
   - Example: `useUserStore`

2. **State Names**:
   - Use descriptive names
   - Avoid abbreviations
   - Example: `activeStep`

3. **Action Names**:
   - Start with verbs
   - Describe intent
   - Example: `setActiveStep`

## 🔧 Configuration

### Environment Variables

Project uses a layered configuration system with the following priority:

1. `window.$config` (public/config.js)
2. `.env.local` (local development)
3. `.env` (base configuration)

See detailed configuration in respective files.

## 📄 License

[MIT](LICENSE)