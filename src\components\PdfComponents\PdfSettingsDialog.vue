<template>
  <el-drawer
    v-model="visible"
    :title="$t('intelligence.markdown.pdfsettings')"
    :size="'80%'"
    :direction="'rtl'"
    :class="$style.drawer">
    <div :class="$style.container">
      <div :class="$style.settingsPanel">
        <el-scrollbar>
          <PdfSettingsForm 
            v-model="form" 
            @update:modelValue="handleFormChange"/>
        </el-scrollbar>
      </div>

      <div :class="$style.previewPanel">
        <PdfPreview :content="content"
                    :title="title"
                    :options="form"
                    :images="images"
                    :searchSource="searchSource"/>
      </div>
    </div>

    <template #footer>
      <div :class="$style.footer">
        <el-button @click="visible = false">
          {{ $t("button.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleConfirm">
          {{ $t("button.confirm") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { useVModel } from '@vueuse/core'
import type { PdfOptions } from '@/hooks/usePdf/types'
import {
  createDocumentStyles,
  DEFAULT_HEADER_CONFIG,
  DEFAULT_FOOTER_CONFIG,
  DEFAULT_TITLE_CONFIG
} from '@/hooks/usePdf/styles/documentStyles'
import PdfSettingsForm from './PdfSettingsForm.vue'
import PdfPreview from './PdfPreview.vue'
import type { CreatedReport } from '@/pages/format-report/type'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()
interface Props {
  content: HTMLElement;
  title: string;
  modelValue: boolean;
  images?: Record<string, string>
  searchSource?: CreatedReport['search_result']
  timelineData?: CreatedReport['time_line']
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [value: PdfOptions];
}>()

const visible = useVModel(props, 'modelValue', emit)
const defaultSettings = createDocumentStyles(locale.value as 'chinese' | 'english' | 'japanese')

const form = reactive<PdfOptions>({
  pageSize: defaultSettings.pageSize || 'A4',
  pageOrientation: defaultSettings.pageOrientation || 'portrait',
  pageMargins: defaultSettings.pageMargins || [40, 60, 40, 60],
  headerConfig: { ...DEFAULT_HEADER_CONFIG },
  footerConfig: { ...DEFAULT_FOOTER_CONFIG },
  titleSection: { ...DEFAULT_TITLE_CONFIG, title: props.title },
})

const handleConfirm = () => {
  emit('confirm', { ...form })
  visible.value = false
}

const handleFormChange = (newValue: PdfOptions) => {
  Object.assign(form, newValue)
}
</script>

<style lang="scss" module>
.drawer {
  :global(.el-drawer__body) {
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

.container {
  display: grid;
  grid-template-columns: 50% 50%;
}

.settingsPanel {
  padding: 20px;
}

.previewPanel {
  flex: 1;
}

.footer {
  padding: 16px;
  text-align: right;
  border-top: 1px solid var(--el-border-color-lighter);
}
</style>
