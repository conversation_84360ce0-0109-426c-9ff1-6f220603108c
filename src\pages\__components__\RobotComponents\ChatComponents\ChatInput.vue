<template>
  <div 
    class="p-4 flex" 
    :class="$style.root">
    <el-input
      v-model="message"
      :placeholder="placeholder"
      ref="inputRef"
      :rows="1"
      :autofocus="true"
      @keydown.enter="canSend && sendMessage()"
      :class="{'hover:cursor-not-allowed':loading}">
      <template #suffix>
        <Icon 
          v-if="!loading"
          icon="mdi:send" 
          class="text-[24px] cursor-pointer hover:opacity-80" 
          :class="[
            canSend ? 'text-[#4466BC]' : 'text-gray-300 cursor-not-allowed',
          ]"
          @click="canSend && sendMessage()"/>
        <Icon 
          v-else
          icon="mdi:pause" 
          class="text-[#4466BC] text-[24px] cursor-pointer hover:opacity-80" 
          @click="pauseMessage"/>
      </template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import { Icon } from '@iconify/vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '提问'
  },
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits<{
  sendMessage: [message: string];
  pauseMessage: [];
  'update:modelValue': [value: string];
}>()

const message = ref('')
const inputRef = ref()

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  message.value = newVal
})

// 监听 message 变化
watch(() => message.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 自动聚焦
onMounted(() => {
  nextTick(() => {
    inputRef.value?.input?.focus()
  })
})

// 判断是否可以发送
const canSend = computed(() => {
  return !props.loading && message.value.trim().length > 0
})

const sendMessage = () => {
  if (!canSend.value) {return}
  emit('sendMessage', message.value.trim())
  message.value = ''
}

const pauseMessage = () => {
  emit('pauseMessage')
}

// 暴露方法给父组件
defineExpose({
  focus: () => inputRef.value?.input?.focus()
})
</script>
<style lang="scss" module>
.root {
  border-radius: 48px;
  background-color: #ffffff;
  box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
  :global(.el-input .el-input__wrapper) {
    background: none;
    box-shadow: none;
  }
  :global(.el-input .el-input__inner) {
    height: 30px;
  }
  :global(.el-input .el-input__inner::placeholder) {
    color: #ccc;
    font-size: 20px;
  }
}
</style>
