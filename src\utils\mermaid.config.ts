import type { MermaidConfig } from 'mermaid'

// mermaid 主题配置
export const mermaidTheme = {
  themeVariables: {
    // 使用具体的颜色值，而不是 CSS 变量
    primaryColor: '#4466BC',
    primaryTextColor: '#ffffff',
    primaryBorderColor: '#93a8ff',
    
    // 背景和边框颜色
    clusterBkg: '#f0f2ff', // 对应 brand-color-1
    clusterBorder: '#b2c0ff', // 对应 brand-color-3
    
    // 次要颜色
    secondaryColor: '#f0f2ff',
    secondaryTextColor: '#575872',
    secondaryBorderColor: '#d1d8ff',

    // 背景色
    mainBkg: '#ffffff',
    nodeBkg: '#f0f2ff',
    
    // 文字颜色
    textColor: '#1f2134',
    lineColor: '#575872',
    
    // 边框颜色
    border1: '#d1d8ff',
    border2: '#b2c0ff',

    // 特殊状态颜色
    errorBkgColor: '#fee9e8',
    errorTextColor: '#cc3c39'
  }
}

// mermaid 图表配置
export const mermaidConfig: MermaidConfig = {
  startOnLoad: true,
  theme: 'base',
  themeVariables: mermaidTheme.themeVariables,
  securityLevel: 'loose',
  flowchart: {
    htmlLabels: true,
    curve: 'linear',
    nodeSpacing: 50,
    rankSpacing: 50,
    padding: 15
  },
  sequence: {
    actorMargin: 50,
    boxMargin: 10,
    noteMargin: 10,
    messageMargin: 35
  }
}

// mermaid 样式 - 使用 !important 来确保样式优先级
export const mermaidStyles = `
<style>
.mermaid-wrapper {
  background: var(--main-bg-color) !important;
  border: 1px solid var(--brand-color-2) !important;
  border-radius: var(--radius-default) !important;
  padding: var(--m-default) !important;
  margin: var(--m-default) 0 !important;
}

/* 使用更具体的选择器并添加 !important */
#mermaid svg .cluster rect {
  fill: var(--brand-color-1) !important;
  stroke: var(--brand-color-3) !important;
  rx: var(--radius-small) !important;
  ry: var(--radius-small) !important;
}

#mermaid svg .node rect {
  fill: var(--main-bg-color) !important;
  stroke: var(--brand-color-4) !important;
}

#mermaid svg .edgePath .path {
  stroke: var(--purpleGray-7) !important;
}

#mermaid svg .arrowheadPath {
  fill: var(--purpleGray-7) !important;
  stroke: none !important;
}

#mermaid svg .nodeLabel {
  color: var(--font-color-1) !important;
  font-family: inherit !important;
}
</style>
`

// 修改渲染函数，确保每个图表有唯一的 ID
export const renderMermaidChart = (code: string): string => {
  const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`
  return `
    ${mermaidStyles.replace(/#mermaid/g, `#${id}`)}
    <div class="mermaid-wrapper">
      <pre class="mermaid" id="${id}">
        ${code}
      </pre>
    </div>
  `
} 