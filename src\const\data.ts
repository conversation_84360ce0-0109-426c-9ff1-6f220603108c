interface ComboLabels {
  searchLabel?: string;
  filterLabel?: string;
  outputLabel?: string;
}

const DEFAULT_LABELS: Required<ComboLabels> = {
  searchLabel: '搜索阶段',
  filterLabel: '筛选阶段', 
  outputLabel: '输出阶段'
}

export const createFlowChartData = (labels: ComboLabels = {}) => {
  const finalLabels = { ...DEFAULT_LABELS, ...labels }
  
  return {
    nodes: [
      {
        id: 'search_web',
        label: '搜索各章节网络资料',
        comboId: 'search_combo'
      },
      {
        id: 'search_internal',
        label: '搜索内部数据库资料',
        comboId: 'search_combo'
      },
      {
        id: 'filter_keywords',
        label: '根据关键词筛选资料',
        comboId: 'filter_combo'
      },
      {
        id: 'score_filter',
        label: '给所有资料进行打分',
        comboId: 'filter_combo'
      },
      {
        id: 'credibility_score',
        label: '可信度',
        comboId: 'filter_combo'
      },
      {
        id: 'importance_score',
        label: '重要性',
        comboId: 'filter_combo'
      },
      {
        id: 'summary',
        label: '将章节资料进行提炼总结'
      },
      {
        id: 'thinking',
        label: '思考分析',
        comboId: 'output_combo'
      },
      {
        id: 'reflection',
        label: '反思',
        comboId: 'output_combo'
      },
      {
        id: 'output',
        label: '输出章节内容',
        comboId: 'output_combo'
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'search_web',
        target: 'search_internal'
      },
      {
        id: 'edge-2',
        source: 'search_internal',
        target: 'filter_keywords'
      },
      {
        id: 'edge-3',
        source: 'filter_keywords',
        target: 'score_filter'
      },
      {
        id: 'edge-4a',
        source: 'score_filter',
        target: 'credibility_score'
      },
      {
        id: 'edge-4b',
        source: 'score_filter',
        target: 'importance_score'
      },
      {
        id: 'edge-5a',
        source: 'credibility_score',
        target: 'summary'
      },
      {
        id: 'edge-5b',
        source: 'importance_score',
        target: 'summary'
      },
      {
        id: 'edge-6',
        source: 'summary',
        target: 'thinking'
      },
      {
        id: 'edge-7',
        source: 'thinking',
        target: 'reflection'
      },
      {
        id: 'edge-8',
        source: 'reflection',
        target: 'output'
      }
    ],
    combos: [
      {
        id: 'search_combo',
        label: finalLabels.searchLabel,
        style: {
          fill: '#F0F7FF',
          stroke: '#C6E5FF'
        }
      },
      {
        id: 'filter_combo',
        label: finalLabels.filterLabel,
        style: {
          fill: '#F6F0FF',
          stroke: '#E6D3FF'
        }
      },
      {
        id: 'output_combo',
        label: finalLabels.outputLabel,
        style: {
          fill: '#F0FFF2',
          stroke: '#C6FFD1'
        }
      }
    ]
  }
}