<template>
  <div class="p-6 flex justify-start">
    <Card class="shadow-sm border-[rgba(68,102,188,0.1)] max-w-3xl w-full">
      <CardHeader class="pb-2">
        <CardTitle class="text-xl font-semibold text-[#1f2134]">{{ $t("setting.userInfo.loginAccount") }}</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="grid grid-cols-[120px_1fr] items-center py-4 border-b border-[rgba(68,102,188,0.1)]">
            <Label class="text-sm font-medium text-[--font-color-2] text-left">{{ $t('setting.userInfo.account') }}</Label>
            <span class="text-[--font-color-1] font-medium">{{ userInfo?.user_name }}</span>
          </div>
          
          <div class="grid grid-cols-[120px_1fr] items-center py-4 border-b border-[rgba(68,102,188,0.1)]">
            <Label class="text-sm font-medium text-[--font-color-2] text-left">{{ $t('setting.userInfo.email') }}</Label>
            <span class="text-[--font-color-1]">{{ userInfo?.email }}</span>
          </div>
          
          <div class="grid grid-cols-[120px_1fr] items-start py-4 border-b border-[rgba(68,102,188,0.1)]">
            <Label class="text-sm font-medium text-[--font-color-2] text-left pt-2">{{ $t('setting.userInfo.language') }}</Label>
            <div class="flex gap-6">
              <div
                v-for="lang in SUPPORTED_LANGUAGES" 
                :key="lang.value" 
                class="flex items-center space-x-2 cursor-pointer"
                @click="currentLanguage !== lang.value && setLanguage(lang.value)">
                <input 
                  type="radio" 
                  :id="`lang-${lang.value}`" 
                  :value="lang.value" 
                  :checked="currentLanguage === lang.value"
                  class="w-4 h-4 text-[--primary-color] border-gray-300 focus:ring-[--primary-color]"/>
                <Label 
                  :for="`lang-${lang.value}`" 
                  class="cursor-pointer"
                  :class="{'text-[--primary-color] font-medium': currentLanguage === lang.value}">
                  {{ lang.label }}
                </Label>
              </div>
            </div>
          </div>
          
          <div class="grid grid-cols-[120px_1fr] items-center py-4">
            <Label class="text-sm font-medium text-[--font-color-2] text-left">{{ $t('setting.userInfo.password') }}</Label>
            <Button 
              variant="link" 
              class="h-auto p-0 text-[--primary-color] justify-start hover:text-[--primary-color] hover:underline font-medium"
              @click="dialogVisible = true">
              {{ $t("setting.userInfo.changePassword") }}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <changePassword
      :show="dialogVisible"
      @update:show="dialogVisible = $event"/>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { definePage } from 'vue-router/auto'
import changePassword from './__components__/changePassword.vue'
import { useUserStore } from '@/stores/user'
import { useLanguageSwitch } from '@/composables/useLanguageSwitch'
import { SUPPORTED_LANGUAGES } from '@/types/language'
import type { Language } from '@/types/language'
import { storeToRefs } from 'pinia'
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

definePage({
  meta: {
    requiresAuth: true,
  },
})

const userStore = useUserStore()
const { currentLocale, switchLanguage } = useLanguageSwitch()
const { userInfo } = storeToRefs(userStore)
const dialogVisible = ref(false)

const currentLanguage = computed<Language>(() => 
  userInfo.value?.language || currentLocale.value as Language
)

const setLanguage = async (newLanguage: Language) => {
  try {
    await switchLanguage(newLanguage, async (lang) => {
      await userStore.changeUserLanguage(lang)
    })
  } catch (error) {
    console.error('Language switch failed:', error)
  }
}
</script>

<style lang="scss" module>
.root {
  .title {
    color: #1f2134;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
  }

  .email {
    color: #797a85;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-right: 24px;
  }

  .change-password-text {
    color: var(--primary-color);
    cursor: pointer;
  }
}
</style>
