<template>
  <div 
    class="fixed bottom-6 right-6 z-50 cursor-pointer group"
    @click="emit('click')">
    <div class="flex items-center justify-center w-14 h-14 bg-white rounded-full shadow-lg border-2 border-primary/10 transition-all duration-300 group-hover:border-primary/30">
      <Icon
        icon="ri:customer-service-2-fill"
        class="text-main-color text-2xl"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

const emit = defineEmits<{
  'click': []
}>()
</script> 