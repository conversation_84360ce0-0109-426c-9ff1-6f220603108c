// element.scss

:root {
  --el-color-primary: var(--primary-color);
  --el-text-color-primary: var(--font-color-1);
  --el-color-danger: var(--state-danger-icon);
  --el-main-padding: var(--m-large);
  --el-text-color-regular: var(--font-color-1);
  --el-popper-border-radius: var(--radius-default);
  --el-box-shadow-light: var(--shadow-hover);

  --el-component-size: 36px;
  .el-button {
    height: var(--el-component-size);
  }
  .el-input .el-input__icon {
    height: calc(var(--el-input-height, 32px) - 2px);
  }
  .el-select__wrapper {
    min-height: var(--el-component-size);
  }

  .el-table {
    --el-table-row-hover-bg-color: var(--gray-transparent-hover);
  }
  --el-border-color: var(--color-border-2);
  .el-input {
    --el-input-border-radius: var(--radius-small);
    --el-input-text-color: var(--font-color-1);
  }
  .el-button {
    --el-button-active-bg-color: var(--brand-color-7);
    --el-button-active-border-color: var(--brand-color-7);
    --el-button-width: var(--minW-button-default);
    &:not(.is-circle, .is-link, .is-icon, .el-button--text) {
      min-width: var(--el-button-width);
    }
    --el-button-hover-border-color: var(--gray-5);
    --el-button-hover-bg-color: var(--brand-color-1);
    &.is-disabled {
      --el-button-disabled-bg-color: var(--link-primary-color-disabled);
      --el-button-disabled-border-color: var(--link-primary-color-disabled);
    }
    &:not(.is-round) {
      border-radius: var(--radius-default);
    }
    &.el-button--small {
      --el-button-width: var(--minW-button-small);
    }
    &.el-button--primary {
      --el-button-hover-bg-color: var(--brand-color-6);
      --el-button-hover-border-color: var(--brand-color-6);

      &.is-plain {
        --el-button-bg-color: #fff;
        --el-button-border-color: var(--primary-color);
        --el-button-hover-border-color: var(--link-primary-color-hover);
        --el-button-hover-bg-color: #fff;
        --el-button-active-border-color: var(--link-primary-color-hover);
        --el-button-active-bg-color: #fff;
        --el-button-bg-color: #fff;
        --el-button-hover-text-color: var(--link-primary-color-hover);
        --el-button-border-color: var(--primary-color);
        --el-button-active-text-color: var(--link-primary-color-hover);
      }
    }
    &.el-button--danger {
      --el-button-hover-bg-color: var(--state-danger-color-1);
      --el-button-hover-border-color: var(--state-danger-color-1);
      &.is-plain {
        --el-button-hover-text-color: var(--state-danger-color-1);
        --el-button-hover-bg-color: #fff;
        --el-button-hover-border-color: var(--state-danger-color-1);
        --el-button-active-text-color: var(--state-danger-color-2);
        --el-button-active-border-color: var(--state-danger-color-2);
        --el-button-border-color: var(--el-color-danger);
        --el-button-bg-color: #fff;
        --el-button-active-bg-color: var(--state-danger-disable);
        &.disabled {
          color: var(--state-danger-disable);
          background-color: #fff;
          border-color: var(--state-danger-disable);
        }
      }
    }
    &.el-button--text {
      padding-left: 8px;
      padding-right: 8px;
      &:not(.is-disabled):hover {
        color: var(--brand-color-6);
      }
    }
  }
  .el-button + .el-button {
    margin-left: 8px;
  }

  .el-link.el-link--primary {
    --el-link-hover-text-color: var(--link-primary-color-hover);
    --el-link-disabled-text-color: var(--link-primary- color-disabled);
  }

  .el-overlay-dialog {
    top: 50px;
    bottom: 50px;
  }
  .el-dialog {
    --el-dialog-border-radius: var(--radius-default);
    display: flex;
    flex-direction: column;
    max-height: 100%;
    padding: 0;

    .el-dialog__header {
      padding: 18px 32px;
      .el-dialog__title {
        font-weight: var(--weight-font-Semiblod);
      }
      .el-dialog__headerbtn {
        top: 6px;
        right: 6px;
        font-size: 20px;
        color: var(--purpleGray-7);
      }
    }

    .el-dialog__body {
      padding: 12px 32px 0;
      overflow-y: auto;
    }

    .el-dialog__footer {
      height: 80px;
      align-items: center;
      display: flex;
      padding: 0 32px;
      text-align: right;
      justify-content: flex-end;
    }
  }
  .el-message-box {
    --el-message-close-size: 20px;
    --el-messagebox-border-radius: var(--radius-default);
    padding: 0 32px;
    .el-message-box__header {
      padding-top: 15px;
      padding-bottom: 15px;
    }
    .el-message-box__headerbtn {
      top: 10px;
      right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-message-box__title {
      --el-messagebox-font-line-height: 34px;
      font-weight: var(--weight-font-Semiblod);
    }
    .el-message-box__content {
      padding: 16px 0;
    }
    .el-message-box__btns {
      padding-top: 22px;
      padding-bottom: 22px;
    }
  }
  .el-popover {
    --el-popover-border-radius: var(--radius-default);
    --el-popover-border-color: var(--color-border-2);
    --el-border-color-light: var(--color-border-2);
  }
  .el-popper {
    --el-popper-border-radius: var(--radius-default);
    &[role="tooltip"] {
      .el-select__selection {
        max-width: 400px;
        max-height: 70vh;
        overflow-y: auto;
      }
      @media screen and (max-height: 1080px) {
        .el-select__selection {
          max-height: 415px;
        }
      }
    }
    > .el-scrollbar {
      border-radius: var(--radius-default);
    }
    .el-date-picker__header {
      display: flex;
      align-items: center;
      > span:nth-child(1) {
        margin-right: auto;
      }
      > span:nth-child(2),
      > span:nth-child(3) {
        text-align: center;
        margin-right: 10px;
      }
      > span:nth-child(4) {
        margin-left: auto;
      }
    }
  }
  .el-tooltip__trigger { outline: none; }
  .el-dropdown__popper {
    --el-dropdown-menuItem-hover-fill: var(--gray-transparent-hover);
    .el-dropdown-menu__item {
      margin: 0 6px;
      padding: 5px 12px;
      border-radius: var(--radius-default);
    }
  }

  .el-form {
    .el-form-item {
      &.is-error .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--state-danger-icon) inset;
      }
      .el-form-item__error {
        color: var(--state-danger-icon);
      }
      label {
        margin-bottom: 4px !important;
      }
    }
  }

  .el-select__wrapper {
    border-radius: var(--radius-small);
    .el-select__prefix {
      color: var(--font-color-2);
    }
    &.is-disabled {
      background-color: var(--purpleGray-2);
    }
    &.is-focused {
      .el-select__placeholder.is-transparent {
        color: var(--el-text-color-regular);
      }
    }
  }
  .el-select__selection .el-select__selected-item {
    .el-tag--info {
      color: var(--el-text-color-regular);
    }
  }
  .el-select-group__title {
    color: var(--font-color-2);
  }
  .el-select__placeholder.is-transparent {
    color: var(--font-color-4);
  }
  .el-select-dropdown__header {
    border-bottom: none;
    padding: 6px 18px 2px 18px;
  }
  .el-select__popper {
    .el-select-dropdown__item {
      &.is-selected {
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: inherit;
        }
      }
    }
  }
  .el-select-dropdown {
    &:not(.el-tree-select__popper) {
      .el-select-dropdown__item {
        padding: 0;
        > * {
          margin: 0 6px;
          padding: 0 12px;
          border-radius: var(--radius-default);
          display: flex;
        }
        &.is-hovering {
          background: none;
          > * {
            background-color: var(--gray-transparent-hover);
          }
        }
      }
    }
    &.is-multiple {
      min-width: 226px;
      .el-select-dropdown__list {
        min-width: 226px;
      }
    }
  }

  .el-tree {
    --el-tree-node-hover-bg-color: var(--gray-transparent-hover);
  }

  .el-cascader-panel {
    --el-cascader-node-background-hover: var(--gray-transparent-hover);
  }

  .el-checkbox {
    --el-checkbox-border-radius: var(--radius-mini);
    &.el-checkbox--large {
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
      }
      .el-checkbox__inner:after {
        left: 5px;
        top: 2px;
        width: 4px;
      }
    }
  }

  .el-picker-panel [slot="sidebar"],
  .el-picker-panel__sidebar {
    border-top-left-radius: var(--el-popper-border-radius);
    border-bottom-left-radius: var(--el-popper-border-radius);
  }

  .el-pagination__editor.el-input {
    --el-input-height: 32px;
  }
  .el-tag {
    font-size: 13px;
  }
}
