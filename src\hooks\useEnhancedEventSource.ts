import { ref, shallowRef, watch, computed } from 'vue'
import { useEventSource, useNetwork } from '@vueuse/core'
import type { EventSourceStatus } from '@vueuse/core'
import type { MaybeRefOrGetter } from '@vueuse/shared'
import type { CreatedReport } from '@/pages/format-report/type'
import { getToken } from '@/hooks/useToken'

export interface SSEMessage {
  type?: string;
  message?: string;
  section_name?: string;
  data?: any;
  search_result?: CreatedReport['search_result'];
  un_cited_result?: CreatedReport['search_result'];
  chart_type?: 'line' | 'radar' | 'bar' | 'word_cloud' | 'single_line';
}

export interface AgentStatusMessage {
  type: 'update' | 'heartbeat';
  data?: boolean;
}

export interface EnhancedEventSourceOptions {
  reconnectConfig?: {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffFactor: number;
  };
  onMessage?: (message: SSEMessage) => void;
  onError?: (error: Error) => void;
  onStateChange?: (status: EventSourceStatus) => void;
  onConnected?: () => void;
  onRetry?: (retryCount: number) => void;
  onMaxRetriesExceeded?: () => void;
  onClose?: () => void;
  immediate?: boolean;
  eventSourceInit?: EventSourceInit;
  closeEvents?: string[];
  endMessage?: string;
}

const DEFAULT_OPTIONS = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffFactor: 2,
  closeEvents: ['on_close'],
  endMessage: 'end'
} as const

export function useEnhancedEventSource<T extends string[]>(
  url: MaybeRefOrGetter<string | URL>,
  events: T = ['message', 'on_message', 'on_close'] as unknown as T,
  options: EnhancedEventSourceOptions = {}
) {
  const {
    reconnectConfig = DEFAULT_OPTIONS,
    onMessage,
    onError,
    onStateChange,
    onConnected,
    onRetry,
    onMaxRetriesExceeded,
    onClose,
    immediate = true,
    eventSourceInit = { withCredentials: true },
    closeEvents = DEFAULT_OPTIONS.closeEvents,
    endMessage = DEFAULT_OPTIONS.endMessage
  } = options

  const retryCount = ref(0)
  const isReconnecting = ref(false)
  const messageHistory = ref<SSEMessage[]>([])
  const fullMessageString = ref('')
  const { isOnline } = useNetwork()
  let reconnectTimeout: number | undefined

  const safeReconnectConfig = {
    ...DEFAULT_OPTIONS,
    ...reconnectConfig,
  }

  const eventSourceInstance = useEventSource(url, events, {
    immediate: false,
    ...eventSourceInit,
  })

  const {
    status,
    error,
    eventSource,
    close: baseClose,
    data,
    open: baseOpen,
  } = eventSourceInstance

  // 监听状态变化
  watch(status, (newStatus) => {
    onStateChange?.(newStatus)

    if (newStatus === 'OPEN') {
      retryCount.value = 0
      isReconnecting.value = false
      onConnected?.()
    }
  })

  // 监听数据变化
  watch(data, (newData) => {
    if (!newData) {return}

    try {
      const parsedData = JSON.parse(newData as string) as SSEMessage
      
      // 过滤掉心跳信息
      if (parsedData.type === 'heartbeat') {
        return
      }

      // 处理关闭事件
      if (parsedData.type === endMessage) {
        close()
        onClose?.()
      }
      
      messageHistory.value.push(parsedData)

      if (parsedData.type === 'append-text'){
        fullMessageString.value += parsedData.message || ''
      }
      onMessage?.(parsedData)
    } catch (error) {
      console.error('Error parsing SSE message:', error)
    }
  })

  // 监听错误和网络状态
  watch([error, isOnline], ([err, online]) => {
    if (!err || !online) {return}
    
    onError?.(err as any)
    handleReconnect()
  })

  const calculateDelay = (): number => {
    const { baseDelay, maxDelay, backoffFactor } = safeReconnectConfig
    const delay = baseDelay * Math.pow(backoffFactor, retryCount.value)
    return Math.min(delay, maxDelay)
  }

  const handleReconnect = (): void => {
    const { maxRetries } = safeReconnectConfig

    if (retryCount.value >= maxRetries) {
      isReconnecting.value = false
      onMaxRetriesExceeded?.()
      return
    }

    if (!isOnline.value) {
      // 等待网络恢复
      watch(isOnline, (online) => {
        if (online) {
          attemptReconnect()
        }
      }, { once: true })
      return
    }

    attemptReconnect()
  }

  const attemptReconnect = (): void => {
    isReconnecting.value = true
    retryCount.value++
    onRetry?.(retryCount.value)

    const delay = calculateDelay()
    clearTimeout(reconnectTimeout)
    reconnectTimeout = window.setTimeout(() => {
      open()
    }, delay)
  }

  const clear = () =>{
    fullMessageString.value = ''
    messageHistory.value = []
  }

  const open = (): void => {
    clear()
    baseClose()
    baseOpen()
  }

  const close = (): void => {
    isReconnecting.value = false
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
    }
    baseClose()
  }

  if (immediate) {
    open()
  }

  return {
    ...eventSourceInstance,
    fullMessageString,
    retryCount,
    isReconnecting,
    messageHistory,
    close,
    open,
  }
}

export function useAgentStatusSSE(agentType: string) {
  const status = ref<boolean>(false)
  
  const url = computed(() => {
    const token = getToken()
    return `/api/formatted_report/update_agent_status?agent_type=${agentType}&token=${token}`
  })

  const { close, open, isReconnecting } = useEnhancedEventSource<['message']>(url, ['message'], {
    immediate: false,
    reconnectConfig: {
      maxRetries: 5,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 1.5
    },
    onMessage: (message: SSEMessage) => {
      try {
        const data = message as unknown as AgentStatusMessage
        if (data.type === 'update' && data.data !== undefined) {
          status.value = data.data
        }
      } catch (error) {
        console.error('Error parsing agent status message:', error)
      }
    }
  })

  return {
    status,
    isReconnecting,
    connect: open,
    disconnect: close
  }
}
