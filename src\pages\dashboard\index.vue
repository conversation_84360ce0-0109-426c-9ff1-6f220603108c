<template>
  <div :class="$style['dashboard']">
    <!-- Completed Work Section -->
    <div :class="$style['main-column']">
      <div :class="$style['section-header']">
        <span>{{ $t("agent.completed_agents") }}</span>
        <div class="flex items-center gap-2">
          <span 
            class="flex items-center gap-2 text-sm text-red-600 hover:underline cursor-pointer"
            @click="showRiskCompaniesDrawer = true">
            <span>{{ $t("agent.view_all_risk_companies") }}</span>
            <Icon icon="mdi:arrow-right" class="w-4 h-4"/>
          </span>
        </div>
      </div>

      <div :class="$style['agents-grid']" v-loading="isAgentListLoading">
        <template v-if="displayAgents.length">
          <!-- Fixed Agents Section -->
          <div :class="$style['fixed-agents-container']">
            <div :class="$style['agent-section-header']" class="justify-between">
              <div class="flex items-center gap-2">
                <Icon icon="mdi:star" class="text-yellow-500 w-5 h-5"/>
                <span class="text-[13px] font-semibold text-[#1e293b]">{{ $t("agent.special_agents") }}</span>
                <el-tooltip :content="$t('agent.special_agents_tooltip')" placement="top">
                  <Icon icon="mdi:help-circle-outline" class="text-gray-400 w-4 h-4 cursor-help"/>
                </el-tooltip>
              </div>
            </div>
            <div :class="$style['fixed-agents']">
              <template v-if="fixedAgents.length">
                <SpecialAgentCard
                  v-for="agent in fixedAgents"
                  :key="agent.id"
                  :agent="agent"
                  @clickAgent="handleAgentClick"
                  @refreshAgent="handleRefreshAgent"
                  @tagClick="handleTagClick"
                  @deleteAgent="handleDeleteAgent"
                  @changeTime="handleChangeTime"/>
              </template>
              <DisabledFixedAgentCard v-else/>
            </div>
          </div>

          <!-- Scrollable Agents Section -->
          <div :class="$style['scrollable-agents-container']" v-show="(standardReports?.length || 0) > 0">
            <div :class="$style['agent-section-header']" class="justify-between">
              <div class="flex items-center gap-2">
                <Icon icon="mdi:format-list-text" class="text-blue-500 w-5 h-5"/>
                <span>{{ $t("agent.regular_agents") }}</span>
                <el-tooltip :content="$t('agent.regular_agents_tooltip')" placement="top">
                  <Icon icon="mdi:help-circle-outline" class="text-gray-400 w-4 h-4 cursor-help"/>
                </el-tooltip>
              </div>
              <div class="flex items-center gap-3">
                <router-link
                  to="/format-report"
                  class="flex items-center gap-2 text-sm text-main-color hover:underline">
                  <span>{{ $t("agent.view_all_reports") }}</span>
                  <Icon icon="mdi:arrow-right" class="w-4 h-4"/>
                </router-link>
              </div>
            </div>
            <div :class="$style['swiper-container']" v-loading="isStandardReportsLoading">
              <Swiper
                :modules="swiperModules"
                :direction="'vertical'"
                :pagination="{
                  clickable: true
                }"
                class="w-full vertical-swiper"
                :style="{ height: '220px' }"
                @swiper="onSwiperInit"
                @slideChange="onSlideChange">
                <SwiperSlide
                  v-for="agent in standardReports"
                  :key="agent.id"
                  class="swiper-slide-card">
                  <AgentCompletedCard
                    :key="agent.id"
                    class="w-full cursor-pointer overflow-hidden"
                    v-bind="mapAgentData(agent)"
                    @click="handleAgentClick(mapAgentData(agent))">
                    <template #duty>
                      <div class="flex items-start gap-2 flex-col">
                        <span class="text-main-color text-sm">{{ agent.name }}</span>
                        <div v-if="agent.updated_at">
                          <span class="text-gray-500 text-xs">{{ $t("agent.updated_at") }}: </span>
                          <span class="text-main-color text-sm">{{
                            formatCalendarDateTime(dayjs(agent.updated_at).valueOf())
                          }}</span>
                        </div>
                      </div>
                    </template>
                    <template #summary>
                      <div
                        class="text-main-color text-sm text-start w-full overflow-hidden mt-2">
                        <OverClampText
                          :content="agent.summary"
                          :line-clamp="3"
                          class="leading-6"/>
                      </div>
                    </template>
                  </AgentCompletedCard>
                </SwiperSlide>
              </Swiper>
            </div>
            <!-- 移动导航按钮到底部 -->
            <div :class="$style['swiper-navigation']">
              <button
                :class="[
                  $style['swiper-button'],
                  $style['swiper-button-prev'],
                  { [$style['swiper-button-disabled']]: isFirstSlide }
                ]"
                @click="handlePrevSlide"
                :disabled="isFirstSlide">
                <Icon icon="mdi:chevron-up" class="w-6 h-6"/>
              </button>

              <!-- 简化的页码指示器 -->
              <div :class="$style['pagination-indicator']">
                {{ currentSlideIndex }} / {{ totalSlides }}
              </div>

              <button
                :class="[
                  $style['swiper-button'],
                  $style['swiper-button-next'],
                  { [$style['swiper-button-disabled']]: isLastSlide }
                ]"
                @click="handleNextSlide"
                :disabled="isLastSlide">
                <Icon icon="mdi:chevron-down" class="w-6 h-6"/>
              </button>
            </div>
          </div>
        </template>
        <div v-else class="w-[400px] min-h-[220px] shrink-0">
          <DisabledFixedAgentCard/>
        </div>
      </div>
    </div>

    <!-- Performance + Task List Container -->
    <div :class="$style['performance-task-container']">
      <!-- Performance Section -->
      <div :class="$style['performance-section']">
        <div :class="$style['section-header']">
          <span>{{ $t("agent.agent_performance") }}</span>
        </div>

        <template v-if="agentResponses.length">
          <AgentsResponseCard
            :class="$style['agent-response-card']"
            v-for="response in agentResponses"
            :key="response.name"
            v-memo="[response.steps]"
            v-bind="response">
            <template #duty>
              <div class="flex items-start gap-2 flex-col">
                <span class="text-main-color text-sm">{{
                  response.report_name
                }}</span>
                <span class="text-gray-500 text-xs">{{ response.duty }}</span>
              </div>
            </template>
          </AgentsResponseCard>
        </template>
        <EmptyAgentToMarket v-else/>
      </div>

      <!-- Task List Section -->
      <div :class="$style['task-column']">
        <div :class="$style['section-header']">
          <span>{{ $t("agent.agent_task_list") }}</span>
        </div>

        <div :class="$style['tasks-list']">
          <template v-if="tasks.length">
            <!-- Scheduled Tasks Group -->
            <div v-if="scheduledTasks.length" class="flex flex-col gap-2">
              <div :class="$style['task-group-header']">
                {{ $t("agent.scheduled_tasks") }}
              </div>
              <AgentRunningCard
                v-for="task in scheduledTasks"
                :key="task.id"
                v-memo="[task.id, task.progress]"
                v-bind="task"
                class="cursor-pointer"
                @click="handleTaskClick(task)">
                <template #name>
                  <div
                    class="flex items-start gap-2 flex-col cursor-pointer hover:text-main-color">
                    <div class="flex items-center gap-2 justify-between w-full">
                      <span>{{ task.name }}</span>
                    </div>
                    <el-tag type="primary">{{
                      $t("agent.special_agent")
                    }}</el-tag>
                    <span class="text-gray-500 text-xs">{{ task.duty }}</span>
                  </div>
                </template>
              </AgentRunningCard>
            </div>

            <!-- Regular Tasks Group -->
            <div v-if="regularTasks.length">
              <div :class="$style['task-group-header']">
                {{ $t("agent.regular_tasks") }}
              </div>
              <AgentRunningCard
                v-for="task in regularTasks"
                :key="task.id"
                v-memo="[task.id, task.progress]"
                v-bind="task"
                class="cursor-pointer"
                @click="handleTaskClick(task)">
                <template #name>
                  <div
                    class="flex items-start gap-2 flex-col cursor-pointer hover:text-main-color">
                    <span>{{ task.name }}</span>
                    <span class="text-main-color text-xs">{{
                      task.report_name
                    }}</span>
                    <span class="text-gray-500 text-xs">{{ task.duty }}</span>
                  </div>
                </template>
              </AgentRunningCard>
            </div>
          </template>
          <EmptyAgentToMarket v-else/>
        </div>
      </div>
    </div>

    <!-- Add drawer component after your existing template content -->
    <AgentTableDrawer
      v-model:visible="showBatchOperations"
      :agents="scrollableAgents"
      :loading="isAgentListLoading"
      @view="handleAgentView"
      @delete="handleSingleDelete"
      @batch-view="handleBatchView"
      @batch-delete="handleBatchDelete"
      @refresh="getStandardReports"/>

    <RiskCompanyDrawer v-model:visible="showRiskCompaniesDrawer"/>

    <!-- 添加时间间隔对话框 -->
    <TimeIntervalDialog
      v-model:visible="showTimeIntervalDialog"
      :agent="selectedAgent || {} as Agent"
      :current-frequency="currentFrequency"
      @confirm="handleTimeIntervalSubmit"/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted, watch, defineAsyncComponent } from 'vue'
import { definePage, useRouter } from 'vue-router/auto'
import {
  AgentCompletedCard,
  AgentRunningCard,
  AgentsResponseCard,
} from '@specificlab/ui'
import { useReportProgress, AVATAR_MAP, SPECIAL_AGENT_TYPES } from '@/composables/useReportProgress'
import { useI18n } from 'vue-i18n'
import { usePost, useGet } from '@/hooks/useHttp'
import type { CompletedAgent, Agent } from './type'
import OverClampText from '@/components/OverClampText.vue'
import EmptyAgentToMarket from '@/pages/__components__/DashboardComponents/EmptyAgentToMarket.vue'
import { Icon } from '@iconify/vue'
import dayjs from 'dayjs'
import emitter from '@/utils/events'
import SpecialAgentCard from '@/pages/__components__/DashboardComponents/SpecialAgentCard.vue'
import AgentTableDrawer from '@/pages/__components__/DashboardComponents/AgentTableDrawer.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { formatCalendarDateTime } from '@/utils/filter'
import { Swiper, SwiperSlide } from 'swiper/vue'
import type { Swiper as SwiperType } from 'swiper/types'
import 'swiper/css'
import 'swiper/css/pagination'
import { Pagination, Navigation } from 'swiper/modules'
import DisabledFixedAgentCard from '@/pages/__components__/DashboardComponents/DisabledFixedAgentCard.vue'
import TimeIntervalDialog from '@/pages/__components__/DashboardComponents/TimeIntervalDialog.vue'

import type { ReportList } from '@/pages/format-report/type'

const { t } = useI18n()
definePage({
  meta: {
    layout: 'default',
    requiresAuth: true,
  },
})

const router = useRouter()


interface Task {
  id: string;
  name: string;
  avatar?: string;
  description: string;
  currentStep: number;
  totalSteps: number;
  progress: number;
  report_type?: string;
  report_name: string;
  duty: string;
}

// Add state management
const currentPage = ref(1)

const {
  state: agentList,
  execute: getAgentList,
  isLoading: isAgentListLoading,
} = usePost<CompletedAgent[]>(
  '/formatted_report/report_list',
  {},
  {},
  {
    immediate: false,
  }
)

const { execute: refreshAgent, isLoading: isRefreshAgentLoading } = usePost(
  '/formatted_params/refresh_agent',
  {},
  {},
  {
    immediate: false,
  }
)

const { execute: changeTime, isLoading: isChangeTimeLoading } = usePost(
  '/agent/up_agent_task',
  {},
  {},
  {
    immediate: false,
  }
)
const completedAgents = computed(() => {
  // Memoize the transformation using Map for better performance
  const memoizedAgents = new Map()

  return agentList.value?.map((agent: CompletedAgent) => {
    const cachedAgent = memoizedAgents.get(agent.id)
    if (cachedAgent) {return cachedAgent}

    const transformedAgent = {
      id: agent.id,
      name: agent.agent_name || t('agent.no_agent_name', { name: agent.report_name }),
      duty: agent.report_name,
      report_type: agent.report_type,
      summary: agent.summary,
      status: agent.risk_status,
      avatar: AVATAR_MAP[agent.report_type as keyof typeof AVATAR_MAP],
      updated_at: agent.updated_at,
      frequency: agent.frequency,
    }

    memoizedAgents.set(agent.id, transformedAgent)
    return transformedAgent
  }) || []
})
const { open, close, activeAgents, tasks, agentResponses } = useReportProgress(
  'api/formatted_report/report_progress'
)


const handleRefreshAgent = async (agent: Agent) => {
  console.log('Refresh agent:', agent)
  await refreshAgent(0, {
    id: agent.report_type,
  })
  getAgentList()
  setTimeout(() => {
    emitter.emit('async:update')
  }, 500)
}

// Update constants
const PAGE_SIZE = 3

// Add display agents array
const displayAgents = ref<Agent[]>([])

const fixedAgents = computed(() => {
  // Create a Map to deduplicate by ID while preserving order
  const uniqueAgents = new Map()

  displayAgents.value
    .filter(agent => SPECIAL_AGENT_TYPES.includes(agent.report_type || ''))
    .forEach(agent => {
      if (!uniqueAgents.has(agent.id)) {
        uniqueAgents.set(agent.id, agent)
      }
    })

  return Array.from(uniqueAgents.values())
})

const scrollableAgents = computed(() => {
  const agents = displayAgents.value.filter(
    (agent) => !SPECIAL_AGENT_TYPES.includes(agent.report_type || '')
  )
  return agents
})

// Optimize watchers with proper cleanup
watch(completedAgents, (newAgents) => {
  if (currentPage.value === 1) {
    displayAgents.value = newAgents
  } else {
    // Only append new agents that don't already exist
    const existingIds = new Set(displayAgents.value.map((a: Agent) => a.id))
    const newUniqueAgents = newAgents.filter((a: Agent) => !existingIds.has(a.id))
    displayAgents.value.push(...newUniqueAgents)
  }
}, { deep: true })

// Optimize tasks watcher
watch(tasks, (newTasks, oldTasks) => {
  // Only refresh if completed tasks increased
  if (oldTasks.length && newTasks.length < oldTasks.length) {
    refreshAsyncReportList()
    getAgentList()
  }
}, { deep: false }) // Remove deep watching if not needed

const refreshAsyncReportList = async () => {
  await getStandardReports()
  setTimeout(() => {
    emitter.emit('async:update')
  }, 500)
}
// Update onMounted
onMounted(async () => {
  open()
  currentPage.value = 1
  await getAgentList()
  await refreshAsyncReportList()
})


// 修改handleAgentClick函数，添加已读状态更新
const handleAgentClick = (agent: Agent) => {
  if (SPECIAL_AGENT_TYPES.includes(agent.report_type || '')) {
    router.push(`/dashboard/${agent.id}?report_type=${agent.report_type}`)
    return
  }
  const to = router.resolve(`/format-report/view/${agent.id}`)
  router.push(to.path)
}

const handleTagClick = (tagData: { agent: Agent, tag: string }) => {
  console.log('Tag clicked:', tagData)
  router.push(`/dashboard/${tagData.agent.id}?report_type=${tagData.agent.report_type}&tag=${tagData.tag}`)
}

const handleTaskClick = (task: Task) => {
  router.push(`/dashboard/${task.id}?report_type=${task.report_type}`)
}

// Add these computed properties
const scheduledTasks = computed(() => {
  return tasks.value.filter((task) =>
    SPECIAL_AGENT_TYPES.includes(task.report_type || '')
  )
})

const regularTasks = computed(() => {
  return tasks.value.filter(
    (task) => !SPECIAL_AGENT_TYPES.includes(task.report_type || '')
  )
})

// Add state
const showBatchOperations = ref(false)

// Add batch operation handlers
const handleBatchView = (ids: string[]) => {
  // Open multiple agents in new tabs
  ids.forEach(id => {
    const agent = scrollableAgents.value.find(a => a.id === id)
    if (agent) {
      const route = agent.report_type && SPECIAL_AGENT_TYPES.includes(agent.report_type)
        ? `/dashboard/${id}?report_type=${agent.report_type}`
        : `/format-report/view/${id}`
      window.open(router.resolve(route).href, '_blank')
    }
  })
}

const handleSingleDelete = async (agent: Agent, type: 'agent' | 'report' = 'report') => {
  try {
    await ElMessageBox.confirm(
      t('agent.delete_confirm'),
      t('common.warning'),
      { type: 'warning' }
    )
    const res = await batchDeleteReports(0, {
      ids: [agent.id],
      source: type
    })
    if (res?.success) {
      ElMessage.success(t('common.delete_success'))
      showBatchOperations.value = false
      refreshAgentList()
      getStandardReports()
    } else {
      ElMessage.error(t('common.operation_failed'))
    }
  } catch (err) {
    // Handle error
    if (err !== 'cancel') {
      ElMessage.error(t('common.operation_failed'))
    }
  }
}

const handleDeleteAgent = async (agent: Agent) => {
  await handleSingleDelete(agent, 'agent')
}
const handleBatchDelete = async (ids: string[]) => {
  try {
    await ElMessageBox.confirm(
      t('agent.batch_delete_confirm', { count: ids.length }),
      t('common.warning'),
      {
        type: 'warning',
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel')
      }
    )

    const res = await batchDeleteReports(0, {
      ids,
      source: 'report'
    })

    if (res?.success) {
      showBatchOperations.value = false
      ElMessage.success(
        t('common.batch_delete_success', {
          count: res?.deleted_count
        })
      )

      // If there are items not found, show warning
      if (res?.not_found?.length) {
        ElMessage.warning(
          t('common.items_not_found', {
            count: res?.not_found.length
          })
        )
      }

      await refreshAgentList()
      getStandardReports()
    } else {
      ElMessage.error(t('common.operation_failed'))
    }
  } catch (err) {
    // Only show error if not cancelled
    if (err !== 'cancel') {
      ElMessage.error(t('common.operation_failed'))
      console.error('Batch delete failed:', err)
    }
  }
}

const refreshAgentList = async () => {
  currentPage.value = 1
  await getAgentList()
  setTimeout(() => {
    emitter.emit('async:update')
  }, 500)
}


// Add new API hook
const { execute: batchDeleteReports } = usePost<{
  success: boolean;
  deleted_count: number;
  not_found: string[];
}>(
  '/formatted_report/batch_delete_report',
  {},
  {},
  {
    immediate: false,
  }
)

// 修改handleAgentView函数，添加已读状态更新
const handleAgentView = (agent: Agent) => {
  const route = agent.report_type && SPECIAL_AGENT_TYPES.includes(agent.report_type)
    ? `/dashboard/${agent.id}?report_type=${agent.report_type}`
    : `/format-report/view/${agent.id}`
  window.open(router.resolve(route).href, '_blank')
}
// Swiper相关
const swiperModules = [Pagination, Navigation]
const swiperInstance = ref<SwiperType | null>(null)

const currentSlideIndex = ref(1)
const totalSlides = computed(()=> standardReports.value?.length || 0)

const onSwiperInit = (swiper: SwiperType) => {
  swiperInstance.value = swiper

  // 初始化总页数

  // 添加额外的事件监听
  swiper.on('slideChangeTransitionStart', () => {
    // 可以在这里添加幻灯片切换开始时的动画效果
  })

  swiper.on('slideChangeTransitionEnd', () => {
    // 可以在这里添加幻灯片切换结束时的动画效果
  })
}

const onSlideChange = (swiper: SwiperType) => {
  // 更新当前页码（Swiper的activeIndex是从0开始的，所以+1）
  currentSlideIndex.value = swiper.activeIndex + 1
}

// 标准报告数据
const {
  state: standardReports,
  execute: getStandardReports,
  isLoading: isStandardReportsLoading
} = useGet<ReportList[]>(
  '/formatted_report/standard_report_list',
  {},
  {
    immediate: true
  }
)

// 将ReportList数据映射为Agent数据
const mapAgentData = (report: ReportList) => {
  return {
    id: report.id,
    name: report.agent_name || t('agent.no_agent_name', { name: report.name }),
    duty: report.name,
    report_type: report.report_type,
    summary: report.summary || '',
    status: 'normal', // 默认状态，因为ReportList中没有risk_status字段
    avatar: report.report_type ? AVATAR_MAP[report.report_type as keyof typeof AVATAR_MAP] : undefined,
    updated_at: report.updated_at,
  } as Agent
}

// 添加计算属性判断是否是第一页或最后一页
const isFirstSlide = computed(() => currentSlideIndex.value === 1)
const isLastSlide = computed(() => currentSlideIndex.value === totalSlides.value)

// 修改handlePrevSlide和handleNextSlide方法，添加禁用状态检查
const handlePrevSlide = () => {
  if (swiperInstance.value && !isFirstSlide.value) {
    swiperInstance.value.slidePrev()
  }
}

const handleNextSlide = () => {
  if (swiperInstance.value && !isLastSlide.value) {
    swiperInstance.value.slideNext()
  }
}

const showRiskCompaniesDrawer = ref(false)

// 使用异步组件
const RiskCompanyDrawer = defineAsyncComponent(() => 
  import('../__components__/DashboardComponents/RiskCompanyDrawer.vue')
)

// 添加状态管理变量
const showTimeIntervalDialog = ref(false)
const selectedAgent = ref<Agent | null>(null)
const currentFrequency = ref(1) // 默认值，可以根据需要调整

// 修改handleChangeTime函数
const handleChangeTime = (agent: Agent) => {
  selectedAgent.value = agent
  // 如果有必要，这里可以获取当前的frequency值
  currentFrequency.value = agent.frequency || 1
  showTimeIntervalDialog.value = true
}

// 处理提交
const handleTimeIntervalSubmit = async (data: { agent_id: string, frequency: number }) => {
  try {
    await changeTime(0, {
      agent_id: data.agent_id,
      frequency: data.frequency
    })
    
    ElMessage.success(t('agent.update_time_success'))
    // 更新代理列表
    getAgentList()
    
  } catch (error) {
    console.error('Failed to update time interval:', error)
    ElMessage.error(t('agent.update_time_error'))
  }
}
</script>

<style lang="scss" module>
// 通用滚动条样式
@mixin scrollbar-y {
  overflow-y: auto;
  min-height: 0;
}

@mixin scrollbar-x {
  overflow-x: auto;
}
.dashboard {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  min-height: calc(100vh - 60px); /* 修改为最小高度 */
  background: #f8fafc;
  color: #1e293b;
  overflow: hidden;
  box-sizing: border-box;
  min-width: 800px;
}

.main-column {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-height: 0;
  overflow: hidden;
  background: #fff;
  border-radius: 12px;
  padding: 12px 12px 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: fit-content; /* 修改为适应内容高度 */
}

.performance-task-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  :global(.sl-agents-response-timeline) {
    max-height: 400px;
    @include scrollbar-y;
  }
}

.performance-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  height: 100%;
  padding-right: 8px;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  @include scrollbar-y;
}

.task-column {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  min-height: 0;

  &.h-full {
    flex: 1;
    overflow: hidden;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  shrink: 0;
  > span {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
  }
}

.count {
  font-size: 14px;
  color: #64748b;
}

.agents-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 12px;
  overflow: hidden;
  min-height: 0;
  height: auto; /* 修改为自适应高度 */

  @media (max-width: 1366px) {
    grid-template-columns: 1fr 400px;
  }

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
}

.fixed-agents-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid var(--brand-color-3);
  overflow: hidden;
  min-width: 0;
  width: 100%;
  height: fit-content; /* 修改为适应内容高度 */

  @media (max-width: 992px) {
    width: 100%;
  }

  :global(.sl-special-agent-card) {
    height: 220px !important;
  }
}

.agent-section-header {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding-bottom: 6px;
  margin-bottom: 4px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.fixed-agents {
  display: flex;
  gap: 12px;
  overflow-x: auto; /* 添加水平滚动 */
  padding-bottom: 8px; /* 为滚动条留出空间 */
  width: 100%; /* 确保占满容器宽度 */
  min-height: 211px;

  /* 美化滚动条 */
  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(68, 102, 188, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(68, 102, 188, 0.5);
  }
}

.scrollable-agents-container {
  display: flex;
  flex-direction: column;
  /* gap: 20px; */
  min-height: 0;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid var(--brand-color-3);
  overflow: hidden;
  min-width: 0; /* 允许容器缩小到比内容更小 */
  width: 100%; /* 确保容器占满分配的空间 */
  height: auto; /* 修改为自适应高度 */
  max-width: 400px; /* 限制最大宽度与grid列宽一致 */
  position: relative;
}

.swiper-container {
  width: 100%;
  height: auto; /* 修改为自适应高度 */
  min-height: 220px; /* 保持最小高度 */
  position: relative;

  @media (max-width: 768px) {
    min-height: 220px;
    padding: 8px;
  }
}

.swiper-slide-card {
  width: 100%;
  height: auto; /* 修改为自适应高度 */
  min-height: 220px; /* 保持最小高度 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.vertical-swiper {
  width: 100%;
  height: auto; /* 修改为自适应高度 */
  position: relative;
}

.swiper-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 6px;
  margin-bottom: 4px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.swiper-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.swiper-indicators {
  font-size: 12px;
  color: #64748b;
}

.swiper-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
  padding-right: 8px;
  @include scrollbar-y;
  :global(.sl-agent-running-avatar) {
    cursor: pointer;
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Remove all other unused styles
.loadMoreIcon,
.blurredCard,
.blurOverlay,
.load-more-card {
  display: none;
}

.task-group-header {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  margin-top: 8px;

  &:first-child {
    margin-top: 0;
  }
}
.danger-card {
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 59, 48, 0.1) 0%,
      rgba(255, 0, 0, 0.2) 100%
    );
    border-radius: inherit;
    animation: pulse 2s ease-in-out infinite;
    pointer-events: none;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.custom-nav-button {
  color: var(--brand-color-1);

  &::after {
    font-size: 24px;
    font-weight: bold;
  }

  &:hover {
    color: var(--brand-color-2);
  }
}

.swiper-pagination-custom {
  display: none;
}

.pagination-bullet {
  display: none;
}

.pagination-bullet-active {
  display: none;
}

.swiper-navigation {
  position: absolute;
  bottom: 5px; /* 移动到底部 */
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  justify-content: space-between; /* 左右两侧分布 */
  padding: 0 20px; /* 左右边距 */
  z-index: 10;
}

.swiper-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--brand-color-3);
  border: 2px solid var(--brand-color-3);
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    background-color: var(--brand-color-3);
    color: white;
    transform: scale(1.05);
  }

  &:focus {
    outline: none;
  }

  :global(.iconify) {
    width: 24px !important;
    height: 24px !important;
  }
}

/* 添加禁用状态样式 */
.swiper-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  color: #a0a0a0;
  box-shadow: none;

  &:hover {
    background-color: #e0e0e0;
    color: #a0a0a0;
    transform: none;
  }
}

/* 移除不需要的上下边距 */
.swiper-button-prev, .swiper-button-next {
  margin: 0;
}

.pagination-indicator {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
</style>
