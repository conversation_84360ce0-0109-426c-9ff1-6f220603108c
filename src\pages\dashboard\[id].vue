<template>
  <div class="min-h-screen flex flex-col" :class="$style.root" v-loading="!singleReportData?.id">
    <!-- Header -->
    <Header :is-show-menu="false"/>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Left Sidebar -->
      <div class="w-[15%] p-4 flex flex-col" :class="$style['left-sidebar']">
        <div :class="$style['nav-actions']">
          <el-button @click="handleBack" class="w-full">
            <Icon icon="mdi:arrow-left" class="mr-2"/> {{ $t('common.back') }}
          </el-button>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="flex-1 flex flex-col overflow-hidden h-full">
        <!-- 搜索过滤栏 - 重新设计为精美的卡片式 -->
        <div class="bg-white border-b border-gray-100 px-6 py-6">
          <div class="w-full pl-4">
            <!-- 主要过滤控件行 -->
            <div class="flex items-center gap-6 mb-4">
              <!-- 搜索框 - 靠左对齐 -->
              <div class="w-80">
                <div class="relative">
                  <Icon icon="mdi:magnify" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"/>
                  <Input 
                    ref="searchInputRef"
                    v-model="searchKeyword"
                    :placeholder="$t('agent.search_company_placeholder')"
                    class="pl-10 pr-16 h-10 bg-white border-gray-200 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg transition-all duration-200"
                    @keydown.enter="handleSearchEnter"/>
                  <!-- <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none inline-flex h-5 select-none items-center gap-0.5 rounded-md bg-gray-100 px-1.5 font-mono text-[10px] font-medium text-gray-500 border border-gray-200">
                    <span class="text-xs">{{ shortcutKey }}</span>K
                  </kbd> -->
                </div>
              </div>
              
              <!-- 过滤器组 -->
              <div class="flex items-center gap-6 flex-1">
                <!-- 状态过滤器 -->
                <div class="flex items-center gap-3">
                  <span class="text-sm font-medium text-gray-700 whitespace-nowrap">{{ $t('agent.status_filter') }}</span>
                  <Select @update:model-value="handleStatusFilterChange">
                    <SelectTrigger class="w-[160px]">
                      <SelectValue :placeholder="$t('agent.all_status')"/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectItem value="all">
                          <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                            <span>{{ $t('agent.all_status') }}</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="COMPLETED">
                          <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-emerald-500"></div>
                            <span>{{ $t('agent.status_completed') }}</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="PROCESSING">
                          <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                            <span>{{ $t('agent.status_processing') }}</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="PENDING">
                          <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-amber-500"></div>
                            <span>{{ $t('agent.status_pending') }}</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="ERROR">
                          <div class="flex items-center gap-2">
                            <div class="w-2 h-2 rounded-full bg-red-500"></div>
                            <span>{{ $t('agent.status_error') }}</span>
                          </div>
                        </SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
                
                <!-- 风险状态过滤器 - 简化为单个toggle -->
                <div class="flex items-center gap-3">
                  <span class="text-sm font-medium text-gray-700 whitespace-nowrap">{{ $t('agent.risk_filter') }}</span>
                  <button
                    @click="handleRiskFilterChange(!(riskFilter === 'risk'))"
                    :class="[
                      'h-10 px-4 cursor-pointer border rounded-lg transition-all duration-200 shadow-sm inline-flex items-center gap-2',
                      riskFilter === 'risk'
                        ? 'bg-red-50 text-red-700 border-red-200' 
                        : 'text-gray-600 hover:bg-gray-50 border-gray-200'
                    ]">
                    <Icon icon="mdi:alert-circle" class="h-4 w-4"/>
                    {{ $t('agent.risk_danger') }}
                  </button>
                </div>
                
                <!-- 清空过滤器按钮 -->
                <div class="flex">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    @click="clearFilters"
                    v-show="searchKeyword.trim() || statusFilter || riskFilter"
                    class="h-10 px-4 text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-gray-200 rounded-lg shadow-sm transition-all duration-200">
                    <Icon icon="mdi:filter-off" class="h-4 w-4 mr-2"/>
                    {{ $t('agent.clear_filters') }}
                  </Button>
                </div>
              </div>
            </div>
            
            <!-- 统计信息行 -->
            <div class="flex items-center justify-start gap-4 text-sm">
              <div class="flex items-center gap-6 text-gray-600">
                <span class="font-medium">
                  {{ $t('agent.filter_results', { total: filteredSteps.length, all: steps.length }) }}
                </span>
                
                <!-- 状态统计 -->
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1.5">
                    <div class="w-2 h-2 rounded-full bg-emerald-500"></div>
                    <span>{{ $t('agent.status_completed') }} {{ stepStatus.completed }}</span>
                  </div>
                  <div class="flex items-center gap-1.5">
                    <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                    <span>{{ $t('agent.status_processing') }} {{ stepStatus.inProgress }}</span>
                  </div>
                  <div class="flex items-center gap-1.5">
                    <div class="w-2 h-2 rounded-full bg-amber-500"></div>
                    <span>{{ $t('agent.status_pending') }} {{ stepStatus.pending }}</span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center gap-4">
                <!-- 活跃过滤器标识 -->
                <!-- <div v-if="activeFiltersCount > 0" class="flex items-center gap-2">
                  <Badge variant="secondary" class="bg-blue-50 text-blue-700 border-blue-200 px-2 py-1">
                    <Icon icon="mdi:filter" class="h-3 w-3 mr-1"/>
                    {{ activeFiltersCount }} 个过滤器
                  </Badge>
                </div> -->
                
                <!-- 筛选状态描述 -->
                <div 
                  :class="[
                    'flex items-center gap-2 px-4 py-2 rounded-lg border shadow-sm transition-all duration-200',
                    (searchKeyword.trim() || statusFilter || riskFilter) 
                      ? 'bg-blue-50 text-blue-700 border-blue-200' 
                      : 'bg-gray-50 text-gray-600 border-gray-200'
                  ]">
                  <Icon 
                    icon="mdi:filter-variant" 
                    :class="[
                      'h-4 w-4',
                      (searchKeyword.trim() || statusFilter || riskFilter) 
                        ? 'text-blue-600' 
                        : 'text-gray-500'
                    ]"/>
                  <span class="text-sm font-medium">{{ filterStatusDescription }}</span>
                </div>
                
                <!-- 快捷键提示 -->
                <!-- <span class="text-xs text-gray-400 hidden lg:inline-flex items-center gap-1">
                  <Icon icon="mdi:keyboard" class="h-3 w-3"/>
                  {{ $t('agent.shortcuts_tip') }}
                </span> -->
              </div>
            </div>
          </div>
        </div>

        <agent-detail-layout
          :task-id="taskId"
          :agent-name="agentName"
          :progress="progress"
          @back="handleBack">
          <template #avatar>
            <Avatar class="ring-2 ring-white shadow-sm h-10 w-10">
              <!-- 根据不同的情报官类型显示不同的图标和背景 -->
              <AvatarFallback class="bg-gradient-to-br from-amber-400 to-orange-400 text-white">
                <BellRingIcon class="h-5 w-5"/>
              </AvatarFallback>
            </Avatar>
          </template>
          <template #header> 
            <div class="flex items-start gap-2 flex-col">
              <span class="text-black text-[30px] font-bold">{{ t('agent.filter.notify') }}</span>
              <!-- <span class="text-gray-500 text-sm">{{ singleReportData?.report_name || t('agent.filter.notify') }}</span> -->
            </div>
          </template>
          
          <template #default>
            <div ref="mainContentRef" class="overflow-auto h-full">
              <!-- 使用虚拟滚动包装 AgentStepTimeline -->
              <DynamicScroller
                v-if="filteredSteps.length > 0"
                :items="filteredSteps"
                :min-item-size="120"
                key-field="number"
                class="h-full">
                <template v-slot="{ item: step, index, active }">
                  <DynamicScrollerItem
                    :item="step"
                    :active="active"
                    :data-index="index">
                    <agent-step-timeline
                      :steps="[step]"
                      @step-click="handleStepClick">
                      <template #header="{ step: timelineStep }">
                        <div class="flex justify-between items-center">
                          <div class="flex items-center gap-2">
                            <h3 class="text-base font-medium text-slate-800 m-0">
                              {{ timelineStep.number }}. 
                              <span v-if="searchKeyword.trim()" v-html="highlightKeyword(timelineStep.title, searchKeyword)"></span>
                              <span v-else>{{ timelineStep.title }}</span>
                            </h3>
                            
                            <span v-if="getCompanyStatus(timelineStep.title)"
                                  class="text-xs font-medium px-3 py-1 rounded-full
                                     bg-indigo-50 text-indigo-600 border border-indigo-100">
                              {{ getCompanyStatus(timelineStep.title) }}
                            </span>
                            
                            <span v-if="getCompanyLevel(timelineStep.title)"
                                  class="text-xs font-medium px-3 py-1 rounded-full
                                     bg-amber-50 text-amber-600 border border-amber-100">
                              {{ getCompanyLevel(timelineStep.title) }}
                            </span>
                          </div>
                          
                          <span :class="[
                            'text-xs font-medium px-3 py-1 rounded-full',
                            {
                              'bg-green-50 text-green-600 border border-green-100': timelineStep.status === 'COMPLETED',
                              'bg-blue-50 text-blue-600 border border-blue-100': timelineStep.status === 'PROCESSING',
                              'bg-gray-50 text-gray-600 border border-gray-100': timelineStep.status === 'PENDING'
                            }
                          ]">
                            {{ timelineStep.status }}
                          </span>
                        </div>
                      </template>
                      <template #content="slotProps">
                        <slot 
                          name="step-content"
                          v-bind="slotProps"/>
                      </template>
                      <template #custom-content="{ step: timelineStep }">
                        <div class="flex items-start gap-2 flex-col mt-2" :data-id="timelineStep?.title">
                          <div class="text-gray-500 text-sm"
                               :class="{
                                 'text-red-500': timelineStep?.special_summary?.risk_status === 'Danger',
                               }"
                               v-html="timelineStep?.special_summary?.summary"></div>
                          
                          <div v-if="getCompanyChartData(timelineStep?.title)" class="flex flex-col gap-2 mt-2">
                            <span class="text-sm text-gray-500">{{ $t('agent.risk_indicators') }}:</span>
                            <div class="flex flex-col gap-4">
                              <RadarChartComponent 
                                v-for="(chart, chartIndex) in getCompanyChartData(timelineStep?.title)" 
                                :key="chartIndex"
                                :chart-data="chart"
                                :thumbnail="true"
                                :showIndicators="true"/>
                            </div>
                          </div>
                          
                          <el-popconfirm
                            :title="$t('agent.confirm_generate_report', { name: timelineStep?.title })"
                            @confirm="handleGenerateDetailReport(timelineStep)"
                            width="250px"
                            v-if="timelineStep?.status === 'COMPLETED' && SPECIAL_AGENT_TYPES.includes(singleReportData?.report_type || '')">
                            <template #reference>
                              <span class="text-main-color text-sm underline cursor-pointer">
                                {{ $t('agent.generate_detail_report', { name: timelineStep?.title }) }}
                              </span>
                            </template>
                          </el-popconfirm>
                        </div>
                      </template>
                    </agent-step-timeline>
                  </DynamicScrollerItem>
                </template>
              </DynamicScroller>
              
              <!-- 无搜索结果提示 -->
              <div v-else-if="steps.length > 0" class="flex flex-col items-center justify-center py-16 text-gray-500">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <Icon icon="mdi:magnify" class="h-8 w-8 text-gray-400"/>
                </div>
                <h3 class="text-lg font-medium text-gray-800 mb-2">{{ $t('agent.no_search_results') }}</h3>
                <p class="text-sm text-center text-gray-500 mb-6 max-w-md">{{ $t('agent.try_different_keywords') }}</p>
                <Button variant="outline" @click="clearFilters" class="border-gray-200 text-gray-600 hover:bg-gray-50">
                  <Icon icon="mdi:filter-off" class="h-4 w-4 mr-2"/>
                  {{ $t('agent.clear_all_filters') }}
                </Button>
              </div>
              
              <MarkdownContainer class="mt-[20px]" v-if="singleReportData?.summary" :content="singleReportData?.summary"/>
            </div>
          </template>
          
          <!-- Side Content -->
          <template #side>
            <el-button 
              class="w-full mb-2"
              :loading="exportRiskExcelLoading"
              :disabled="allRiskCompanyNames.length === 0"
              @click="exportRiskOgExcel">
              <Icon icon="vscode-icons:file-type-excel" class="mr-1 text-[16px]"/>
              {{ $t('agent.export_legal_og_excel') }}
            </el-button>
            <agent-progress-overview
              :current-step="currentStep"
              :total-steps="steps.length"
              :progress="progress"
              :time="time"
              :step-status="stepStatus"
              :showEstimatedTime="false"
              @refresh="handleRefresh"/>
          </template>
          
        </agent-detail-layout>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, onUnmounted, ref, computed, nextTick, watch } from 'vue'
import { definePage, useRoute, useRouter } from 'vue-router/auto'
import type { AgentStep, TimeInfo, StepStatus } from '@specificlab/ui'
import { AgentDetailLayout, AgentStepTimeline, AgentProgressOverview } from '@specificlab/ui'
import Header from '@/components/LayoutComponents/Header.vue'
import { Icon } from '@iconify/vue'
import { useReportProgress, SPECIAL_AGENT_TYPES } from '@/composables/useReportProgress'
import MarkdownContainer from '@/components/MarkdownComponents/MarkdownContainer.vue'
import { usePost } from '@/hooks/useHttp'
import { getToken } from '@/hooks/useToken'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import emitter from '@/utils/events'
import { useSmoothScroll, addHighlightStyles } from '@/hooks/useSmoothScroll'
import { isObject } from 'lodash-es'
// 引入雷达图组件
import RadarChartComponent from '@/pages/__components__/DashboardComponents/RadarChartComponent.vue'
import { BellRingIcon } from 'lucide-vue-next'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import dayjs from 'dayjs'
// 引入虚拟滚动组件
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
// 引入UI组件
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Select, 
  SelectContent, 
  SelectGroup,
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Toggle } from '@/components/ui/toggle'

const { t, locale } = useI18n()

// 搜索过滤相关的响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const riskFilter = ref('')

// 搜索框引用
const searchInputRef = ref<HTMLInputElement | null>(null)

// 筛选状态描述计算属性
const filterStatusDescription = computed(() => {
  const activeFilters = []
  
  // 检查搜索关键词
  if (searchKeyword.value.trim()) {
    activeFilters.push({
      type: 'search',
      value: searchKeyword.value.trim()
    })
  }
  
  // 检查状态过滤器
  if (statusFilter.value) {
    activeFilters.push({
      type: 'status',
      value: statusFilter.value
    })
  }
  
  // 检查风险过滤器
  if (riskFilter.value === 'risk') {
    activeFilters.push({
      type: 'risk',
      value: 'risk'
    })
  }
  
  // 如果没有任何过滤器，返回默认描述
  if (activeFilters.length === 0) {
    return t('agent.filter_description.all_companies', { total: steps.value.length })
  }
  
  // 构建描述文案
  let description = t('agent.filter_description.current_showing')
  
  // 处理组合过滤器描述
  if (activeFilters.length === 1) {
    const filter = activeFilters[0]
    if (filter.type === 'search') {
      description = t('agent.filter_description.search_company', { company: filter.value })
    } else if (filter.type === 'status') {
      const statusText = t(`agent.status_${filter.value.toLowerCase()}`)
      description = t('agent.filter_description.status_companies', { status: statusText })
    } else if (filter.type === 'risk') {
      description = t('agent.filter_description.risk_companies')
    }
  } else {
    // 多重过滤器组合
    const filterParts = []
    
    for (const filter of activeFilters) {
      if (filter.type === 'search') {
        filterParts.push(t('agent.filter_description.company_part', { company: filter.value }))
      } else if (filter.type === 'status') {
        const statusText = t(`agent.status_${filter.value.toLowerCase()}`)
        filterParts.push(t('agent.filter_description.status_part', { status: statusText }))
      } else if (filter.type === 'risk') {
        filterParts.push(t('agent.filter_description.risk_part'))
      }
    }
    
    description = t('agent.filter_description.combined', { 
      filters: filterParts.join(t('agent.filter_description.separator'))
    })
  }
  
  return description
})

// // 检测操作系统以显示正确的快捷键
// const shortcutKey = computed(() => {
//   const userAgent = navigator.userAgent
//   const isMac = /Mac|iPhone|iPod|iPad/.test(userAgent)
//   return isMac ? '⌘' : 'Win'
// })

// 搜索回车处理
const handleSearchEnter = () => {
  // 回车时可以执行额外的搜索逻辑，比如记录搜索历史
  if (searchKeyword.value.trim()) {
    // 这里可以添加搜索分析或日志记录
  }
}

// 清空过滤器
const clearFilters = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
  riskFilter.value = ''
}

// 处理风险过滤器变化
const handleRiskFilterChange = (value: boolean) => {
  riskFilter.value = value ? 'risk' : ''
}

const { execute: delayReport, isLoading: isDelaying } = usePost('/formatted_report/delay_report_agent', {}, {}, {
  immediate: false,
})

const { execute: exportRiskExcel, isLoading: exportRiskExcelLoading, state: excelState } = usePost<{data: Blob}>('/risk_company/legal_excel_export', {
}, {
  responseType: 'blob'
}, {
  immediate: false,
})

definePage({
  meta: {
    layout: false,
    requiresAuth: true,
  },
})

const route = useRoute('/dashboard/[id]')
const router = useRouter()
const taskId = route.params.id as string
const reportType = route.query.report_type as string
const tagParam = route.query.tag as string

// 获取URL参数用于初始化过滤状态
const filterType = route.query.filter_type as string
const initialRiskStatus = route.query.risk_status as string
const initialStatus = route.query.status as string
const searchCompany = route.query.search_company as string

const {
  open, 
  close,
  singleReportData,
  detailSteps,
  stepStatus,
  timeInfo
} = useReportProgress('/api/formatted_report/one_report_progress', {
  single: true,
  report_id: taskId,
  report_type: reportType
})

const allRiskCompanyNames = computed(()=> singleReportData.value?.tools_results?.filter(item => item.risk_status === 'Danger').map(i => i.name) || [] )

// const exportRiskOgExcel = () => {
//   exportRiskExcel(0, {
//     company_name_list: allRiskCompanyNames.value
//   })
// }
const exportRiskOgExcel = async () => {
  try {
    await exportRiskExcel(0, { // Assuming 0 is a valid first argument for execute
      company_name_list: allRiskCompanyNames.value
    })

    // Check the response state and data
    if (!excelState.value || !excelState.value.data) {
      ElMessage.error(t('common.error', 'Export failed: No data received from server.'))
      console.error('Export failed: No response data in excelState.value or excelState.value.data is missing.')
      return
    }

    const blobData = excelState.value.data
    if (!(blobData instanceof Blob)) {
      ElMessage.error(t('common.error', 'Export failed: Invalid data format received.'))
      console.error('Export failed: Response data is not a Blob. Received type:', typeof blobData)
      return
    }
    
    if (blobData.size === 0) {
      ElMessage.error(t('common.error', 'Export failed: Received empty file.'))
      console.error('Export failed: Received an empty Blob (0 bytes).')
      return
    }

    // 新增日志
    console.log('Blob details before download - Type:', blobData.type, 'Size:', blobData.size)
    console.log('Full Blob object:', blobData)

    // Create a URL for the Blob
    const url = window.URL.createObjectURL(blobData)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    // Set the download filename with a .zip extension.
    // The browser should ideally use the filename from Content-Disposition header if provided by backend.
    // This client-side name acts as a fallback.
    a.download = `legal_risk_export_${dayjs().format('YYYYMMDD_HHmm')}.zip` 
    document.body.appendChild(a)
    a.click()

    // Clean up by revoking the object URL and removing the anchor element
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    ElMessage.success(t('common.success', 'Export successful!'))
  } catch (error: any) {
    console.error('Error during Excel export:', error)
    ElMessage.error(error?.response?.data?.message || error?.message || t('common.error', 'An unexpected error occurred during export.'))
  }
}
// 修改获取特定公司的雷达图数据函数
const getCompanyChartData = (companyName: string | undefined) => {
  if (!companyName) {return null}
  
  // 首先尝试从 legal_agent_data 获取数据
  if (singleReportData.value?.legal_agent_data && singleReportData.value.legal_agent_data[companyName]) {
    return singleReportData.value.legal_agent_data[companyName].chart_data || null
  }
  
  // 如果没有 legal_agent_data 或对应公司不存在，再尝试从 company_status 获取数据 (保持向后兼容)
  if (singleReportData.value?.company_status && singleReportData.value.company_status[companyName]) {
    return singleReportData.value.company_status[companyName].chart_data || null
  }
  
  return null
}

// 修改获取所有公司的雷达图数据计算属性
const allCompanyChartData = computed(() => {
  const result = []
  
  // 首先尝试从 legal_agent_data 获取数据
  if (singleReportData.value?.legal_agent_data) {
    for (const companyName in singleReportData.value.legal_agent_data) {
      const companyData = singleReportData.value.legal_agent_data[companyName]
      if (companyData?.chart_data) {
        result.push({
          companyName,
          chartData: companyData.chart_data
        })
      }
    }
  }
  
  // 如果没有数据，再尝试从 company_status 获取数据 (保持向后兼容)
  if (result.length === 0 && singleReportData.value?.company_status) {
    for (const companyName in singleReportData.value.company_status) {
      const companyData = singleReportData.value.company_status[companyName]
      if (companyData?.chart_data) {
        result.push({
          companyName,
          chartData: companyData.chart_data
        })
      }
    }
  }
  
  return result
})

// Computed properties for the component
const agentName = computed(() => singleReportData.value?.report_name || '')
const currentStep = computed(() => stepStatus.value.completed + stepStatus.value.inProgress)
// progress 进度需要向上取整
const progress = computed(() => Math.ceil(singleReportData.value?.progress || 0))
const avatar = computed(() => singleReportData.value?.avatar || '')

const steps = computed(() => detailSteps.value)

// 标记是否已经初始化过，避免重复初始化
const hasInitialized = ref(false)

// 初始化过滤状态（在响应式数据定义之后）
const initializeFilters = () => {
  // 如果已经初始化过，则不再处理URL参数
  if (hasInitialized.value) {
    return
  }

  // 实时读取当前路由参数
  const currentFilterType = route.query.filter_type as string
  const currentRiskStatus = route.query.risk_status as string
  const currentStatus = route.query.status as string
  const currentSearchCompany = route.query.search_company as string
  
  // 清空现有过滤器状态
  riskFilter.value = ''
  statusFilter.value = ''
  searchKeyword.value = ''
  
  // 根据URL参数设置初始过滤状态
  if (currentFilterType === 'risk' && currentRiskStatus === 'risk') {
    riskFilter.value = 'risk'
  }
  
  if (currentFilterType === 'status' && currentStatus) {
    statusFilter.value = currentStatus
  }
  
  // 如果有搜索公司参数，设置搜索关键词
  if (currentSearchCompany) {
    searchKeyword.value = currentSearchCompany
  }
  
  // 标记已初始化
  hasInitialized.value = true
  
  // 清除URL中的过滤参数，避免后续操作受影响
  if (currentFilterType || currentRiskStatus || currentStatus || currentSearchCompany) {
    const newQuery = { ...route.query }
    delete newQuery.filter_type
    delete newQuery.risk_status
    delete newQuery.status
    delete newQuery.search_company
    
    router.replace({
      path: route.path,
      query: newQuery
    })
  }
}

// 监听路由变化以重新初始化过滤器
watch(() => route.query, (newQuery, oldQuery) => {
  // 检查是否有过滤相关的参数变化
  const hasFilterChanges = (
    newQuery.filter_type !== oldQuery?.filter_type ||
    newQuery.risk_status !== oldQuery?.risk_status ||
    newQuery.status !== oldQuery?.status ||
    newQuery.search_company !== oldQuery?.search_company
  )
  
  if (hasFilterChanges) {
    nextTick(() => {
      initializeFilters()
    })
  }
}, { immediate: false })

// 监听数据加载完成后重新初始化（仅在未初始化时）
watch(() => singleReportData.value?.id, (newId) => {
  if (newId && !hasInitialized.value) {
    nextTick(() => {
      initializeFilters()
    })
  }
}, { immediate: true })

// 过滤逻辑
const filteredSteps = computed(() => {
  let filtered = steps.value
  
  // 根据搜索关键词过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    filtered = filtered.filter(step => 
      step.title.toLowerCase().includes(keyword)
    )
  }
  
  // 根据状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(step => step.status === statusFilter.value)
  }
  
  // 根据风险状态过滤
  if (riskFilter.value === 'risk') {
    // 有风险：严格检测方法，避免误判
    filtered = filtered.filter(step => {
      const summary = step.special_summary?.summary || ''
      const riskStatus = step.special_summary?.risk_status
      
      // 1. 首要检查：risk_status 是否明确为 'Danger'
      if (riskStatus === 'Danger') {
        return true
      }
      
      // 2. 检查原始工具结果中的风险状态
      const originalTool = singleReportData.value?.tools_results?.find(t => t.name === step.title)
      if (originalTool?.risk_status === 'Danger') {
        return true
      }
      
      // 3. 检查tools_results_dict中的is_danger标识（CompositeAgent专用）
      const toolResult = singleReportData.value?.tools_results_dict?.[step.title]
      if (toolResult && typeof toolResult === 'object') {
        // 检查CompositeAgent的legal_summary和social_summary
        if ('legal_summary' in toolResult) {
          const isLegalDanger = ((toolResult as any).legal_summary)?.is_danger
          if (isLegalDanger === true) {
            return true
          }
        }
        if ('social_summary' in toolResult) {
          const isSocialDanger = ((toolResult as any).social_summary)?.is_danger
          if (isSocialDanger === true) {
            return true
          }
        }
        // 检查嵌套结构
        if ('summary' in toolResult) {
          const compositeData = (toolResult as any).summary
          const isLegalDanger = compositeData?.legal_summary?.is_danger
          const isSocialDanger = compositeData?.social_summary?.is_danger
          if (isLegalDanger === true || isSocialDanger === true) {
            return true
          }
        }
      }
      
      // 4. 最后检查摘要中是否有明确的高风险标识（更严格的条件）
      // 只检查明确的风险标识，而不是简单的"风险"关键词
      if (summary.includes('text-red-500')) {
        return true
      }
      
      return false
    })
  }
  
  return filtered
})

// 活跃过滤器数量
const activeFiltersCount = computed(() => {
  let count = 0
  if (searchKeyword.value.trim()) {
    count++
  }
  if (statusFilter.value) {
    count++
  }
  if (riskFilter.value) {
    count++
  }
  return count
})

// 高亮搜索关键词的函数
const highlightKeyword = (text: string, keyword: string) => {
  if (!keyword.trim()) {
    return text
  }
  const regex = new RegExp(`(${keyword.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">$1</mark>')
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd/Win + K 聚焦搜索框
  if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'k') {
    event.preventDefault()
    // 使用ref直接聚焦搜索框
    if (searchInputRef.value) {
      searchInputRef.value.focus()
    }
  }
  // Escape 清空搜索
  if (event.key === 'Escape' && (searchKeyword.value || statusFilter.value || riskFilter.value)) {
    clearFilters()
  }
}

const time = computed(() => timeInfo.value)
const isCompleted = computed(() => ['success', 'completed'].includes(singleReportData.value?.status || ''))
const frequencyLabel = computed(() => {
  if (!singleReportData.value) {
    return ''
  }
  
  // 使用as any强制转换整个对象
  const report = singleReportData.value as any
  const freq = report.frequency || 
              report.tools_results_dict?.frequency || 
              report.params?.frequency
  
  if (freq !== undefined) {
    const frequency = Number(freq)
    if (frequency === 1) {
      return t('common.frequency_daily')
    }
    if (frequency === 7) {
      return t('common.frequency_weekly')
    }
    if (frequency === 30) {
      return t('common.frequency_monthly')
    }
    return t('common.frequency_days', { count: frequency })
  }
  return ''
})

const mainContentRef = ref<HTMLElement | null>(null)

// 初始化自定义滚动 hook
const { scrollToElementById } = useSmoothScroll({
  container: mainContentRef,
  offset: 0, // 可能需要根据你的 UI 调整
  duration: 800, // 使滚动更平滑
  highlightDuration: 3000 // 高亮持续时间
})

const handleStepClick = (step: AgentStep) => {
  console.log('Step clicked:', step)
}

const handleGenerateDetailReport = async (step: AgentStep) => {
  console.log('Generating detail report...', step, singleReportData.value)
  
  try {
    // 确定需要生成的报告类型
    const reportTypes = singleReportData.value?.report_type === 'CompositeAgent'
      ? ['RecentNewsAndSocialOpinionTrackingReport', 'CompanyLegalAnalysisReport']
      : [singleReportData.value?.report_type === 'SocialAgent' 
        ? 'RecentNewsAndSocialOpinionTrackingReport'
        : 'CompanyLegalAnalysisReport']

    // 构造并发送请求
    await Promise.all(
      reportTypes.map(reportType => 
        delayReport(0, {
          report_type: reportType,
          company_name_list: [step.title]
        })
      )
    )

    ElMessage.success(t('common.success'))
    setTimeout(() => {
      emitter.emit('async:update')
    }, 500)
  } catch (error: any) {
    console.error('API Error:', error)
    ElMessage.error(error?.message || t('common.error'))
  }
} 

const handleRefresh = () => {
  console.log('Refreshing data...')
}

const handleBack = () => {
  router.push('/workspace')
}

// 处理标签滚动
const handleTagScroll = async () => {
  if (!tagParam || !detailSteps.value.length) {return}
  
  // 给元素足够的时间渲染
  setTimeout(async () => {
    const success = await scrollToElementById(tagParam)
    
    // 如果第一次尝试失败，再试一次
    if (!success) {
      setTimeout(async () => {
        await scrollToElementById(tagParam)
      }, 500)
    }
  }, 100)
}

// 监听标签参数和步骤数据的变化
watch([() => tagParam, () => detailSteps.value.length], ([newTag, stepsLength]) => {
  if (newTag && stepsLength > 0) {
    handleTagScroll()
  }
}, { immediate: true })

watch(singleReportData, (newData) => {
  console.log('singleReportData', newData)
  if (['success', 'error'].includes(newData?.status || '') || newData?.progress === 100.0){
    setTimeout(() => {
      emitter.emit('async:update')
    }, 500)
  }
})

// 获取公司状态
const getCompanyStatus = (companyName: string) => {
  return singleReportData.value?.legal_agent_data?.[companyName]?.company_status
}

// 获取公司评级
const getCompanyLevel = (companyName: string) => {
  return singleReportData.value?.legal_agent_data?.[companyName]?.legal_score?.level
}

// 处理状态过滤器变化
const handleStatusFilterChange = (value: any) => {
  // 如果选择了"all"，则设置为空字符串表示显示全部
  statusFilter.value = value === 'all' ? '' : (typeof value === 'string' ? value : '')
}

onMounted(() => {
  open()
  addHighlightStyles()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
  
  // 立即尝试初始化，如果数据还没加载完成，watch会再次触发
  console.log('🚀 组件已挂载，尝试初始化过滤器')
  initializeFilters()
})

onUnmounted(() => {
  close()
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang='scss' module>
.root {
  background: #f8fafc;
  height: 100vh;

  .nav-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
    :global(.el-button + .el-button) {
      margin-left: 0;
    }
  }

  .left-sidebar {
    border-right: 1px solid #eee;
  }
}

// 清空按钮动画
:global(.clear-filters-btn) {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out !important;
  
  &[style*="display: none"] {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
  }
}
</style>