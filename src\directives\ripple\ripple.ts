import { createContainer } from './utils/createContainerElement'
import { createRippleElement } from './utils/createRippleElement'
import { getDistanceToFurthestCorner } from './utils/getDistanceToFurthestCorner'
import { getRelativePointer } from './utils/getRelativePointer'
import {
  decrementRippleCount,
  deleteRippleCount,
  getRippleCount,
  incrementRippleCount
} from './utils/rippleCount'
import { type IRippleDirectiveOptions } from './options'
const MULTIPLE_NUMBER = 2.05
const ripple = (
  event: PointerEvent,
  el: HTMLElement,
  options: IRippleDirectiveOptions
): void => {
  const rect = el.getBoundingClientRect()
  const computedStyles = window.getComputedStyle(el)
  const { x, y } = getRelativePointer(event, rect)
  const size = MULTIPLE_NUMBER * getDistanceToFurthestCorner(x, y, rect)

  const rippleContainer = createContainer(computedStyles)
  const rippleEl = createRippleElement(x, y, size, options)
  let originalPositionValue = ''
  let shouldDissolveRipple = false
  let token: number | null = null
  function dissolveRipple () {
    rippleEl.style.transition = 'opacity 150ms linear'
    rippleEl.style.opacity = '0'

    setTimeout(() => {
      rippleContainer.remove()

      decrementRippleCount(el)

      if (getRippleCount(el) === 0) {
        deleteRippleCount(el)
        el.style.position = originalPositionValue
      }
    }, 150)
  }
  function releaseRipple (e?: PointerEvent) {
    if (typeof e !== 'undefined') {
      document.removeEventListener('pointerup', releaseRipple)
      document.removeEventListener('pointercancel', releaseRipple)
    }

    if (shouldDissolveRipple) {dissolveRipple()}
    else {shouldDissolveRipple = true}
  }

  function cancelRipple() {
    token && clearTimeout(token)

    rippleContainer.remove()
    document.removeEventListener('pointerup', releaseRipple)
    document.removeEventListener('pointercancel', releaseRipple)
    document.removeEventListener('pointercancel', cancelRipple)
  }

  incrementRippleCount(el)

  if (computedStyles.position === 'static') {
    if (el.style.position) {originalPositionValue = el.style.position}
    el.style.position = 'relative'
  }

  rippleContainer.appendChild(rippleEl)
  el.appendChild(rippleContainer)


  document.addEventListener('pointerup', releaseRipple)
  document.addEventListener('pointercancel', releaseRipple)

  token = window.setTimeout(() => {
    document.removeEventListener('pointercancel', cancelRipple)
    // If the document is hidden, we don't want to animate the ripple, so we just dissolve it
    if (document.visibilityState === 'hidden') {
      releaseRipple()
      return
    }

    requestAnimationFrame(() => {
      rippleEl.style.transform = 'translate(-50%,-50%) scale(1)'
      rippleEl.style.opacity = `${options.finalOpacity}`

      setTimeout(() => releaseRipple(), options.duration)
    })
  }, options.delay)
  document.addEventListener('pointercancel', cancelRipple)
}

export { ripple }
