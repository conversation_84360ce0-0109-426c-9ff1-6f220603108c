<template>
  <div :class="$style.outline">
    <h3 :class="$style.title">{{ $t('intelligence.outline.title') }}</h3>
    <ul :class="$style.list">
      <li 
        v-for="section in sections" 
        :key="section.key"
        :class="[$style.item, { [$style.active]: activeSection === section.key }]"
        @click="handleClick(section.key)">
        {{ $t(section.titleKey) }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import type { MarkdownKeyType } from '@/types/intelligence'
const props = defineProps<{
  sections: Array<{
    key: MarkdownKeyType
    titleKey: string
  }>
  activeSection?: MarkdownKeyType
}>()

const emit = defineEmits<{
  'select': [section: MarkdownKeyType]
}>()

const handleClick = (section: MarkdownKeyType) => {
  emit('select', section)
}
</script>

<style lang="scss" module>
.outline {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.title {
  color: rgb(107 114 128 / var(--tw-text-opacity));
  font-size: var(--size-font-6);
  font-weight: var(--weight-font-medium);
  margin-bottom: var(--m-large);
}

.list {
  display: flex;
  flex-direction: column;
}

.item {
  font-size: var(--size-font-7);
  line-height: 2;
  color: var(--text-color-regular);
  cursor: pointer;
  transition: color 0.2s ease;
  padding: var(--p-mini) 0;

  // hover状态
  &:hover {
    color: var(--primary-color);
  }

  /* // 选中状态
  &.active {
    color: var(--primary-color);
    font-weight: var(--weight-font-medium);
  } */
}
</style> 