
import { defineStore } from 'pinia'
import { defaults } from 'lodash-es'
import { RegisterStep, StepName } from './enum'
import type { ProfileFields, PostData } from './type'
import { usePost } from '@/hooks/useHttp'
import { reactive, ref, computed } from 'vue'
import { useGet } from '@/hooks/useHttp'
import { useUserStore } from '@/stores/user'

const emptyFields = {
  industry: [],
  area: [],
  style: [],
  overseas: [],
}
  
export const usePollStepStore = defineStore('poll-step', () => {
  
  const userStore = useUserStore()
  // state
  const {
    execute,
    isLoading:fieldsLoading,
  } = useGet<ProfileFields>('/common/get_profile_fields', {}, {
    immediate:false
  })

  const getFields = async ()=>{
    const result = await execute()
    result && setFieldsData(result)
  }

  const activeStep = ref<RegisterStep>(0)

  const postStep = computed(()=> activeStep.value + 1)
  const profileFields = reactive<ProfileFields>(defaults({}, emptyFields))

  const stepOnePostData = reactive<PostData>({
    step:1,
    user_profile:{
      industry:[],
      industry_value:''
    }
  })
  const stepTwoPostData = reactive<PostData>({
    step:2,
    user_profile:{
      area:[],
      area_value:''
    },

  })
  const stepThreePostData = reactive<PostData>({
    step:3,
    user_profile:{
      style:[],
      style_value:''
    },
  })
  const stepFourPostData = reactive<PostData>({
    step:4,
    user_profile:{
      overseas:[],
      overseas_value:''
    },

  })
  const setActiveStep = (step:RegisterStep)=>{
    activeStep.value = step
  }

  function setSingleFieldsData<T extends keyof ProfileFields>(key: T, value: ProfileFields[T]): void {
    profileFields[key] = value
  }

  const setFieldsData = (data: Partial<ProfileFields>) => {
    Object.entries(data).forEach(([key, value]) => {
      if (key in profileFields) {
        setSingleFieldsData(key as keyof ProfileFields, value)
      }
    })
  }

  const {
    execute:submitStepResult,
    isLoading
  } = usePost('/user/completion', undefined, {
    // 10 mins
    timeout: 600000
  }, {
    immediate:false
  })
 
  const submitRefreshUserAccount = async (data:PostData)=>{
    await submitStepResult(0, data)
    await userStore.loadAccount()
  }
  // 新增映射对象
  const stepNameMapping: { [key in RegisterStep]: StepName | null } = {
    [RegisterStep.StepOne]: StepName.industry,
    [RegisterStep.StepTwo]: StepName.area,
    [RegisterStep.StepThree]: StepName.style,
    [RegisterStep.StepFour]: StepName.overseas,
    [RegisterStep.FinallyStep]: null // 或者可以选择不映射
  }

  // 使用示例
  function getStepName(step: RegisterStep): StepName {
    return stepNameMapping[step]!
  }
  return {
    activeStep,
    profileFields,
    isLoading,
    fieldsLoading,
    postStep,
    stepFourPostData,
    stepThreePostData,
    stepTwoPostData,
    stepOnePostData,

    setFieldsData,
    setActiveStep,
    submitRefreshUserAccount,
    getStepName,
    getFields
  }
})