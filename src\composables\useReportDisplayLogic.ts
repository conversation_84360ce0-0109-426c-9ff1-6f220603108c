import { computed, unref, type MaybeRef, type ComputedRef } from 'vue'
import { useI18n } from 'vue-i18n'
// Import directly from original sources
import type { CompletedAgent } from '@/pages/dashboard/type'
import type { ReportList as StandardReport } from '@/pages/format-report/type'
// Import RunningAgent, AgentStep, and the canonical Report type from workspace types
import type { Report, RunningAgent, AgentStep } from '@/pages/workspace/types'
import { AVATAR_MAP, SPECIAL_AGENT_TYPES } from '@/composables/useReportProgress'

// Helper type for badge variants
type BadgeVariant = 'default' | 'outline' | 'destructive' | 'secondary' | null | undefined

// --- Type Guards --- 
function isMonitorAgent(report: Report | undefined | null): report is CompletedAgent {
  // Ensure the object has properties of CompletedAgent beyond just report_type if needed for stricter check
  return !!report && 'id' in report && 'agent_name' in report && SPECIAL_AGENT_TYPES.includes((report as CompletedAgent).report_type || '')
}

function isRunningAgent(report: Report | undefined | null): report is RunningAgent {
  return !!report && 'steps' in report && 'progress' in report && typeof report.progress === 'number' && Array.isArray(report.steps)
}

function isStandardReport(report: Report | undefined | null): report is StandardReport {
  return !!report && 'id' in report && 'name' in report && 'language' in report && !('steps' in report)
}

// --- Define the return type for the composable --- 
interface UseReportDisplayLogicReturn {
  // Type guards results
  isMonitorAgent: ComputedRef<boolean>;
  isRunningAgent: ComputedRef<boolean>;
  report: ComputedRef<Report | null>;

  // Statuses
  isPending: ComputedRef<boolean>;
  isRunning: ComputedRef<boolean>;
  isCompleted: ComputedRef<boolean>;
  isError: ComputedRef<boolean>;
  hasSteps: ComputedRef<boolean>;

  // Data
  reportDate: ComputedRef<string>;
  reportType: ComputedRef<string>;
  agentName: ComputedRef<string>;
  agentAvatar: ComputedRef<string>;
  reportName: ComputedRef<string>;
  reportDescription: ComputedRef<string>;
  reportProgress: ComputedRef<number>;
  progressMessage: ComputedRef<string>;
  summary: ComputedRef<string>;
  summaryArray: ComputedRef<string[]>;
  frequencyLabel: ComputedRef<string>;
  riskStatus: ComputedRef<string>;

  // Styling - General
  statusIndicatorClass: ComputedRef<string>;
  statusBorderClass: ComputedRef<string>;
  statusBgClass: ComputedRef<string>;
  statusBadgeClass: ComputedRef<string>;
  statusVariant: ComputedRef<BadgeVariant>;
  statusLabel: ComputedRef<string>;

  // Styling - Risk (MonitorRespon)
  riskStatusBorderClass: ComputedRef<string>;
  riskStatusBgClass: ComputedRef<string>;
  riskStatusAccentClass: ComputedRef<string>;
  riskStatusVariant: ComputedRef<BadgeVariant>;
  riskStatusLabel: ComputedRef<string>;

  // Styling - Step (Functions)
  getStepStatusVariant: (status: string | undefined) => BadgeVariant;
  getStepStatusBadgeClass: (status: string | undefined) => string;

  // i18n function
  t: (key: string, ...args: any[]) => string;
}


export function useReportDisplayLogic(reportRef: MaybeRef<Report>): UseReportDisplayLogicReturn {
  const { t } = useI18n() // Get the translation function

  const report = computed(() => unref(reportRef)) // Ensure report is reactive

  // --- Status Checks ---
  const isPending = computed(
    () => {
      const currentReport = report.value
      return isRunningAgent(currentReport) && currentReport.status === 'PENDING'
    },
    {
      onTrack(e) {
        console.log('[useReportDisplayLogic] isPending - onTrack:', e)
      },
      onTrigger(e) {
        console.log('[useReportDisplayLogic] isPending - onTrigger:', e)
      },
    }
  )
  const isRunning = computed(
    () => {
      const currentReport = report.value
      return isRunningAgent(currentReport) && currentReport.status === 'RUNNING'
    },
    {
      onTrack(e) {
        console.log('[useReportDisplayLogic] isRunning - onTrack:', e)
      },
      onTrigger(e) {
        console.log('[useReportDisplayLogic] isRunning - onTrigger:', e)
      },
    }
  )
  const isCompleted = computed(
    () => {
      const currentReport = report.value
      if (isStandardReport(currentReport)) { return true }
      if (isRunningAgent(currentReport)) { return currentReport.status === 'COMPLETED' }
      if (isMonitorAgent(currentReport)) {
        const status = currentReport.status?.toLowerCase() ?? ''
        return status === 'success'
      }
      return false
    },
    {
      onTrack(e) {
        console.log('[useReportDisplayLogic] isCompleted - onTrack:', e)
      },
      onTrigger(e) {
        console.log('[useReportDisplayLogic] isCompleted - onTrigger:', e)
      },
    }
  )
  const isError = computed(
    () => {
      const currentReport = report.value
      if (isRunningAgent(currentReport)) { return currentReport.status === 'ERROR' }
      if (isMonitorAgent(currentReport)) {
        const status = currentReport.status?.toLowerCase() ?? ''
        return status === 'error'
      }
      return false
    },
    {
      onTrack(e) {
        console.log('[useReportDisplayLogic] isError - onTrack:', e)
      },
      onTrigger(e) {
        console.log('[useReportDisplayLogic] isError - onTrigger:', e)
      },
    }
  )
  const hasSteps = computed(() => isRunningAgent(report.value) && !!report.value.steps && report.value.steps.length > 0)

  // --- Data Extraction ---
  const reportDate = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return new Date(0).toISOString()}
    if (isMonitorAgent(currentReport)) {return currentReport.updated_at || currentReport.created_at || new Date(0).toISOString()}
    if (isRunningAgent(currentReport)) {return currentReport.date || new Date().toISOString()}
    if (isStandardReport(currentReport)) {return currentReport.updated_at || new Date(0).toISOString()}
    return new Date(0).toISOString()
  })

  const reportType = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return t('common.unknown_type')}
    let typeKey = ''
    if (isMonitorAgent(currentReport)) {typeKey = currentReport.report_type || 'monitor_agent_type'}
    else if (isRunningAgent(currentReport)) {typeKey = currentReport.report_type || 'running_task_type'}
    else if (isStandardReport(currentReport)) {typeKey = currentReport.report_type || 'standard_report_type'}
    return t(`report.${typeKey}`, typeKey || t('common.unknown_type'))
  })

  const agentName = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return t('agent.unknown_agent_name')}
    if (isMonitorAgent(currentReport)) {return currentReport.agent_name || currentReport.report_name || t('agent.monitor_agent_name')}
    if (isRunningAgent(currentReport)) {return currentReport.name || t('agent.running_agent_name')}
    if (isStandardReport(currentReport)) {return currentReport.agent_name || currentReport.name || t('agent.report_agent_name')}
    return t('agent.unknown_agent_name')
  })

  const agentAvatar = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return ''}
    const type = (currentReport as any).report_type // Still might need 'as any' if report_type isn't strictly on all union members
    if (type && AVATAR_MAP[type as keyof typeof AVATAR_MAP]) {
      return AVATAR_MAP[type as keyof typeof AVATAR_MAP]
    }
    return ''
  })

  const reportName = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return t('report.unknown_report_title')}
    if (isMonitorAgent(currentReport)) {return currentReport.report_name || t('report.monitor_report_title')}
    if (isRunningAgent(currentReport)) {return currentReport.report_name || t('report.running_report_title')}
    if (isStandardReport(currentReport)) {return currentReport.name || t('report.standard_report_title')}
    return t('report.unknown_report_title')
  })

  const reportDescription = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return t('report.processing_default_desc')}
    if (isMonitorAgent(currentReport)) {return currentReport.report_name || t('report.monitor_task_default_desc')}
    if (isRunningAgent(currentReport)) {return currentReport.duty || t('report.running_task_default_desc')}
    if (isStandardReport(currentReport)) {return currentReport.summary || t('report.completed_default_desc')}
    return t('report.processing_default_desc')
  })

  const reportProgress = computed(() => {
    const currentReport = report.value
    if (isRunningAgent(currentReport)) {return currentReport.progress || 0}
    if (isMonitorAgent(currentReport)) {return isCompleted.value ? 100 : 0} // Rely on computed isCompleted
    if (isStandardReport(currentReport)) {return 100}
    return 0
  })

  const progressMessage = computed(() => {
    const currentReport = report.value
    if (isRunningAgent(currentReport)) {return currentReport.description || t('report.processing_default_desc')}
    return t('report.processing_default_desc')
  })

  const summary = computed(() => {
    const currentReport = report.value
    if (!currentReport) {return t('report.summary_unavailable')}
    let summaryText = t('report.summary_unavailable')

    if (isMonitorAgent(currentReport)) {
      const summaryData = (currentReport as any).special_summary || currentReport.summary // Use 'as any' for special_summary
      if (typeof summaryData === 'object' && summaryData !== null && summaryData.summary) {
        if (currentReport.report_type === 'CompositeAgent' && summaryData.summary.legal_summary && summaryData.summary.social_summary) {
          const legal = summaryData.summary.legal_summary.summary || t('report.no_legal_summary')
          const social = summaryData.summary.social_summary.summary || t('report.no_social_summary')
          summaryText = `${t('agent.legal_summary')}: ${legal}\n${t('agent.social_summary')}: ${social}`
        } else {
          summaryText = summaryData.summary
        }
      } else if (typeof summaryData === 'string') {
        summaryText = summaryData
      } else if (Array.isArray(currentReport.summary)) {
        summaryText = currentReport.summary.join('\n')
      } else if (typeof currentReport.summary === 'string') {
        summaryText = currentReport.summary
      } else {
        summaryText = t('report.monitor_summary_unavailable')
      }
    } else if (isRunningAgent(currentReport)) {
      summaryText = currentReport.description || t('report.task_running_summary')
    } else if (isStandardReport(currentReport)) {
      summaryText = currentReport.summary || t('report.report_summary_unavailable')
    }
    return summaryText || t('report.summary_unavailable')
  })

  const summaryArray = computed((): string[] => {
    const currentReport = report.value
    if (isMonitorAgent(currentReport)) {
      const summaryData = (currentReport as any).special_summary || currentReport.summary // Use 'as any' for special_summary
      if (Array.isArray(currentReport.summary)) {
        return currentReport.summary
      }
      if (typeof summaryData === 'object' && summaryData !== null && summaryData.summary) {
        if (currentReport.report_type === 'CompositeAgent' && summaryData.summary.legal_summary && summaryData.summary.social_summary) {
          const arr = []
          if (summaryData.summary.legal_summary?.summary) { arr.push(`${t('agent.legal_summary')}: ${summaryData.summary.legal_summary.summary}`) }
          if (summaryData.summary.social_summary?.summary) { arr.push(`${t('agent.social_summary')}: ${summaryData.summary.social_summary.summary}`) }
          return arr.length > 0 ? arr : [t('report.composite_summary_unavailable')]
        }
        if (typeof summaryData.summary === 'string') { return [summaryData.summary] }
      }
      if (typeof summaryData === 'string') { return summaryData.split('\n').filter(s => s.trim() !== '') }
      if (typeof currentReport.summary === 'string') { return currentReport.summary.split('\n').filter(s => s.trim() !== '') }
      return [t('report.monitor_details_unavailable')]
    }
    return []
  })


  const frequencyLabel = computed(() => {
    const currentReport = report.value
    if (isMonitorAgent(currentReport)) {
      const freq = currentReport.frequency
      if (freq !== undefined) {
        if (freq === 1) {return t('common.frequency_daily')}
        if (freq === 7) {return t('common.frequency_weekly')}
        if (freq === 30) {return t('common.frequency_monthly')}
        return t('common.frequency_days', { count: freq })
      }
    }
    return ''
  })

  const riskStatus = computed(() => {
    const currentReport = report.value
    if (isMonitorAgent(currentReport)) {
      return currentReport.risk_status || 'Safe'
    }
    return 'Safe'
  })


  // --- Styling Computeds ---
  const statusIndicatorClass = computed(() => {
    if (!report.value) {return 'bg-gray-500'}
    if (isRunning.value) {return 'bg-blue-500 animate-pulse'}
    if (isPending.value) {return 'bg-amber-500'}
    if (isError.value) {return 'bg-red-500'}
    if (isCompleted.value) {
      if (isMonitorAgent(report.value)) {
        switch (riskStatus.value?.toLowerCase()) {
        case 'danger': return 'bg-red-500'
        case 'warning': return 'bg-amber-500'
        case 'safe': default: return 'bg-green-500'
        }
      } else {
        return 'bg-green-500'
      }
    }
    return 'bg-gray-500'
  })

  const statusBorderClass = computed(
    () => {
      if (isRunning.value) { return 'border-l-blue-500' }
      if (isPending.value) { return 'border-l-amber-500' }
      if (isCompleted.value) { return 'border-l-green-500' }
      if (isError.value) { return 'border-l-red-500' }
      return 'border-l-slate-200'
    },
    {
      onTrack(e) {
        console.log('[useReportDisplayLogic] statusBorderClass - onTrack:', e)
      },
      onTrigger(e) {
        console.log('[useReportDisplayLogic] statusBorderClass - onTrigger:', e)
      },
    }
  )

  const statusBgClass = computed(
    () => {
      if (isRunning.value) { return 'bg-blue-50/80' }
      if (isPending.value) { return 'bg-amber-50/80' }
      if (isCompleted.value) { return 'bg-green-50/80' }
      if (isError.value) { return 'bg-red-50/80' }
      return 'bg-white/80'
    },
    {
      onTrack(e) {
        console.log('[useReportDisplayLogic] statusBgClass - onTrack:', e)
      },
      onTrigger(e) {
        console.log('[useReportDisplayLogic] statusBgClass - onTrigger:', e)
      },
    }
  )

  const statusBadgeClass = computed(() => { // For non-monitor card header badge
    if (!report.value || isMonitorAgent(report.value)) {return 'bg-slate-100 text-slate-700 border-0'}
    if (isRunning.value) {return 'bg-blue-100 text-blue-700 border-0'}
    if (isPending.value) {return 'bg-amber-100 text-amber-700 border-0'}
    if (isCompleted.value) {return 'bg-green-100 text-green-700 border-0'}
    if (isError.value) {return 'bg-red-100 text-red-700 border-0'}
    return 'bg-slate-100 text-slate-700 border-0'
  })

  const statusVariant = computed((): BadgeVariant => { // For non-monitor card header badge
    if (!report.value || isMonitorAgent(report.value)) {return 'outline'}
    if (isRunning.value) {return 'secondary'}
    if (isPending.value) {return 'secondary'}
    if (isCompleted.value) {return 'default'}
    if (isError.value) {return 'destructive'}
    return 'outline'
  })

  const statusLabel = computed(() => { // For non-monitor card header badge
    // if (!report.value || isMonitorAgent(report.value)) {return t('common.agent_status_unknown')}
    if (isRunning.value) {return t('common.agent_status_running')}
    if (isPending.value) {return t('common.agent_status_pending')}
    if (isCompleted.value) {return t('common.agent_status_completed')}
    if (isError.value) {return t('common.agent_status_error')}
    return t('common.agent_status_unknown')
  })


  // Risk Status Styling (for Monitor Cards)
  const riskStatusBorderClass = computed(() => {
    if (!report.value || !isMonitorAgent(report.value)) {return 'border border-slate-200'}
    if (isRunning.value) {return 'border border-blue-200'}
    if (isError.value) {return 'border border-red-200'}

    switch (riskStatus.value?.toLowerCase()) {
    case 'danger': return 'border border-red-200'
    case 'warning': return 'border border-amber-200'
    case 'safe': default: return 'border border-green-200'
    }
  })

  const riskStatusBgClass = computed(() => {
    if (!report.value || !isMonitorAgent(report.value)) {return 'bg-white/80'}
    if (isRunning.value) {return 'bg-blue-50/80'}
    if (isError.value) {return 'bg-red-50/80'}

    switch (riskStatus.value?.toLowerCase()) {
    case 'danger': return 'bg-red-50/80'
    case 'warning': return 'bg-amber-50/80'
    case 'safe': default: return 'bg-green-50/80'
    }
  })

  const riskStatusAccentClass = computed(() => {
    if (!report.value || !isMonitorAgent(report.value)) {return 'bg-gradient-to-b from-slate-400 to-slate-600'}
    if (isRunning.value) {return 'bg-gradient-to-b from-blue-400 to-blue-600'}
    if (isError.value) {return 'bg-gradient-to-b from-red-400 to-red-600'}

    switch (riskStatus.value?.toLowerCase()) {
    case 'danger': return 'bg-gradient-to-b from-red-400 to-red-600'
    case 'warning': return 'bg-gradient-to-b from-amber-400 to-amber-600'
    case 'safe': default: return 'bg-gradient-to-b from-green-400 to-green-600'
    }
  })

  const riskStatusVariant = computed((): BadgeVariant => { // For monitor card header badge
    if (!report.value || !isMonitorAgent(report.value)) {return 'outline'}
    if (isRunning.value) {return 'secondary'}
    if (isError.value) {return 'destructive'}

    switch (riskStatus.value?.toLowerCase()) {
    case 'danger': return 'destructive'
    case 'warning': return 'secondary'
    case 'safe': default: return 'default'
    }
  })

  const riskStatusLabel = computed(() => { // For monitor card header badge
    // if (!report.value || !isMonitorAgent(report.value)) {return t('common.type_unknown')}
    if (isRunning.value) {return t('common.agent_status_running')}
    if (isError.value) {return t('common.agent_status_error')}

    switch (riskStatus.value?.toLowerCase()) {
    case 'danger': return t('agent.risk_alert')
    case 'warning': return t('common.risk_warning')
    case 'safe': return t('common.risk_safe')
    case 'normal': return t('common.risk_normal')
    default: return t('common.type_unknown')
    }
  })


  // Step Status Styling (used in both components if they show steps)
  const getStepStatusVariant = (status: string | undefined): BadgeVariant => {
    switch (status?.toUpperCase()) {
    case 'COMPLETED': return 'default'
    case 'PROCESSING': return 'secondary'
    case 'PENDING': return 'outline'
    case 'ERROR': return 'destructive'
    default: return 'outline'
    }
  }

  const getStepStatusBadgeClass = (status: string | undefined): string => {
    switch (status?.toUpperCase()) {
    case 'COMPLETED': return 'bg-green-100 text-green-700 border-0'
    case 'PROCESSING': return 'bg-blue-100 text-blue-700 border-0'
    case 'PENDING': return 'bg-slate-100 text-slate-600 border-0'
    case 'ERROR': return 'bg-red-100 text-red-700 border-0'
    default: return 'bg-slate-100 text-slate-600 border-0'
    }
  }

  // --- Expose necessary values ---
  return {
    // Expose boolean results directly for simpler use in templates
    isMonitorAgent: computed(() => isMonitorAgent(report.value)),
    isRunningAgent: computed(() => isRunningAgent(report.value)),
    report, // Expose the reactive report itself

    // Statuses
    isPending,
    isRunning,
    isCompleted,
    isError,
    hasSteps,

    // Data
    reportDate,
    reportType,
    agentName,
    agentAvatar,
    reportName,
    reportDescription,
    reportProgress,
    progressMessage,
    summary,
    summaryArray,
    frequencyLabel,
    riskStatus,

    // Styling - General
    statusIndicatorClass,
    statusBorderClass,
    statusBgClass,
    statusBadgeClass,
    statusVariant,
    statusLabel,

    // Styling - Risk (MonitorRespon)
    riskStatusBorderClass,
    riskStatusBgClass,
    riskStatusAccentClass,
    riskStatusVariant,
    riskStatusLabel,

    // Styling - Step (Functions)
    getStepStatusVariant,
    getStepStatusBadgeClass,

    // i18n function
    t
  }
} 