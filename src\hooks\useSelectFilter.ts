import { escapeRegexpString } from '@/utils'
import type { ISelectV2Props } from 'element-plus'
import { computed, isRef, ref, watch, shallowReadonly, toValue, type MaybeRefOrGetter, isReadonly } from 'vue'
import PinyinMatch from 'pinyin-match'

type SelectOption = ISelectV2Props['options'][number]
export const useSelectFilter = <R extends SelectOption[] | void = SelectOption[]>(options: MaybeRefOrGetter<SelectOption[]>, params?: {
  filterMethod?: (filter: string) => R
  openFilterCount?: number
}) => {
  let oldFilter = ''
  const isRefOptions = isRef(options) && !isReadonly(options)
  const _options = isRefOptions ? options : ref(toValue(options))
  if (!isRefOptions) {
    watch(() => toValue(options), (value) => {
      _options.value = value
      defaultFilterMethod(oldFilter)
    })
  }

  const enabledFilter = computed(() => _options.value.length >= (params?.openFilterCount || 10))

  const filterOptions = (value: SelectOption[], filter?: string) => {
    if (!filter) {
      return value
    }
    const regex = new RegExp(escapeRegexpString(filter), 'i')
    return value.filter(option => regex.test(option.label) || PinyinMatch.match(option.label, filter))
  }
  const defaultFilterMethod = (filter: string): SelectOption[] => {
    const filteredOptions = filterOptions(_options.value, filter)
    oldFilter = filter
    if (isRefOptions) {
      options.value = filteredOptions
    } else {
      _options.value = filteredOptions
    }
    return filteredOptions
  }
  const finalFilterMethod = (filter: string) => {
    return (params?.filterMethod ? params.filterMethod(filter) : defaultFilterMethod(filter)) as R extends never ? SelectOption[] : R
  }

  return {
    options: shallowReadonly(_options),
    enabledFilter,
    filterOptions,
    filterMethod: finalFilterMethod
  }
}
