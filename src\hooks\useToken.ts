import dayjs from 'dayjs'

// 1. Configuration Types / 配置类型
type Environment = 'prod' | 'dev' | 'test' | 'stage'

// 2. Environment Detection / 环境检测
const getCurrentEnv = (): Environment => {
  const hostname = window.location.hostname
  
  // 本地开发环境
  if (hostname === 'localhost' || /^(192\.168\.|127\.0\.0\.1)/.test(hostname)) {
    return 'dev'
  }
  
  // 生产环境域名检测
  if (/^(www\.)?specific-ai\.com$/.test(hostname)) {
    return 'prod'
  }
  
  if (/^(www\.)?dev\.specific-ai\.com$/.test(hostname)) {
    return 'dev'
  }
  
  if (/^(www\.)?test\.specific-ai\.com$/.test(hostname)) {
    return 'test'
  }
  
  if (/^(www\.)?stage\.specific-ai\.com$/.test(hostname)) {
    return 'stage'
  }
  
  return 'prod'
}

// 3. Token Configuration / Token配置
const ENV = getCurrentEnv()
const TOKEN_PREFIX = 'specific-app'
export const TokenKey = `${TOKEN_PREFIX}-token-${ENV}`

// 4. localStorage操作的retry函数
function retry<T>(
  operation: () => T,
  maxAttempts: number = 3
): T {
  let lastError: Error | null = null
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      return operation()
    } catch (error) {
      lastError = error as Error
      // 最后一次尝试失败才抛出错误
      if (attempt === maxAttempts - 1) {
        throw lastError
      }
      // 同步等待一小段时间再重试
      const waitTill = Date.now() + 1000 // 1秒重试间隔
      while (Date.now() < waitTill) {/* busy wait */}
    }
  }
  
  // TypeScript需要这个返回语句，实际上永远不会执行到这里
  throw lastError
}

// 5. 获取Token
export function getToken(): string {
  try {
    const token = localStorage.getItem(TokenKey)
    
    // 保持严格的token验证
    if (!token || typeof token !== 'string') {
      return ''
    }
    
    const trimmedToken = token.trim()
    if (!trimmedToken || trimmedToken.toLowerCase() === 'null') {
      return ''
    }
    
    return isTokenValid(trimmedToken) ? trimmedToken : ''
  } catch (error) {
    console.error('[getToken] Error:', error)
    return ''
  }
}

// 6. 设置Token
export function setToken(token = ''): void {
  try {
    // 处理token删除
    if (!token) {
      retry(() => {
        localStorage.removeItem(TokenKey)
        return true
      })
      return
    }
    
    // 设置token
    retry(() => {
      localStorage.setItem(TokenKey, token)
      return true
    })
    
    // 验证设置
    const savedToken = getToken()
    if (savedToken !== token) {
      throw new Error('Token verification failed')
    }
  } catch (error) {
    console.error('[setToken] Error:', error)
    throw new Error('Failed to set token')
  }
}

// 7. 删除Token
export function removeToken(): void {
  try {
    setToken('')
    
    // 双重检查确保删除
    const remainingToken = getToken()
    if (remainingToken) {
      throw new Error('Token removal verification failed')
    }
  } catch (error) {
    console.error('[removeToken] Error:', error)
    throw new Error('Failed to remove token')
  }
}

// 8. Token Validation / Token验证
export function isTokenValid(token: string): boolean {
  if (!token || typeof token !== 'string') {return false}
  
  const trimmedToken = token.trim()
  if (!trimmedToken || trimmedToken.toLowerCase() === 'null') {return false}
  
  // 添加其他必要的token格式验证
  const TOKEN_PATTERN = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/
  return TOKEN_PATTERN.test(trimmedToken)
}

// 9. Error Types / 错误类型
export class TokenError extends Error {
  constructor(message: string, public code: string) {
    super(message)
    this.name = 'TokenError'
  }
}
