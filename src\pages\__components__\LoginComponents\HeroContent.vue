<template>
  <div class="max-w-2xl">
    <h1 class="text-5xl font-bold text-gray-900 mb-6">
      {{ t('login.title') }}
    </h1>
    <p class="text-5xl font-bold text-gray-900 mb-6">
      {{ t('login.subtitle') }}
    </p>
    <p class="text-xl text-gray-700 mb-8">
      {{ t('login.description') }}
    </p>
    <div :class="$style['input-wrapper']">
      <ContactUsEmailInput
        :email="emailValue"
        :placeholder="t('login.emailPlaceholder')"
        @update:email="emailValue = $event"
        @submit="$emit('tryNow', emailValue)"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import ContactUsEmailInput from '@/components/ContactUsEmailInput.vue'

const props = defineProps<{
  email: string
}>()

const emit = defineEmits<{
  'update:email': [value: string]
  'tryNow': [value: string]
}>()

const { t } = useI18n()
const emailValue = ref(props.email)

watch(emailValue, (val) => {
  emit('update:email', val)
})
</script>

<style lang="scss" module>
.input-wrapper {
  min-width: 200px;
  :global {
    .el-input {
      .el-input__wrapper {
        background-color: #ffffff;
        border-radius: 8px;
      }
    }
  }
}
</style>
