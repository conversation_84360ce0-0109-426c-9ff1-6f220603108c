<!-- Generated by SpecStory -->

# 修改detailSteps逻辑以处理错误状态 (2025-02-24 18:17:24)

_**User**_

在detailSteps的逻辑中需要修改，当singleReportData为以下数据的时候

{"type": "update", "data": {"id": "67bc422d7b483949d1847898", "sync_type": false, "report_type": "LegalAgent", "agent_name": "法律风险预警专家", "report_name": "LegalAgent", "status": "running", "progress": 100.0, "tools_results": [{"name": "深圳市亚太电路科技有限公司", "status": "error"}], "tools_results_dict": {}}}

tools_results的status为error的时候，应该直接将status的状态变为error，而不是PROCESSING

---

_**Assistant**_

```thinking
Let me analyze this request carefully...

First, let me understand what's being asked:
- The issue is with the detailSteps computed property logic
- Specifically when handling tools_results items with status "error"
- Currently it's converting "error" to "PROCESSING" incorrectly
- Need to preserve "error" status and convert it to "ERROR" instead

Looking at the current code:
- It's in the detailSteps computed property
- The status mapping logic happens when processing tools array
- Currently there's no explicit handling of "error" status
- The logic focuses on COMPLETED, PROCESSING, PENDING states

The sample data shows:
- tools_results has one item with status "error"
- This should remain as an error state
- But current logic may convert it to PROCESSING incorrectly

Key changes needed:
1. Need to add explicit handling of "error" status
2. Should map "error" to "ERROR" status
3. Need to do this before the existing PROCESSING logic

The fix approach:
1. Add error status check early in the mapping
2. Convert "error" to "ERROR" directly
3. Keep rest of the logic flow for other statuses
4. This ensures error states are preserved properly

Let me verify the full context:
- This is part of useReportProgress composable
- Used for tracking report generation progress
- Error states are important signals
- Should not be overridden with PROCESSING state

Additional considerations:
- Need to maintain consistency with other status mappings
- Should align with the SPECIAL_AGENT_TYPES handling
- Error status is critical for UI feedback
- Should not affect other valid status transitions

I'll prepare the code edit to implement this fix...
```

I'll help you modify the code to handle the error status correctly. Here's what I'll do:

1. Modify the detailSteps computed property to properly handle error status
2. Ensure error status is preserved and converted to ERROR instead of PROCESSING

Let me make this change:

---

```diff
      if (!singleReportData.value) { return [] }
      const isSpecialAgent = SPECIAL_AGENT_TYPES.includes(singleReportData.value?.report_type || '')
-     // Find the last COMPLETED step index
      const tools = [...singleReportData.value.tools_results]
      const lastCompletedIndex = tools.map(t => t.status).lastIndexOf('COMPLETED')
+     
+     // Handle error status first
+     tools.forEach(tool => {
+       if (tool.status === 'error') {
+         tool.status = 'ERROR'
+       }
+     })
      
      // If there's a next step after the last completed one, set it to PROCESSING
-     if (lastCompletedIndex !== -1 && lastCompletedIndex + 1 < tools.length) {
+     if (lastCompletedIndex !== -1 && lastCompletedIndex + 1 < tools.length && tools[lastCompletedIndex + 1].status !== 'ERROR') {
        tools[lastCompletedIndex + 1].status = 'PROCESSING'
      }
-     // If no completed steps and there are tools, set first to PROCESSING
-     else if (lastCompletedIndex === -1 && tools.length > 0) {
+     // If no completed steps and there are tools, set first to PROCESSING if not ERROR
+     else if (lastCompletedIndex === -1 && tools.length > 0 && tools[0].status !== 'ERROR') {
        tools[0].status = 'PROCESSING'
      }
```

---



I see there's a TypeScript error because we need to handle the type properly. Let me fix that:

---

```diff
      // Handle error status first
      tools.forEach(tool => {
-       if (tool.status === 'error') {
+       if (tool.status === 'ERROR' || tool.status === 'error') {
          tool.status = 'ERROR'
        }
```

---



I see we need to look at the type definitions more carefully. Let me check the ToolResult interface first:

---

<details>
            <summary>Grep search for "interface ToolResult" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/code/formal_code/go-global-fd/src/composables/useReportProgress.ts` | L15 | `export interface ToolResult {` |
| `file:///d%3A/code/formal_code/go-global-fd/src/composables/useReportProgress.ts` | L15 | `export interface ToolResult {` |

</details>

---

Read file: src/composables/useReportProgress.ts

---



Now I see the issue. The ToolResult interface defines status as: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'ERROR' | 'running' | 'success' | 'pending'

Let me fix the code to handle this properly:

---

```diff
      // Handle error status first
      tools.forEach(tool => {
-       if (tool.status === 'ERROR' || tool.status === 'error') {
+       if (tool.status === 'ERROR') {
          tool.status = 'ERROR'
        }
```

---



I see we need to handle this differently. Looking at the code more carefully, I notice that there's already status mapping logic in the onMessage handler. Let's modify that part instead:

---

```diff
                      tool.status = 'PENDING'
                    }
+                   if (tool.status === 'error'){
+                     tool.status = 'ERROR'
+                   }
                    
                    return tool
```

```diff
                      tool.status = 'PENDING'
                    }
+                   if (tool.status === 'error'){
+                     tool.status = 'ERROR'
+                   }
                    return tool
                  })
```

---



I see we need to handle the type system properly. Let me check the full type definition and fix this:

---

```diff
  export interface ToolResult {
    name: string
-   status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'ERROR' | 'running' | 'success' | 'pending'
+   status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'ERROR' | 'running' | 'success' | 'pending' | 'error'
    id?: string
    risk_status?: string
```

---



Now that we've fixed the type definition, let's also update the detailSteps computed property to handle error status properly:

---

```diff
      // Handle error status first
      tools.forEach(tool => {
-       if (tool.status === 'ERROR') {
+       if (tool.status === 'error') {
          tool.status = 'ERROR'
        }
```

---



Perfect! Now let me explain the changes I've made in both English and Chinese:

English:
1. Added 'error' to the ToolResult interface status type union to properly handle lowercase error status
2. Added error status handling in the onMessage handler to convert 'error' to 'ERROR'
3. Updated the detailSteps computed property to handle error status properly before processing other status changes
4. Ensured that error status is preserved and not overridden by PROCESSING status

These changes will now correctly handle the case when tools_results contains an item with status "error", converting it to "ERROR" and preserving that state.

中文：
1. 在 ToolResult 接口的 status 类型联合中添加了 'error'，以正确处理小写的错误状态
2. 在 onMessage 处理程序中添加了错误状态处理，将 'error' 转换为 'ERROR'
3. 更新了 detailSteps 计算属性，在处理其他状态更改之前正确处理错误状态
4. 确保错误状态被保留，不会被 PROCESSING 状态覆盖

这些更改现在将正确处理 tools_results 包含状态为 "error" 的项目的情况，将其转换为 "ERROR" 并保持该状态。

The code will now properly handle your test case:
```json
{
  "type": "update", 
  "data": {
    "id": "67bc422d7b483949d1847898", 
    "sync_type": false, 
    "report_type": "LegalAgent", 
    "agent_name": "法律风险预警专家", 
    "report_name": "LegalAgent", 
    "status": "running", 
    "progress": 100.0, 
    "tools_results": [
      {
        "name": "深圳市亚太电路科技有限公司", 
        "status": "error"
      }
    ], 
    "tools_results_dict": {}
  }
}
```

The status will be properly converted to "ERROR" and maintained in that state.

---

_**User**_

在dashboardData中，tools_results，中的status为error的时候应该也算是tool运行完成了，结合 @useReportProgress.ts 的singleReportData处理的逻辑修改这个tools_results的处理逻辑，注意不要遗漏需要修改的代码

---

_**Assistant**_

```thinking
Let me analyze this request carefully...

First, let me understand what's being asked:
- In dashboardData's tools_results handling
- When status is 'error', it should be counted as completed
- Need to align with singleReportData's handling logic
- Need to find all places where tools_results status is used

Looking at the current code:
1. In singleReportData handling:
- We convert 'error' to 'ERROR'
- We handle this in onMessage handler
- We also handle it in detailSteps computed

2. In dashboardData handling:
- We have similar status conversion in onMessage
- But we don't count 'ERROR' status in completedTools calculation
- This is used in tasks computed property

Key areas that need modification:
1. completedTools calculation in tasks computed
2. Any other places where tool completion is checked

Looking at the code more carefully:
- In tasks computed, we only count 'COMPLETED' status
- In detailSteps, we handle progress calculation
- In special_summary, we check for 'COMPLETED' status

Changes needed:
1. Modify completedTools filter to include 'ERROR' status
2. Update progress calculation in detailSteps
3. Update special_summary completion check
4. Keep status conversion consistent

Additional considerations:
- Need to maintain type safety
- Keep logic consistent across all status checks
- Ensure backward compatibility
- Maintain readability of code

I'll prepare the code edits to implement these changes...
```

I'll help you modify the code to handle error status as completed. Here's what I'll do:

1. First, let's modify the tasks computed property to count ERROR status as completed:

---

```diff
      dashboardData.value.map(report => {
        const completedTools = report.tools_results?.filter(
-         tool => tool.status === 'COMPLETED'
+         tool => tool.status === 'COMPLETED' || tool.status === 'ERROR'
        ).length
  
        // Find current processing tool or last completed tool
        const processingTool = report.tools_results.find(tool => tool.status === 'PROCESSING')
-       const lastCompletedTool = report.tools_results.slice().reverse().find(tool => tool.status === 'COMPLETED')
+       const lastCompletedTool = report.tools_results.slice().reverse().find(tool => 
+         tool.status === 'COMPLETED' || tool.status === 'ERROR'
+       )
        
        let description
```

---



2. Now let's update the detailSteps computed property to handle progress calculation for ERROR status:

---

```diff
            description: isSpecialAgent ? tool.name : i18nInstance.global.t(`tools.${tool.name}Desc`),
            data: {
-             progress: tool.status === 'COMPLETED' ? 100 : 
+             progress: tool.status === 'COMPLETED' || tool.status === 'ERROR' ? 100 : 
                tool.status === 'PROCESSING' ? 50 : 0,
            }
```

---





3. Finally, let's update the special_summary completion check:

---

```diff
          special_summary = {
            summary: singleReportData.value?.tools_results_dict?.[tool.name] || '',
-           risk_status: singleReportData.value?.tools_results.find(t =>t.status === 'COMPLETED' && t.name === tool.name)?.risk_status || ''
+           risk_status: singleReportData.value?.tools_results.find(t => 
+             (t.status === 'COMPLETED' || t.status === 'ERROR') && t.name === tool.name
+           )?.risk_status || ''
          }
        }
```

---



Let me explain the changes I've made in both English and Chinese:

English:
1. Modified the tasks computed property:
   - Updated completedTools filter to count both 'COMPLETED' and 'ERROR' status as completed
   - Updated lastCompletedTool finder to include 'ERROR' status as completed

2. Updated the detailSteps computed property:
   - Modified progress calculation to show 100% for both 'COMPLETED' and 'ERROR' status
   - This ensures error states are shown as completed in the UI

3. Modified the special_summary logic:
   - Updated the completion check to include both 'COMPLETED' and 'ERROR' status
   - This ensures risk_status is properly retrieved for error cases

These changes ensure consistent handling of error status across all parts of the code.

中文：
1. 修改了 tasks 计算属性：
   - 更新了 completedTools 过滤器，将 'COMPLETED' 和 'ERROR' 状态都计算为已完成
   - 更新了 lastCompletedTool 查找器，将 'ERROR' 状态也视为已完成

2. 更新了 detailSteps 计算属性：
   - 修改了进度计算，对于 'COMPLETED' 和 'ERROR' 状态都显示 100% 进度
   - 确保错误状态在 UI 中显示为已完成

3. 修改了 special_summary 逻辑：
   - 更新了完成状态检查，同时包含 'COMPLETED' 和 'ERROR' 状态
   - 确保在错误情况下也能正确获取 risk_status

这些更改确保了在代码的所有部分中一致地处理错误状态。

The code will now properly handle your test case where tools_results contains items with 'error' status, treating them as completed tasks while still maintaining their error state for proper UI display and status tracking.

---

