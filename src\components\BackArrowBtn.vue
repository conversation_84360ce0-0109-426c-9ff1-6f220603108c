<template>
  <button @click="handleClick" :class="$style['back-button']">
    <slot>
      <Icon icon="mdi:arrow-left" class="text-[24px] text-white"/>
    </slot>
  </button>
</template>
  
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useRouter } from 'vue-router'
  
const props = defineProps<{
    to?: string
  }>()
  
const router = useRouter()
  
const handleClick = () => {
  if (props.to) {
    router.push(props.to)
  } else {
    router.go(-1)
  }
}
</script>
  
<style lang="scss" module>
.back-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #0056b3;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.back-button:hover {
  background-color: #003d82;
}

.back-button:active {
  background-color: #00285a;
}

/* 确保图标颜色与背景形成对比 */
:deep(svg) {
  fill: #ffffff;
}
  </style>