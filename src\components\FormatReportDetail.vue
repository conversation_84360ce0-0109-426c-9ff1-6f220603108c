<template>
  <div class="min-h-screen flex flex-col" :class="$style.root" v-loading="isLoading">
    <!-- Header -->
    <Header :is-show-menu="false"/>

    <!-- Main Content -->
    <div class="flex-1 flex">
      <!-- Left Sidebar -->
      <div class="w-[15%] p-4 flex flex-col" :class="$style['left-sidebar']">
        <div :class="$style['nav-actions']">
          <el-button @click="handleBack" class="w-full" :loading="delaying">
            <Icon icon="mdi:arrow-left" class="mr-2"/> {{ t("global.back") }}
          </el-button>
          <el-button 
            v-if="reportData?.report_type === 'CompanyLegalAnalysisReport'"
            class="w-full mb-2"
            :loading="exportRiskExcelLoading"
            :disabled="reportData?.report_type === 'CompanyLegalAnalysisReport' && !reportData?.company_name"
            @click="exportRiskOgExcel(reportData?.company_name)">
            <Icon icon="vscode-icons:file-type-excel" class="mr-1 text-[16px]"/>
            {{ $t('agent.export_legal_og_excel') }}
          </el-button>

          <el-button
            v-if="status !== 'OPEN' && showRegenerateButton"
            @click="emit('regenerate')"
            class="w-full"
            type="primary"
            :loading="isReconnecting">
            <Icon icon="mdi:refresh" class="mr-2"/>
            {{
              isReconnecting
                ? `${t("global.retrying")} (${retryCount})`
                : t("global.regenerate")
            }}
          </el-button>

          <el-button
            v-else-if="status !== 'CLOSED'"
            @click="handleStop"
            class="w-full"
            :loading="delaying"
            type="danger">
            <Icon icon="mdi:stop" class="mr-2"/> {{ t("global.stop") }}
          </el-button>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="flex-1 flex flex-col overflow-y-auto h-full">
        <!-- Title Area Slot -->
        <slot name="title">
          <div
            class="py-3 text-center w-full flex justify-center flex-col items-center gap-[10px] h-[100px]">
            <div class="flex items-center justify-center">
              <span class="text-[20px] font-bold">{{ `${reportData?.user_query?.company_name || ''} ${reportData?.report_title}` }}</span>
              <el-tooltip :content="$t('formatReport.flowChartTip')">
                <Icon
                  v-if="status === 'CLOSED'"
                  icon="mdi:information-outline"
                  class="ml-2 cursor-pointer text-primary-500 hover:text-primary-600"
                  @click="showProcessFlow = true"/>
              </el-tooltip>
            </div>

            <!-- PDF Actions -->
            <template v-if="containerRef">
              <template v-if="isReadyToPdf">
                <PdfActionButtons
                  :container-ref="containerRef"
                  :chart-images="chartImages"
                  :search-source="searchResultsList"
                  :title="reportData?.report_title"
                  :timeline-data="timelineData"
                  :legal-timeline-data="legalTimelineData?.time_line"
                  :is-ready="status === 'CLOSED'"/>
              </template>
              <template v-else>
                <div class="text-sm text-gray-500 flex items-center gap-2">
                  <Icon icon="mdi:loading" class="animate-spin"/>
                  {{ t("intelligence.markdown.pdfnotready") }}
                </div>
              </template>
            </template>
          </div>
        </slot>

        <!-- Main Content Slot -->
        <div
          ref="containerRef"
          class="flex-1 overflow-y-auto px-4 h-full"
          :class="$style['report-container']"
          data-pdf-container>
          <BaseTimeLine
            v-if="timelineData.length > 0"
            :items="timelineData"
            :hollow="true"
            :placement="'top'"/>
          <ChartDisplay
            v-if="charts && charts.length > 0"
            ref="chartDisplayRef"
            :charts="charts"
            :title="t('formatReport.chartTitle')"
            @charts-rendered="onChartsRendered"
            @render-error="onChartRenderError"/>

          <LegalChartTimeLine
            v-if="legalTimelineData"
            :timeline="legalTimelineData.time_line"
            :chart-data="legalTimelineData.chart_data"
            @charts-rendered="onChartsRendered"/>
          <slot></slot>
          <SearchResultsList
            v-if="searchResultsList?.length"
            :results="searchResultsList"/>
        </div>
      </div>

      <!-- Right Sidebar -->
      <div class="w-[15%] p-4 flex flex-col gap-4">
        <div
          class="w-full h-[100px] flex items-center justify-start px-4 gap-4">
          <div
            v-for="item in iconMap"
            :key="item.id"
            class="cursor-pointer rounded-md p-2 flex items-center justify-center"
            :class="{ 'bg-gray-100': currentView === item.id }">
            <Icon
              :icon="item.icon"
              @click="handleIconClick(item.id)"
              class="cursor-pointer text-[20px]"></Icon>
          </div>
        </div>
        <Outline
          v-if="currentView === 'outline'"
          :sections="reportData?.report_outline || {}"
          :highlighted-section="highlightedSection"
          :status="status"
          :started-sections="startedSections"
          @section-change="handleSectionChange"/>
        <template v-if="currentView === 'mindmap'">
          <!-- <MindMap :data="mindMapData" @select-node="handleSelectNode"/> -->
          <BasicMindMap
            :data="basicMindMapData"
            @select-node="handleSelectNode"/>
        </template>
        <SimpleSearchResultsList
          v-else-if="currentView === 'search_list' && searchResultsList"
          :search-results="searchResultsList"/>
      </div>
    </div>
  </div>
  <ProcessFlowGraph
    v-model="showProcessFlow"
    :data="createFlowChartData()"
    @update:modelValue="showProcessFlow = $event"/>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  onBeforeUnmount,
  computed,
  toRaw,
  nextTick,
  type PropType,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import Header from '@/components/LayoutComponents/Header.vue'
import { isFontLoaded } from '@/hooks/usePdf/fonts/useFonts'
import PdfActionButtons from '@/components/PdfActionButtons.vue'
import Outline from '@/pages/format-report/__components__/Outline.vue'
import type { MindMapNodeData, NodeObj } from './MindMap/type'
import { useTippyReference } from '@/hooks/useTippyReference'
import SimpleSearchResultsList from '@/components/SimpleSearchResultsList.vue'
import ChartDisplay from '@/pages/format-report/__components__/ChartDisplay.vue'
import type {
  ChartData,
  ChartDisplayInstance,
} from '@/pages/format-report/__components__/ChartDisplay.vue'
import BaseTimeLine from '@/pages/format-report/__components__/BaseTimeLine.vue'
import BasicMindMap from '@/components/MindMap/index.vue'
import ProcessFlowGraph from '@/components/ProcessFlow/ProcessFlowGraph.vue'
import { createFlowChartData } from '@/const/data'
import LegalChartTimeLine from '@/pages/format-report/__components__/LegalChartTimeLine.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { url } from '@/pages/format-report/type'
import SearchResultsList from '@/components/SearchResultsList.vue'
import { usePost } from '@/hooks/useHttp'
import dayjs from 'dayjs'
const { t, locale } = useI18n()

const containerRef = ref<HTMLElement | null>(null)

const showProcessFlow = ref(false)

const { execute: exportRiskExcel, isLoading: exportRiskExcelLoading, state: excelState } = usePost<{data: Blob}>('/risk_company/legal_excel_export', {
}, {
  responseType: 'blob'
}, {
  immediate: false,
})


const exportRiskOgExcel = async (company_name:string) => {
  try {
    await exportRiskExcel(0, { // Assuming 0 is a valid first argument for execute
      company_name_list: [company_name]
    })

    // Check the response state and data
    if (!excelState.value || !excelState.value.data) {
      ElMessage.error(t('common.error', 'Export failed: No data received from server.'))
      console.error('Export failed: No response data in excelState.value or excelState.value.data is missing.')
      return
    }

    const blobData = excelState.value.data
    if (!(blobData instanceof Blob)) {
      ElMessage.error(t('common.error', 'Export failed: Invalid data format received.'))
      console.error('Export failed: Response data is not a Blob. Received type:', typeof blobData)
      return
    }
    
    if (blobData.size === 0) {
      ElMessage.error(t('common.error', 'Export failed: Received empty file.'))
      console.error('Export failed: Received an empty Blob (0 bytes).')
      return
    }

    // 新增日志
    console.log('Blob details before download - Type:', blobData.type, 'Size:', blobData.size)
    console.log('Full Blob object:', blobData)

    // Create a URL for the Blob
    const url = window.URL.createObjectURL(blobData)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    // Set the download filename with a .zip extension.
    // The browser should ideally use the filename from Content-Disposition header if provided by backend.
    // This client-side name acts as a fallback.
    a.download = `${company_name}_${dayjs().format('YYYY-MM-DD')}.zip` 
    document.body.appendChild(a)
    a.click()

    // Clean up by revoking the object URL and removing the anchor element
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    ElMessage.success(t('common.success', 'Export successful!'))
  } catch (error: any) {
    console.error('Error during Excel export:', error)
    ElMessage.error(error?.response?.data?.message || error?.message || t('common.error', 'An unexpected error occurred during export.'))
  }
}
type ViewType = 'outline' | 'mindmap' | 'search_list';
const currentView = ref<ViewType>('outline')
const iconMap: { id: ViewType; icon: string }[] = [
  {
    id: 'outline',
    icon: 'mdi:table-of-contents',
  },
  {
    id: 'mindmap',
    icon: 'mdi:file-tree',
  },
  {
    id: 'search_list',
    icon: 'mdi:list-box-outline',
  },
]
const chartDisplayRef = ref<ChartDisplayInstance>()

const handleIconClick = (id: ViewType) => {
  currentView.value = id
}
const props = defineProps({
  reportData: {
    type: Object,
    required: true,
  },
  status: {
    type: String,
    required: true,
  },
  isReconnecting: {
    type: Boolean,
    required: true,
  },
  retryCount: {
    type: Number,
    required: true,
  },
  highlightedSection: {
    type: String,
    required: true,
  },
  startedSections: {
    type: Array as PropType<string[]>,
    required: false,
  },
  searchResultsList: {
    type: Array as PropType<url[]>,
    required: true,
  },
  unCitedResults: {
    type: Object,
    required: false,
    default: () => ({}),
  },
  showRegenerateButton: {
    type: Boolean,
    required: false,
    default: true,
  },
  pdfParser: {
    type: Function as PropType<(node: HTMLElement) => HTMLElement>,
    required: false,
  },
  charts: {
    type: Array as PropType<ChartData[]>,
    default: () => [],
  },
  timelineData: {
    type: Array as PropType<{ time: string; content: string }[]>,
    default: () => [],
  },
  legalTimelineData: {
    type: Object as PropType<{
      time_line: { time: string; content: string }[]
      chart_data: {
        data: { x: string; y: number }[]
      }
    }>,
    default: null
  },
  isLoading: {
    type: Boolean,
    required: false,
    default: false,
  },
  delaying: {
    type: Boolean,
    required: false,
    default: false,
  },
})

const emit = defineEmits<{
  back: [];
  regenerate: [];
  stop: [];
  'section-change': [section: string];
  'charts-rendered': [images: Record<string, string>];
  'chart-error': [errors: Record<string, Error>];
}>()
const chartImages = ref<Record<string, string>>({})

// 处理图表渲染完成事件
const onChartsRendered = (images: Record<string, string>) => {
  chartImages.value = images
  console.log('chartDisplayRef.value', chartDisplayRef.value)
  emit('charts-rendered', images)
}

// 处理图表渲染错误
const onChartRenderError = (errors: Record<string, Error>) => {
  console.error('Chart render errors:', errors)
  // 可以在这里添加错误处理逻辑，比如显示错误示等
  emit('chart-error', errors)
}

const isReadyToPdf = computed(() => {
  return isFontLoaded.value || locale.value.toLowerCase().includes('en')
})


// 使用 tippy hook
const { initializeReferenceLinks, cleanupReferenceLinks } = useTippyReference({
  containerRef,
  searchResultItems: computed(() => toRaw(props.searchResultsList || [])),
})

// 监听内容变化
const observer = ref<MutationObserver | null>(null)

// 监听内容变化并重新初始化引用链接
const setupContentObserver = () => {
  observer.value = new MutationObserver((mutations) => {
    const hasNewContent = mutations.some(
      (mutation) =>
        mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0
    )

    if (hasNewContent) {
      // 清理旧的事件监听
      cleanupReferenceLinks()
      // 重新初始化新的事件监听
      initializeReferenceLinks()
    }
  })

  if (containerRef.value) {
    observer.value.observe(containerRef.value, {
      childList: true,
      subtree: true,
    })
  }
}

// Section handling
const handleSectionChange = (sectionKey: string, shouldScroll = false) => {
  emit('section-change', sectionKey)
  if (shouldScroll) {
    scrollToSection(sectionKey)
  }
}

const handleSelectNode = (node: NodeObj) => {
  console.log(node)
  scrollToSection(node.topic)
}

// Scroll to section functionality
const scrollToSection = (sectionKey: string) => {
  if (!containerRef.value) {
    return
  }

  const titleElement = containerRef.value.querySelector(
    `div[data-pdf-section="${sectionKey}"]`
  )
  if (titleElement) {
    const containerTop = containerRef.value.getBoundingClientRect().top
    const elementTop = titleElement.getBoundingClientRect().top
    const offset = elementTop - containerTop

    containerRef.value.scrollTo({
      top: containerRef.value.scrollTop + offset - 20,
      behavior: 'smooth',
    })
  }
}

// Enhanced scroll handler with debounce
const handleScroll = () => {
  if (!containerRef.value || props.status === 'OPEN') {
    return
  }

  const container = containerRef.value
  const titles = container.querySelectorAll('div[data-pdf-section]')
  const containerTop = container.getBoundingClientRect().top

  let currentSection = null
  for (const title of titles) {
    const titleTop = title.getBoundingClientRect().top
    if (titleTop >= containerTop) {
      currentSection = title.getAttribute('data-pdf-section')
      break
    }
  }

  if (currentSection && currentSection !== props.highlightedSection) {
    handleSectionChange(currentSection, false)
  }
}

// Lifecycle hooks for scroll event management
onMounted(() => {
  // 初始化引用链接
  initializeReferenceLinks()
  // 设置内容观察器
  setupContentObserver()

  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', handleScroll)
  }
})

onBeforeUnmount(() => {
  // 清理引用链接
  cleanupReferenceLinks()
  // 断开观察器连接
  if (observer.value) {
    observer.value.disconnect()
    observer.value = null
  }

  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', handleScroll)
  }
})

// 转换函数
const convertToMindMapData = (report: Record<string, any>): MindMapNodeData => {
  return {
    nodeData: {
      id: 'root',
      topic: report.report_title,
      root: true,
      children: Object.keys(report?.report_outline || {}).map((key) => ({
        id: `node-${Date.now()}-${Math.random()}`,
        topic: key,
      })),
    },
  }
}

// 计算属性
const mindMapData = computed(() => {
  if (!props.reportData?.report_outline) {
    return {
      nodeData: {
        id: 'root',
        topic: props.reportData.report_title,
        root: true,
        children: [],
      },
    }
  }
  return convertToMindMapData(props.reportData)
})

const basicMindMapData = computed(() => {
  return {
    title: props.reportData.report_title,
    outline: Object.keys(props.reportData.report_outline || {}).map(
      (key) => key
    ),
  }
})

const createdConfirmMessage = async() =>{
  await ElMessageBox.confirm(
    t('global.confirmBack'),
    t('global.warning'),
    {
      confirmButtonText: t('button.confirm'),
      cancelButtonText: t('button.cancel'),
      dangerouslyUseHTMLString: true,
      type: 'warning',
    }
  )
}
const handleBack = async () => {
  if (props.delaying || props.status === 'CLOSED') {
    emit('back')
    return
  }

  try {
    await createdConfirmMessage()
    emit('back')
  } catch {
    // User cancelled
  }
}

const handleStop = async () => {
  if (props.delaying) {
    return
  }

  try {
    await createdConfirmMessage()
    emit('stop')
  } catch {
    // User cancelled
  }
}
</script>

<style lang="scss" module>
.root {
  .searchInputFilter {
    max-width: 220px;
  }

  .nav-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
    :global(.el-button + .el-button) {
      margin-left: 0;
    }
  }

  .left-sidebar {
    border-right: 1px solid #eee;
  }

  .skeleton-section {
    padding: 20px;
    margin: 14px auto;
    background: #fff;
    max-width: 1150px;
    border-radius: 8px;
    border: 1px solid #e4e4e4; // 添加边框
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05); // 添加阴影效果
  }

  .skeleton-container {
    animation: skeleton-loading 1.5s infinite;
  }

  .skeleton-title {
    height: 24px;
    background: #f2f2f2;
    border-radius: 4px;
    margin-bottom: 16px;
    width: 40%;
  }

  .skeleton-line {
    height: 16px;
    background: #f2f2f2;
    border-radius: 4px;
    margin-bottom: 12px;
    width: 100%;
  }

  .skeleton-line-short {
    @extend .skeleton-line;
    width: 60%;
  }

  @keyframes skeleton-loading {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
  .report-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-width: 100%;
    box-shadow: 0 2px 4px #0000000d;
    border-radius: 4px;
    margin: 0 16px;
    max-height: calc(100vh - 220px);
  }
}
</style>
