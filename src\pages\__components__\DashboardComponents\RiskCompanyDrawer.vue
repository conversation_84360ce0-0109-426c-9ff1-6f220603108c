<template>
  <el-drawer
    v-model="drawerVisible"
    size="90%"
    :title="$t('agent.all_risk_companies')"
    :destroy-on-close="true"
    @close="handleClose"
    @open="handleDrawerOpen">
    <div class="h-full flex flex-col">
      <!-- 搜索和筛选区域 -->
      <div class="p-4 border-b">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <el-input
              v-model="searchQuery"
              :placeholder="$t('common.search_placeholder')"
              clearable
              @input="handleSearch"
              class="w-[300px]!">
              <template #prefix>
                <Icon icon="mdi:search" class="text-gray-500"/>
              </template>
            </el-input>

            <!-- <el-select
              v-model="filterType"
              :placeholder="$t('agent.filter_by_type')"
              clearable
              class="w-[200px]!">
              <el-option
                v-for="type in agentTypes"
                :key="type"
                :label="type"
                :value="type"/>
            </el-select> -->
          </div>
          <el-button 
            :loading="isExporting"
            :disabled="companies.length === 0"
            @click="exportToExcel">
            <Icon icon="vscode-icons:file-type-excel" class="mr-1 text-[16px]"/>
            {{ $t('common.export_excel') }}
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="flex-1 overflow-auto p-4" v-loading="isDelaying">
        <el-table
          v-loading="isLoading"
          :data="companies"
          style="width: 100%"
          height="100%"
          row-key="id"
          @scroll="handleScroll"
          @row-click="handleRowClick">
          <el-table-column
            prop="company_name"
            :label="$t('login.companyName')"
            min-width="200">
            <template #default="{ row }">
              <div :class="$style['company-name']">
                {{ row.company_name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="risk_description"
            :label="$t('agent.risk_description')"
            min-width="400">
            <template #default="{ row }">
              <div :class="$style['risk-description']">
                <div :class="$style['risk-badge']">
                  <Icon icon="mdi:alert-circle" class="mr-1"/>
                  {{ $t('agent.risk_alert') }}
                </div>
                <div :class="$style['description-content']" class="line-clamp-3">
                  {{ row.risk_description }}
                </div>
                <el-button
                  :class="$style['view-more-btn']"
                  type="text"
                  size="small"
                  @click.stop="showFullDescription(row)">
                  {{ $t('common.view_more') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="notify_agent"
            :label="$t('agent.alert_from')"
            min-width="150">
            <template #default="{ row }">
              <div class="flex items-center gap-2">
                <img :src="AVATAR_MAP[row.notify_agent.agent_type as keyof typeof AVATAR_MAP]" class="w-6 h-6 rounded-full"/>
                {{ row.notify_agent.agent_name }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="updated_at"
            :label="$t('agent.updated_at')"
            width="180"
            sortable>
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('common.actions')"
            width="120"
            fixed="right">
            <template #default="{ row }">
              <!-- <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)">
                {{ $t('common.view') }}
              </el-button> -->
              <span class="text-main-color hover:underline cursor-pointer" @click="handleViewDetail(row)">
                {{ $t('common.generate_report') }}
              </span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 加载更多提示 -->
        <div v-if="hasMore" class="text-center py-4">
          <el-button
            type="text"
            :loading="isLoadingMore"
            @click="loadMore">
            {{ $t('common.load_more') }}
          </el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useDebounceFn, useVModel } from '@vueuse/core'
import { Icon } from '@iconify/vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { useGet, usePost } from '@/hooks/useHttp'
import { useI18n } from 'vue-i18n'
import { AVATAR_MAP } from '@/composables/useReportProgress'
import emitter from '@/utils/events'

const { execute: delayReport, isLoading: isDelaying } = usePost('/formatted_report/delay_report_agent', {}, {}, {
  immediate: false,
})
const { t } = useI18n()
interface RiskCompany {
  id: string
  company_name: string
  risk_description: string
  notify_agent: {
    agent_type: string
    agent_name: string
  }
  created_at: string
  updated_at: string
}

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 使用 useVModel 进行双向绑定
const drawerVisible = useVModel(props, 'visible', emit)
const { execute: executeMockData, isLoading: isMocking } = usePost('/risk_company/mock_data', {}, {}, {
  immediate: false,
})
// 数据状态
const companies = ref<RiskCompany[]>([])
const isLoading = ref(false)
const isLoadingMore = ref(false)
const hasMore = ref(true)
const lastId = ref<string | null>(null)

// 搜索和筛选
const searchQuery = ref('')
const filterType = ref('')
const agentTypes = ref(['SocialAgent', 'LegalAgent', 'FinancialAgent'])

// 添加日期范围相关的状态
const dateRange = ref<[string, string] | null>(null)

// 日期快捷选项
const dateShortcuts = [
  {
    text: t('common.last_week'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('common.last_month'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 处理搜索
const handleSearch = useDebounceFn(() => {
  lastId.value = null
  companies.value = []
  loadCompanies()
}, 300)

// 加载更多
const loadMore = async () => {
  if (isLoadingMore.value || !hasMore.value) {return}
  isLoadingMore.value = true
  await loadCompanies()
  isLoadingMore.value = false
}

// 使用 useGet 进行请求
const { execute: fetchCompanies } = useGet<{
  data: RiskCompany[]
  has_more: boolean
  next_cursor: string
}>('/risk_company/list', {}, {
  immediate: false,
})

// 在组件顶部定义 exportExcelRequest
const { execute: executeExportExcel, isLoading: isExporting, state: excelState } = useGet<{data: Blob}>('/risk_company/excel_export', {
  responseType: 'blob'
}, {
  immediate: false,
  resetOnExecute: true
})

// 获取公司数据
const loadCompanies = async () => {
  try {
    isLoading.value = true
    const params = {
      limit: 50,
      last_id: lastId.value,
      search: searchQuery.value,
      agent_type: filterType.value
    }

    const response = await fetchCompanies(0, { params })
    if (response) {
      const newCompanies = response.data
      if (newCompanies.length > 0) {
        companies.value.push(...newCompanies)
        lastId.value = response.next_cursor
      }
      hasMore.value = response.has_more
    }
  } catch (error) {
    ElMessage.error('Failed to fetch companies')
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

// 关闭处理
const handleClose = () => {
  companies.value = []
  lastId.value = null
  searchQuery.value = ''
  filterType.value = ''
  dateRange.value = null
}

// 查看详情
const handleViewDetail = async (company: RiskCompany) => {
  // 实现查看详情逻辑
  const reportType = company.notify_agent.agent_type === 'SocialAgent' 
    ? 'RecentNewsAndSocialOpinionTrackingReport' 
    : 'CompanyLegalAnalysisReport'
  const company_name = company.company_name
  const requestData = {
    report_type: reportType,
    company_name_list: [company_name],
  }
  await delayReport(0, requestData)
  ElMessage.success(t('common.success'))
  setTimeout(() => {
    emitter.emit('async:update')
  }, 500)
}

// 添加 handleScroll 方法
const handleScroll = ({ scrollTop, scrollHeight, clientHeight }: { scrollTop: number, scrollHeight: number, clientHeight: number }) => {
  if (scrollHeight - scrollTop <= clientHeight + 50 && hasMore.value && !isLoadingMore.value) {
    loadMore()
  }
}

// 在 el-table 上添加 onShow 回调
const handleRowClick = (row: RiskCompany) => {
  // 实现行点击逻辑
}

// 在 drawer 的 @open 事件中初始化数据
const handleDrawerOpen = () => {
  companies.value = []
  lastId.value = null
  loadCompanies()
}

// 修改 showFullDescription 方法
const showFullDescription = (row: RiskCompany) => {
  ElMessageBox({
    title: row.company_name,
    message: row.risk_description,
    dangerouslyUseHTMLString: false,
    customClass: 'risk-description-dialog',
    showClose: true,
    closeOnClickModal: true,
    confirmButtonText: t('common.close'),
    beforeClose: (action, instance, done) => {
      instance.confirmButtonLoading = true
      setTimeout(() => {
        done()
        instance.confirmButtonLoading = false
      }, 200)
    }
  })
}

// 修改 exportToExcel 函数
const exportToExcel = async () => {
  try {
    if (isExporting.value) {return}
    // 执行导出请求
    await executeExportExcel()
    // 检查响应
    if (!excelState.value) {
      throw new Error('Export failed: No response')
    }
    
    // 创建下载链接
    const blob = new Blob([excelState.value.data])
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${t('agent.all_risk_companies')}_${dayjs().format('YYYYMMDD_HHmm')}.xlsx`
    document.body.appendChild(a)
    a.click()
    
    // 清理
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    
    ElMessage.success(t('common.success'))
  } catch (error) {
    console.error('Export failed:', error)
    ElMessage.error(t('common.failed'))
  }
}
</script>

<style lang="scss" module>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: var(--el-color-primary-light-9);
}

.risk-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  border-radius: 8px;
  background-color: rgba(245, 108, 108, 0.08);
  border-left: 4px solid var(--el-color-danger);
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background-color: rgba(245, 108, 108, 0.12);
    box-shadow: 0 4px 16px 0 rgba(245, 108, 108, 0.15);
    transform: translateY(-1px);
  }
}

.risk-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  background-color: var(--el-color-danger);
  color: white;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 8px;
  width: fit-content;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
  
  :global(.iconify) {
    animation: pulse 2s infinite;
  }
}

.description-content {
  line-height: 1.7;
  font-size: 16px;
  color: var(--el-color-danger);
  font-weight: 600;
  margin: 4px 0;
  text-shadow: 0 0 1px rgba(245, 108, 108, 0.1);
/*   
  &::first-letter {
    font-size: 120%;
    color: var(--el-color-danger);
    font-weight: 600;
  } */
}

.view-more-btn {
  align-self: flex-end;
  color: var(--el-color-primary);
  font-size: 13px;
  
  &:hover {
    color: var(--el-color-primary-light-3);
    text-decoration: underline;
  }
}

.company-name {
  font-weight: 600;
  color: var(--el-color-primary-dark-2);
}

.agent-type-tag {
  margin-left: 8px;
}


:global(.el-drawer__body) {
  padding: 0;
}

:global(.risk-description-dialog) {
  width: 650px !important;
  max-width: 90vw;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: none;
}


:global(.risk-description-dialog .el-message-box__title) {
  font-size: 18px;
  font-weight: 600;
}

:global(.risk-description-dialog .el-message-box__content) {
  padding: 24px;
  font-size: 16px;
  color: var(--el-color-danger);
  font-weight: 600;
}

:global(.risk-description-dialog .el-message-box__message) {
  position: relative;
  padding-top: 40px;
}

:global(.risk-description-dialog .el-message-box__message::before) {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed rgba(245, 108, 108, 0.3);
  width: 100%;
}

:global(.risk-description-dialog .el-message-box__btns) {
  padding: 10px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

:global(.risk-description-dialog .el-button--primary) {
  background-color: #f56c6c;
  border-color: #f56c6c;
  padding: 10px 24px;
  border-radius: 6px;
}

:global(.risk-description-dialog .el-button--primary:hover) {
  background-color: #f78989;
  border-color: #f78989;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 