<template>
  <header
    :class="$style.headerOverrides"
    class="container mx-auto w-full px-4 h-[60px] flex items-center justify-between mt-[20px] transition-all duration-300">
    <!-- Logo -->
    <div class="flex items-center shrink-0">
      <el-image
        :src="logo"
        class="h-8 w-auto hover:opacity-80 transition-opacity"
        fit="cover"/>
    </div>

    <!-- Navigation - Desktop -->
    <!-- <el-menu
      mode="horizontal"
      :class="$style.elMenuOverrides"
      class="flex-1 hidden md:flex justify-center mx-4 bg-transparent"
      :ellipsis="false">
      <el-menu-item
        v-for="item in navItems"
        :key="item.key"
        class="h-[60px]! leading-[60px]! hover:text-primary transition-colors"
        @click="scrollToSection(item.key)">
        <span class="text-base font-bold">{{ item.label }}</span>
      </el-menu-item>
    </el-menu> -->

    <!-- Actions -->
    <div class="hidden md:flex items-center gap-6 shrink-0">
      <el-button
        link
        @click="emit('login')"
        class="hover:opacity-80 transition-opacity">
        {{ t("login.login") }}
      </el-button>
      <el-tooltip
        :content="$t('login.betaTestingTip')"
        placement="bottom"
        :disabled="!!invite_code">
        <el-button
          type="primary"
          round
          :disabled="!invite_code"
          @click="emit('register')"
          class="hover:brightness-110 transition-all">
          {{ t("login.register") }}
        </el-button>
      </el-tooltip>
    </div>

    <!-- Mobile Menu Button -->
    <el-button class="md:hidden ml-auto" @click="emit('toggle-mobile-menu')">
    </el-button>
  </header>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import type { NavItem } from './types/navigation'
import logo from '@/assets/logo.svg'
import { useRoute } from 'vue-router/auto'
import { computed } from 'vue'


const route = useRoute()
const invite_code = computed(() => route.query.invite_code as string ?? '')

defineProps<{
  navItems: NavItem[];
}>()

const emit = defineEmits<{
  login: [void];
  register: [void];
  'toggle-mobile-menu': [void];
}>()

const { t } = useI18n()

const scrollToSection = (sectionId: string) => {
  const section = document.getElementById(sectionId)
  if (section) {
    section.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style lang="scss" module>
.headerOverrides {
  :global(.el-menu) {
    --el-menu-bg-color: transparent;
    --el-menu-hover-bg-color: transparent;
    --el-menu-active-color: var(--el-color-primary);
    border-bottom: none;
  }

  :global(.el-menu-item) {
    display: flex;
    align-items: center;
    padding: 0 20px;
  }

  :global(.el-button) {
    height: 32px;
    padding: 0 16px;
  }
}
</style>
