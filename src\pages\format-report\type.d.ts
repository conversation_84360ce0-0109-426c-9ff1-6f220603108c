export interface FormatReportTemplate {
  id: string;
  report_name: string;
  report_description: string;
  user_keywords: Userkeywords;
  report_outline: Reportoutline;
  type?: string;
  report_introduce:string;
  report_type:string[];

}

export interface ProgressReport {
  id: string
  report_name: string
  status: string
  progress: number
  report_type: string
  params: Record<string, any>
}

export interface Reportoutline {
  [key: string]: _;
}

interface _ {
  description: string;
  keywords: string[];
}

interface Userkeywords {
  [key: string]: string;
}
export interface ReportList {
  id: string;
  name: string;
  date: string;
  report_type: string;
  updated_at: string;
  language:string;
  agent_name:string;
  summary:string;
}

interface Params {
  company_name: string;
  focus: string;
  industry: string;
}
export interface CreatedReport {
  report_name: string;
  params: Params;
  date: null;
  [key: string]: string;
  search_result:url[]
  chart_data:any[]
  time_line:TimelineData[]
  legal_data:{
    time_line:TimelineData[]
    chart_data:{
      data:{x:string, y:number}[]
    }
  }
  report_outline:Reportoutline
  report_type:string
  company_name:string
}

interface Params {
  [key: string]: string;
}
export interface url {
  url: string;
  id: number;
  title: string;
  favicon?: string | null;
  image?: string | null;
  text?: string | null;
  summary?: string;
  publish_time?: string;
  brief_summary?: string;
  credibility_score?: number;
  importance_score?: number;
  average_score?: number;
  relevance_score?: number;
}

export interface TimelineData {
  time: string;
  content: string;
}