<template>
  <div class="w-full h-full" :class="$style['slide-content-wrapper']">
    <div class="w-full h-full flex flex-col justify-center items-center">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits([
  'toggle-fullscreen',
  'toggle-chart-swiper',
  'refresh',
  'mouseenter',
  'mouseleave',
])

const handleContentMouseEnter = () => {
  emit('mouseenter')
}

const handleContentMouseLeave = () => {
  emit('mouseleave')
}
</script>

<style lang="scss" module>
.slideContentWrapper {
}
</style>

