<!-- Generated by SpecStory -->

# 添加轮播图控制器的实现 (2025-03-05 11:11:49)

_**User**_

能否在图中我标注的位置上，添加上一个轮播图的控制器，类似于上下箭头，可以供用户点击翻页

---

_**Assistant**_

```thinking
我需要理解用户的需求，他想在标注的位置添加轮播图控制器（上下箭头）。

首先，让我分析一下当前的代码和用户需求：

1. 用户想在图中标注的位置添加轮播图控制器，类似于上下箭头，用于用户点击翻页
2. 从代码中可以看到，页面已经使用了Swiper组件来实现轮播功能
3. 当前的轮播图是垂直方向的（direction="vertical"）
4. 目前已经有分页器（pagination），但用户想要添加明确的上下箭头控制器

我需要在Swiper组件中添加导航按钮（navigation）功能。Swiper提供了内置的导航按钮支持，但需要进行一些自定义来实现垂直方向的上下箭头。

具体需要做的修改：
1. 在Swiper组件中添加navigation配置
2. 导入Navigation模块
3. 添加自定义的上下箭头样式
4. 确保箭头位于用户标注的位置

让我来编写这个修改：
```

# 添加轮播图控制器（上下箭头）

我将在您标注的位置添加轮播图的上下箭头控制器，方便用户手动翻页。需要对Swiper组件进行一些修改，添加导航按钮功能。

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<div :class="$style['swiper-container']" v-loading="isStandardReportsLoading">
  <div :class="$style['swiper-navigation']">
    <button :class="[$style['swiper-button'], $style['swiper-button-prev']]" @click="handlePrevSlide">
      <Icon icon="mdi:chevron-up" class="w-5 h-5" />
    </button>
    <button :class="[$style['swiper-button'], $style['swiper-button-next']]" @click="handleNextSlide">
      <Icon icon="mdi:chevron-down" class="w-5 h-5" />
    </button>
  </div>

  <Swiper
    :modules="swiperModules"
    :direction="'vertical'"
    :pagination="{
      clickable: true
    }"
    class="w-full vertical-swiper"
    :style="{ height: '220px' }"
    @swiper="onSwiperInit">
    <!-- ... existing code ... -->
  </Swiper>
</div>
// ... existing code ...

// 在script setup部分添加以下内容
// ... existing code ...
import { Pagination, Navigation } from 'swiper/modules'
// ... existing code ...

// 修改swiperModules变量
const swiperModules = [Pagination, Navigation]
// ... existing code ...

// 添加处理上下翻页的方法
const handlePrevSlide = () => {
  if (swiperInstance.value) {
    swiperInstance.value.slidePrev()
  }
}

const handleNextSlide = () => {
  if (swiperInstance.value) {
    swiperInstance.value.slideNext()
  }
}
// ... existing code ...
```

然后添加相应的CSS样式：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<style lang="scss" module>
// ... existing code ...
.swiper-container {
  width: 100%;
  height: auto; /* 修改为自适应高度 */
  min-height: 220px; /* 保持最小高度 */
  position: relative;
  
  @media (max-width: 768px) {
    min-height: 220px;
    padding: 8px;
  }
}

.swiper-navigation {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.swiper-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--brand-color-3);
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--brand-color-3);
    color: white;
  }
  
  &:focus {
    outline: none;
  }
}

.swiper-button-prev {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.swiper-button-next {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
// ... existing code ...
</style>
```

这个修改将在轮播图右侧中间位置添加上下箭头控制器，用户可以通过点击这些按钮来手动翻页浏览轮播内容。按钮样式采用了圆形设计，悬停时会有颜色变化效果，与整体UI风格保持一致。

---

_**User**_

目前两个控制的大小太小了，不够明显

---

_**Assistant**_



```thinking
我需要理解用户的反馈，他认为我之前添加的轮播图控制器（上下箭头）太小了，不够明显。

从用户提供的截图中，我可以看到控制器确实比较小，位于右侧标记的区域内。用户希望这些控制器更加明显，更容易被用户注意到和点击。

我需要修改的内容：
1. 增加控制器按钮的尺寸
2. 可能需要调整按钮的位置，使其更加居中或者更加明显
3. 可能需要增强按钮的视觉效果，比如更明显的阴影、边框或背景色

让我来修改CSS样式，使控制器按钮更大更明显：
```

# 增大轮播图控制器尺寸

我将修改控制器按钮的样式，使其更大更明显，便于用户操作。

```vue:src/pages/dashboard/index.vue
// ... existing code ...
.swiper-navigation {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 10;
}

.swiper-button {
  width: 40px;  /* 增大按钮尺寸 */
  height: 40px; /* 增大按钮尺寸 */
  border-radius: 50%;
  background-color: white;
  border: 2px solid var(--brand-color-3); /* 加粗边框 */
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  
  &:hover {
    background-color: var(--brand-color-3);
    color: white;
    transform: scale(1.05); /* 悬停时轻微放大效果 */
  }
  
  &:focus {
    outline: none;
  }
  
  /* 增大图标尺寸 */
  :global(.iconify) {
    width: 24px !important;
    height: 24px !important;
  }
}

.swiper-button-prev {
  margin-bottom: 4px;
}

.swiper-button-next {
  margin-top: 4px;
}
// ... existing code ...
```

同时，我们需要修改按钮中的图标尺寸：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<div :class="$style['swiper-navigation']">
  <button :class="[$style['swiper-button'], $style['swiper-button-prev']]" @click="handlePrevSlide">
    <Icon icon="mdi:chevron-up" class="w-6 h-6" /> <!-- 增大图标尺寸 -->
  </button>
  <button :class="[$style['swiper-button'], $style['swiper-button-next']]" @click="handleNextSlide">
    <Icon icon="mdi:chevron-down" class="w-6 h-6" /> <!-- 增大图标尺寸 -->
  </button>
</div>
// ... existing code ...
```

这些修改将使轮播图控制器按钮更大、更明显，具体变化包括：
1. 按钮尺寸从28px增加到40px
2. 边框加粗为2px
3. 增强阴影效果，使按钮更加突出
4. 图标尺寸增大
5. 添加悬停时的轻微放大效果，提升交互体验

这样用户就能更容易看到并点击这些控制按钮了。

---

_**User**_

能否将两个按钮的位置放在我框定的底部两个红框中

---

_**Assistant**_



```thinking
用户希望我将轮播图控制按钮的位置移动到他在图片中标记的底部两个红框位置，而不是当前在右侧中间的位置。

从图片来看，用户希望将控制按钮分别放在轮播图底部的左侧和右侧位置。这需要我修改以下内容：

1. 修改 `.swiper-navigation` 的位置样式，将其从右侧中间移动到底部
2. 将按钮的排列方式从垂直排列（flex-direction: column）改为水平排列（flex-direction: row）
3. 调整按钮的位置，使左按钮在左侧红框位置，右按钮在右侧红框位置
4. 可能需要调整按钮的图标，从上下箭头改为左右箭头，以符合水平布局

让我来修改代码：
```

# 移动轮播图控制按钮到底部两侧

我将把轮播控制按钮移动到您指定的底部两个红框位置，并调整相应的样式。

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<div :class="$style['swiper-container']" v-loading="isStandardReportsLoading">
  <Swiper
    :modules="swiperModules"
    :direction="'vertical'"
    :pagination="{
      clickable: true
    }"
    class="w-full vertical-swiper"
    :style="{ height: '220px' }"
    @swiper="onSwiperInit">
    <!-- ... existing swiper content ... -->
  </Swiper>
  
  <!-- 移动导航按钮到底部 -->
  <div :class="$style['swiper-navigation']">
    <button :class="[$style['swiper-button'], $style['swiper-button-prev']]" @click="handlePrevSlide">
      <Icon icon="mdi:chevron-up" class="w-6 h-6" />
    </button>
    <button :class="[$style['swiper-button'], $style['swiper-button-next']]" @click="handleNextSlide">
      <Icon icon="mdi:chevron-down" class="w-6 h-6" />
    </button>
  </div>
</div>
// ... existing code ...
```

然后修改CSS样式：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
.swiper-navigation {
  position: absolute;
  bottom: 10px; /* 移动到底部 */
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  justify-content: space-between; /* 左右两侧分布 */
  padding: 0 20px; /* 左右边距 */
  z-index: 10;
}

.swiper-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid var(--brand-color-3);
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  
  &:hover {
    background-color: var(--brand-color-3);
    color: white;
    transform: scale(1.05);
  }
  
  &:focus {
    outline: none;
  }
  
  :global(.iconify) {
    width: 24px !important;
    height: 24px !important;
  }
}

/* 移除不需要的上下边距 */
.swiper-button-prev, .swiper-button-next {
  margin: 0;
}
// ... existing code ...
```

这样修改后，两个控制按钮将会分别位于轮播图底部的左侧和右侧，对应您标记的两个红框位置。按钮仍然保持原来的样式和大小，只是位置发生了变化。

您是否需要我将上下箭头图标也改为左右箭头图标，以更符合水平布局的直觉？

---

_**User**_

请你基于当前 @index.vue 的代码，给图中红框位置添加上当前轮播图的current/page的提示

---

_**Assistant**_





```thinking
用户希望我在轮播图底部红框位置添加当前页码/总页数的提示，类似于"当前页/总页数"的显示。

我需要做以下修改：

1. 在轮播图底部红框位置添加一个显示当前页码和总页数的元素
2. 添加状态变量来跟踪当前页码和总页数
3. 在Swiper的事件中更新这些状态变量
4. 设计适当的样式使其与整体UI风格一致

让我来实现这些修改：

首先，我需要添加状态变量来跟踪当前页码和总页数：
- 当前页码：currentSlideIndex
- 总页数：totalSlides

然后，我需要在Swiper的事件中更新这些状态变量，比如在slideChange事件中。

最后，在底部红框位置添加显示这些信息的元素。

让我开始编写代码：
```


# 添加轮播图页码指示器

我将在轮播图底部红框位置添加当前页/总页数的指示器，让用户清楚知道当前浏览的位置。

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<div :class="$style['swiper-container']" v-loading="isStandardReportsLoading">
  <Swiper
    :modules="swiperModules"
    :direction="'vertical'"
    :pagination="{
      clickable: true
    }"
    class="w-full vertical-swiper"
    :style="{ height: '220px' }"
    @swiper="onSwiperInit"
    @slideChange="onSlideChange">
    <!-- ... existing swiper content ... -->
  </Swiper>
  
  <!-- 移动导航按钮到底部 -->
  <div :class="$style['swiper-navigation']">
    <button :class="[$style['swiper-button'], $style['swiper-button-prev']]" @click="handlePrevSlide">
      <Icon icon="mdi:chevron-up" class="w-6 h-6"/>
    </button>
    
    <!-- 添加页码指示器 -->
    <div :class="$style['pagination-indicator']">
      {{ currentSlideIndex }} / {{ totalSlides }}
    </div>
    
    <button :class="[$style['swiper-button'], $style['swiper-button-next']]" @click="handleNextSlide">
      <Icon icon="mdi:chevron-down" class="w-6 h-6"/>
    </button>
  </div>
</div>
// ... existing code ...

// 在script setup部分添加以下内容
// ... existing code ...
const currentSlideIndex = ref(1)
const totalSlides = ref(0)

const onSwiperInit = (swiper: SwiperType) => {
  swiperInstance.value = swiper
  
  // 初始化总页数
  totalSlides.value = swiper.slides.length
  
  // 添加额外的事件监听
  swiper.on('slideChangeTransitionStart', () => {
    // 可以在这里添加幻灯片切换开始时的动画效果
  })
  
  swiper.on('slideChangeTransitionEnd', () => {
    // 可以在这里添加幻灯片切换结束时的动画效果
  })
}

const onSlideChange = (swiper: SwiperType) => {
  // 更新当前页码（Swiper的activeIndex是从0开始的，所以+1）
  currentSlideIndex.value = swiper.activeIndex + 1
}
// ... existing code ...
```


然后添加相应的CSS样式：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<style lang="scss" module>
// ... existing code ...
.swiper-navigation {
  position: absolute;
  bottom: 10px; /* 移动到底部 */
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  justify-content: space-between; /* 左右两侧分布 */
  padding: 0 20px; /* 左右边距 */
  z-index: 10;
}

.pagination-indicator {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 500;
  color: var(--brand-color-1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--brand-color-3);
  display: flex;
  align-items: center;
  justify-content: center;
}

// ... existing code ...
</style>
```


这个修改将在轮播图底部中间位置添加一个页码指示器，显示当前页码和总页数（例如"1 / 5"）。指示器的样式与整体UI风格保持一致，使用了圆角矩形设计，带有轻微的阴影效果。

当用户浏览轮播图时，页码指示器会自动更新，显示当前正在查看的页码。这样用户就能清楚地知道自己在轮播内容中的位置。

---

_**User**_

pagination-indicator样式不需要那么夸张的样式，只需要简单，清晰即可，并且要根据当前页码显示上下翻页按钮的disabled以及可用的状态，比如说如果在第一页那么向上翻页的按钮就无法点击使用，最后一页，那么向下翻页的按钮就不能点击使用，并且样式需要提示

---

_**Assistant**_





```thinking
用户希望我简化pagination-indicator的样式，并且添加按钮的禁用状态逻辑。具体需要做以下修改：

1. 简化pagination-indicator的样式，使其更简洁清晰
2. 添加逻辑：当在第一页时，向上翻页按钮应该禁用
3. 添加逻辑：当在最后一页时，向下翻页按钮应该禁用
4. 为禁用状态的按钮添加相应的样式，以提示用户

我需要修改以下内容：
1. 更新pagination-indicator的CSS样式
2. 添加计算属性来判断是否是第一页或最后一页
3. 根据计算属性添加按钮的disabled属性
4. 添加禁用状态的样式

让我开始编写代码：
```


# 简化页码指示器并添加按钮禁用状态

我将简化页码指示器的样式，并添加按钮的禁用状态逻辑，使其更符合用户体验。

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<div :class="$style['swiper-navigation']">
  <button 
    :class="[
      $style['swiper-button'], 
      $style['swiper-button-prev'],
      { [$style['swiper-button-disabled']]: isFirstSlide }
    ]" 
    @click="handlePrevSlide"
    :disabled="isFirstSlide">
    <Icon icon="mdi:chevron-up" class="w-6 h-6"/>
  </button>
  
  <!-- 简化的页码指示器 -->
  <div :class="$style['pagination-indicator']">
    {{ currentSlideIndex }} / {{ totalSlides }}
  </div>
  
  <button 
    :class="[
      $style['swiper-button'], 
      $style['swiper-button-next'],
      { [$style['swiper-button-disabled']]: isLastSlide }
    ]" 
    @click="handleNextSlide"
    :disabled="isLastSlide">
    <Icon icon="mdi:chevron-down" class="w-6 h-6"/>
  </button>
</div>
// ... existing code ...

// 在script setup部分添加以下内容
// ... existing code ...
// 添加计算属性判断是否是第一页或最后一页
const isFirstSlide = computed(() => currentSlideIndex.value === 1)
const isLastSlide = computed(() => currentSlideIndex.value === totalSlides.value)

// 修改handlePrevSlide和handleNextSlide方法，添加禁用状态检查
const handlePrevSlide = () => {
  if (swiperInstance.value && !isFirstSlide.value) {
    swiperInstance.value.slidePrev()
  }
}

const handleNextSlide = () => {
  if (swiperInstance.value && !isLastSlide.value) {
    swiperInstance.value.slideNext()
  }
}
// ... existing code ...
```


然后更新CSS样式：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<style lang="scss" module>
// ... existing code ...
.pagination-indicator {
  font-size: 14px;
  font-weight: 500;
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--brand-color-3);
  border: 2px solid var(--brand-color-3);
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  
  &:hover {
    background-color: var(--brand-color-3);
    color: white;
    transform: scale(1.05);
  }
  
  &:focus {
    outline: none;
  }
  
  :global(.iconify) {
    width: 24px !important;
    height: 24px !important;
  }
}

/* 添加禁用状态样式 */
.swiper-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  color: #a0a0a0;
  box-shadow: none;
  
  &:hover {
    background-color: #e0e0e0;
    color: #a0a0a0;
    transform: none;
  }
}
// ... existing code ...
</style>
```


这些修改将：

1. 简化页码指示器的样式，移除了背景色、边框和阴影，使其更加简洁
2. 添加了按钮的禁用状态逻辑：
   - 当在第一页时，向上翻页按钮被禁用
   - 当在最后一页时，向下翻页按钮被禁用
3. 为禁用状态的按钮添加了明显的视觉提示：
   - 降低透明度
   - 改变背景色和边框色为灰色
   - 改变文字颜色为浅灰色
   - 移除阴影效果
   - 鼠标指针变为"not-allowed"
   - 禁用悬停效果

这样用户就能清楚地知道当前的翻页状态，以及哪些操作是可用的。

---

_**User**_

@index.vue 请你把这个vue文件的各个组成部分拆分逻辑，目前整个vue文件已经达到1000行的代码量了，维护起来很困难，请你将逻辑拆分为组件到 @DashboardComponents 中，index.vue作为主组件

---

_**Assistant**_







```thinking
用户希望我将index.vue文件拆分成多个组件，以便于维护。这个文件已经达到了1000行代码，确实需要拆分。我需要：

1. 分析当前index.vue的结构和功能
2. 确定可以拆分的逻辑组件
3. 设计新的组件结构
4. 提供拆分后的代码

从之前看到的代码，index.vue主要包含以下部分：
- 已完成工作部分（Completed Work Section）
- 固定代理部分（Fixed Agents Section）
- 可滚动代理部分（Scrollable Agents Section）
- 性能部分（Performance Section）
- 任务列表部分（Task List Container）

可以拆分为以下组件：
1. CompletedWorkSection.vue - 处理已完成工作部分
2. FixedAgentsSection.vue - 处理固定代理部分
3. ScrollableAgentsSection.vue - 处理可滚动代理部分，包含轮播图和分页
4. PerformanceSection.vue - 处理性能部分
5. TaskListContainer.vue - 处理任务列表部分

让我开始设计这些组件：
```



# 将Dashboard组件拆分为多个小组件

我将把dashboard/index.vue拆分成多个组件，以提高代码的可维护性。以下是拆分方案：

## 1. 主组件 (index.vue)

```vue:src/pages/dashboard/index.vue
<template>
  <div :class="$style['dashboard']">
    <!-- 已完成工作部分 -->
    <div :class="$style['main-column']">
      <CompletedWorkSection 
        :agents="displayAgents"
        :fixed-agents="fixedAgents"
        :standard-reports="standardReports"
        :is-agent-list-loading="isAgentListLoading"
        :is-standard-reports-loading="isStandardReportsLoading"
        @agent-click="handleAgentClick"
        @refresh-agent="handleRefreshAgent"
        @tag-click="handleTagClick"
        @delete-agent="handleDeleteAgent" />
    </div>

    <!-- 性能和任务列表容器 -->
    <div :class="$style['performance-task-container']">
      <!-- 性能部分 -->
      <PerformanceSection 
        :agent-responses="agentResponses" />
      
      <!-- 任务列表部分 -->
      <TaskListContainer 
        :tasks="tasks"
        :is-loading="isTasksLoading"
        @refresh-tasks="handleRefreshTasks"
        @delete-task="handleDeleteTask" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router/auto'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { useAgentStore } from '@/stores/agent'
import { useTaskStore } from '@/stores/task'
import { formatCalendarDateTime } from '@/utils/filter'
import { useUserStore } from '@/stores/user'

// 导入拆分后的组件
import CompletedWorkSection from '@/pages/__components__/DashboardComponents/CompletedWorkSection.vue'
import PerformanceSection from '@/pages/__components__/DashboardComponents/PerformanceSection.vue'
import TaskListContainer from '@/pages/__components__/DashboardComponents/TaskListContainer.vue'

// 状态和存储
const agentStore = useAgentStore()
const taskStore = useTaskStore()
const userStore = useUserStore()
const router = useRouter()
const { t } = useI18n()

// 数据加载状态
const isAgentListLoading = ref(false)
const isStandardReportsLoading = ref(false)
const isTasksLoading = ref(false)

// 数据源
const displayAgents = computed(() => agentStore.displayAgents)
const fixedAgents = computed(() => agentStore.fixedAgents)
const standardReports = computed(() => agentStore.standardReports)
const agentResponses = computed(() => agentStore.agentResponses)
const tasks = computed(() => taskStore.tasks)

// 生命周期钩子
onMounted(async () => {
  await fetchAllData()
})

// 方法
const fetchAllData = async () => {
  isAgentListLoading.value = true
  isTasksLoading.value = true
  
  try {
    await Promise.all([
      agentStore.fetchAgents(),
      taskStore.fetchTasks()
    ])
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    ElMessage.error(t('common.fetch_error'))
  } finally {
    isAgentListLoading.value = false
    isTasksLoading.value = false
  }
}

// 事件处理方法
const handleAgentClick = (agent) => {
  router.push(`/agent/${agent.id}`)
}

const handleRefreshAgent = async (agent) => {
  try {
    await agentStore.refreshAgent(agent.id)
    ElMessage.success(t('agent.refresh_success'))
  } catch (error) {
    console.error('Error refreshing agent:', error)
    ElMessage.error(t('agent.refresh_error'))
  }
}

const handleTagClick = (tagData) => {
  const { agent, tag } = tagData
  router.push({
    path: `/agent/${agent.id}`,
    query: { company: tag }
  })
}

const handleDeleteAgent = async (agent) => {
  try {
    await ElMessageBox.confirm(
      t('agent.delete_confirm'),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )
    
    await agentStore.deleteAgent(agent.id)
    ElMessage.success(t('agent.delete_success'))
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting agent:', error)
      ElMessage.error(t('agent.delete_error'))
    }
  }
}

const handleRefreshTasks = async () => {
  isTasksLoading.value = true
  try {
    await taskStore.fetchTasks()
    ElMessage.success(t('task.refresh_success'))
  } catch (error) {
    console.error('Error refreshing tasks:', error)
    ElMessage.error(t('task.refresh_error'))
  } finally {
    isTasksLoading.value = false
  }
}

const handleDeleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      t('task.delete_confirm'),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )
    
    await taskStore.deleteTask(task.id)
    ElMessage.success(t('task.delete_success'))
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting task:', error)
      ElMessage.error(t('task.delete_error'))
    }
  }
}
</script>

<style lang="scss" module>
.dashboard {
  display: flex;
  gap: 24px;
  height: 100%;
  
  @media (max-width: 1200px) {
    flex-direction: column;
  }
}

.main-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.performance-task-container {
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  
  @media (max-width: 1200px) {
    width: 100%;
  }
}
</style>
```

## 2. 已完成工作部分组件

```vue:src/pages/__components__/DashboardComponents/CompletedWorkSection.vue
<template>
  <div>
    <div :class="$style['section-header']">
      <span>{{ $t("agent.completed_agents") }}</span>
    </div>

    <div :class="$style['agents-grid']" v-loading="isAgentListLoading">
      <template v-if="agents.length">
        <!-- 固定代理部分 -->
        <FixedAgentsSection 
          v-show="fixedAgents.length > 0"
          :fixed-agents="fixedAgents"
          @click-agent="handleAgentClick"
          @refresh-agent="handleRefreshAgent"
          @tag-click="handleTagClick"
          @delete-agent="handleDeleteAgent" />

        <!-- 可滚动代理部分 -->
        <ScrollableAgentsSection 
          v-show="(standardReports?.length || 0) > 0"
          :standard-reports="standardReports"
          :is-loading="isStandardReportsLoading"
          @agent-click="handleAgentClick" />
      </template>
      <div v-else class="w-[400px] flex-shrink-0">
        <span>{{ $t("agent.no_completed_agent") }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import FixedAgentsSection from './FixedAgentsSection.vue'
import ScrollableAgentsSection from './ScrollableAgentsSection.vue'

const props = defineProps({
  agents: {
    type: Array,
    required: true
  },
  fixedAgents: {
    type: Array,
    required: true
  },
  standardReports: {
    type: Array,
    required: true
  },
  isAgentListLoading: {
    type: Boolean,
    default: false
  },
  isStandardReportsLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'agent-click',
  'refresh-agent',
  'tag-click',
  'delete-agent'
])

const handleAgentClick = (agent) => {
  emit('agent-click', agent)
}

const handleRefreshAgent = (agent) => {
  emit('refresh-agent', agent)
}

const handleTagClick = (tagData) => {
  emit('tag-click', tagData)
}

const handleDeleteAgent = (agent) => {
  emit('delete-agent', agent)
}
</script>

<style lang="scss" module>
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  span {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.agents-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
</style>
```

## 3. 固定代理部分组件

```vue:src/pages/__components__/DashboardComponents/FixedAgentsSection.vue
<template>
  <div :class="$style['fixed-agents-container']">
    <div :class="$style['agent-section-header']">
      <Icon icon="mdi:star" class="text-yellow-500 w-5 h-5"/>
      <span>{{ $t("agent.special_agents") }}</span>
      <el-tooltip :content="$t('agent.special_agents_tooltip')" placement="top">
        <Icon icon="mdi:help-circle-outline" class="text-gray-400 w-4 h-4 cursor-help"/>
      </el-tooltip>
    </div>
    <div :class="$style['fixed-agents']">
      <SpecialAgentCard
        v-for="agent in fixedAgents"
        :key="agent.id"
        :agent="agent"
        @clickAgent="handleAgentClick"
        @refreshAgent="handleRefreshAgent"
        @tagClick="handleTagClick"
        @deleteAgent="handleDeleteAgent"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { Icon } from '@iconify/vue'
import SpecialAgentCard from './SpecialAgentCard.vue'

const props = defineProps({
  fixedAgents: {
    type: Array,
    required: true
  }
})

const emit = defineEmits([
  'click-agent',
  'refresh-agent',
  'tag-click',
  'delete-agent'
])

const handleAgentClick = (agent) => {
  emit('click-agent', agent)
}

const handleRefreshAgent = (agent) => {
  emit('refresh-agent', agent)
}

const handleTagClick = (tagData) => {
  emit('tag-click', tagData)
}

const handleDeleteAgent = (agent) => {
  emit('delete-agent', agent)
}
</script>

<style lang="scss" module>
.fixed-agents-container {
  margin-bottom: 24px;
}

.agent-section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  
  span {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.fixed-agents {
  display: flex;
  flex-wrap: nowrap;
  gap: 16px;
  overflow-x: auto;
  padding-bottom: 8px;
  
  &::-webkit-scrollbar {
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #ccc;
  }
}
</style>
```

## 4. 可滚动代理部分组件

```vue:src/pages/__components__/DashboardComponents/ScrollableAgentsSection.vue
<template>
  <div :class="$style['scrollable-agents-container']">
    <div :class="$style['agent-section-header']" class="justify-between">
      <div class="flex items-center gap-2">
        <Icon icon="mdi:format-list-text" class="text-blue-500 w-5 h-5"/>
        <span>{{ $t("agent.regular_agents") }}</span>
        <el-tooltip :content="$t('agent.regular_agents_tooltip')" placement="top">
          <Icon icon="mdi:help-circle-outline" class="text-gray-400 w-4 h-4 cursor-help"/>
        </el-tooltip>
      </div>
      <div class="flex items-center gap-3">
        <router-link 
          to="/format-report" 
          class="flex items-center gap-2 text-sm text-main-color hover:underline">
          <span>{{ $t("agent.view_all_reports") }}</span>
          <Icon icon="mdi:arrow-right" class="w-4 h-4"/>
        </router-link>
      </div>
    </div>
    <div :class="$style['swiper-container']" v-loading="isLoading">
      <Swiper
        :modules="swiperModules"
        :direction="'vertical'"
        :pagination="{
          clickable: true
        }"
        class="w-full vertical-swiper"
        :style="{ height: '220px' }"
        @swiper="onSwiperInit"
        @slideChange="onSlideChange">
        <SwiperSlide 
          v-for="agent in standardReports" 
          :key="agent.id"
          class="swiper-slide-card">
          <AgentCompletedCard
            :key="agent.id"
            class="w-full cursor-pointer overflow-hidden"
            v-bind="mapAgentData(agent)"
            @click="handleAgentClick(mapAgentData(agent))">
            <template #duty>
              <div class="flex items-start gap-2 flex-col">
                <span class="text-main-color text-sm">{{ agent.name }}</span>
                <div v-if="agent.updated_at">
                  <span class="text-gray-500 text-xs">{{ $t("agent.updated_at") }}: </span>
                  <span class="text-main-color text-sm">{{
                    formatCalendarDateTime(dayjs(agent.updated_at).valueOf())
                  }}</span>
                </div>
              </div>
            </template>
            <template #summary>
              <div
                class="text-main-color text-sm text-start w-full overflow-hidden mt-2">
                <OverClampText
                  :content="agent.summary"
                  :line-clamp="3"
                  class="leading-6"/>
              </div>
            </template>
          </AgentCompletedCard>
        </SwiperSlide>
      </Swiper>
      
      <!-- 导航按钮 -->
      <div :class="$style['swiper-navigation']">
        <button 
          :class="[
            $style['swiper-button'], 
            $style['swiper-button-prev'],
            { [$style['swiper-button-disabled']]: isFirstSlide }
          ]" 
          @click="handlePrevSlide"
          :disabled="isFirstSlide">
          <Icon icon="mdi:chevron-up" class="w-6 h-6"/>
        </button>
        
        <!-- 页码指示器 -->
        <div :class="$style['pagination-indicator']">
          {{ currentSlideIndex }} / {{ totalSlides }}
        </div>
        
        <button 
          :class="[
            $style['swiper-button'], 
            $style['swiper-button-next'],
            { [$style['swiper-button-disabled']]: isLastSlide }
          ]" 
          @click="handleNextSlide"
          :disabled="isLastSlide">
          <Icon icon="mdi:chevron-down" class="w-6 h-6"/>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import { Icon } from '@iconify/vue'
import { AgentCompletedCard, OverClampText } from '@specificlab/ui'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Navigation } from 'swiper/modules'
import dayjs from 'dayjs'
import { formatCalendarDateTime } from '@/utils/filter'
import type { SwiperType } from '@/types'

// 导入Swiper样式
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

const props = defineProps({
  standardReports: {
    type: Array,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['agent-click'])

// Swiper相关
const swiperModules = [Pagination, Navigation]
const swiperInstance = ref(null)
const currentSlideIndex = ref(1)
const totalSlides = ref(0)

// 计算属性
const isFirstSlide = computed(() => currentSlideIndex.value === 1)
const isLastSlide = computed(() => currentSlideIndex.value === totalSlides.value)

// 方法
const mapAgentData = (agent) => {
  return {
    id: agent.id,
    name: agent.name,
    duty: agent.duty,
    avatar: agent.avatar,
    summary: agent.summary,
    updated_at: agent.updated_at
  }
}

const onSwiperInit = (swiper) => {
  swiperInstance.value = swiper
  totalSlides.value = swiper.slides.length
}

const onSlideChange = (swiper) => {
  currentSlideIndex.value = swiper.activeIndex + 1
}

const handlePrevSlide = () => {
  if (swiperInstance.value && !isFirstSlide.value) {
    swiperInstance.value.slidePrev()
  }
}

const handleNextSlide = () => {
  if (swiperInstance.value && !isLastSlide.value) {
    swiperInstance.value.slideNext()
  }
}

const handleAgentClick = (agent) => {
  emit('agent-click', agent)
}
</script>

<style lang="scss" module>
.scrollable-agents-container {
  position: relative;
}

.agent-section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  span {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.swiper-container {
  position: relative;
  height: 220px;
  border-radius: 8px;
  overflow: hidden;
  
  :global(.vertical-swiper) {
    height: 100%;
  }
  
  :global(.swiper-slide-card) {
    height: auto;
    padding: 0 4px;
  }
}

.swiper-navigation {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.pagination-indicator {
  font-size: 14px;
  font-weight: 500;
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--brand-color-3);
  border: 2px solid var(--brand-color-3);
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  
  &:hover {
    background-color: var(--brand-color-3);
    color: white;
    transform: scale(1.05);
  }
  
  &:focus {
    outline: none;
  }
}

.swiper-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  color: #a0a0a0;
  box-shadow: none;
  
  &:hover {
    background-color: #e0e0e0;
    color: #a0a0a0;
    transform: none;
  }
}
</style>
```

## 5. 性能部分组件

```vue:src/pages/__components__/DashboardComponents/PerformanceSection.vue
<template>
  <div :class="$style['performance-section']">
    <div :class="$style['section-header']">
      <span>{{ $t("agent.agent_performance") }}</span>
    </div>

    <template v-if="agentResponses.length">
      <AgentsResponseCard
        v-for="response in agentResponses"
        :key="response.name"
        v-memo="[response.name, response.duty]"
        v-bind="response">
        <template #duty>
          <div class="flex items-start gap-2 flex-col">
            <span class="text-main-color text-sm">{{
              response.report_name
            }}</span>
            <span class="text-gray-500 text-xs">{{ response.duty }}</span>
          </div>
        </template>
      </AgentsResponseCard>
    </template>
    <div v-else :class="$style['empty-state']">
      <span>{{ $t("agent.no_performance_data") }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import { AgentsResponseCard } from '@specificlab/ui'

const props = defineProps({
  agentResponses: {
    type: Array,
    required: true
  }
})
</script>

<style lang="scss" module>
.performance-section {
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  span {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>
```

## 6. 任务列表部分组件

```vue:src/pages/__components__/DashboardComponents/TaskListContainer.vue
<template>
  <div :class="$style['task-list-container']">
    <div :class="$style['section-header']">
      <span>{{ $t("task.task_list") }}</span>
      <div class="flex items-center gap-2">
        <el-button 
          size="small" 
          type="primary" 
          @click="handleRefresh"
          :loading="isLoading">
          {{ $t("common.refresh") }}
        </el-button>
      </div>
    </div>

    <div :class="$style['task-list']" v-loading="isLoading">
      <template v-if="tasks.length">
        <div 
          v-for="task in tasks" 
          :key="task.id"
          :class="$style['task-item']">
          <div :class="$style['task-content']">
            <div :class="$style['task-header']">
              <span :class="$style['task-name']">{{ task.name }}</span>
              <el-tag 
                :type="getTaskStatusType(task.status)" 
                size="small">
                {{ getTaskStatusText(task.status) }}
              </el-tag>
            </div>
            <div :class="$style['task-description']">
              {{ task.description }}
            </div>
            <div :class="$style['task-footer']">
              <span :class="$style['task-date']">
                {{ formatDate(task.created_at) }}
              </span>
              <el-button 
                type="danger" 
                size="small" 
                text
                @click="handleDelete(task)">
                {{ $t("common.delete") }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
      <div v-else :class="$style['empty-state']">
        <span>{{ $t("task.no_tasks") }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'

const props = defineProps({
  tasks: {
    type: Array,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh-tasks', 'delete-task'])
const { t } = useI18n()

// 方法
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const getTaskStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    running: 'primary',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status) => {
  return t(`task.status.${status}`)
}

const handleRefresh = () => {
  emit('refresh-tasks')
}

const handleDelete = (task) => {
  emit('delete-task', task)
}
</script>

<style lang="scss" module>
.task-list-container {
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  span {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.task-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {

---

_**User**_

继续完成整个拆分

---

_**Assistant**_







```thinking
我需要继续完成拆分工作。我注意到TaskListContainer.vue组件的代码被截断了，需要完成它。此外，我还需要创建一个SpecialAgentCard.vue组件，因为在FixedAgentsSection.vue中引用了这个组件。

让我继续完成这些组件：

1. 完成TaskListContainer.vue组件
2. 创建SpecialAgentCard.vue组件

我将按照之前的设计风格和模式继续完成这些组件。
```



# 继续完成组件拆分

## 1. 完成任务列表组件

```vue:src/pages/__components__/DashboardComponents/TaskListContainer.vue
<template>
  <div :class="$style['task-list-container']">
    <div :class="$style['section-header']">
      <span>{{ $t("task.task_list") }}</span>
      <div class="flex items-center gap-2">
        <el-button 
          size="small" 
          type="primary" 
          @click="handleRefresh"
          :loading="isLoading">
          {{ $t("common.refresh") }}
        </el-button>
      </div>
    </div>

    <div :class="$style['task-list']" v-loading="isLoading">
      <template v-if="tasks.length">
        <div 
          v-for="task in tasks" 
          :key="task.id"
          :class="$style['task-item']">
          <div :class="$style['task-content']">
            <div :class="$style['task-header']">
              <span :class="$style['task-name']">{{ task.name }}</span>
              <el-tag 
                :type="getTaskStatusType(task.status)" 
                size="small">
                {{ getTaskStatusText(task.status) }}
              </el-tag>
            </div>
            <div :class="$style['task-description']">
              {{ task.description }}
            </div>
            <div :class="$style['task-footer']">
              <span :class="$style['task-date']">
                {{ formatDate(task.created_at) }}
              </span>
              <el-button 
                type="danger" 
                size="small" 
                text
                @click="handleDelete(task)">
                {{ $t("common.delete") }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
      <div v-else :class="$style['empty-state']">
        <span>{{ $t("task.no_tasks") }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'

const props = defineProps({
  tasks: {
    type: Array,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh-tasks', 'delete-task'])
const { t } = useI18n()

// 方法
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const getTaskStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    running: 'primary',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status) => {
  return t(`task.status.${status}`)
}

const handleRefresh = () => {
  emit('refresh-tasks')
}

const handleDelete = (task) => {
  emit('delete-task', task)
}
</script>

<style lang="scss" module>
.task-list-container {
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  span {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.task-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #ccc;
  }
}

.task-item {
  background-color: var(--el-fill-color-light);
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.task-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.task-description {
  color: var(--el-text-color-secondary);
  font-size: 13px;
  line-height: 1.5;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.task-date {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>
```


## 2. 特殊代理卡片组件

```vue:src/pages/__components__/DashboardComponents/SpecialAgentCard.vue
<template>
  <div :class="$style['special-agent-card']">
    <div :class="$style['card-header']">
      <div :class="$style['agent-info']" @click="handleAgentClick">
        <div :class="$style['avatar-container']">
          <img 
            :src="agent.avatar || defaultAvatar" 
            :alt="agent.name"
            :class="$style['avatar']">
        </div>
        <div :class="$style['agent-details']">
          <div :class="$style['agent-name']">{{ agent.name }}</div>
          <div :class="$style['agent-duty']">{{ agent.duty }}</div>
        </div>
      </div>
      <div :class="$style['card-actions']">
        <el-tooltip :content="$t('agent.refresh')" placement="top">
          <el-button 
            type="primary" 
            circle 
            size="small"
            @click="handleRefreshAgent">
            <Icon icon="mdi:refresh" class="w-4 h-4"/>
          </el-button>
        </el-tooltip>
        <el-tooltip :content="$t('agent.delete')" placement="top">
          <el-button 
            type="danger" 
            circle 
            size="small"
            @click="handleDeleteAgent">
            <Icon icon="mdi:delete" class="w-4 h-4"/>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <div :class="$style['card-body']" @click="handleAgentClick">
      <div :class="$style['update-time']">
        <span>{{ $t("agent.updated_at") }}: </span>
        <span>{{ formatCalendarDateTime(dayjs(agent.updated_at).valueOf()) }}</span>
      </div>
      
      <div :class="$style['agent-summary']">
        <OverClampText
          :content="agent.summary"
          :line-clamp="3"
          class="leading-6"/>
      </div>
      
      <div :class="$style['agent-tags']" v-if="agent.tags && agent.tags.length">
        <el-tag 
          v-for="tag in agent.tags" 
          :key="tag"
          size="small"
          @click.stop="handleTagClick(tag)">
          {{ tag }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { Icon } from '@iconify/vue'
import { OverClampText } from '@specificlab/ui'
import dayjs from 'dayjs'
import { formatCalendarDateTime } from '@/utils/filter'

// 默认头像
const defaultAvatar = '/images/default-avatar.png'

const props = defineProps({
  agent: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'clickAgent',
  'refreshAgent',
  'tagClick',
  'deleteAgent'
])

// 方法
const handleAgentClick = () => {
  emit('clickAgent', props.agent)
}

const handleRefreshAgent = (event) => {
  event.stopPropagation()
  emit('refreshAgent', props.agent)
}

const handleTagClick = (tag) => {
  emit('tagClick', { agent: props.agent, tag })
}

const handleDeleteAgent = (event) => {
  event.stopPropagation()
  emit('deleteAgent', props.agent)
}
</script>

<style lang="scss" module>
.special-agent-card {
  width: 300px;
  min-width: 300px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.card-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid var(--el-border-color-light);
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.agent-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.agent-duty {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.update-time {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.agent-summary {
  color: var(--el-text-color-primary);
  font-size: 13px;
  line-height: 1.5;
}

.agent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  :global(.el-tag) {
    cursor: pointer;
  }
}
</style>
```


## 3. 类型定义文件

为了确保TypeScript类型正确，我们还需要创建一个类型定义文件：

```typescript:src/types/index.ts
// 如果文件已存在，请添加以下类型定义

// Swiper类型
export interface SwiperType {
  slides: any[];
  activeIndex: number;
  slidePrev: () => void;
  slideNext: () => void;
  on: (event: string, callback: () => void) => void;
}

// Agent类型
export interface Agent {
  id: string;
  name: string;
  duty: string;
  avatar?: string;
  summary: string;
  updated_at: string;
  tags?: string[];
}

// Task类型
export interface Task {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
}
```


这样，我们就完成了整个Dashboard页面的组件拆分。现在代码结构更加清晰，每个组件负责特定的功能，便于维护和扩展。主要组件包括：

1. `index.vue` - 主组件，负责整体布局和数据获取
2. `CompletedWorkSection.vue` - 已完成工作部分
3. `FixedAgentsSection.vue` - 固定代理部分
4. `ScrollableAgentsSection.vue` - 可滚动代理部分，包含轮播图和分页
5. `PerformanceSection.vue` - 性能部分
6. `TaskListContainer.vue` - 任务列表部分
7. `SpecialAgentCard.vue` - 特殊代理卡片组件

这种组件化的结构使得代码更易于理解和维护，每个组件都有明确的职责和边界。

---

