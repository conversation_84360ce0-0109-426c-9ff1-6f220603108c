<template>
  <AgentCompletedCard
    :key="agent.id"
    :class="[
      $style['special-agent-card'],
      { [$style['danger-card']]: agent.status === 'Danger' },
    ]"
    v-bind="agent"
    @click="handleNameClick">
    <template #name>
      <div class="flex flex-col gap-2 w-full">
        <div class="flex items-center gap-2 justify-between">
          <span
            class="text-md font-bold cursor-pointer hover:text-main-color">{{ agent.name }}</span>
          <div class="flex items-center gap-2">
            <Icon icon="mdi:timer-outline"
                  width="20"
                  height="20"
                  class="cursor-pointer hover:text-main-color"
                  @click="handleChangeTime($event)"/>
            <Icon icon="mdi:refresh"
                  width="20"
                  height="20"
                  class="cursor-pointer hover:text-main-color"
                  @click="handleRefresh($event)"/>
            <Icon icon="mdi:delete-outline"
                  width="20"
                  height="20"
                  class="cursor-pointer hover:text-red-500"
                  @click="handleDelete($event)"/>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <el-tag type="primary">{{
            $t("agent.will_run_every", { days: agent.frequency })
          }}</el-tag>
        </div>
      </div>
    </template>
    <template #duty>
      <div class="flex items-start gap-2 flex-col">
        <span class="text-main-color text-sm">{{ agent.duty }}</span>
        <div  v-if="agent.updated_at">
          <span class="text-gray-500 text-xs">{{ $t("agent.updated_at") }}: </span>
          <span class="text-main-color text-sm">{{
            formatCalendarDateTime(dayjs(agent.updated_at).valueOf())
          }}</span>
        </div>
      </div>
    </template>
    <template #summary>
      <div class="text-black text-sm text-start w-full mt-2">
        <template v-if="agent.summary && agent.summary.length > 0">
          <div class="flex flex-col">
            <div class="flex items-center justify-between mb-2">
              <span class="text-md font-bold text-main-color">{{ $t("agent.special_agent_summary") }}:</span>
              <span class="text-xs text-gray-500">{{ $t('agent.companies', { count: agent.summary.length }) }}</span>
            </div>
            
            <!-- 公司磁贴设计 -->
            <div :class="$style['companies-grid']">
              <div 
                v-for="(tag, index) in visibleTags" 
                :key="tag"
                :class="[$style['company-tile'], 'cursor-pointer']"
                :style="{ animationDelay: `${index * 0.05}s` }"
                @click="handleTagClick($event,agent, tag)">
                <span :class="$style['company-name']">{{ tag }}</span>
              </div>
              
              <!-- 显示更多磁贴 -->
              <div 
                v-if="agent.summary.length > visibleCount"
                :class="[$style['more-tile'], 'cursor-pointer']"
                @click="showMoreTags($event)">
                <span>+{{ agent.summary.length - visibleCount }}</span>
              </div>
            </div>
            
            <!-- 标签导航指示器 -->
            <div v-if="totalPages > 1" :class="$style['pagination']">
              <div 
                v-for="i in totalPages" 
                :key="i"
                :class="[
                  $style['page-dot'],
                  { [$style['active']]: currentPage === i - 1 }
                ]"
                @click="goToPage(i - 1)">
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <span class="text-md text-main-color">{{ $t("agent.special_agent_summary") }}:</span>
          <div class="flex justify-between text-main-color">
            <span>{{ $t("agent.no_summary") }}</span>
          </div>
        </template>
      </div>
    </template>
  </AgentCompletedCard>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { AgentCompletedCard } from '@specificlab/ui'
import { Icon } from '@iconify/vue'
import type { PropType } from 'vue'
import { formatCalendarDateTime } from '@/utils/filter'
import dayjs from 'dayjs'

interface Agent {
  id: string;
  name: string;
  duty: string;
  avatar?: string;
  summary: string[] | string;
  type?: string;
  updated_at: string;
  report_type?: string;
  status?: string;
  frequency: number;
}

const props = defineProps({
  agent: {
    type: Object as PropType<Agent>,
    required: true,
  }
})

const emit = defineEmits(['refreshAgent', 'clickAgent', 'tagClick', 'deleteAgent', 'changeTime'])

// 分页逻辑
const currentPage = ref(0)
const itemsPerPage = 2 // 每页显示6个磁贴

const totalPages = computed(() => {
  if (!Array.isArray(props.agent.summary)) {return 0}
  return Math.ceil(props.agent.summary.length / itemsPerPage)
})

const visibleTags = computed(() => {
  if (!Array.isArray(props.agent.summary)) {return []}
  const start = currentPage.value * itemsPerPage
  return props.agent.summary.slice(start, start + itemsPerPage)
})

const visibleCount = computed(() => visibleTags.value.length)

// 导航函数
const goToPage = (page: number) => {
  if (page >= 0 && page < totalPages.value) {
    currentPage.value = page
  }
}

const showMoreTags = (event: Event) => {
  event.stopPropagation()
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value++
  } else {
    currentPage.value = 0 // 循环回到第一页
  }
}

const handleNameClick = () => {
  emit('clickAgent', props.agent)
}

const handleRefresh = (event: Event) => {
  event.stopPropagation()
  emit('refreshAgent', props.agent)
}
const handleChangeTime = (event: Event) => {
  event.stopPropagation()
  emit('changeTime', props.agent)
}
const handleDelete = (event: Event) => {
  event.stopPropagation()
  emit('deleteAgent', props.agent)
}

const handleTagClick = (event: Event, agent: Agent, tag: string) => {
  event.stopPropagation()
  const tagData = {
    agent,
    tag
  }
  emit('tagClick', tagData)
}
</script>

<style lang="scss" module>
.special-agent-card {
  width: 100%;
  max-width: 400px;
  shrink: 0;
  margin-right: 12px;
  
  @media (max-width: 1366px) {
    max-width: 350px;
  }
  
  @media (max-width: 1200px) {
    max-width: 320px;
  }
  
  @media (max-width: 992px) {
    width: 100%;
    max-width: 400px;
    margin-right: 0;
  }
}

.danger-card {
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 59, 48, 0.1) 0%,
      rgba(255, 0, 0, 0.2) 100%
    );
    border-radius: inherit;
    animation: pulse 2s ease-in-out infinite;
    pointer-events: none;
  }
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

.company-tile {
  background-color: rgba(255, 99, 71, 0.08);
  border: 1px solid rgba(255, 99, 71, 0.2);
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 36px;
  transition: all 0.2s ease;
  animation: fadeIn 0.3s ease forwards;
  
  &:hover {
    background-color: rgba(255, 99, 71, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}

.company-name {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color:red;
}

.more-tile {
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px dashed rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 14px;
  color: #666;
  min-height: 36px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-top: 8px;
}

.page-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ddd;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.active {
    background-color: var(--el-color-danger);
    transform: scale(1.2);
  }
  
  &:hover {
    background-color: #ccc;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
</style>
