<template>
  <div :class="$style['disabled-agent-card']">
    <!-- 使用真实的SpecialAgentCard作为背景 -->
    <div :class="$style['card-background']">
      <div :class="$style['card-header']">
        <div class="flex items-center gap-2">
          <img :src="AVATAR_MAP.SocialAgent" class="w-8 h-8 rounded-full"/>
          <div class="flex flex-col">
            <span class="text-sm font-semibold">{{ $t('agent.risk_monitor_agent') }}</span>
            <span class="text-xs text-gray-500">{{ $t('agent.daily_task') }}</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <Icon icon="mdi:refresh" class="text-gray-400 w-5 h-5"/>
          <Icon icon="mdi:delete-outline" class="text-gray-400 w-5 h-5"/>
        </div>
      </div>
      
      <div :class="$style['card-content']">
        <div class="flex flex-col gap-2 mt-2">
          <div class="text-sm font-medium">{{ $t('agent.risk_monitor_description') }}</div>
          <div class="text-xs text-gray-500 line-clamp-3">
            {{ $t('agent.risk_monitor_summary') }}
          </div>
        </div>
      </div>
      
      <div :class="$style['card-footer']">
        <div class="flex items-center justify-between w-full">
          <div class="flex items-center gap-1">
            <Icon icon="mdi:clock-outline" class="text-gray-400 w-4 h-4"/>
            <span class="text-xs text-gray-500">{{ $t('agent.daily_update') }}</span>
          </div>
          <el-button size="small" type="primary" plain>
            {{ $t('common.view') }}
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 毛玻璃覆盖层 -->
    <div :class="$style['glass-overlay']">
      <!-- 提示内容 -->
      <div :class="$style['overlay-content']">
        <div :class="$style['icon-container']">
          <Icon icon="mdi:shield-lock-outline" class="w-10 h-10"/>
        </div>
        <h3 :class="$style['overlay-title']">{{ $t('agent.risk_monitor') }}</h3>
        <p :class="$style['overlay-description']">{{ $t('agent.enable_risk_monitor_tip') }}</p>
        <el-button 
          :class="$style['enable-button']"
          @click="$router.push('/agent-market')">
          <Icon icon="mdi:lightning-bolt" class="mr-1"/>
          {{ $t('agent.enable_now') }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import { AVATAR_MAP } from '@/composables/useReportProgress'

const { t } = useI18n()
</script>

<style lang="scss" module>
.disabled-agent-card {
  position: relative;
  width: 400px;
  height: 220px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    /* transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15); */
    
    .glass-overlay {
      backdrop-filter: blur(5px);
    }
  }
}

// 真实卡片背景样式
.card-background {
  position: absolute;
  inset: 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: white;
  border: 1px solid var(--el-border-color-light);
  border-radius: 16px;
  filter: grayscale(0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 12px 0;
}

.card-footer {
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

// 毛玻璃覆盖层样式
.glass-overlay {
  position: absolute;
  inset: 0;
  backdrop-filter: blur(3px);
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  z-index: 10;
}

.overlay-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  border-radius: 16px;
  max-width: 80%;
}

.icon-container {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    var(--el-color-primary-light-5), 
    var(--el-color-primary)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 4px;
  box-shadow: 0 8px 16px rgba(var(--el-color-primary-rgb), 0.3);
}

.overlay-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.overlay-description {
  font-size: 14px;
  line-height: 1.5;
  color: white;
  margin: 0 0 4px;
}

.enable-button {
  width: 80%;
  height: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, 
    var(--el-color-primary), 
    var(--el-color-primary-light-3)
  );
  border: none;
  color: white;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
  margin-top: 8px;

  /* &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.4);
    background: linear-gradient(135deg, 
      var(--el-color-primary-light-3), 
      var(--el-color-primary)
    );
  } */

  :global(.iconify) {
    font-size: 18px;
  }
}
</style> 