<!-- Generated by SpecStory -->

# 特殊处理报告类型逻辑讨论 (2025-02-22 19:04:52)

_**User**_

在 @useReportProgress.ts 的detailSteps的逻辑中，需要有一个特殊处理，数据结构如下：

{
  "type": "update",
  "data": {
    "id": "67b97217465bdfdf51e912de",
    "sync_type": false,
    "report_type": "FinancialPerformanceTrackingReport",
    "report_name": "兴森科技财务表现追踪报告",
    "status": "running",
    "progress": 30.0,
    "tools_results": [
      {
        "name":"江西志浩",
        "status":"COMPLETED"
      },
      {
        "name":"浙江志浩",
        "status":"COMPLETED"
      },
      {
        "name":"美国志浩",
        "status":"PENDING"
      }
    ],
    "tools_results_dict": {
      "江西志浩":"askdjfalksdfjlkasdjflkas",
      "浙江志浩":"askdjfalksdfjlkasdjflkas"
    }
  }
}


如果report_type为'SocialAgent', 'LegalAgent'的话，需要根据name去singleReportData.value.tools_results_dict查看是否有对应的name的result，如果有需要根据将value取出来放入special_summary中（只有'SocialAgent', 'LegalAgent'有这个逻辑）

---

_**Assistant**_

```thinking
Let me think through this request carefully...

First, I need to understand what's being asked:
- For report_type 'SocialAgent' or 'LegalAgent' only
- Need to check tools_results_dict for matching name
- If found, put the value into special_summary field
- This only applies to these two specific agent types
- The data structure shows tools_results_dict contains name:string pairs

Key considerations:
- Need to check if singleReportData.value exists first
- Need to check if report_type matches before doing lookup
- Need to safely access tools_results_dict which may not exist
- Should maintain existing detailSteps logic while adding this feature
- The special_summary should only be populated for matching cases

The main changes needed:
1. Add check for report_type being SocialAgent/LegalAgent
2. For matching types, look up each tool result name in dict
3. Add the found value to special_summary
4. Keep special_summary empty for non-matching cases

Let me write out the modified code...
```

Here's how we can modify the detailSteps computed property:

```typescript:src/composables/useReportProgress.ts
const detailSteps = computed(() => {
  if (!singleReportData.value) { return [] }

  // Find the last COMPLETED step index
  const tools = [...singleReportData.value.tools_results]
  const lastCompletedIndex = tools.map(t => t.status).lastIndexOf('COMPLETED')
  
  // If there's a next step after the last completed one, set it to PROCESSING
  if (lastCompletedIndex !== -1 && lastCompletedIndex + 1 < tools.length) {
    tools[lastCompletedIndex + 1].status = 'PROCESSING'
  }
  // If no completed steps and there are tools, set first to PROCESSING
  else if (lastCompletedIndex === -1 && tools.length > 0) {
    tools[0].status = 'PROCESSING'
  }

  return tools.map((tool, index) => {
    // Check if this is a special agent type and get summary if available
    let special_summary = ''
    if (['SocialAgent', 'LegalAgent'].includes(singleReportData.value?.report_type)) {
      special_summary = singleReportData.value?.tools_results_dict?.[tool.name] || ''
    }

    return {
      number: index + 1,
      title: i18nInstance.global.t(`tools.${tool.name}`),
      status: tool.status,
      content: {
        type: 'progress',
        title: i18nInstance.global.t(`tools.${tool.name}`),
        description: i18nInstance.global.t(`tools.${tool.name}Desc`),
        data: {
          progress: tool.status === 'COMPLETED' ? 100 : 
            tool.status === 'PROCESSING' ? 50 : 0,
        }
      },
      special_summary
    }
  })
})
```

The changes made:
1. Added a check for special agent types using array.includes()
2. Added lookup in tools_results_dict using optional chaining
3. Set special_summary to empty string by default
4. Added the special_summary to the returned object

This implementation:
- Only looks up summaries for SocialAgent/LegalAgent types
- Safely handles missing data using optional chaining
- Maintains all existing functionality
- Adds the special summary where appropriate

English: The code has been updated to handle special summaries for SocialAgent and LegalAgent report types. When these types are encountered, it looks up each tool's name in the tools_results_dict and includes any found summary in the output.

中文: 代码已更新，可以处理 SocialAgent 和 LegalAgent 报告类型的特殊摘要。当遇到这些类型时，它会在 tools_results_dict 中查找每个工具的名称，并在输出中包含找到的摘要。

---

