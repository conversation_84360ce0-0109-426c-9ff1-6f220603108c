import { omit } from 'lodash-es'
import { ElTooltip } from 'element-plus'
import { getCurrentInstance, h, render, type ComponentInternalInstance, type VNode } from 'vue'

// 使用 InstanceType 获取 ElTooltip 组件实例的类型
type ElTooltipInstance = InstanceType<typeof ElTooltip>
// 从组件实例中提取 Props 类型
const excludeProps = ['virtualTriggering', 'virtualRef', 'content', 'visible'] as const
export type TooltipProps = Omit<ElTooltipInstance['$props'], typeof excludeProps[number]>
export type TooltipContent = VNode | string
let _instance: ComponentInternalInstance | null = null
let fragment: Element | null = null

const removeTooltip = () => {
  if (_instance) {
    _instance.props.visible = false
  }
  if (fragment) {
    render(null, fragment)
    fragment.textContent = null
    fragment = null
  }
  _instance = null
}

const createTooltip = (content: TooltipContent, current: ComponentInternalInstance | null, props: TooltipProps = {}): ComponentInternalInstance => {
  removeTooltip()
  fragment = document.createDocumentFragment() as unknown as Element
  const vNode = h(ElTooltip, {
    ...omit(props, excludeProps),
    virtualTriggering: true,
  }, {
    content: () => content
  })
  vNode.appContext = current?.appContext || null
  render(vNode, fragment)
  _instance = vNode.component as ComponentInternalInstance
  document.body.appendChild(fragment)
  return _instance
}
export const useTooltip = () => {
  const current = getCurrentInstance()
  const showTooltip = (point: DOMRect, content: TooltipContent, props?: TooltipProps) => {
    const instance = createTooltip(content, current, props)
    instance.props.virtualRef = { getBoundingClientRect: () => point }
    instance.props.visible = true
  }

  const hideTooltip = () => {
    removeTooltip()
  }

  return { showTooltip, hideTooltip }
}
