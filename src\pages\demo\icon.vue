<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import createGlobe, { type COBEOptions } from 'cobe'

const props = defineProps<{
  className?: string
  config?: COBEOptions
  size?: number // 👉 传入最大宽度，如 400, 600
}>()

const maxWidth = props.size ?? 600

function detectLowEndDevice(): boolean {
  const mem = (navigator as any).deviceMemory
  return typeof mem === 'number' && mem < 4
}
const isLowEndDevice = detectLowEndDevice()

const BASE_CONFIG: COBEOptions = {
  width: 100, // will be overwritten
  height: 100,
  devicePixelRatio: 1,
  phi: 0,
  theta: 0.3,
  dark: 0,
  diffuse: 0.4,
  mapSamples: isLowEndDevice ? 4000 : 6000,
  mapBrightness: 1.0,
  baseColor: [1, 1, 1],
  markerColor: [251 / 255, 100 / 255, 21 / 255],
  glowColor: [1, 1, 1],
  markers: [
    { location: [14.5995, 120.9842], size: 0.03 },
    { location: [19.076, 72.8777], size: 0.1 },
    { location: [23.8103, 90.4125], size: 0.05 },
    { location: [30.0444, 31.2357], size: 0.07 },
    { location: [39.9042, 116.4074], size: 0.08 },
    { location: [-23.5505, -46.6333], size: 0.1 },
    { location: [19.4326, -99.1332], size: 0.1 },
    { location: [40.7128, -74.006], size: 0.1 },
    { location: [34.6937, 135.5022], size: 0.05 },
    { location: [41.0082, 28.9784], size: 0.06 },
  ],
  onRender: () => {
    console.log('onRender')
  },
}

const mergedConfig = computed(() => ({
  ...BASE_CONFIG,
  ...props.config,
}))

const wrapperRef = ref<HTMLElement | null>(null)
const canvasRef = ref<HTMLCanvasElement | null>(null)
const globeInstance = ref<ReturnType<typeof createGlobe> | null>(null)

const pointerInteracting = ref<number | null>(null)
const pointerInteractionMovement = ref(0)
const r = ref(0)

let phi = 0
let width = 0
let lastRender = Date.now()
const FRAME_INTERVAL = 1000 / 24
const isVisible = ref(true)

const updatePointerInteraction = (value: number | null) => {
  pointerInteracting.value = value
  if (canvasRef.value) {
    canvasRef.value.style.cursor = value !== null ? 'grabbing' : 'grab'
  }
}

const updateMovement = (clientX: number) => {
  if (pointerInteracting.value !== null) {
    const delta = clientX - pointerInteracting.value
    pointerInteractionMovement.value = delta
    r.value = delta / 200
  }
}

const onRender = (state: Record<string, any>) => {
  const now = Date.now()
  if (now - lastRender < FRAME_INTERVAL || !isVisible.value) {return}
  lastRender = now

  if (pointerInteracting.value === null) {
    phi += 0.004
  }
  state.phi = phi + r.value
  state.width = width * 2
  state.height = width * 2
}

const onResize = () => {
  if (wrapperRef.value) {
    const rect = wrapperRef.value.getBoundingClientRect()
    width = rect.width
  }
}

const createGlobeInstance = () => {
  if (!canvasRef.value || !wrapperRef.value || globeInstance.value) {return}

  onResize()
  globeInstance.value = createGlobe(canvasRef.value, {
    ...mergedConfig.value,
    width: width * 2,
    height: width * 2,
    onRender,
  })

  setTimeout(() => {
    canvasRef.value!.style.opacity = '1'
  }, 100)
}

const destroyGlobe = () => {
  globeInstance.value?.destroy()
  globeInstance.value = null
}

const setupObservers = () => {
  if (!wrapperRef.value) {return}

  const observer = new IntersectionObserver(
    ([entry]) => {
      isVisible.value = entry.isIntersecting && document.visibilityState === 'visible'
      if (isVisible.value && !globeInstance.value) {createGlobeInstance()}
    },
    { threshold: 0.1 }
  )
  observer.observe(wrapperRef.value)
  onUnmounted(() => observer.disconnect())

  const handleVisibility = () => {
    isVisible.value = document.visibilityState === 'visible'
  }
  document.addEventListener('visibilitychange', handleVisibility)
  onUnmounted(() => document.removeEventListener('visibilitychange', handleVisibility))
}

onMounted(() => {
  window.addEventListener('resize', onResize)
  setupObservers()
  onResize()

  onUnmounted(() => {
    window.removeEventListener('resize', onResize)
    destroyGlobe()
  })
})
</script>

<template>
  <div class="flex items-center justify-center w-full h-full">
    <div
      ref="wrapperRef"
      class="relative aspect-square w-[90vw]"
      :style="{ maxWidth: `${maxWidth}px` }">
      <canvas
        ref="canvasRef"
        class="absolute top-0 left-0 w-full h-full opacity-0 transition-opacity duration-700 [contain:layout_paint_size]"
        @pointerdown="e => updatePointerInteraction(e.clientX - pointerInteractionMovement)"
        @pointerup="() => updatePointerInteraction(null)"
        @pointerout="() => updatePointerInteraction(null)"
        @mousemove="e => updateMovement(e.clientX)"
        @touchmove="e => e.touches[0] && updateMovement(e.touches[0].clientX)"/>
    </div>
  </div>
</template>

<style scoped>
html,
body,
#app {
  height: 100%;
}
</style>
