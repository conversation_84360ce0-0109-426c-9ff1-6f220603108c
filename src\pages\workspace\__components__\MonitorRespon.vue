<template>
  <Card
    class="relative backdrop-blur-md shadow-sm hover:shadow-md transition-all overflow-hidden"
    :class="[logic.riskStatusBorderClass.value, logic.riskStatusBgClass.value]">
    <!-- Left accent border for monitor agents -->
    <div class="absolute left-0 top-0 bottom-0 w-1.5" :class="logic.riskStatusAccentClass.value"></div>

    <CardHeader class="py-3 px-4 bg-white/20 flex flex-row items-center justify-between pl-6">
      <!-- Left side content -->
      <div class="flex items-center gap-2 max-w-[calc(100%-100px)]"> <!-- Adjust max-width if needed -->
        <Badge :variant="logic.riskStatusVariant.value" class="shadow-sm shrink-0">
          {{ logic.riskStatusLabel.value }}
        </Badge>
        <!-- <span class="text-sm font-medium text-slate-800 truncate">{{ logic.reportName.value }}</span> -->
      </div>
      <!-- Right side content (frequency and view button with dropdown) -->
      <div class="flex items-center gap-2 shrink-0">
        <Badge variant="outline" class="bg-white/60 text-slate-600 border-slate-200 shadow-sm">
          {{ logic.frequencyLabel.value }}
        </Badge>
        
        <!-- Dropdown Menu for View Options -->
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button 
              variant="ghost" 
              size="sm"
              class="h-7 px-2 text-xs text-slate-600 hover:text-main-color hover:bg-slate-100/50 flex items-center gap-1 cursor-pointer transition-all duration-200">
              <ExternalLinkIcon class="h-3.5 w-3.5 shrink-0"/>
              <span>{{ $t('common.view_details') }}</span>
              <ChevronDownIcon class="h-3 w-3 ml-1 transition-transform duration-200 group-data-[state=open]:rotate-180"/>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-64 bg-white/95 backdrop-blur-md border border-slate-200/50 shadow-xl rounded-xl p-2">
            <DropdownMenuItem class="flex items-center gap-3 px-4 py-3 text-sm text-slate-700 hover:bg-blue-50 rounded-lg transition-all duration-200 cursor-pointer group" @click="viewReport(report as unknown as Report)">
              <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-50 group-hover:bg-blue-100 transition-colors">
                <FileTextIcon class="h-5 w-5 text-blue-600"/>
              </div>
              <div class="flex flex-col flex-1">
                <span class="font-semibold text-slate-800">{{ $t('common.view_details') }}</span>
                <span class="text-xs text-slate-500 mt-0.5">{{ $t('agent.view_details_desc') }}</span>
              </div>
            </DropdownMenuItem>
            
            <div class="h-px bg-gradient-to-r from-transparent via-slate-200 to-transparent my-2 mx-2"></div>
            
            <DropdownMenuItem class="flex items-center gap-3 px-4 py-3 text-sm text-slate-700 hover:bg-red-50 rounded-lg transition-all duration-200 cursor-pointer group" @click="viewRiskCompanies">
              <div class="flex items-center justify-center w-10 h-10 rounded-full bg-red-50 group-hover:bg-red-100 transition-colors">
                <AlertCircleIcon class="h-5 w-5 text-red-500"/>
              </div>
              <div class="flex flex-col flex-1">
                <span class="font-semibold text-red-700">{{ $t('agent.view_all_risk_companies') }}</span>
                <span class="text-xs text-slate-500 mt-0.5">{{ $t('agent.view_risk_companies_desc') }}</span>
              </div>
            </DropdownMenuItem>
            
            <DropdownMenuItem class="flex items-center gap-3 px-4 py-3 text-sm text-slate-700 hover:bg-green-50 rounded-lg transition-all duration-200 cursor-pointer group" @click="viewCompletedCompanies">
              <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-50 group-hover:bg-green-100 transition-colors">
                <CheckCircleIcon class="h-5 w-5 text-green-500"/>
              </div>
              <div class="flex flex-col flex-1">
                <span class="font-semibold text-green-700">{{ $t('agent.view_all_completed_companies') }}</span>
                <span class="text-xs text-slate-500 mt-0.5">{{ $t('agent.view_completed_companies_desc') }}</span>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </CardHeader>

    <template v-if="logic.isRunning.value">
      <CardContent class="p-4 pl-6">
        <p class="text-sm mb-3 text-slate-700 break-words">
          {{ logic.progressMessage.value }}
        </p>
        <div class="space-y-2">
          <Progress :value="logic.reportProgress.value"
                    class="h-2"
                    :class="{'bg-blue-100': logic.isRunning.value}"/>
          <p class="text-xs text-right text-slate-500">
            {{ $t('common.completion_degree') }}: {{ logic.reportProgress.value }}%
          </p>
        </div>
        <!-- Steps -->
        <div v-if="logic.hasSteps.value" class="mt-4 space-y-2 max-h-[300px] overflow-y-auto pr-1">
          <div 
            v-for="(step, index) in (logic.isRunningAgent ? (report as unknown as RunningAgent).steps : [])"
            :key="index" 
            class="p-3 rounded-lg border border-slate-100 bg-white/30  transition-all break-words"
            :class="{
              'border-l-4 border-l-blue-500 shadow-sm': step.status === 'PROCESSING',
              'border-l-4 border-l-amber-300': step.status === 'PENDING',
              'border-l-4 border-l-green-500': step.status === 'COMPLETED'
            }">
            <div class="flex justify-between items-center mb-1 gap-2">
              <span class="text-sm font-medium text-slate-800 truncate">{{ step.title }}</span>
              <Badge :variant="logic.getStepStatusVariant(step.status)" class="text-xs shrink-0" :class="logic.getStepStatusBadgeClass(step.status)">
                {{ $t(`common.step_status_${step.status.toLowerCase()}`, step.status) }}
              </Badge>
            </div>
            <p v-if="step.description" class="text-xs text-slate-500 break-words">
              {{ step.description }}
            </p>
          </div>
        </div>
      </CardContent>
    </template>
    <template v-else>
      <CardContent class="p-4 pl-6">
        <!-- Summary Items as List -->
        <div v-if="logic.summaryArray.value.length > 0" class="space-y-2">
          <div v-for="(item, index) in logic.summaryArray.value"
               :key="index" 
               class="flex items-start gap-2 p-2 rounded-md bg-white/40 border border-slate-100 shadow-sm">
            <AlertCircleIcon v-if="logic.riskStatus.value === 'Danger'" class="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0"/>
            <AlertTriangleIcon v-else-if="logic.riskStatus.value === 'Warning'" class="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0"/>
            <CheckCircleIcon v-else class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0"/>
            <span class="text-sm text-slate-700 break-words cursor-pointer hover:text-main-color" @click="handleItemClick($event, item)">{{ item }}</span>
          </div>
        </div>
                
        <!-- Single Summary (Fallback if summaryArray is empty but summary string exists) -->
        <p v-else-if="logic.summary.value && logic.summary.value !== $t('report.monitor_summary_unavailable')" 
           class="text-sm leading-relaxed text-slate-700 break-words">
          {{ logic.summary.value }}
        </p>
      </CardContent>
    </template>
  </Card>
</template>

<script setup lang="ts">
import { 
  Card, CardHeader, CardContent
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { 
  AlertCircleIcon, 
  AlertTriangleIcon, 
  CheckCircleIcon, 
  ExternalLinkIcon,
  ChevronDownIcon,
  FileTextIcon
} from 'lucide-vue-next'
import { toRefs } from 'vue'
import type { Report, RunningAgent } from '../types'
import { useReportDisplayLogic } from '@/composables/useReportDisplayLogic'
import { useRouter } from 'vue-router'

// Define Props - Accept generic Report, logic handles specifics
const props = defineProps<{
  report: Report; // Accept generic Report type
  viewReport:(report: Report) => void;
}>() 

// Use the composable - Pass the potentially broader report type
// Ensure useReportDisplayLogic can handle the generic Report type or cast internally
const logic = useReportDisplayLogic(toRefs(props).report)

const router = useRouter()

// 定义事件
const emit = defineEmits<{
  (e: 'viewReport', report: Report): void
  (e: 'tagClick', tagData: any): void
}>()

// 查看所有风险公司
const viewRiskCompanies = () => {
  const reportId = (props.report as any).id
  const reportType = (props.report as any).report_type
  
  if (!reportId || !reportType) {
    console.error('Report ID or type is missing')
    return
  }
  
  const to = router.resolve({
    path: `/dashboard/${reportId}`,
    query: {
      report_type: reportType,
      filter_type: 'risk',
      risk_status: 'risk'
    }
  })
  window.open(to.href, '_blank')
}

// 查看所有已完成公司
const viewCompletedCompanies = () => {
  const reportId = (props.report as any).id
  const reportType = (props.report as any).report_type
  
  if (!reportId || !reportType) {
    console.error('Report ID or type is missing')
    return
  }
  
  const to = router.resolve({
    path: `/dashboard/${reportId}`,
    query: {
      report_type: reportType,
      filter_type: 'status',
      status: 'COMPLETED'
    }
  })
  window.open(to.href, '_blank')
}

// 处理摘要项目点击事件
const handleItemClick = (event: Event, tag: string) => {
  event.stopPropagation() // 阻止事件冒泡，避免触发卡片的点击事件
  
  // 跳转到详情页并搜索特定公司
  const reportId = (props.report as any).id
  const reportType = (props.report as any).report_type
  
  if (!reportId || !reportType) {
    console.error('Report ID or type is missing')
    return
  }
  
  const to = router.resolve({
    path: `/dashboard/${reportId}`,
    query: {
      report_type: reportType,
      search_company: tag // 传递公司名用于搜索
    }
  })
  window.open(to.href, '_blank')
  
  // 发送tagClick事件，传递report和tag
  emit('tagClick', {
    report: props.report,
    tag
  })
}

// Expose t for the template if necessary (already done by composable if passed)
// Expose viewReport for the template

</script>
