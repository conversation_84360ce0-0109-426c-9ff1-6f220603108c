<template>
  <EchartsComponent :params="getChartOption" :auto-size="true" data-pdf-chart/>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'
import EchartsComponent from '@/components/EchartsComponent.vue'
import type { EChartsType } from 'echarts'
import { formatCurrency } from '@/utils/index'
import { useI18n } from 'vue-i18n'
import { isObject, merge } from 'lodash-es'
interface DataPoint {
  x: string;
  y: number;
}

interface SeriesData {
  name: string;
  data: DataPoint[];
}

const props = defineProps<{
  data: SeriesData[];
  config?: Record<string, any>;
}>()

const { locale } = useI18n()
// 颜色配置
const COLORS = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B']

// 转换数据格式
const chartData = computed(() => {
  if (!props.data?.length) {return { xData: [], series: [] }}
  
  // 对第一个系列的数据点进行排序，并提取排序后的x值
  const sortedData = [...props.data[0].data].sort((a, b) => 
    dayjs(a.x).valueOf() - dayjs(b.x).valueOf()
  )
  const xData = sortedData.map(item => dayjs(item.x).format('YYYY-MM-DD'))
  
  // 转换每个系列的数据，保持与x轴顺序一致
  const series = props.data.map((item, index) => ({
    name: item.name,
    type: 'line',
    data: sortedData.map(point => {
      const matchingPoint = item.data.find(p => dayjs(p.x).format('YYYY-MM-DD') === dayjs(point.x).format('YYYY-MM-DD'))
      return matchingPoint ? matchingPoint.y : null
    }),
    smooth: true,
    symbol: 'circle',
    symbolSize: 6,
    itemStyle: {
      color: COLORS[index],
    },
    lineStyle: {
      width: 2,
      color: COLORS[index],
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: `${COLORS[index]}33`,
          },
          {
            offset: 1,
            color: `${COLORS[index]}00`,
          },
        ],
      },
    },
  }))

  return { xData, series }
})

// 图表配置
const getChartOption = computed(() => {
  const baseOption = {
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      formatter: (params: any[]) => {
        const date = params[0].axisValue
        return `${date}<br/>${params
          .map(param => `${param.seriesName}: ${formatCurrency(param.value, locale.value as 'chinese' | 'english' | 'japanese')}`)
          .join('<br/>')}`
      },
    },
    legend: {
      show: true,
      top: 0,
      left: 'center',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '50px',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.xData,
      axisLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#000000'
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#000000',
        formatter: (value: number) => {
          return value !== null ? formatCurrency(value, locale.value as 'chinese' | 'english') : '0'
        }
      }
    },
    series: chartData.value.series,
  }
  
  return isObject(props.config) ? merge(baseOption, props.config) : baseOption
})

</script>
