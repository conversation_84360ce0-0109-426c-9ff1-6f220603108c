import { ref, shallowReadonly, type Ref } from 'vue'

export type PageData<T> = {
  total: number
  list: T[]
}
type PageLoad<T> = (page: number, pageSize: number, ...args: any[]) => Promise<PageData<T> | null>
interface Options {
  immediate: boolean,
  defaultParams?: any[]
}

export const usePageData = <T = any>(pageSize: number, pageLoad: PageLoad<T>, options?: Options) => {
  const data = ref<Array<T>>([]) as Ref<Array<T>>
  const isLoading = ref(false)
  const currentPage = ref(1)
  const total = ref(0)
  const _pageSize = ref(pageSize)
  const execute = async (...args: any[]) => {
    isLoading.value = true
    try {
      const res = await pageLoad(currentPage.value, pageSize, ...args)
      total.value = res?.total || 0
      data.value = data.value.concat(res?.list || [])
    } finally {
      isLoading.value = false
    }
  }
  const loadWithPage = (page: number, ...args: any[]) => {
    currentPage.value = page
    data.value = []
    return execute(...args)
  }
  const nextPage = (...args: any[]) => {
    currentPage.value += 1
    return execute(...args)
  }
  const prevPage = (...args: any[]) => {
    if (currentPage.value <= 1) {
      return
    }
    currentPage.value -= 1
    return execute(...args)
  }
  const refresh = (...args: any[]) => {
    currentPage.value = 1
    data.value = []
    return execute(...args)
  }
  if (options?.immediate) {
    execute(...(options?.defaultParams || []))
  }
  return {
    isLoading: shallowReadonly(isLoading),
    data: shallowReadonly(data),
    total: shallowReadonly(total),
    currentPage: shallowReadonly(currentPage),
    pageSize: _pageSize,
    execute,
    prevPage,
    nextPage,
    refresh,
    loadWithPage,
  }
}
