import { ref, computed } from 'vue'
import { useEnhancedEventSource } from '@/hooks/useEnhancedEventSource'
import type { SSEMessage } from '@/hooks/useEnhancedEventSource'
import { getToken } from '@/hooks/useToken'
import { getI18nInstance } from '@/lang/i18n'
import RecentNewsAndSocialOpinionTrackingReport from '@/assets/images/avatar/RecentNewsAndSocialOpinionTrackingReport.svg'
import CompanyTrackingAnalysisReport from '@/assets/images/avatar/CompanyTrackingAnalysisReport.svg'
import ProductTrackingAnalysisReport from '@/assets/images/avatar/ProductTrackingAnalysisReport.svg'
import FinancialPerformanceTrackingReport from '@/assets/images/avatar/FinancialPerformanceTrackingReport.svg'
import CompanyLegalAnalysisReport from '@/assets/images/avatar/CompanyLegalAnalysisReport.svg'
import SocialAgent from '@/assets/images/avatar/SocialAgent.svg'
import LegalAgent from '@/assets/images/avatar/LegalAgent.svg'
import CompositeAgent from '@/assets/images/avatar/CompositeAgent.svg'

export const SPECIAL_AGENT_TYPES = ['SocialAgent', 'LegalAgent', 'CompositeAgent']
export const SPECIAL_REPORT_TYPES = ['RecentNewsAndSocialOpinionTrackingReport', 'CompanyTrackingAnalysisReport', 'ProductTrackingAnalysisReport', 'FinancialPerformanceTrackingReport', 'CompanyLegalAnalysisReport']
export const BIDING_AGENT_TYPES = ['TENDER', 'AWARDED']
export interface ToolResult {
  name: string
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'ERROR' | 'running' | 'success' | 'pending' | 'error'
  id?: string
  risk_status?: string
  summary?: string | Record<string, string>
}

export const AVATAR_MAP = {
  'RecentNewsAndSocialOpinionTrackingReport': RecentNewsAndSocialOpinionTrackingReport,
  'CompanyTrackingAnalysisReport': CompanyTrackingAnalysisReport,
  'ProductTrackingAnalysisReport': ProductTrackingAnalysisReport,
  'FinancialPerformanceTrackingReport': FinancialPerformanceTrackingReport,
  'CompanyLegalAnalysisReport': CompanyLegalAnalysisReport,
  'SocialAgent': SocialAgent,
  'LegalAgent': LegalAgent,
  'CompositeAgent': CompositeAgent
}
export interface ReportParams {
  company_name: string
  reporting_period: string
  legal_focus: string
  [key: string]: string
}

export interface ReportData {
  id: string
  sync_type: boolean
  report_type: string
  report_name: string
  report_description: string
  params: ReportParams
  status: 'pending' | 'running' | 'completed' | 'error'
  progress: number
  tools_results: ToolResult[]
  summary?: string
  agent_name: string
}

export interface ToolOutput {
  [key: string]: any
}

export interface ChartData {
  type: string;
  title: string;
  indicators: Array<{
    name: string;
    max: number;
  }>;
  data: Array<{
    name: string;
    value: number[];
  }>;
}

export interface CompanyStatusData {
  company_status: null | any;
  chart_data?: ChartData[];
}

export interface LegalAgentData {
  company_status: string;
  chart_data: ChartData[];
  legal_score?: {
    level: string;
    score: number;
    basic_score: number;
  };
}

export interface SingleReportData extends Omit<ReportData, 'tools_out_put'> {
  tools_out_put: ToolOutput;
  avatar: string;
  tools_results_dict: Record<string, string>;
  special_summary: {
    summary: string;
    risk_status: string;
  };
  chart_data?: ChartData[];
  company_status?: Record<string, CompanyStatusData>;
  legal_agent_data?: Record<string, LegalAgentData>;
}

export interface TimeInfo {
  elapsed: string
  remaining: string
}

export interface StepStatus {
  completed: number
  inProgress: number
  pending: number
}

export interface StepContent {
  type: 'list' | 'progress' | 'tags'
  title: string
  description: string
  data: {
    list?: string[]
    progress?: number
    tags?: Array<{ percentage: number; label: string }>
  }
}

export interface DetailStep {
  number: number
  title: string
  status: 'COMPLETED' | 'PROCESSING' | 'PENDING' | 'ERROR'
  content: StepContent
}

export function useReportProgress(api_url: string, options?: {
  single?: boolean
  report_id?: string
  report_type?: string
  onConnected?: () => void
}) {
  const i18nInstance = getI18nInstance()
  const dashboardData = ref<ReportData[]>([])
  const singleReportData = ref<SingleReportData | null>(null)

  const {
    status: connectionStatus,
    error,
    messageHistory,
    close,
    open,
    isReconnecting,
    retryCount
  } = useEnhancedEventSource(
    () => {
      const baseUrl = api_url
      const url = new URL(baseUrl, window.location.origin)
      const token = getToken()
      const language = i18nInstance.global.locale.value

      url.searchParams.append('token', token)
      url.searchParams.append('language', language)

      if (options?.single && options.report_id && options.report_type){
        url.searchParams.append('id', options.report_id)
        url.searchParams.append('report_type', options.report_type)
      }

      return url
    },
    ['message', 'on_message', 'on_close'],
    {
      reconnectConfig: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 5000,
        backoffFactor: 1.5,
      },
      onMessage: (message: SSEMessage) => {
        if (message.type === 'heartbeat') {
          return
        }
        // if (message.data.length === 0){
        //   message = {
        //     'type': 'update',
        //     'data': [
        //       {
        //         'id': '67fe441c4af697de0dcce9f2',
        //         'sync_type': false,
        //         'report_type': 'TENDER',
        //         'report_name': 'TENDER',
        //         'agent_name': 'TENDER',
        //         'status': 'pending',
        //         'progress': 0.0,
        //         'tools_results': [],
        //         'next_time': '明天早上8点',
        //         'tools_results_dict': {},
        //         'last_run_date': null
        //       }
        //     ]
        //   }
        // }
        if (message.type === 'update') {
          if (options?.single && message.data) {
            if (message.data?.id){
              singleReportData.value = (message.data as SingleReportData)
              singleReportData.value.avatar = AVATAR_MAP[singleReportData.value.report_type as keyof typeof AVATAR_MAP]
              if (SPECIAL_AGENT_TYPES.includes(singleReportData.value.report_type)){
                singleReportData.value.tools_results = singleReportData.value.tools_results.map((tool: ToolResult) =>{
                  if (tool.status === 'success'){
                    tool.status = 'COMPLETED'
                  }
                  if (tool.status === 'running'){
                    tool.status = 'PROCESSING'
                  }
                  if (tool.status === 'pending'){
                    tool.status = 'PENDING'
                  }
                  if (tool.status === 'error'){
                    tool.status = 'ERROR'
                  }

                  return tool
                })
              }
            }
          } else if (Array.isArray(message.data)) {
            dashboardData.value = message.data.map(item =>{
              if (SPECIAL_AGENT_TYPES.includes(item.report_type)){
                item.tools_results = item.tools_results.map((tool: ToolResult) =>{
                  if (tool.status === 'success'){
                    tool.status = 'COMPLETED'
                  }
                  if (tool.status === 'running'){
                    tool.status = 'PROCESSING'
                  }
                  if (tool.status === 'pending'){
                    tool.status = 'PENDING'
                  }
                  if (tool.status === 'error'){
                    tool.status = 'ERROR'
                  }
                  return tool
                })
              }
              console.log('item', item)
              return item
            })
          }
        }
      },
      onError: (err) => {
        console.error('SSE Error:', err)
      },
      onClose: () => {
        console.log('SSE connection closed')
      },
      onStateChange: (newStatus) => {
        if (newStatus === 'CLOSED') {
          console.log('Connection closed unexpectedly')
        } else if (newStatus === 'OPEN') {
          console.log('SSE connection established successfully')
          if (options?.onConnected) {
            options.onConnected()
          }
        }
      },
      immediate: false,
      eventSourceInit: {
        withCredentials: true,
      },
    }
  )

  // Computed properties for dashboard display
  const activeAgents = computed(() =>
    dashboardData.value.map(report => ({
      id: report.id,
      name: report.report_name,
      summary: report.report_description,
      type: report.status === 'error' ? 'danger' :
        report.status === 'running' ? 'warning' : 'normal',
      avatar: AVATAR_MAP[report.report_type as keyof typeof AVATAR_MAP]
    }))
  )

  const tasks = computed(() =>
    dashboardData.value.map(report => {
      const completedTools = report.tools_results?.filter(
        tool => tool.status === 'COMPLETED' || tool.status === 'ERROR'
      ).length

      // Find current processing tool or last completed tool
      const processingTool = report.tools_results.find(tool => tool.status === 'PROCESSING')
      const lastCompletedTool = report.tools_results.slice().reverse().find(tool =>
        tool.status === 'COMPLETED' || tool.status === 'ERROR'
      )

      let description
      if (processingTool) {
        description = SPECIAL_AGENT_TYPES.includes(report.report_type) ? processingTool.name : i18nInstance.global.t(`tools.${processingTool.name}`)
      } else if (lastCompletedTool) {
        description = SPECIAL_AGENT_TYPES.includes(report.report_type) ? lastCompletedTool.name : i18nInstance.global.t(`tools.${lastCompletedTool.name}`)
      } else {
        description = i18nInstance.global.t('agent.init')
      }

      return {
        id: report.id,
        name: report.agent_name || i18nInstance.global.t('agent.no_agent_name', { name:report.report_name }),
        report_name: report.report_name,
        report_type: report.report_type,
        duty: report.report_description,
        description,
        currentStep: completedTools,
        totalSteps: report.tools_results.length,
        progress: Math.ceil(report.progress || 0),
        avatar: AVATAR_MAP[report.report_type as keyof typeof AVATAR_MAP]
      }
    })
  )

  const agentResponses = computed(() =>
    dashboardData.value.map(report => ({
      name: report.agent_name || i18nInstance.global.t('agent.no_agent_name', { name:report.report_name }),
      report_name: report.report_name,
      duty: report.report_description,
      status: report.status.toUpperCase(),
      avatar: AVATAR_MAP[report.report_type as keyof typeof AVATAR_MAP],
      steps: report.tools_results.map(tool => ({
        title: SPECIAL_AGENT_TYPES.includes(report.report_type) ? tool.name : i18nInstance.global.t(`tools.${tool.name}`),
        description: SPECIAL_AGENT_TYPES.includes(report.report_type) ? tool.name : i18nInstance.global.t(`tools.${tool.name}Desc`),
        status: tool.status
      }))
    }))
  )

  const processSpecialSummary = (summary:string | Record<string, string>) => {
    if (typeof summary === 'string'){
      return summary
    }

    // 处理CompositeAgent的特殊数据结构
    if (singleReportData.value?.report_type === 'CompositeAgent' && summary && typeof summary === 'object') {
      // 直接处理tools_results_dict中的数据结构
      if ('legal_summary' in summary && 'social_summary' in summary) {
        const legalSummary = ((summary.legal_summary as any)?.summary) || ''
        const socialSummary = ((summary.social_summary as any)?.summary) || ''
        const isLegalDanger = ((summary.legal_summary as any)?.is_danger) || false
        const isSocialDanger = ((summary.social_summary as any)?.is_danger) || false

        // 只为法律摘要添加红色样式
        const legalSummaryHtml = isLegalDanger
          ? `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.legal_summary')}:</span> <span class="text-red-500">${legalSummary}</span>`
          : `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.legal_summary')}:</span> ${legalSummary}`

        // 社交摘要保持默认样式
        const socialSummaryHtml = isSocialDanger ? `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.social_summary')}:</span> <span class="text-red-500">${socialSummary}</span>`
          : `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.social_summary')}:</span> ${socialSummary}`
        return [legalSummaryHtml, socialSummaryHtml].join('</br></br>')
      }

      // 处理嵌套的summary结构
      if ('summary' in summary) {
        const compositeData = summary.summary as any
        if (compositeData) {
          const legalSummary = compositeData.legal_summary?.summary || ''
          const socialSummary = compositeData.social_summary?.summary || ''
          const isLegalDanger = compositeData.legal_summary?.is_danger || false
          const isSocialDanger = compositeData.social_summary?.is_danger || false

          // 只为法律摘要添加红色样式
          const legalSummaryHtml = isLegalDanger
            ? `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.legal_summary')}:</span> <span class="text-red-500">${legalSummary}</span>`
            : `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.legal_summary')}:</span> ${legalSummary}`

          // 社交摘要保持默认样式
          const socialSummaryHtml = isSocialDanger
            ? `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.social_summary')}:</span> <span class="text-red-500">${socialSummary}</span>`
            : `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t('agent.social_summary')}:</span> ${socialSummary}`

          return [legalSummaryHtml, socialSummaryHtml].join('</br></br>')
        }
      }
    }

    // 如果是对象，使用summary对象中的key去适应国际化翻译，并且value作为summary
    return Object.entries(summary).map(([key, value]) => {
      // 过滤掉不需要展示的字段
      if (key === 'legal_avg_score' || key === 'social_avg_score' ||
          (typeof value === 'object' && value && 'legal_avg_score' in value) ||
          (typeof value === 'object' && value && 'social_avg_score' in value)) {
        return ''
      }
      return `<span class="font-bold text-md text-main-color! mr-1">${i18nInstance.global.t(`agent.${key}`)}:</span>  ${value}`
    }).filter(item => item !== '').join('</br></br>')
  }
  // Add computed properties for detail view
  const detailSteps = computed(() => {
    if (!singleReportData.value) { return [] }
    const isSpecialAgent = SPECIAL_AGENT_TYPES.includes(singleReportData.value?.report_type || '')
    const tools = [...singleReportData.value.tools_results]
    const lastCompletedIndex = tools.map(t => t.status).lastIndexOf('COMPLETED')

    // Handle error status first
    tools.forEach(tool => {
      if (tool.status === 'error') {
        tool.status = 'ERROR'
      }
    })

    // If there's a next step after the last completed one, set it to PROCESSING
    if (lastCompletedIndex !== -1 && lastCompletedIndex + 1 < tools.length && tools[lastCompletedIndex + 1].status !== 'ERROR') {
      tools[lastCompletedIndex + 1].status = 'PROCESSING'
    }
    // If no completed steps and there are tools, set first to PROCESSING if not ERROR
    else if (lastCompletedIndex === -1 && tools.length > 0 && tools[0].status !== 'ERROR') {
      tools[0].status = 'PROCESSING'
    }

    return tools.map((tool, index) => {
      // Check if this is a special agent type and get summary if available
      let special_summary:{
        summary: string
        risk_status: string
      } | null = null
      if (singleReportData.value?.report_type && SPECIAL_AGENT_TYPES.includes(singleReportData.value?.report_type)) {
        const toolResult = singleReportData.value?.tools_results_dict?.[tool.name] || ''

        // 处理CompositeAgent的risk_status
        let riskStatus = ''
        if (singleReportData.value?.report_type === 'CompositeAgent' && typeof toolResult === 'object' && toolResult) {
          // 直接处理tools_results_dict中的数据结构
          if ('legal_summary' in toolResult && 'social_summary' in toolResult) {
            const isLegalDanger = ((toolResult as any).legal_summary)?.is_danger || false
            const isSocialDanger = ((toolResult as any).social_summary)?.is_danger || false

            // 使用法律摘要或社交摘要的is_danger状态来设置risk_status
            riskStatus = (isLegalDanger || isSocialDanger) ? 'Danger' : ''
          }
          // 处理嵌套的summary结构
          else if ('summary' in toolResult) {
            const compositeData = (toolResult as any).summary
            const isLegalDanger = compositeData?.legal_summary?.is_danger || false
            const isSocialDanger = compositeData?.social_summary?.is_danger || false

            // 使用法律摘要或社交摘要的is_danger状态来设置risk_status
            riskStatus = (isLegalDanger || isSocialDanger) ? 'Danger' : ''
          }
        } else {
          // 使用原有逻辑获取risk_status
          riskStatus = singleReportData.value?.tools_results.find(t =>
            (t.status === 'COMPLETED' || t.status === 'ERROR' ||
             t.status === 'success' || t.status === 'error') && t.name === tool.name
          )?.risk_status || ''
        }

        special_summary = {
          summary: processSpecialSummary(toolResult),
          risk_status: riskStatus
        }
      }

      return {
        number: index + 1,
        title: isSpecialAgent ? tool.name : i18nInstance.global.t(`tools.${tool.name}`),
        status: tool.status,
        content: {
          type: 'progress',
          title: isSpecialAgent ? tool.name : i18nInstance.global.t(`tools.${tool.name}`),
          description: isSpecialAgent ? tool.name : i18nInstance.global.t(`tools.${tool.name}Desc`),
          data: {
            progress: tool.status === 'COMPLETED' || tool.status === 'ERROR' ? 100 :
              tool.status === 'PROCESSING' ? 50 : 0,
          }
        },
        special_summary,
        chart_data: singleReportData.value?.chart_data // 添加雷达图数据
      }
    })
  })

  const stepStatus = computed(() => {
    if (!singleReportData.value) {return { completed: 0, inProgress: 0, pending: 0 }}

    const tools = singleReportData.value.tools_results
    return {
      completed: tools?.filter(t => t.status === 'COMPLETED').length,
      inProgress: tools?.filter(t => t.status === 'PROCESSING').length,
      pending: tools?.filter(t => t.status === 'PENDING').length
    }
  })

  const timeInfo = computed(() => ({
    elapsed: '0h', // This should be calculated based on actual timestamps
    remaining: '0h' // This should be calculated based on progress and average time
  }))

  return {
    // Dashboard data
    dashboardData,
    activeAgents,
    tasks,
    agentResponses,

    // Detail view data
    singleReportData,
    detailSteps,
    stepStatus,
    timeInfo,

    // Connection status
    connectionStatus,
    error,
    messageHistory,
    close,
    open,
    isReconnecting,
    retryCount
  }
}
