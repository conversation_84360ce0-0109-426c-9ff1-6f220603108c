<template>
  <Transition
    enter-active-class="transition duration-300 ease-out"
    enter-from-class="transform opacity-0 scale-95"
    enter-to-class="transform opacity-100 scale-100"
    leave-active-class="transition duration-300 ease-in"
    leave-from-class="transform opacity-100 scale-100"
    leave-to-class="transform opacity-0 scale-95">
    <div v-if="loading" class="flex items-center justify-center z-50">
      <div class="w-full max-w-[1150px] bg-white/90 dark:bg-gray-800/90 backdrop-blur-xs rounded-lg p-6" :class="$style['highlight-filter']">
        <div class="relative flex flex-col items-center gap-4">
          <!-- Main Filter Icon -->
          <div class="relative">
            <Icon
              :icon="icon"
              class="text-4xl"
              :class="[
                iconClass,
                $style['animate-bounce']
              ]"/>
            
            <!-- Floating Particles -->
            <div class="absolute top-0 left-0 w-full h-full">
              <div 
                v-for="i in 6" 
                :key="i"
                class="absolute w-1.5 h-1.5 bg-primary-500/60 rounded-full"
                :class="[
                  $style[`particle-${i}`],
                  $style['animate-float']
                ]"/>
            </div>
          </div>
          
          <!-- Loading Text -->
          <p class="text-main-color dark:text-gray-200 font-medium">
            {{ text }}
          </p>
          <SearchResultsList
            v-if="searchResults?.length"
            :results="searchResults"
            :wrap-class="$style['search-results-list']"/>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import type { url } from '@/pages/format-report/type'
import { Icon } from '@iconify/vue'
import SearchResultsList from '@/components/SearchResultsList.vue'

const props = withDefaults(defineProps<{
  loading: boolean
  text?: string
  searchResults?: url[]
  icon?: string
  iconClass?: string
}>(), {
  icon: 'mdi-filter-variant',
  iconClass: 'text-primary-600 dark:text-primary-400'
})

</script>

<style lang="scss" module>
.particle-1 { animation-delay: 0s; left: 0%; }
.particle-2 { animation-delay: 0.2s; left: 20%; }
.particle-3 { animation-delay: 0.4s; left: 40%; }
.particle-4 { animation-delay: 0.6s; left: 60%; }
.particle-5 { animation-delay: 0.8s; left: 80%; }
.particle-6 { animation-delay: 1s; left: 100%; }

@keyframes float {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px) scale(0);
    opacity: 0;
  }
}

.animate-float {
  animation: float 1.5s infinite;
}

/* 添加自定义的bounce动画 */
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce {
  animation: bounce 0.8s infinite;
}
.highlight-filter {
  animation: breathing-highlight 2s ease-in-out infinite !important;
}

@keyframes breathing-highlight {
  0% {
    border-color: transparent;
    box-shadow: none;
  }
  50% {
    border-color: var(--el-color-primary-light-9);
    box-shadow: 0 0 30px var(--el-color-primary-light-7);
  }
  100% {
    border-color: transparent;
    box-shadow: none;
  }
}
.search-results-list{
  max-height: 300px !important;
}
</style>