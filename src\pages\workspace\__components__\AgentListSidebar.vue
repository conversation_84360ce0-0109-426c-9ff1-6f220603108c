<template>
  <Card class="bg-white/70 backdrop-blur-md border-slate-100 shadow-sm flex flex-col flex-1 min-h-0 overflow-hidden">
    <CardHeader class="border-b border-slate-100 bg-white/50 backdrop-blur-sm">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-slate-800">{{ t('page.report_chat.agent_list_title') }}</h2>
        <Badge variant="secondary" class="bg-violet-100 text-violet-700 border-0">{{ t('page.report_chat.active_agent_count', { count: activeAgentsCount }) }}</Badge>
      </div>
      
      <!-- 添加分组选项卡 -->
      <!-- <div class="flex items-center gap-1.5 mt-2 mb-2 overflow-x-auto pb-1">
        <Button 
          v-for="mode in AGENT_FILTER_OPTIONS"
          :key="mode.value"
          :variant="currentAgentType === mode.value ? 'default' : 'ghost'"
          size="sm"
          class="whitespace-nowrap flex items-center gap-1"
          :class="currentAgentType === mode.value ? 'bg-violet-100 text-violet-700 border-violet-200' : 'bg-white/80 text-slate-600 border-slate-200'"
          @click="currentAgentType = mode.value">
          <component :is="mode.icon" class="h-3.5 w-3.5"/>
          {{ t(mode.label) }}
        </Button>
      </div> -->
              
      <div class="relative mt-2">
        <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"/>
        <Input 
          :placeholder="t('common.search_placeholder')" 
          class="pl-9 bg-white/80 border-slate-200" 
          :model-value="agentSearch" 
          @update:model-value="emit('update:agentSearch', String($event))"/>
      </div>
    </CardHeader>
            
    <CardContent class="flex-1 min-h-0 overflow-hidden p-0">
      <ScrollArea class="h-full">
        <div class="p-3 space-y-1.5">
          <!-- Special Insights Group Button -->
          <div
            v-if="canAccessBindingFeature"
            class="p-2 rounded-lg cursor-pointer transition-colors group hover:bg-blue-50/50"
            :class="{ 'bg-blue-100/80 shadow-sm': activeTab === 'special' }"
            @click="emit('switch-to-special-tab', 'SPECIAL_GROUP')">
            <div class="flex items-center gap-3">
              <Avatar class="h-9 w-9 ring-2 ring-white/70">
                <AvatarFallback class="bg-gradient-to-br from-blue-400 to-indigo-400 text-white group-hover:scale-105 transition-transform">
                  <LayersIcon class="h-4 w-4"/>
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 class="text-sm font-medium text-slate-800 group-hover:text-blue-700">{{ t('agent.filter.binding') }}</h3>
                <p class="text-xs text-slate-500 group-hover:text-blue-600">{{ t('agent.special_insight_group_desc') }}</p>
              </div>
            </div>
          </div>
          <!-- All Agents Option -->
          <div
            v-if="canAccessNormalFeature"
            class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors"
            :class="{ 'bg-white/80 shadow-sm': selectedAgent === null }"
            @click="emit('select-dynamic-filter', null)">
            <div class="flex items-center gap-3">
              <Avatar class="h-9 w-9 ring-2 ring-white/70">
                <AvatarFallback class="bg-gradient-to-br from-violet-500 to-cyan-500 text-white">
                  <UsersIcon class="h-4 w-4"/>
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 class="text-sm font-medium text-slate-800">{{ t('agent.filter.normal') }}</h3>
                <p class="text-xs text-slate-500">{{ t('common.view_all_reports') }}</p>
              </div>
            </div>
          </div>
                
          <!-- Monitor/Alert Agents Group -->
          <div
            v-if="canAccessNotifyFeature"
            class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors"
            :class="{ 'bg-white/80 shadow-sm': selectedAgent === '__MONITOR__' }"
            @click="emit('select-dynamic-filter', '__MONITOR__')">
            <div class="flex items-center gap-3">
              <Avatar class="h-9 w-9 ring-2 ring-white/70">
                <AvatarFallback class="bg-gradient-to-br from-amber-400 to-orange-400 text-white">
                  <BellRingIcon class="h-4 w-4"/>
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 class="text-sm font-medium text-slate-800">{{ t('agent.filter.notify') }}</h3>
                <p class="text-xs text-slate-500">{{ t('agent.monitor_alert_group_desc') }}</p>
                <div class="flex items-center gap-1.5 mt-1">
                  <Badge variant="outline" class="text-[10px] px-1 py-0 bg-amber-100 text-amber-800 border-0">
                    {{ t('common.automation') }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

                
          <!-- Separator/Header for Standard Agents -->
          <!-- <div v-if="filteredDisplayAgents.length > 0" class="pt-2 pb-1">
            <div class="text-xs font-medium text-slate-500 px-2">{{ t('agent.standard_agents', '标准代理') }}</div>
          </div> -->
          <!-- 绑定工作代理 -->
          <template v-if="canAccessBindingFeature && currentAgentType === 'all' && hasTypeInDisplay('binding')">
            <div class="mt-2 mb-1 border-t border-slate-100 pt-2">
              <div class="text-xs font-medium text-blue-600 px-2 flex items-center">
                <LayersIcon class="h-3 w-3 mr-1"/>
                {{ t('agent.filter.binding') }}
              </div>
            </div>
          </template>

          <div
            v-for="agent in getAgentsByType('binding')"
            :key="agent.report_type"
            v-show="canAccessBindingFeature && (currentAgentType === 'all' || currentAgentType === 'binding')"
            class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors group relative"
            :class="{ 'bg-white/80 shadow-sm': selectedAgent === agent.report_type, 'agent-working-border': agent.status === 'running' }"
            @click="handleAgentClick(agent)">
            <div class="flex items-center gap-3">
              <div class="w-2 h-full self-stretch bg-blue-100 rounded-full"></div>
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium truncate text-slate-800">{{ agent.role }}</h3>
                <!-- <p class="text-xs text-slate-500">{{ agent.role }}</p> -->
                <div class="flex items-center gap-1.5 mt-1">
                  <Badge :variant="getAgentStatusVariant(agent.status)" class="text-[10px] px-1 py-0" :class="getAgentStatusBadgeClass(agent.status)">
                    {{ t(`common.agent_status_${agent.status.toLowerCase()}`, agent.status) }}
                  </Badge>
                  <span class="text-xs truncate text-slate-500">
                    {{ agent.currentTask || t('common.no_active_task') }}
                  </span>
                </div>
              </div>
            </div>
            <!-- Config Button -->
            <TooltipProvider :delay-duration="100">
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-7 w-7 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                    @click.stop="navigateToAgentConfig(agent.id)">
                    <CogIcon class="h-4 w-4 text-slate-500 hover:text-slate-700"/>
                  </Button>
                </TooltipTrigger>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <!-- 标准类型代理 -->
          <template v-if="canAccessNormalFeature && currentAgentType === 'all' && hasTypeInDisplay('normal')">
            <div class="mt-2 mb-1 border-t border-slate-100 pt-2">
              <div class="text-xs font-medium text-violet-600 px-2 flex items-center">
                <UsersIcon class="h-3 w-3 mr-1"/>
                {{ t('agent.filter.normal') }}
              </div>
            </div>
          </template>

          <div
            v-for="agent in getAgentsByType('normal')"
            :key="agent.report_type"
            v-show="canAccessNormalFeature && (currentAgentType === 'all' || currentAgentType === 'normal')"
            class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors group relative"
            :class="{ 'bg-white/80 shadow-sm': selectedAgent === agent.report_type, 'agent-working-border': agent.status === 'running' }"
            @click="handleAgentClick(agent)">
            <div class="flex items-center gap-3">
              <div class="w-2 h-full self-stretch bg-violet-100 rounded-full"></div>
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium truncate text-slate-800">{{ agent.role }}</h3>
                <!-- <p class="text-xs text-slate-500">{{ agent.role }}</p> -->
                <div class="flex items-center gap-1.5 mt-1">
                  <Badge :variant="getAgentStatusVariant(agent.status)" class="text-[10px] px-1 py-0" :class="getAgentStatusBadgeClass(agent.status)">
                    {{ t(`common.agent_status_${agent.status.toLowerCase()}`, agent.status) }}
                  </Badge>
                  <span class="text-xs truncate text-slate-500">
                    {{ agent.currentTask || t('common.no_active_task') }}
                  </span>
                </div>
              </div>
            </div>
            <!-- Config Button -->
            <TooltipProvider :delay-duration="100">
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-7 w-7 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                    @click.stop="navigateToAgentConfig(agent.id)">
                    <CogIcon class="h-4 w-4 text-slate-500 hover:text-slate-700"/>
                  </Button>
                </TooltipTrigger>
              </Tooltip>
            </TooltipProvider>
          </div>

          <!-- 预警类型代理 -->
          <template v-if="canAccessNotifyFeature && currentAgentType === 'all' && hasTypeInDisplay('notify')">
            <div class="mt-2 mb-1 border-t border-slate-100 pt-2">
              <div class="text-xs font-medium text-amber-600 px-2 flex items-center">
                <BellRingIcon class="h-3 w-3 mr-1"/>
                {{ t('agent.filter.notify') }}
              </div>
            </div>
          </template>

          <div
            v-for="agent in getAgentsByType('notify')"
            :key="agent.report_type"
            v-show="canAccessNotifyFeature && (currentAgentType === 'all' || currentAgentType === 'notify')"
            class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors group relative"
            :class="{ 'bg-white/80 shadow-sm': selectedAgent === agent.report_type, 'agent-working-border': agent.status === 'running' }"
            @click="handleAgentClick(agent)">
            <div class="flex items-center gap-3">
              <div class="w-2 h-full self-stretch bg-amber-100 rounded-full"></div>
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium truncate text-slate-800">{{ agent.role }}</h3>
                <!-- <p class="text-xs text-slate-500">{{ agent.role }}</p> -->
                <div class="flex items-center gap-1.5 mt-1">
                  <Badge :variant="getAgentStatusVariant(agent.status)" class="text-[10px] px-1 py-0" :class="getAgentStatusBadgeClass(agent.status)">
                    {{ t(`common.agent_status_${agent.status.toLowerCase()}`, agent.status) }}
                  </Badge>
                  <span class="text-xs truncate text-slate-500">
                    {{ agent.currentTask || t('common.no_active_task') }}
                  </span>
                </div>
              </div>
            </div>
            <!-- Config Button -->
            <TooltipProvider :delay-duration="100">
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-7 w-7 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                    @click.stop="navigateToAgentConfig(agent.id)">
                    <CogIcon class="h-4 w-4 text-slate-500 hover:text-slate-700"/>
                  </Button>
                </TooltipTrigger>
              </Tooltip>
            </TooltipProvider>
          </div>
          
        </div>
      </ScrollArea>
            
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router/auto'
import { useUserStore } from '@/stores/user'
import { SearchIcon, UsersIcon, BellRingIcon, ClockIcon, LayersIcon, CogIcon } from 'lucide-vue-next'
import { Card, CardHeader, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

// --- Type Definitions ---
type BadgeVariant = 'default' | 'outline' | 'destructive' | 'secondary' | null | undefined

export interface Agent {
  name: string;
  role: string;
  status: string;
  avatar: string;
  currentTask: string | null;
  isMonitor?: boolean;
  report_type: string;
  id: string;
}

// --- Props ---
const props = defineProps<{
  agents: Agent[];
  selectedAgent: string | null | '__MONITOR__';
  agentSearch: string;
  activeTab: string;
}>()

// --- Emits ---
const emit = defineEmits<{
  (e: 'trigger-change-time', agentName: string): void;
  (e: 'update:agentSearch', value: string): void;
  (e: 'switch-to-special-tab', reportType: string): void;
  (e: 'select-dynamic-filter', filterValue: string | null | '__MONITOR__'): void;
}>()

// --- Router ---
const router = useRouter()

// --- i18n ---
const { t } = useI18n()

// --- User Store ---
const userStore = useUserStore()
const { canAccessNotifyFeature, canAccessNormalFeature, canAccessBindingFeature } = userStore

// --- Constants ---
// 定义特殊代理类型和绑定工作代理类型，参照useReportProgress.ts中的定义
const SPECIAL_AGENT_TYPES = ['SocialAgent', 'LegalAgent', 'CompositeAgent'] // 直接从useReportProgress.ts引入
const BINDING_WORK_AGENT = ['STRATEGIC', 'AWARDED', 'TENDER'] // 保持与index.vue一致

// 定义分组选项，参照index.vue
const AGENT_FILTER_OPTIONS = [
  {
    label: 'agent.filter.all',
    value: 'all',
    icon: SearchIcon
  },
  {
    label: 'agent.filter.notify',
    value: 'notify',
    icon: BellRingIcon
  },
  {
    label: 'agent.filter.normal',
    value: 'normal',
    icon: UsersIcon
  },
  {
    label: 'agent.filter.binding',
    value: 'binding',
    icon: LayersIcon
  }
]

// 当前选中的代理类型
const currentAgentType = ref('all')

// --- Computed Properties ---
// 按照过滤条件展示代理列表
const filteredDisplayAgents = computed(() => {
  const filtered = filteredLocalAgents.value
  
  switch (currentAgentType.value) {
  case 'notify':
    return filtered.filter(agent => 
      SPECIAL_AGENT_TYPES.includes(agent.report_type || '')
    )
  case 'normal':
    return filtered.filter(agent => 
      !SPECIAL_AGENT_TYPES.includes(agent.report_type || '') && 
      !BINDING_WORK_AGENT.includes(agent.report_type || '')
    )
  case 'binding':
    return filtered.filter(agent => 
      BINDING_WORK_AGENT.includes(agent.report_type || '')
    )
  default:
    return filtered
  }
})

const filteredLocalAgents = computed(() => {
  // 1. 首先应用搜索过滤
  const search = props.agentSearch.toLowerCase()
  const filtered = props.agents.filter(agent => 
    !search || 
    agent.name.toLowerCase().includes(search) || 
    (agent.role && agent.role.toLowerCase().includes(search))
  )
  
  // 2. 创建索引映射来保持原始顺序信息（先来后到）
  const originalIndexMap = new Map(
    filtered.map((agent, index) => [agent, index])
  )
  
  // 3. 排序逻辑：运行中 > 预警类型 > 其他
  return [...filtered].sort((a, b) => {
    // 运行中的 agent 有最高优先级
    const aIsRunning = a.status.toLowerCase() === 'running'
    const bIsRunning = b.status.toLowerCase() === 'running'
    
    if (aIsRunning && !bIsRunning) { return -1 }
    if (!aIsRunning && bIsRunning) { return 1 }
    
    // 如果运行状态相同，预警类型优先
    if (a.isMonitor && !b.isMonitor) { return -1 }
    if (!a.isMonitor && b.isMonitor) { return 1 }
    
    // 如果运行状态和类型都相同，保持原有顺序（先来后到）
    return originalIndexMap.get(a)! - originalIndexMap.get(b)!
  })
})

const activeAgentsCount = computed(() => {
  // 计算活跃 agent 数量
  return props.agents.filter(agent => 
    agent.status.toLowerCase() === 'running' || agent.status.toLowerCase() === 'pending'
  ).length
})

// --- Helper Functions (Copied from index.vue or adapted) ---
function getInitials(name: string | undefined): string {
  if (!name || name === t('agent.unknown_agent_name')) { return '?' }
  if (typeof name === 'string' && name.length > 0) {
    const nameParts = name.trim().split(/[\s_-]+/)
    if (nameParts.length > 1) {
      if (nameParts[0].length > 1 && nameParts[1].toLowerCase() === 'agent') { return nameParts[0][0].toUpperCase() }
      return nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase()
    }
    return name[0].toUpperCase()
  }
  return '?'
}

function getAgentStatusClass(status: string): string {
  const normalizedStatus = status ? status.toLowerCase() : 'idle'
  switch (normalizedStatus) {
  case 'running': return 'bg-blue-500'
  case 'pending': return 'bg-amber-500'
  case 'completed': case 'success': case 'idle': return 'bg-green-500'
  case 'error': return 'bg-red-500'
  default: return 'bg-slate-400'
  }
}

function getAgentStatusVariant(status: string): BadgeVariant {
  const normalizedStatus = status ? status.toLowerCase() : 'idle'
  switch (normalizedStatus) {
  case 'running': return 'secondary'
  case 'pending': return 'secondary'
  case 'completed': case 'success': case 'idle': return 'default'
  case 'error': return 'destructive'
  default: return 'outline'
  }
}

function getAgentStatusBadgeClass(status: string): string {
  const normalizedStatus = status ? status.toLowerCase() : 'idle'
  switch (normalizedStatus) {
  case 'running': return 'bg-blue-100 text-blue-700 border-0'
  case 'pending': return 'bg-amber-100 text-amber-700 border-0'
  case 'completed': case 'success': case 'idle': return 'bg-green-100 text-green-700 border-0'
  case 'error': return 'bg-red-100 text-red-700 border-0'
  default: return 'bg-slate-100 text-slate-600 border-0'
  }
}

// 根据代理类型返回头像背景颜色
function getAgentTypeClass(reportType: string): string {
  if (SPECIAL_AGENT_TYPES.includes(reportType)) {
    return 'bg-gradient-to-br from-amber-100 to-slate-100 text-amber-600'
  } 
  if (BINDING_WORK_AGENT.includes(reportType)) {
    return 'bg-gradient-to-br from-blue-100 to-slate-100 text-blue-600'
  } 
  return 'bg-gradient-to-br from-violet-100 to-slate-100 text-violet-600'
}

// 根据代理分组返回头像样式
function getAgentGroupClass(reportType: string): string {
  if (SPECIAL_AGENT_TYPES.includes(reportType)) {
    return 'bg-gradient-to-br from-amber-400 to-orange-400 text-white'
  } 
  if (BINDING_WORK_AGENT.includes(reportType)) {
    return 'bg-gradient-to-br from-blue-400 to-indigo-400 text-white'
  } 
  return 'bg-gradient-to-br from-violet-400 to-indigo-400 text-white'
}

// Note: isAgentMonitorType logic is now handled by the `isMonitor` flag passed in the agent prop.

// --- New Click Handler Method --- 
const specialAgentTypes = ['TENDER', 'AWARDED', 'STRATEGIC']

function handleAgentClick(agent: Agent) {
  if (specialAgentTypes.includes(agent.report_type)) {
    // Special agents switch to the special tab
    emit('switch-to-special-tab', agent.report_type)
  } else {
    // Standard agents apply a filter to the dynamic feed
    emit('select-dynamic-filter', agent.report_type)
  }
}

// --- New Navigation Function --- 
function navigateToAgentConfig(agentId: string) {
  console.log(`Navigating to configure agent: ${agentId}`)
  router.push({
    path: '/agent-market',
    query: { 
      configureAgentId: agentId,
      autoOpenDialog: 'true'
    }
  })
}

// --- New Helper Functions ---
function hasTypeInDisplay(type: string): boolean {
  if (type === 'notify') {
    return canAccessNotifyFeature && filteredDisplayAgents.value.some(agent =>
      SPECIAL_AGENT_TYPES.includes(agent.report_type || '')
    )
  }
  if (type === 'normal') {
    return canAccessNormalFeature && filteredDisplayAgents.value.some(agent =>
      !SPECIAL_AGENT_TYPES.includes(agent.report_type || '') &&
      !BINDING_WORK_AGENT.includes(agent.report_type || '')
    )
  }
  if (type === 'binding') {
    return canAccessBindingFeature && filteredDisplayAgents.value.some(agent =>
      BINDING_WORK_AGENT.includes(agent.report_type || '')
    )
  }
  return false
}

function getAgentsByType(type: string): Agent[] {
  if (type === 'notify' && canAccessNotifyFeature) {
    return filteredDisplayAgents.value.filter(agent =>
      SPECIAL_AGENT_TYPES.includes(agent.report_type || '')
    )
  }
  if (type === 'normal' && canAccessNormalFeature) {
    return filteredDisplayAgents.value.filter(agent =>
      !SPECIAL_AGENT_TYPES.includes(agent.report_type || '') &&
      !BINDING_WORK_AGENT.includes(agent.report_type || '')
    )
  }
  if (type === 'binding' && canAccessBindingFeature) {
    return filteredDisplayAgents.value.filter(agent =>
      BINDING_WORK_AGENT.includes(agent.report_type || '')
    )
  }
  return []
}

</script>

<style scoped>
/* Add specific styles for the sidebar if needed */
/* 新增：Agent工作状态辉光效果 */
.agent-working-border {
  animation: pulse-glow 2s infinite ease-in-out;
  /* Card组件本身应有其边框样式，此类添加辉光效果 */
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 2px 0px rgba(14, 165, 233, 0.3); /* 初始细微辉光 (sky-500) */
  }
  50% {
    box-shadow: 0 0 6px 2px rgba(14, 165, 233, 0.6); /* 更宽、更明显辉光 (sky-500) */
  }
}
</style> 