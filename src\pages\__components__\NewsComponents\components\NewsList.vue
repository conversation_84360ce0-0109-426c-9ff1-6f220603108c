<template>
  <div :class="$style['news-list-container']">
    <div 
      :class="$style['news-list-scroll']" 
      ref="containerRef"
      v-infinite-scroll="loadMoreNews"
      :infinite-scroll-disabled="isLastPage || isDailyNewsLoading"
      :infinite-scroll-distance="200"
      :infinite-scroll-delay="500"
      :infinite-scroll-immediate="false">
      <!-- Initial Loading or Content -->
      <template v-if="isInitialLoading">
        <LoadingState :count="10"/>
      </template>
      <template v-else>
        <NewsTimeline 
          :news="dailyNews"
          :flashing-ids="flashingNewsIds"
          @click-news="handleNewsClick"/>
      </template>

      <!-- Bottom Area -->
      <div :class="$style['bottom-loading-area']">
        <LoadingState 
          v-if="isLoadingMore"
          :count="2"/>
        <LastPageIndicator
          v-else-if="isLastPage"
          @to-top="scrollToTop"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useNewsStore } from '../store'
import { storeToRefs } from 'pinia'
import NewsTimeline from './NewsTimeline.vue'
import LoadingState from './LoadingState.vue'
import LastPageIndicator from './LastPageIndicator.vue'

const emit = defineEmits<{
  'click-news': [index: number]
}>()

const store = useNewsStore()
const { dailyNews, isDailyNewsLoading, isLastPage, flashingNewsIds } = storeToRefs(store)

const containerRef = ref<HTMLElement | null>(null)

// 计算属性优化条件判断
const isInitialLoading = computed(() => 
  isDailyNewsLoading.value && !dailyNews.value.length
)

const isLoadingMore = computed(() => 
  isDailyNewsLoading.value && dailyNews.value.length > 0
)

// 初始化加载数据
onMounted(async () => {
  await store.refreshDailyNews(false)
})

const loadMoreNews = async () => {
  await store.loadMoreNews()
}

const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTo({ 
      top: 0, 
      behavior: 'smooth' 
    })
  }
}

const handleNewsClick = (index: number) => {
  emit('click-news', index)
}
</script>

<style lang="scss" module>
.news-list-container {
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.news-list-scroll {
  grow: 1;
  overflow-y: auto;
  width: 100%;
  padding-right: 20px;
  padding-left: 5px;

  :global(.flashing-news-item) {
    animation: flashAnimation 3s ease-in-out;
    animation-iteration-count: 1;
  }
}

@keyframes flashAnimation {
  0% {
    background-color: transparent;
  }
  20% {
    background-color: rgba(68, 102, 188, 0.1); // --primary-color with opacity
  }
  40% {
    background-color: rgba(68, 102, 188, 0.15);
  }
  60% {
    background-color: rgba(68, 102, 188, 0.1);
  }
  80% {
    background-color: rgba(68, 102, 188, 0.05);
  }
  100% {
    background-color: transparent;
  }
}

.bottom-loading-area {
  padding: 20px 0;
  position: relative;
}
</style>
