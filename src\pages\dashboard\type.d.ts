export interface CompletedAgent {
    created_at: string;
    updated_at: string;
    company: Company;
    user: Company;
    sync_type: boolean;
    report_type: string;
    report_name: string;
    language: string;
    params: Params;
    date: string;
    summary: string;
    risk_status: string;
    tools_results: any[];
    progress: number;
    status: string;
    id: string;
    agent_name: string;
    frequency: number;
  }

export interface Agent {
    id: string;
    name: string;
    duty: string;
    avatar?: string;
    summary: string;
    type?: string;
    updated_at: string;
    report_type?: string;
    status?: string;
    frequency: number;
  }
  