<template>
  <div class="relative">
    <!-- 缩略图模式 -->
    <div v-if="thumbnail" class="flex items-start">
      <!-- 缩略图 -->
      <el-popover
        trigger="hover"
        placement="right"
        :width="600"
        :show-arrow="true"
        popper-class="radar-popover"
        :hide-after="0">
        <template #reference>
          <div :class="$style['radar-thumbnail-container']">
            <div :class="$style['radar-thumbnail']">
              <EchartsComponent 
                :params="thumbnailParams"
                :auto-size="true"
                class="w-full h-full"/>
            </div>
          </div>
        </template>
        
        <!-- 弹出框内容 -->
        <div class="p-2">
          <div class="text-lg font-medium mb-2">{{ title || chartData.title }}</div>
          <EchartsComponent 
            :params="chartParams"
            :auto-size="true"
            class="w-full h-[300px]"/>
            
          <!-- 弹出框中显示所有指标值 -->
          <div class="all-indicators mt-4 grid grid-cols-2 gap-2">
            <div v-for="(indicator, index) in allIndicatorsWithValues" :key="index" :class="$style['indicator-detail']">
              <div class="flex justify-between items-center">
                <span :class="$style['indicator-name']">{{ indicator.name }}:</span>
                <span :class="[$style['indicator-value'], getValueColorClass(indicator.value)]">{{ indicator.value }}</span>
              </div>
              <el-progress 
                :percentage="indicator.percentage" 
                :color="getProgressColor(indicator.value)"
                :stroke-width="8"
                :show-text="false"/>
            </div>
          </div>
        </div>
      </el-popover>
      
      <!-- 缩略图右侧的低分指标 - 使用grid布局 -->
      <div v-if="lowScoreIndicators.length > 0" class="ml-3" :class="$style['risk-indicators-mini']">
        <div :class="$style['risk-indicators-grid']">
          <div 
            v-for="(indicator, index) in lowScoreIndicators" 
            :key="index" 
            :class="$style['risk-indicator-item']">
            <div class="flex justify-between items-center">
              <span :class="$style['indicator-name-mini']">{{ indicator.name }}:</span>
              <span :class="[$style['indicator-value-mini'], 'text-red-500']">{{ indicator.value }}</span>
            </div>
            <el-progress 
              :percentage="indicator.percentage" 
              :color="getProgressColor(indicator.value)"
              :stroke-width="4"
              :show-text="false"
              class="mini-progress"/>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 正常模式 -->
    <div v-else class="w-full">
      <div v-if="showTitle" class="text-lg font-medium mb-2">{{ title || chartData.title }}</div>
      <EchartsComponent 
        :params="chartParams"
        :auto-size="true"
        :class="{'w-full': true, [heightClass]: true}"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue'
import EchartsComponent from '@/components/EchartsComponent.vue'
import type { ChartData } from '@/composables/useReportProgress'

const props = defineProps({
  chartData: {
    type: Object as () => ChartData,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  thumbnail: {
    type: Boolean,
    default: false
  },
  showTitle: {
    type: Boolean,
    default: true
  },
  showIndicators: {
    type: Boolean,
    default: true
  }
})

const heightClass = computed(() => `h-[${props.height}]`)

// 获取所有指标及其值
const allIndicatorsWithValues = computed(() => {
  if (!props.chartData || !props.chartData.indicators || !props.chartData.data || props.chartData.data.length === 0) {
    return []
  }
  
  const { indicators } = props.chartData
  const values = props.chartData.data[0].value
  
  return indicators.map((indicator, index) => ({
    name: indicator.name,
    value: values[index],
    percentage: (values[index] / indicator.max) * 100
  }))
})

// 获取低分指标（低于50分的）
const lowScoreIndicators = computed(() => {
  if (allIndicatorsWithValues.value.length === 0) {return []}
  
  // 筛选出低于50分的指标
  return allIndicatorsWithValues.value
    .filter(indicator => indicator.value < 50)
    .sort((a, b) => a.value - b.value)
})

// 获取关键指标（得分最低的2个）
const keyIndicators = computed(() => {
  if (allIndicatorsWithValues.value.length === 0) {return []}
  
  // 按值从小到大排序，取最小的2个
  return [...allIndicatorsWithValues.value]
    .sort((a, b) => a.value - b.value)
    .slice(0, 2)
})

// 根据值获取颜色类
const getValueColorClass = (value: number) => {
  if (value < 50) {return 'text-red-500'}
  return 'text-green-500'
}

// 获取进度条颜色 - 简化为只有风险和非风险两种颜色
const getProgressColor = (value: number) => {
  if (value < 50) {return '#F56C6C'} // 风险 - 红色
  return '#67C23A' // 非风险 - 绿色
}

// 创建50分的基准数据
const createBaselineData = computed(() => {
  if (!props.chartData || !props.chartData.indicators) {return null}
  
  const indicators = props.chartData.indicators
  return {
    name: '基准线(50分)',
    value: indicators.map(() => 50),
    lineStyle: {
      type: 'dashed',
      width: 1,
      color: '#909399'
    },
    areaStyle: {
      opacity: 0.1,
      color: '#909399'
    },
    symbol: 'none',
    itemStyle: {
      borderWidth: 0
    }
  }
})

// 完整图表参数
const chartParams = computed(() => {
  const { indicators, data } = props.chartData
  const seriesData = [...data.map(item => ({
    value: item.value,
    name: item.name,
    areaStyle: {
      opacity: 0.3
    }
  }))]
  
  // 添加基准线
  if (createBaselineData.value) {
    seriesData.push(createBaselineData.value)
  }
  
  return {
    title: props.thumbnail ? null : {
      text: props.title || props.chartData.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        let result = `${params.name}<br/>`
        params.value.forEach((val: number, index: number) => {
          if (indicators[index]) {
            result += `${indicators[index].name}: <b>${val}</b><br/>`
          }
        })
        return result
      }
    },
    legend: props.thumbnail ? null : {
      data: [...data.map(item => item.name), '基准线(50分)'],
      bottom: 0
    },
    radar: {
      indicator: indicators,
      shape: 'circle',
      splitNumber: 5,
      axisName: {
        color: '#333',
        fontSize: props.thumbnail ? 8 : 12
      },
      splitLine: {
        lineStyle: {
          color: ['#ddd', '#ccc', '#bbb', '#aaa']
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    },
    series: [
      {
        type: 'radar',
        data: seriesData
      }
    ]
  }
})

// 缩略图参数 - 简化版本
const thumbnailParams = computed(() => {
  const { indicators, data } = props.chartData
  const seriesData = [...data.map(item => ({
    value: item.value,
    name: item.name,
    areaStyle: {
      opacity: 0.3
    },
    lineStyle: {
      width: 1
    },
    itemStyle: {
      borderWidth: 0
    }
  }))]
  
  // 添加基准线
  if (createBaselineData.value) {
    seriesData.push({
      ...createBaselineData.value,
      lineStyle: {
        ...createBaselineData.value.lineStyle,
        width: 0.5
      }
    })
  }
  
  return {
    grid: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5,
      containLabel: true
    },
    radar: {
      indicator: indicators,
      shape: 'circle',
      splitNumber: 3,
      axisName: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: ['#eee']
        }
      },
      splitArea: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#eee'
        }
      }
    },
    series: [
      {
        type: 'radar',
        data: seriesData
      }
    ]
  }
})
</script>

<style lang="scss" module>
.radar-thumbnail-container {
  display: flex;
  flex-direction: column;
  width: 80px;
}

.radar-thumbnail {
  width: 80px;
  height: 80px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
  
  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.risk-indicators-mini {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 8px;
  background-color: #fff;
}

.risk-indicators-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.risk-indicator-item {
  min-width: 100px;
  min-height: 40px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.indicator-name {
  color: #666;
  white-space: nowrap;
  font-size: 0.875rem;
}

.indicator-name-mini {
  color: #666;
  font-size: 0.75rem;
  white-space: normal;
  line-height: 1.2;
  flex: 1;
}

.indicator-value {
  font-weight: 600;
  font-size: 0.875rem;
}

.indicator-value-mini {
  font-weight: 600;
  font-size: 0.75rem;
  margin-left: 4px;
}

.indicator-detail {
  padding: 6px;
  border-radius: 4px;
  background-color: #f9f9f9;
}
</style>

<style>
.radar-popover {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.mini-progress .el-progress-bar__inner {
  transition: all 0.2s;
}
</style> 