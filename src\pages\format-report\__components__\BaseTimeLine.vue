<template>
  <div :class="$style['timeline-container']">
    <div class="w-full flex items-center justify-start text-2xl font-bold text-main-color mb-2">{{ $t('formatReport.timelineTitle') }}</div>
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in items"
        :key="index"
        :timestamp="formatTime(item.time)"
        :type="type"
        :size="size"
        :hollow="hollow"
        :placement="placement">
        <template #dot v-if="$slots.dot">
          <slot name="dot" :item="item"/>
        </template>
          
        <template v-if="$slots.default">
          <slot :item="item"/>
        </template>
        <template v-else>
          <div :class="$style.content" v-html="markdownToHtml(item.content)"></div>
        </template>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>
  
<script setup lang="ts">
import { markdownToHtml } from '@/utils/markdown'
  interface TimelineItem {
    content: string
    time: string | number | Date
  }
  
  interface Props {
    items: TimelineItem[]
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    size?: 'normal' | 'large'
    placement?: 'top' | 'bottom'
    hollow?: boolean
    timeFormat?: string
  }
  
const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'normal',
  placement: 'bottom',
  hollow: false,
  timeFormat: 'YYYY-MM-DD HH:mm'
})
  
const formatTime = (time: string | number | Date): string => {
  if (time instanceof Date) {
    return time.toLocaleString()
  }
  if (typeof time === 'number') {
    return new Date(time).toLocaleString()
  }
  return time
}
</script>
  
  <style lang="scss" module>
  .timeline-container {
    width: 100%;
    max-width: 1150px;
    margin: 20px auto;
    padding: 20px;
    background: var(--main-bg-color);
    border-radius: var(--radius-default);
    box-shadow: var(--shadow-normal);
  }

  .content {

  }
  </style>