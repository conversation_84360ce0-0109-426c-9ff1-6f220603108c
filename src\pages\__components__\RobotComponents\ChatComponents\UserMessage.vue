<template>
  <div class="ml-auto flex items-center justify-end w-full">
    <div class="bg-[#BDEC59] text-gray-800 p-4 rounded-lg shadow-lg text-[16px] mr-[20px]">
      {{ content }}
    </div>
    <UserAvatar/>
  </div>
</template>
  
<script lang="ts" setup>
import UserAvatar from '@/components/LayoutComponents/UserAvatar.vue'
import { defineProps } from 'vue'
  
const props = defineProps<{
    content: string;
  }>()
</script>