import { ref, watch, onUnmounted, type Ref, readonly } from 'vue'
import { useWebSocket } from './useWebSocket'
import type { SocketIOInstance } from './useWebSocket'

export interface SafeWebSocketOptions {
  autoReconnect?: boolean
  debug?: boolean
  onError?: (error: Error) => void
}

export function useSafeWebsocket<T>(
  eventHandlers: Record<string, (data: T) => void>,
  options: SafeWebSocketOptions = {}
): {
  socket: SocketIOInstance<T>
  isConnected: Ref<boolean>
  isConnecting: Ref<boolean>
  lastError: Ref<Error | null>
} {
  const { autoReconnect = true, debug = false, onError } = options

  const socket = useWebSocket<T>()
  const isConnected = ref(socket.isConnected.value)
  const isConnecting = ref(socket.isConnecting.value)
  const lastError = ref<Error | null>(null)

  const log = (...args: any[]) => {
    if (debug) {
      console.log('[SafeWebSocket]', ...args)
    }
  }

  const setupEventListeners = () => {
    Object.keys(eventHandlers).forEach(event => {
      socket.off(event) // 先移除旧的监听器
      socket.on(event, async (data: T) => {
        try {
          log(`Received event: ${event}`, data)
          await eventHandlers[event](data)
        } catch (error) {
          log(`Error handling event ${event}:`, error)
          lastError.value = error as Error
          onError?.(error as Error)
        }
      })
    })
  }

  watch(() => socket.isConnected.value, (newStatus) => {
    isConnected.value = newStatus
    if (newStatus) {
      log('Connected, setting up event listeners')
      lastError.value = null
      setupEventListeners()
    } else {
      log('Disconnected')
      if (autoReconnect) {
        log('Attempting to reconnect...')
        socket.connect().catch(error => {
          log('Reconnection failed:', error)
          lastError.value = error
          onError?.(error)
        })
      }
    }
  })

  watch(() => socket.isConnecting.value, (connecting) => {
    isConnecting.value = connecting
    log(connecting ? 'Connecting...' : 'Connection attempt complete')
  })

  onUnmounted(() => {
    log('Cleaning up event listeners')
    Object.keys(eventHandlers).forEach(event => {
      socket.off(event)
    })
  })

  if (isConnected.value) {
    log('Initially connected, setting up event listeners')
    setupEventListeners()
  }

  return {
    socket,
    isConnected: readonly(isConnected),
    isConnecting: readonly(isConnecting),
    lastError: readonly(lastError)
  }
} 