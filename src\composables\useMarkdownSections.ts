import { computed, type Ref } from 'vue'
import type { MarkdownData, MarkdownKeyType } from '@/types/intelligence'
import { isEmpty } from 'lodash-es'

export const MARKDOWN_SECTIONS = {
  fixedSections: [
    { key: 'topic_name' as const, titleKey: 'intelligence.markdown.topic' },
    { key: 'summary' as const, titleKey: 'intelligence.markdown.summary' },
  ],
  rewritableSections: [
    { key: 'deep_analysis' as const, titleKey: 'intelligence.markdown.analysis' },
    { 
      key: 'opportunity_and_risk' as const, 
      titleKey: 'intelligence.markdown.potentialOpportunities' 
    },
    { 
      key: 'suggestion_list' as const, 
      titleKey: 'intelligence.markdown.suggestions' 
    },
  ]
} as const

export function useMarkdownSections(markdownData: Ref<MarkdownData>) {
  // Get all sections sorted
  const sortedMarkdownSections = computed(() => [
    ...MARKDOWN_SECTIONS.fixedSections,
    ...MARKDOWN_SECTIONS.rewritableSections,
  ])

  // Check if section should be displayed
  const shouldDisplaySection = (key: MarkdownKeyType): boolean => {
    return !isEmpty(markdownData.value[key])
  }

  // Get only visible sections
  const visibleSections = computed(() => 
    sortedMarkdownSections.value.filter(section => 
      shouldDisplaySection(section.key)
    )
  )

  return {
    sortedMarkdownSections,
    visibleSections,
    shouldDisplaySection
  }
} 