import type { CSSProperties, Component } from 'vue'
import type { Placement } from 'element-plus'

// 菜单配置项接口
export interface MenuConfigItem {
  id: string
  status: boolean
  tour_completed: boolean
}

// 主题配置接口
export interface STourThemeConfig {
  // 主色调
  primaryColor?: string
  // 遮罩颜色
  maskColor?: string
  // 自定义样式
  customStyles?: {
    card?: CSSProperties
    arrow?: CSSProperties
    mask?: CSSProperties
    button?: CSSProperties
  }
}

// 单个步骤配置
export interface STourStep {
  id: string
  target: string
  title: string
  description: string
  placement?: 'top' | 'right' | 'bottom' | 'left'
  mask?: boolean
  theme?: STourThemeConfig
  customContent?: any
}

// API 配置接口
export interface STourApiConfig {
  // 是否启用API集成
  enabled: boolean
  // 获取导览配置的接口URL
  configUrl?: string
  // 更新进度的接口URL
  progressUrl?: string
  // 自定义请求头
  headers?: Record<string, string>
  // 版本控制
  version?: string
}

// 导览进度状态
export interface STourProgress {
  // 完成的步骤ID
  completedSteps: (string | number)[]
  // 当前步骤索引
  currentStep: number
  // 最后访问时间
  lastVisited?: Date
  // 是否已完成
  isCompleted: boolean
}

// 事件处理器接口
export interface STourEvents {
  onStart?: () => void
  onFinish?: () => void
  onClose?: () => void
  onStepChange?: (currentStep: number) => void
}

// 主配置接口
export interface STourConfig {
  // 是否启用导览
  enabled: boolean
  // 步骤配置
  steps: STourStep[]
  // 主题配置
  theme?: STourThemeConfig
  // API配置
  api?: STourApiConfig
  // 是否在完成后自动禁用
  disableAfterComplete?: boolean
  // 是否持久化进度
  persistProgress?: boolean
  // 自定义类名
  customClass?: string
  // 是否显示关闭按钮
  showClose?: boolean
  // 是否显示步骤指示器
  showIndicators?: boolean
  // 自定义指示器组件
  customIndicator?: Component
  // 事件处理器
  events?: STourEvents
}

// 单个Tour配置
export interface TourInstance {
  id: string
  title: string
  steps: STourStep[]
  theme?: STourThemeConfig
  completed?: boolean
}

// 修改模块导览配置接口
export interface ModuleTourConfig {
  moduleId: string
  steps: STourStep[]
  theme?: STourThemeConfig
}

// 组件Props接口
export interface STourProps extends STourConfig {
  visible?: boolean
}
