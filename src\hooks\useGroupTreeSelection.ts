import type { ElTreeSelect } from 'element-plus'
import { isNil } from 'lodash-es'
import { computed, ref, toValue, watchEffect, type MaybeRef, type Ref } from 'vue'

// 定义节点接口
interface Node {
  id: number;
  label: string;
  disabled?: boolean;
  children?: Node[];
  originalDisabled?: boolean;
}

// 定义选择结果接口
interface SelectionResult {
  groups: number[];
  values: number[];
}

// 导出用于处理组树选择的函数
export function useGroupTreeSelection(nodes: MaybeRef<Node[]>, treeRef?: Ref<InstanceType<typeof ElTreeSelect>>) {
  // 定义选中的节点ID
  const selectedIds = ref<string[]>([])

  // 生成唯一ID的函数
  const generateUniqueId = (groupId: number, nodeId: number) => `${groupId}:${nodeId}`

  // 存储子节点到组的映射
  const childToGroups: Map<number, number[]> = new Map()
  // 存储组到子节点的映射
  const groupMap: Map<number, number[]> = new Map()
  const groupItemMap: Map<number, Node> = new Map()
  const childItemMap: Map<number, Node> = new Map()
  const rawNodes = computed(() => toValue(nodes))

  watchEffect(() => {
    rawNodes.value.forEach(group => {
      const childIds: number[] = []
      group.children?.forEach(child => {
        if (!childToGroups.has(child.id)) {
          childToGroups.set(child.id, [])
        }
        childToGroups.get(child.id)?.push(group.id)
        childIds.push(child.id)
        childItemMap.set(child.id, child)
      })
      groupMap.set(group.id, childIds)
      groupItemMap.set(group.id, group)
    })
  })

  const setNodeData = (key: string | number, value: Partial<Node> | ((data: Node) => Partial<Node>)) => {
    if (!treeRef?.value) {
      return
    }
    const node = treeRef.value.getNode(key)
    if (!node) {
      return
    }
    const data = value instanceof Function ? value(node.data as Node) : value
    node.setData({
      ...node.data,
      ...data
    })
  }

  let oldValues: string[] = selectedIds.value
  const setSelectedIds = (newValues: string[], required?: string[]) => {
    // 初始化所有选中的唯一ID集合，包含新的值
    const allSelectedIds: Set<string> = new Set(newValues)
    const deleteId = (id: string) => {
      if (required && required.includes(id)) {
        return
      }
      allSelectedIds.delete(id)
    }

    // 遍历旧的值，如果新的值中不包含旧的值，表示删除，需要把所有分组的该子节点删除选中
    oldValues.forEach((val) => {
      if (!newValues.includes(val)) {
        const [groupId, childId] = `${val}`.split(':').map(x => parseInt(x))
        // 取消的是子节点，需要把所有分组的该子节点删除选中
        if (childId) {
          const childUniqueIds = childToGroups.get(childId)?.map(gid => generateUniqueId(gid, childId))
          childUniqueIds?.forEach(deleteId)
          return
        }
        // 取消的是分组，需要把分组的disabled还原
        groupMap.get(groupId)?.forEach(childId => {
          const uniqueId = generateUniqueId(groupId, childId)
          // 恢复子节点的原始disabled
          setNodeData(uniqueId, (data) => {
            return {
              disabled: data.originalDisabled || false,
            }
          })
        })
      }
    })

    allSelectedIds.forEach(val => {
      const [, childId] = `${val}`.split(':').map(x => parseInt(x))
      // 把其他组的所有当前子节点添加为选中
      if (childId) {
        childToGroups.get(childId)?.forEach(groupId => {
          const uniqueId = generateUniqueId(groupId, childId)
          allSelectedIds.add(uniqueId)
        })
      }
    })

    allSelectedIds.forEach(val => {
      const [groupId, childId] = `${val}`.split(':').map(x => parseInt(x))
      if (childId) {
        return
      }
      // 如果不存在子ID，表示选中的是分组，需要把分组的所有子节点删除选中
      groupMap.get(groupId)?.forEach(childId => {
        const uniqueId = generateUniqueId(groupId, childId)
        deleteId(uniqueId)
        // 禁用分组的子节点
        setNodeData(uniqueId, (data) => {
          return {
            disabled: true,
            originalDisabled: isNil(data.originalDisabled) ? (data.disabled || false) : data.originalDisabled
          }
        })
      })

    })

    // 更新选中的ID数组，并将旧的值设置为当前选中的ID数组
    selectedIds.value = Array.from(allSelectedIds)
    oldValues = selectedIds.value
  }
  // 定义计算属性model
  const model = computed({
    get: () => selectedIds.value,
    set: (newValues: string[]) => {
      setSelectedIds(newValues)
    }
  })

  // 定义用于UI显示的计算属性
  const displayNodes = computed(() => {
    return rawNodes.value.map(group => ({
      ...group,
      disabled: group.disabled || false,
      children: group.children?.map(child => ({
        ...child,
        id: generateUniqueId(group.id, child.id)
      }))
    }))
  })

  // 获取实际选中的数据
  const getRealSelectedData = (): SelectionResult => {
    const groupsSet: Set<number> = new Set()
    const valuesSet: Set<number> = new Set()

    selectedIds.value.forEach(id => {
      const [groupIdStr, nodeIdStr] = `${id}`.split(':').map(x => parseInt(x))
      if (nodeIdStr) {
        valuesSet.add(nodeIdStr)
        return
      }
      groupsSet.add(groupIdStr)
    })

    return {
      groups: Array.from(groupsSet),
      values: Array.from(valuesSet)
    }
  }

  const selectedItems = computed(() => {
    const selectedData = getRealSelectedData()
    const items = [
      ...selectedData.groups.map(groupId => groupItemMap.get(groupId)),
      ...selectedData.values.map(valueId => childItemMap.get(valueId))
    ]
    return items.filter(item => !!item) as Node[]
  })

  // 将实际数据转换为v-model数据
  const convertToModelData = (data: SelectionResult): string[] => {
    const modelData: string[] = []
    data.groups.forEach(groupId => modelData.push(groupId.toString()))
    data.values.forEach(valueId => {
      childToGroups.get(valueId)?.forEach(groupId => {
        modelData.push(generateUniqueId(groupId, valueId))
      })
    })
    return modelData
  }

  return { model, displayNodes, selectedItems, setSelectedIds, getRealSelectedData, convertToModelData }
}
