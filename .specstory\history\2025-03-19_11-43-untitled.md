<!-- Generated by SpecStory -->

# Untitled (2025-03-19 19:43:35)

_**User**_

需要在 @DashboardComponents 中封装一个Numbinput的dialog弹窗组件，这个组件主要是让用户输出任务在服务器定时任务的间隔时间，当用户点击 @SpecialAgentCard.vue 的时候会弹出dialog，用户选择后，用户点击确认 ， emit数据到 @index.vue  会执行changeTime的post请求，参数为{agent_id,frequency} 

---

_**Assistant**_

```thinking
好的，我需要为 DashboardComponents 中创建一个带有数字输入的弹窗组件，实现定时任务的时间间隔设置功能。首先，让我理解当前的工作流程：

1. 用户点击 SpecialAgentCard.vue 中的时钟图标
2. 弹出一个包含数字输入框的对话框组件
3. 用户输入定时任务的时间间隔
4. 用户点击确认后，数据会被 emit 到 index.vue 
5. index.vue 会使用这些数据执行 changeTime 的 POST 请求，参数为 {agent_id, frequency}

让我先查看相关文件：
1. SpecialAgentCard.vue - 这里已经有一个处理点击时钟图标的函数 handleChangeTime
2. index.vue - 这里已经有一个 changeTime 的 POST 请求函数

现在我需要创建一个新的组件 "TimeIntervalDialog.vue"，放在 DashboardComponents 文件夹中。

这个组件需要：
1. 使用 Element Plus 的 Dialog 和 Input Number 组件
2. 接收显示/隐藏的控制 prop
3. 接收当前 agent 信息的 prop
4. 提供输入框让用户输入时间间隔（频率）
5. 确认和取消按钮
6. 在确认时向父组件 emit 数据，包含 agent_id 和 frequency

然后需要在 index.vue 中引入这个组件，并处理从组件传出的事件。

让我开始编写这个组件：
```

# 创建定时任务间隔设置弹窗组件

我将为您创建一个数字输入对话框组件，用于设置特殊代理的定时任务间隔时间。

以下是具体实现方案：

1. 首先，我们需要创建一个新的 `TimeIntervalDialog.vue` 组件
2. 然后在 `SpecialAgentCard.vue` 中正确触发该对话框
3. 最后在 `dashboard/index.vue` 中处理提交的数据

## 步骤 1: 创建 TimeIntervalDialog.vue 组件

让我们首先创建这个新组件：

```vue:src/pages/__components__/DashboardComponents/TimeIntervalDialog.vue
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('agent.set_interval_time')"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose">
    <div class="flex flex-col gap-4">
      <div class="text-sm text-gray-600">
        {{ $t('agent.interval_description') }}
      </div>
      
      <el-form :model="form" label-position="top">
        <el-form-item :label="$t('agent.frequency_hours')">
          <el-input-number
            v-model="form.frequency"
            :min="1"
            :max="168"
            :step="1"
            controls-position="right"
            style="width: 100%"
            :placeholder="$t('agent.enter_hours')" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <el-button @click="handleClose">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">
          {{ $t('common.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import type { PropType } from 'vue';

interface Agent {
  id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agent: {
    type: Object as PropType<Agent>,
    default: () => ({})
  },
  currentFrequency: {
    type: Number,
    default: 24 // 默认为24小时
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 计算属性用于控制对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const form = ref({
  frequency: props.currentFrequency
});

// 提交状态
const isSubmitting = ref(false);

// 当agent或currentFrequency变化时，更新表单
watch(() => props.currentFrequency, (newValue) => {
  form.value.frequency = newValue;
});

// 处理关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 处理提交
const handleSubmit = () => {
  if (!form.value.frequency || form.value.frequency < 1) {
    return;
  }
  
  isSubmitting.value = true;
  
  // 向父组件发送数据
  emit('confirm', {
    agent_id: props.agent.id,
    frequency: form.value.frequency
  });
  
  // 实际应用中，这应该在父组件中的回调中处理
  setTimeout(() => {
    isSubmitting.value = false;
    handleClose();
  }, 300);
};
</script>

<style lang="scss" module>
// 可以添加自定义样式
</style>
```

## 步骤 2: 修改 SpecialAgentCard.vue 以使用该对话框

现在我们需要修改 SpecialAgentCard.vue 中的 handleChangeTime 方法，使其通知父组件打开对话框：

```vue:src/pages/__components__/DashboardComponents/SpecialAgentCard.vue
// ... existing code ...
<Icon icon="mdi:timer-outline"
      width="20"
      height="20"
      class="cursor-pointer hover:text-main-color"
      @click="handleChangeTime($event)"/>
// ... existing code ...

const handleChangeTime = (event: Event) => {
  event.stopPropagation()
  emit('changeTime', props.agent)
}
// ... existing code ...
```

## 步骤 3: 更新 dashboard/index.vue 以集成对话框

最后，我们需要在 dashboard/index.vue 中添加该对话框组件并处理它的事件：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
import TimeIntervalDialog from '@/pages/__components__/DashboardComponents/TimeIntervalDialog.vue'
// ... existing code ...

// 添加状态管理变量
const showTimeIntervalDialog = ref(false)
const selectedAgent = ref<Agent | null>(null)
const currentFrequency = ref(24) // 默认值，可以根据需要调整

// 修改handleChangeTime函数
const handleChangeTime = (agent: Agent) => {
  selectedAgent.value = agent
  // 如果有必要，这里可以获取当前的frequency值
  // currentFrequency.value = agent.frequency || 24
  showTimeIntervalDialog.value = true
}

// 处理提交
const handleTimeIntervalSubmit = async (data: { agent_id: string, frequency: number }) => {
  try {
    await changeTime(0, {
      id: data.agent_id,
      frequency: data.frequency
    })
    
    ElMessage.success(t('agent.update_time_success'))
    // 更新代理列表
    getAgentList()
    
  } catch (error) {
    console.error('Failed to update time interval:', error)
    ElMessage.error(t('agent.update_time_error'))
  }
}

// 在模板中添加对话框组件
</template>
  <!-- 其他现有内容 -->
  
  <!-- 添加时间间隔对话框 -->
  <TimeIntervalDialog
    v-model:visible="showTimeIntervalDialog"
    :agent="selectedAgent || {}"
    :current-frequency="currentFrequency"
    @confirm="handleTimeIntervalSubmit" />
  
  <!-- 其他现有内容 -->
</template>
// ... existing code ...
```

这样，整个流程就完成了：
1. 用户点击 SpecialAgentCard 上的时钟图标
2. SpecialAgentCard 触发 changeTime 事件
3. dashboard/index.vue 接收事件，显示 TimeIntervalDialog
4. 用户输入频率并确认
5. TimeIntervalDialog 触发 confirm 事件
6. dashboard/index.vue 接收 confirm 事件数据，调用 changeTime API

这样用户就可以设置特殊代理的定时任务间隔时间了。

需要注意的是，您可能需要添加相应的国际化翻译，确保界面文本根据当前语言正确显示。

---

_**User**_

图中的dialog的UI样式太丑了，请你重新设计，

并且间隔时间需要以天为单位，而不是小时

---

_**Assistant**_

```thinking
我需要创建一个更美观的时间间隔对话框组件。根据用户的需求，主要要点是：

1. 现有的对话框UI不够美观，需要重新设计
2. 需要将时间间隔单位从小时改为天

从截图中可以看到当前的对话框设计非常简单，只有一个基本的输入框和两个按钮。界面看起来比较朴素，没有特别的设计元素。

我会对TimeIntervalDialog.vue进行以下改进：

1. 改善整体视觉设计，使用更现代的UI元素
2. 将输入单位从小时改为天
3. 增加更丰富的视觉反馈和交互效果
4. 优化布局和间距

看起来现有的对话框使用了Element Plus的组件库，我会继续使用这些组件，但会添加更多的样式和交互元素来提升视觉效果。

让我开始设计新的TimeIntervalDialog.vue组件:
```

# 重新设计时间间隔设置对话框

根据您的需求，我已经重新设计了一个更加美观的时间间隔对话框，并且将时间单位从小时改为天。

以下是改进后的代码实现：

```vue:src/pages/__components__/DashboardComponents/TimeIntervalDialog.vue
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('agent.set_interval_time')"
    width="450px"
    :close-on-click-modal="false"
    :class="$style['time-interval-dialog']"
    @close="handleClose">
    
    <div :class="$style['dialog-content']">
      <!-- 图标和说明部分 -->
      <div :class="$style['header-section']">
        <div :class="$style['icon-container']">
          <Icon icon="mdi:timer-outline" :class="$style['timer-icon']" />
        </div>
        <p :class="$style['description']">
          {{ $t('agent.interval_description_days') }}
        </p>
      </div>
      
      <!-- 定时天数选择部分 -->
      <div :class="$style['selection-container']">
        <p :class="$style['selection-label']">{{ $t('agent.select_days') }}</p>
        
        <!-- 快速选择按钮组 -->
        <div :class="$style['quick-select-buttons']">
          <el-button 
            v-for="day in quickSelectDays" 
            :key="day"
            :type="form.frequency === day ? 'primary' : 'default'"
            :class="[$style['day-button'], form.frequency === day ? $style['active'] : '']"
            @click="selectDays(day)">
            {{ day }} {{ $t('agent.days') }}
          </el-button>
        </div>
        
        <!-- 自定义输入 -->
        <div :class="$style['custom-input-container']">
          <p :class="$style['custom-input-label']">{{ $t('agent.custom_days') }}</p>
          <el-input-number
            v-model="form.frequency"
            :min="1"
            :max="365"
            :step="1"
            controls-position="right"
            :class="$style['days-input']"
            :placeholder="$t('agent.enter_days')" />
        </div>
        
        <!-- 当前配置信息 -->
        <div :class="$style['current-config']" v-if="props.agent?.name">
          <div :class="$style['agent-name']">
            <Icon icon="mdi:robot-outline" :class="$style['agent-icon']" />
            <span>{{ props.agent.name }}</span>
          </div>
          <div :class="$style['config-details']">
            {{ $t('agent.will_run_every') }} <span :class="$style['days-highlight']">{{ form.frequency }}</span> {{ $t('agent.days') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div :class="$style['dialog-footer']">
        <el-button @click="handleClose" :class="$style['cancel-button']">
          {{ $t('common.cancel') }}
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="isSubmitting"
          :class="$style['confirm-button']">
          {{ $t('common.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import type { PropType } from 'vue';
import { Icon } from '@iconify/vue';

interface Agent {
  id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agent: {
    type: Object as PropType<Agent>,
    default: () => ({})
  },
  currentFrequency: {
    type: Number,
    default: 1 // 默认为1天，原来是24小时
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 计算属性用于控制对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const form = ref({
  frequency: props.currentFrequency
});

// 快速选择天数选项
const quickSelectDays = [1, 3, 7, 14, 30];

// 提交状态
const isSubmitting = ref(false);

// 当agent或currentFrequency变化时，更新表单
watch(() => props.currentFrequency, (newValue) => {
  form.value.frequency = newValue;
});

// 快速选择天数
const selectDays = (days: number) => {
  form.value.frequency = days;
};

// 处理关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 处理提交
const handleSubmit = () => {
  if (!form.value.frequency || form.value.frequency < 1) {
    return;
  }
  
  isSubmitting.value = true;
  
  // 向父组件发送数据
  emit('confirm', {
    agent_id: props.agent.id,
    frequency: form.value.frequency
  });
  
  // 实际应用中，这应该在父组件中的回调中处理
  setTimeout(() => {
    isSubmitting.value = false;
    handleClose();
  }, 300);
};
</script>

<style lang="scss" module>
.time-interval-dialog {
  :global(.el-dialog__header) {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    
    :global(.el-dialog__title) {
      font-weight: 600;
      font-size: 18px;
      color: #1e293b;
    }
  }
  
  :global(.el-dialog__body) {
    padding: 24px;
  }
  
  :global(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.icon-container {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  display: flex;
  justify-content: center;
  align-items: center;
}

.timer-icon {
  width: 32px;
  height: 32px;
  color: var(--el-color-primary);
}

.description {
  color: #475569;
  font-size: 14px;
  line-height: 1.5;
  max-width: 360px;
}

.selection-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.selection-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 15px;
  margin-bottom: 4px;
}

.quick-select-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.day-button {
  flex: 1;
  min-width: 80px;
  
  &.active {
    font-weight: 600;
  }
}

.custom-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.custom-input-label {
  font-size: 14px;
  color: #64748b;
}

.days-input {
  width: 100%;
}

.current-config {
  margin-top: 16px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px dashed rgba(0, 0, 0, 0.1);
}

.agent-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 8px;
}

.agent-icon {
  width: 18px;
  height: 18px;
  color: #64748b;
}

.config-details {
  font-size: 14px;
  color: #64748b;
}

.days-highlight {
  font-weight: 600;
  color: var(--el-color-primary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirm-button {
  min-width: 100px;
}

.cancel-button {
  min-width: 100px;
  border-color: #e2e8f0;
}
</style>
```

## 调整 index.vue 中的处理逻辑

我们需要稍微修改一下 index.vue 中的 handleTimeIntervalSubmit 函数，将其适配为天数而不是小时：

```vue:src/pages/dashboard/index.vue
// 处理提交
const handleTimeIntervalSubmit = async (data: { agent_id: string, frequency: number }) => {
  try {
    await changeTime(0, {
      id: data.agent_id,
      frequency: data.frequency
    })
    
    ElMessage.success(t('agent.update_time_success'))
    // 更新代理列表
    getAgentList()
    
  } catch (error) {
    console.error('Failed to update time interval:', error)
    ElMessage.error(t('agent.update_time_error'))
  }
}
```

## 需要添加的国际化翻译

您可能需要在翻译文件中添加以下翻译键值：

```
agent.set_interval_time: 设置定时任务间隔
agent.interval_description_days: 设置代理自动执行的时间间隔（天数）。代理将按照设定的频率自动更新相关信息。
agent.select_days: 选择间隔天数
agent.custom_days: 自定义天数
agent.days: 天
agent.enter_days: 输入天数
agent.will_run_every: 代理将每
agent.update_time_success: 更新定时任务成功
agent.update_time_error: 更新定时任务失败
```

## 新设计的特点

1. 现代化的UI设计，包含图标和更清晰的视觉层次
2. 时间单位从小时改为天
3. 添加了快速选择按钮，便于用户快速选择常用天数
4. 增加了当前配置信息的预览，让用户更清楚将应用的设置
5. 更好的视觉层次和间距，提高了可读性和美观度

这个设计既保持了简单易用，又增加了必要的视觉引导和反馈，使界面更加直观和美观。

---

