// vite.config.ts
import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite@5.3.3_@types+node@20.1_874bbde7e8d556898131f33ef073753c/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vi_3f914bbac8852a9278faf438b0192d6f/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/@vitejs+plugin-vue-jsx@3.1._6b4a474ca23a7df2b5491b39c7e93e2a/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import VueDevTools from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-vue-devtools@7._5ab42e16e277860fa16c91aa1b148260/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import viteCompression from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-compression@0.5_21282f6bf0c99dedcc79b64ca53f6aea/node_modules/vite-plugin-compression/dist/index.mjs";
import VueRouter from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/unplugin-vue-router@0.8.8_r_3f01a3ffaca05dcdff2855c7e29eace6/node_modules/unplugin-vue-router/dist/vite.mjs";
import Layouts from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-vue-layouts@0.1_04cb19705de9fec2177e0a8191eafdaa/node_modules/vite-plugin-vue-layouts/dist/index.mjs";
import PreprocessorDirectives from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/unplugin-preprocessor-direc_668ffac9c17c2fc85dc50dbbf06864a1/node_modules/unplugin-preprocessor-directives/dist/vite.js";

// src/vue-plugins/vite-plugin-cdn2.ts
import { cdn } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-cdn2@1.1.0_rollup@4.18.1/node_modules/vite-plugin-cdn2/dist/index.mjs";
import { defineResolve } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-cdn2@1.1.0_rollup@4.18.1/node_modules/vite-plugin-cdn2/dist/resolve.mjs";
var CDN_POOL = [
  "https://cdn.jsdelivr.net/npm/",
  "https://unpkg.com/",
  "https://fastly.jsdelivr.net/npm/",
  "https://gcore.jsdelivr.net/npm/"
];
function vitePluginCDN2(options) {
  const cdnPlugin = {
    name: "vite-plugin-cdn2-wrapper",
    // 将vite-plugin-cdn2的插件内容整合到这个插件中
    ...cdn({
      modules: options.modules.map((module) => {
        if (module.path) {
          return {
            name: module.name,
            var: module.var || module.name,
            path: module.path,
            css: module.css
          };
        }
        return {
          name: module.name,
          var: module.var,
          global: module.global,
          relativeModule: module.relativeModule,
          aliases: module.aliases
        };
      }),
      resolve: defineResolve({
        name: "resolve:cdn-fallback",
        setup: ({ extra }) => {
          const { version, name, relativeModule } = extra;
          const path2 = `${name}@${version}/${relativeModule || ""}`;
          return {
            url: `${CDN_POOL[0]}${path2}`,
            injectTo: "head-prepend",
            attrs: {
              crossOrigin: "anonymous",
              referrerpolicy: "no-referrer"
            }
          };
        }
      })
    })
  };
  return cdnPlugin;
}

// src/vue-plugins/cdnFallback.ts
var CDN_POOL2 = [
  "https://cdn.jsdelivr.net/npm/",
  "https://unpkg.com/",
  "https://fastly.jsdelivr.net/npm/",
  "https://gcore.jsdelivr.net/npm/"
];
function cdnFallbackPlugin(options = {}) {
  const enableDebugLog = options.enableDebugLog ?? true;
  const cdnPool = options.cdnPool ?? CDN_POOL2;
  const preloadPackages2 = options.preloadPackages ?? [];
  return {
    name: "vite-plugin-cdn-fallback",
    apply: "build",
    // 默认仅在build模式下应用
    configResolved(config) {
      if (config.command === "serve" && config.mode === "development") {
        console.log("[CDN Fallback] \u5F00\u53D1\u73AF\u5883\u5DF2\u542F\u7528CDN\u964D\u7EA7\u529F\u80FD");
      }
    },
    transformIndexHtml(html) {
      const scriptContent = `
      // CDN\u964D\u7EA7\u5904\u7406\u811A\u672C
      ;(function() {
        // \u8BE6\u7EC6\u7684\u8C03\u8BD5\u65E5\u5FD7\u5F00\u5173
        const ENABLE_DEBUG_LOG = ${enableDebugLog};
        
        // CDN \u6C60\u914D\u7F6E
        const CDN_POOL = ${JSON.stringify(cdnPool)};
        
        // \u7528\u4E8E\u8DDF\u8E2A\u5DF2\u7ECF\u5C1D\u8BD5\u964D\u7EA7\u7684\u8D44\u6E90
        const attemptedFallbacks = new Set();
        
        // \u8C03\u8BD5\u65E5\u5FD7
        function debugLog(...args) {
          if (ENABLE_DEBUG_LOG) {
            console.log('[CDN Fallback]', ...args);
          }
        }
        
        // CDN \u964D\u7EA7\u52A0\u8F7D\u51FD\u6570
        window.loadScriptWithFallback = function(packageName, version, path, index = 0) {
          if (index >= CDN_POOL.length) {
            console.error('[CDN Fallback] \u6240\u6709 CDN \u6E90\u52A0\u8F7D\u5931\u8D25:', packageName);
            return;
          }
          
          const cdn = CDN_POOL[index];
          const script = document.createElement('script');
          script.crossOrigin = 'anonymous';
          script.referrerPolicy = 'no-referrer';
          const fullPath = \`\${cdn}\${packageName}@\${version}/\${path}\`;
          script.src = fullPath;
          
          debugLog(\`\u5C1D\u8BD5\u4ECE \${cdn} \u52A0\u8F7D \${packageName}@\${version}/\${path}\`);
          
          // \u4F7F\u7528\u8D44\u6E90\u8BA1\u65F6API\u6765\u6D4B\u91CF\u52A0\u8F7D\u65F6\u95F4
          const startTime = performance.now();
          
          script.onload = function() {
            const loadTime = performance.now() - startTime;
            debugLog(\`\u6210\u529F\u52A0\u8F7D \${fullPath}\uFF0C\u8017\u65F6 \${loadTime.toFixed(2)}ms\`);
          };
          
          script.onerror = function(e) {
            const elapsedTime = performance.now() - startTime;
            console.warn(\`[CDN Fallback] \${cdn} \u52A0\u8F7D\u5931\u8D25 (\${elapsedTime.toFixed(2)}ms)\uFF0C\u5C1D\u8BD5\u4E0B\u4E00\u4E2A\u6E90...\`);
            
            // \u9632\u6B62\u91CD\u590D\u5C1D\u8BD5\u76F8\u540C\u7684\u8D44\u6E90
            attemptedFallbacks.add(fullPath);
            
            // \u5C1D\u8BD5\u4E0B\u4E00\u4E2ACDN
            loadScriptWithFallback(packageName, version, path, index + 1);
          };
          
          document.head.appendChild(script);
          return script;
        };
      
        // \u9884\u52A0\u8F7D\u6240\u6709CDN\u6C60\u4E2D\u7684\u8D44\u6E90
        function preloadResource(cdn) {
          // \u521B\u5EFA\u9884\u52A0\u8F7D\u94FE\u63A5
          const link = document.createElement('link');
          link.rel = 'preconnect';
          link.href = cdn;
          link.crossOrigin = 'anonymous';
          document.head.appendChild(link);
          
          debugLog(\`\u9884\u8FDE\u63A5\u5230 \${cdn}\`);
        }
        
        // \u9884\u8FDE\u63A5\u5230\u6240\u6709CDN
        CDN_POOL.forEach(cdn => {
          preloadResource(cdn);
        });

        // \u9884\u52A0\u8F7D\u6307\u5B9A\u7684\u5305
        ${JSON.stringify(preloadPackages2)}.forEach(pkg => {
          debugLog(\`\u9884\u52A0\u8F7D\u5305: \${pkg.name}@\${pkg.version}/\${pkg.path}\`);
          loadScriptWithFallback(pkg.name, pkg.version, pkg.path);
        });
        
        // \u5168\u5C40\u9519\u8BEF\u5904\u7406\uFF0C\u6355\u83B7\u6240\u6709CDN\u8D44\u6E90\u52A0\u8F7D\u9519\u8BEF\u5E76\u5C1D\u8BD5\u964D\u7EA7
        window.addEventListener('error', function(e) {
          // \u68C0\u67E5\u662F\u5426\u662F\u811A\u672C\u6216\u6837\u5F0F\u8868\u52A0\u8F7D\u9519\u8BEF
          if (e.target && (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') && (e.target.src || e.target.href)) {
            const src = e.target.src || e.target.href;
            
            // \u5982\u679C\u5DF2\u7ECF\u5C1D\u8BD5\u8FC7\u8FD9\u4E2A\u8D44\u6E90\uFF0C\u8DF3\u8FC7
            if (attemptedFallbacks.has(src)) {
              debugLog(\`\u5DF2\u7ECF\u5C1D\u8BD5\u8FC7\u964D\u7EA7: \${src}\`);
              return;
            }
            
            // \u68C0\u67E5\u662F\u5426\u5305\u542B\u5728CDN\u6C60\u4E2D\u7684\u57DF\u540D
            const matchedCdnIndex = CDN_POOL.findIndex(cdn => src.includes(cdn.replace(/\\/$/, '')));
            
            if (matchedCdnIndex !== -1) {
              debugLog(\`\u68C0\u6D4B\u5230CDN\u8D44\u6E90\u52A0\u8F7D\u9519\u8BEF: \${src}\`);
              
              // \u4ECEURL\u4E2D\u63D0\u53D6\u5305\u4FE1\u606F
              try {
                const urlObj = new URL(src);
                const pathParts = urlObj.pathname.split('/');
                
                // \u5C1D\u8BD5\u627E\u5230\u5305\u542B@\u7684\u90E8\u5206\u4F5C\u4E3A\u5305\u540D\u548C\u7248\u672C
                let packageWithVersion = null;
                for (let i = 0; i < pathParts.length; i++) {
                  if (pathParts[i].includes('@')) {
                    packageWithVersion = pathParts[i];
                    break;
                  }
                }
                
                if (packageWithVersion && packageWithVersion.includes('@')) {
                  const [packageName, version] = packageWithVersion.split('@');
                  
                  // \u6784\u5EFA\u5269\u4F59\u8DEF\u5F84
                  const packageIndex = urlObj.pathname.indexOf(packageWithVersion);
                  const remainingPath = urlObj.pathname.substring(packageIndex + packageWithVersion.length + 1);
                  
                  debugLog(\`\u89E3\u6790\u8D44\u6E90: \u5305\u540D=\${packageName}, \u7248\u672C=\${version}, \u8DEF\u5F84=\${remainingPath}\`);
                  
                  // \u4F7F\u7528 CDN \u964D\u7EA7\u52A0\u8F7D - \u4ECE\u4E0B\u4E00\u4E2ACDN\u5F00\u59CB
                  console.warn(\`[CDN Fallback] \u8D44\u6E90 \${src} \u52A0\u8F7D\u5931\u8D25\uFF0C\u5C1D\u8BD5\u4F7F\u7528\u5907\u7528CDN\u52A0\u8F7D\`);
                  e.preventDefault(); // \u963B\u6B62\u9ED8\u8BA4\u9519\u8BEF\u5904\u7406
                  
                  // \u6807\u8BB0\u4E3A\u5DF2\u5C1D\u8BD5
                  attemptedFallbacks.add(src);
                  
                  // \u4ECE\u4E0B\u4E00\u4E2ACDN\u5F00\u59CB\u5C1D\u8BD5
                  loadScriptWithFallback(packageName, version, remainingPath, matchedCdnIndex + 1);
                  return;
                }
              } catch (err) {
                console.error('[CDN Fallback] \u89E3\u6790URL\u5931\u8D25:', err, src);
              }
            }
          }
        }, true); // \u4F7F\u7528\u6355\u83B7\u9636\u6BB5
        
        // \u4E5F\u6355\u83B7\u672A\u5904\u7406\u7684Promise\u9519\u8BEF
        window.addEventListener('unhandledrejection', function(e) {
          const reason = e.reason;
          
          // \u68C0\u67E5\u662F\u5426\u662F\u7F51\u7EDC\u8BF7\u6C42\u76F8\u5173\u7684\u9519\u8BEF
          if (reason && 
              (reason.message && reason.message.includes('NetworkError') || 
               reason.message && reason.message.includes('Failed to fetch') ||
               reason.name === 'TypeError' && reason.message.includes('Failed to load'))) {
            debugLog('\u68C0\u6D4B\u5230\u672A\u5904\u7406\u7684Promise\u9519\u8BEF\uFF0C\u53EF\u80FD\u662FCDN\u8D44\u6E90\u52A0\u8F7D\u5931\u8D25:', reason);
          }
        });
        
        // DOMContentLoaded\u4E8B\u4EF6\uFF0C\u68C0\u67E5\u6240\u6709\u5DF2\u52A0\u8F7D\u7684\u811A\u672C\u662F\u5426\u6B63\u5E38\u5DE5\u4F5C
        document.addEventListener('DOMContentLoaded', function() {
          debugLog('\u9875\u9762\u52A0\u8F7D\u5B8C\u6210\uFF0C\u68C0\u67E5CDN\u8D44\u6E90');
          
          // \u5728\u8FD9\u91CC\u53EF\u4EE5\u6DFB\u52A0\u68C0\u67E5\u5173\u952E\u5E93\u662F\u5426\u6210\u529F\u52A0\u8F7D\u7684\u903B\u8F91
          setTimeout(function() {
            if (typeof Vue === 'undefined' && document.querySelector('script[src*="vue"]')) {
              console.warn('[CDN Fallback] Vue \u5E93\u4F3C\u4E4E\u672A\u6210\u529F\u52A0\u8F7D\uFF0C\u5C1D\u8BD5\u91CD\u65B0\u52A0\u8F7D');
              loadScriptWithFallback('vue', '3.3.4', 'dist/vue.global.prod.js');
            }
            
            if (typeof ElementPlus === 'undefined' && document.querySelector('script[src*="element-plus"]')) {
              console.warn('[CDN Fallback] ElementPlus \u5E93\u4F3C\u4E4E\u672A\u6210\u529F\u52A0\u8F7D\uFF0C\u5C1D\u8BD5\u91CD\u65B0\u52A0\u8F7D');
              loadScriptWithFallback('element-plus', '2.7.4', 'dist/index.full.min.js');
            }
            
            if (typeof pdfMake === 'undefined' && document.querySelector('script[src*="pdfmake"]')) {
              console.warn('[CDN Fallback] pdfMake \u5E93\u4F3C\u4E4E\u672A\u6210\u529F\u52A0\u8F7D\uFF0C\u5C1D\u8BD5\u91CD\u65B0\u52A0\u8F7D');
              loadScriptWithFallback('pdfmake', '0.2.14', 'build/pdfmake.min.js');
            }
          }, 1000);
        });
      })();
      `;
      return {
        html,
        tags: [
          {
            tag: "script",
            attrs: {
              type: "text/javascript"
            },
            children: scriptContent,
            injectTo: "head-prepend"
          }
        ]
      };
    }
  };
}

// vite.config.ts
import { visualizer } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/rollup-plugin-visualizer@5.12.0_rollup@4.18.1/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { chunkSplitPlugin } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-chunk-split@0.5_780e5e3a67da7a6f92cfc6be17046257/node_modules/vite-plugin-chunk-split/dist/index.mjs";

// vite-plugins/env.types.ts
import fg from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/fast-glob@3.3.2/node_modules/fast-glob/out/index.js";
import { loadEnv } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite@5.3.3_@types+node@20.1_874bbde7e8d556898131f33ef073753c/node_modules/vite/dist/node/index.js";
import { writeFile } from "fs/promises";
function EnvTypes(options = {}) {
  const { dts = "env.d.ts" } = options;
  const ignoreKeys = ["BASE_URL", "MODE", "DEV", "PROD"];
  return {
    name: "vite-plugin-env-types",
    apply: "serve",
    enforce: "post",
    async configResolved(config) {
      const { envDir, envPrefix } = config;
      const envFiles2 = await fg(".env*", {
        cwd: envDir,
        onlyFiles: true
      });
      let keys = envFiles2.map((file) => {
        const mode = file.slice(5);
        if (mode === "local") {
          return [];
        }
        const _env = loadEnv(mode, envDir, envPrefix);
        return Object.keys(_env);
      }).flat();
      keys = [...new Set(keys)].filter((k) => !ignoreKeys.includes(k));
      await writeFile(
        dts,
        `/// <reference types="vite/client" />

interface ImportMetaEnv {
  ${keys.map((k) => `readonly ${k}: string`).join("\n	")}
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}`
      );
    }
  };
}

// build.region.env.ts
import dotenv from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/dotenv@16.4.5/node_modules/dotenv/lib/main.js";
import fs from "node:fs";
import path from "node:path";
import { normalizePath, loadEnv as loadEnv2 } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite@5.3.3_@types+node@20.1_874bbde7e8d556898131f33ef073753c/node_modules/vite/dist/node/index.js";
import { pick } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/lodash.js";
var region = (process.env.BUILD_REG || "CN").toLowerCase();
function getEnvFilesForMode(mode, envDir = "") {
  return [
    /** region file */
    `.env.${mode}`,
    /** region local file */
    `.env.${mode}.local`
  ].map((file) => normalizePath(path.join(envDir, file)));
}
function tryStatSync(file) {
  try {
    return fs.statSync(file, { throwIfNoEntry: false });
  } catch {
  }
}
var env = loadEnv2(region, "");
var envFiles = getEnvFilesForMode(region, "");
var parsed = Object.fromEntries(envFiles.flatMap((filePath) => {
  if (!tryStatSync(filePath)?.isFile()) {
    return [];
  }
  return Object.entries(dotenv.parse(fs.readFileSync(filePath)));
}));
var regionEnv = pick(env, Object.keys(parsed));
var regionEnvVars = Object.entries(regionEnv).reduce((acc, [key, value]) => {
  acc[`import.meta.env.${key}`] = JSON.stringify(value);
  return acc;
}, {});

// vite.config.ts
import { viteMockServe } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-mock@3.0.2_esbu_141376548af1a2831c114dda14149c59/node_modules/vite-plugin-mock/dist/index.mjs";
import { spaLoading } from "file:///D:/code/formal_code/go-global-fd/node_modules/.pnpm/vite-plugin-spa-loading@1.2_8f4b798d188a46325c30413c20ae2b45/node_modules/vite-plugin-spa-loading/dist/index.js";
var __vite_injected_original_import_meta_url = "file:///D:/code/formal_code/go-global-fd/vite.config.ts";
console.log(env.VITE_API_BASE_URL, env.VITE_SERVER_API_URL, env.VITE_WS_SERVER_URL, process.env.PRIVATE);
var excludeRouter = ["**/__components__/**", "**/composables/**"];
if (process.env.NODE_ENV === "production") {
  excludeRouter.push("**/demo/**");
}
var prodCDNConfig = {
  modules: [
    {
      name: "vue",
      var: "Vue",
      path: "https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.prod.js"
    },
    {
      name: "vue-router",
      var: "VueRouter",
      path: "https://cdn.jsdelivr.net/npm/vue-router@4.2.4/dist/vue-router.global.prod.js"
    },
    {
      name: "vue-i18n",
      var: "VueI18n",
      path: "https://cdn.jsdelivr.net/npm/vue-i18n@9.4.1/dist/vue-i18n.global.prod.js"
    },
    {
      name: "vue-demi",
      var: "VueDemi",
      path: "https://cdn.jsdelivr.net/npm/vue-demi@0.14.6/lib/index.iife.min.js"
    },
    {
      name: "pinia",
      var: "Pinia",
      path: "https://cdn.jsdelivr.net/npm/pinia@2.1.6/dist/pinia.iife.prod.js"
    },
    {
      name: "axios",
      var: "axios",
      path: "https://cdn.jsdelivr.net/npm/axios@1.5.0/dist/axios.min.js"
    },
    {
      name: "vue-request",
      var: "VueRequest",
      path: "https://cdn.jsdelivr.net/npm/vue-request@2.0.3/dist/vue-request.min.js"
    },
    {
      name: "element-plus",
      var: "ElementPlus",
      path: "https://cdn.jsdelivr.net/npm/element-plus@2.9.0/dist/index.full.min.js",
      css: "https://cdn.jsdelivr.net/npm/element-plus@2.9.0/dist/index.min.css"
    },
    {
      name: "echarts",
      var: "echarts",
      path: "https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"
    },
    {
      name: "@element-plus/icons-vue",
      var: "ElementPlusIconsVue",
      path: "https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.3.1/dist/index.min.js"
    },
    {
      name: "pdfmake",
      var: "pdfMake",
      path: "https://cdn.jsdelivr.net/npm/pdfmake@0.2.14/build/pdfmake.min.js"
    }
  ]
};
var preloadPackages = [
  {
    name: "vue",
    version: "3.3.4",
    path: "dist/vue.global.prod.js"
  },
  {
    name: "element-plus",
    version: "2.9.0",
    path: "dist/index.full.min.js"
  },
  {
    name: "pdfmake",
    version: "0.2.14",
    path: "build/pdfmake.min.js"
  }
];
var vite_config_default = defineConfig({
  plugins: [
    // 添加CDN降级插件
    cdnFallbackPlugin({
      enableDebugLog: true,
      preloadPackages
    }),
    spaLoading("text", {
      tipText: "Please wait a moment...",
      devEnable: true
    }),
    VueRouter({
      exclude: excludeRouter
    }),
    vue(),
    vueJsx(),
    VueDevTools(),
    Layouts(),
    PreprocessorDirectives(),
    chunkSplitPlugin({
      customSplitting: {
        lodash: [/lodash-es/],
        "components-news": [/src\/pages\/__components__\/NewsComponents/],
        "components-intelligence-dashboard": [/src\/pages\/__components__\/IntelligenceComponents\/DashboardComponents/],
        "components-intelligence-panel": [/src\/pages\/__components__\/IntelligenceComponents\/RightPanelComponents/],
        "components-intelligence-collapsible": [/src\/pages\/__components__\/IntelligenceComponents\/CollapsiblePanel/],
        "components-intelligence-base": [/src\/pages\/__components__\/IntelligenceComponents\/BaseComponents/],
        "components-robot": [/src\/pages\/__components__\/RobotComponents/],
        "components-login": [/src\/pages\/__components__\/LoginComponents/],
        "markdown-output": [/src\/pages\/__components__\/IntelligenceComponents\/MarkdownOutputDialog.vue/],
        directives: [/src\/directives/]
      }
    }),
    viteCompression(),
    EnvTypes(),
    visualizer({
      gzipSize: true,
      brotliSize: true,
      emitFile: false,
      filename: "debugger/stats.html"
    }),
    process.env.USE_MOCK ? viteMockServe({
      ignore: /^\_/,
      mockPath: "mock"
    }) : null,
    // 使用封装的CDN插件
    vitePluginCDN2({
      modules: prodCDNConfig.modules
    })
  ],
  define: {
    ...regionEnvVars,
    "import.meta.env.VITE_BUILD_VERSION": JSON.stringify(Date.now())
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
    }
  },
  server: {
    host: "0.0.0.0",
    port: 10001,
    proxy: {
      [env.VITE_API_BASE_URL]: {
        target: env.VITE_SERVER_API_URL,
        changeOrigin: true,
        secure: false,
        rewrite: (path2) => {
          return path2.replace(new RegExp(`^${env.VITE_API_BASE_URL}`), "");
        }
      }
      // '/socket.io': {
      //   target: env.VITE_WS_SERVER_URL,
      //   changeOrigin: true,
      //   ws: true,
      //   secure: false,
      //   // rewrite: (path) => {
      //   //   return path.replace(/^\/ws/, '')
      //   // }
      // }
    }
  },
  build: {
    minify: "terser",
    terserOptions: {
      compress: {
        // 保留CDN降级相关的console日志用于调试
        drop_console: false,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        entryFileNames: "assets/[name].[hash].js",
        chunkFileNames: "assets/[name].[hash].js",
        assetFileNames: "assets/[name].[hash].[ext]",
        manualChunks: {
          mermaid: ["mermaid"]
        }
      }
    },
    manifest: true,
    emptyOutDir: true
  },
  // 添加对.node文件的处理
  assetsInclude: ["**/*.node"],
  optimizeDeps: {
    include: ["mermaid"],
    exclude: ["rs-module-lexer", "pdfmake"]
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
