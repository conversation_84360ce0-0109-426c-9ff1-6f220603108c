<template>
  <div
    class="w-full h-full flex flex-col justify-start items-center px-4 sm:px-6 md:px-12 lg:px-[90px] overflow-y-auto bg-gray-50">
    <div class="flex justify-center items-center w-full h-[50px] my-4 sm:my-6 md:my-8 lg:my-10">
      <span class="font-bold text-[30px]">{{ $t("robot.title") }}</span>
    </div>
    <RobotHelper :list="robotList || []" @select="toChat"/>
    
    <!-- 客服功能 -->
    <!-- <CustomerServiceButton @click="showServiceDialog = true"/>
    <CustomerServiceDialog 
      :show="showServiceDialog"
      @close="showServiceDialog = false"
      @send="handleServiceMessage"/> -->
  </div>
</template>

<script lang="ts" setup>
import RobotHelper from '../__components__/RobotComponents/RobotHelper.vue'
import CustomerServiceButton from '../__components__/CustomerService/CustomerServiceButton.vue'
import CustomerServiceDialog from '../__components__/CustomerService/CustomerServiceDialog.vue'
import router from '@/router'
import { definePage } from 'vue-router/auto'
import { useGet } from '@/hooks/useHttp'
import { onMounted, ref } from 'vue'
import type { RobotHelper as RobotHelperType } from './type'

const showServiceDialog = ref(false)
const { state: robotList, execute: getRobotList } = useGet<RobotHelperType[]>('/common/robot_profile', undefined, {
  immediate: false
})

definePage({
  meta: {
    layout: 'default',
    requiresAuth: true,
  },
})

onMounted(async () => {
  await getRobotList()
})

const toChat = (helper: any) => {
  router.push({
    path: router.resolve('/robot/chat').path,
    query: {
      id: helper.robot_id
    }
  })
}

const handleServiceMessage = (message: string) => {
  // 处理发送消息的逻辑
  console.log('Send message:', message)
}
</script>

<style lang="scss" module></style>
