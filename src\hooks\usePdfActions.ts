// src/composables/usePdfActions.ts
import { ref, type Ref } from 'vue'
import { usePdf } from '@/hooks/usePdf'
import type { CreatedReport } from '@/pages/format-report/type'

export const usePdfActions = (containerRef: Ref<HTMLElement | null>) => {
  const pdfSettings = ref({})
  const showSettings = ref(false)

  const createPDF = async (title: string, images?:Record<string, string>, searchSource?:CreatedReport['search_result'], time_lineData?:CreatedReport['time_line'], legalTimelineData?:CreatedReport['legal_data']['time_line']) => {
    if (!containerRef.value) {return null}
    const { generatePdf } = usePdf()
    console.log('legalTimelineData', legalTimelineData)
    return await generatePdf(title, containerRef.value, pdfSettings.value, images, searchSource, time_lineData, legalTimelineData)
  }

  const downloadPDF = async (title: string, images?:Record<string, string>, searchSource?:CreatedReport['search_result'], time_lineData?:CreatedReport['time_line'], legalTimelineData?:CreatedReport['legal_data']['time_line']) => {
    const pdfMaker = await createPDF(title, images, searchSource, time_lineData, legalTimelineData)
    pdfMaker?.download(title)
  }

  const previewPDF = async (title: string, images?:Record<string, string>, searchSource?:CreatedReport['search_result'], time_lineData?:CreatedReport['time_line'], legalTimelineData?:CreatedReport['legal_data']['time_line']) => {
    const pdfMaker = await createPDF(title, images, searchSource, time_lineData, legalTimelineData)
    pdfMaker?.open()
  }

  const updatePdfSettings = (settings: any) => {
    pdfSettings.value = settings
  }

  return {
    showSettings,
    pdfSettings,
    createPDF,
    downloadPDF, 
    previewPDF,
    updatePdfSettings
  }
}