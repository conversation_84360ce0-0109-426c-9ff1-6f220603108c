<script setup lang="ts">

const stars = 108
const columns = 18
</script>

<template>
  <div
    class="absolute inset-0 z-0 grid gap-[1px] pointer-events-none"
    :style="{ gridTemplateColumns: `repeat(${columns}, 1fr)` }">
    <div
      v-for="i in stars"
      :key="i"
      class="relative flex items-center justify-center">
      <div
        class="h-[1.5px] w-[1.5px] rounded-full z-20 bg-gray-300"/>
    </div>
  </div>
</template>

<style scoped>
</style>
