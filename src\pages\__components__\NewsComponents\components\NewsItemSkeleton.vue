<template>
  <div class="mb-[15px]">
    <el-skeleton :loading="true" animated :class="[$style.skeleton,skeletonClass]">
      <template #template>
        <div class="flex justify-start items-center gap-[20px] h-full">
          <el-skeleton-item variant="text" style="width: 40px; height: 40px" v-if="showIndex"/>
          
          <template v-if="useCard">
            <el-card style="flex: 1">
              <el-skeleton-item variant="p" style="width: 50%"/>
              <el-skeleton-item variant="text" style="width: 100%"/>
              <el-skeleton-item variant="text" style="width: 100%"/>
              <div class="flex justify-end">
                <el-skeleton-item variant="text" style="width: 30%"/>
              </div>
            </el-card>
          </template>
          
          <template v-else>
            <div style="flex: 1">
              <el-skeleton-item variant="p" style="width: 50%"/>
              <el-skeleton-item variant="text" style="width: 100%"/>
              <el-skeleton-item variant="text" style="width: 100%"/>
              <div class="flex justify-end">
                <el-skeleton-item variant="text" style="width: 30%"/>
              </div>
            </div>
          </template>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script lang="ts" setup>
import { ElSkeleton, ElSkeletonItem, ElCard } from 'element-plus'

defineProps<{
  showIndex?: boolean;
  skeletonClass?: string;
  useCard?: boolean
}>()
</script>
<style lang="scss" module>
.skeleton{
  :global(.el-card){
    height: 100%;
  }
}
</style>
