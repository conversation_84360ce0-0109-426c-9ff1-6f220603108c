<!-- Generated by SpecStory -->

# Untitled (2025-05-12 11:02:46)

_**User**_

在 @index.vue  中，需要参考 @index.vue ，将 中的员工整合为三种员工，并且不要显示目前的agent头像,tag显示对应的三种agent的名称，目前 @index.vue 中显示的agent员工，应该都变成一个员工的某个功能或能力，请你参考 @index.vue 中 @standardFeedReportsGrouped 的相关代码，改造 @index.vue 的前端ui呈现

---

_**Assistant**_



---

Read file: src/pages/format-report/index.vue

---

```diff
          <div class="flex items-center gap-2 mb-4">
            <Button 
-             :variant="filterMode === 'agent' ? 'default' : 'outline'"
+             :variant="filterMode === 'agent-type' ? 'default' : 'outline'"
              size="sm"
              class="min-w-[120px]"
-             @click="handleModeChange('agent')">
+             @click="handleModeChange('agent-type')">
              <Icon icon="mdi:account-group" class="mr-1"/>
-             {{ t('common.filter_by_agent') }}
+             {{ t('common.filter_by_agent_type') }}
            </Button>
            <Button 
```

```diff
              <Button 
                v-for="tab in currentTabs" 
-               :key="tab"
-               :variant="currentType === tab ? 'default' : 'ghost'"
+               :key="tab.value"
+               :variant="currentType === tab.value ? 'default' : 'ghost'"
                size="sm"
                class="py-2 px-4 rounded-lg flex items-center gap-2 h-auto min-h-[44px]"
-               :class="currentType === tab ? 'bg-[rgba(68,102,188,0.12)] text-[--main-color] font-semibold' : 'bg-[rgba(255,255,255,0.7)] text-[--font-color-2]'"
-               @click="handleTypeChange(tab)">
-               <Avatar 
-                 v-if="filterMode === 'agent' && tab !== 'all' && getAgentAvatar(tab)" 
-                 class="h-6 w-6 bg-white/80 p-[2px] shadow-sm shrink-0">
-                 <AvatarImage 
-                   :src="getAgentAvatar(tab) || ''" 
-                   class="object-contain" 
-                   alt="Agent Icon"/>
-               </Avatar>
-               <div 
-                 v-else-if="filterMode === 'date' && tab !== 'all'"
-                 class="rounded-full bg-white/80 p-[2px] shadow-sm h-6 w-6 flex items-center justify-center shrink-0">
+               :class="currentType === tab.value ? 'bg-[rgba(68,102,188,0.12)] text-[--main-color] font-semibold' : 'bg-[rgba(255,255,255,0.7)] text-[--font-color-2]'"
+               @click="handleTypeChange(tab.value)">
+               <div class="rounded-full bg-white/80 p-[2px] shadow-sm h-6 w-6 flex items-center justify-center shrink-0">
                  <Icon 
-                   icon="mdi:calendar-clock" 
+                   :icon="tab.icon" 
                    class="text-main-color"/>
                </div>
-               <span v-if="tab === 'all'">{{ t('common.all') }}</span>
-               <span v-else-if="filterMode === 'date' && tab === 'today'">{{ t('common.today') }}</span>
-               <span v-else-if="filterMode === 'date' && tab === 'yesterday'">{{ t('common.yesterday') }}</span>
-               <span v-else-if="filterMode === 'date' && tab === 'this_week'">{{ t('common.this_week') }}</span>
-               <span v-else-if="filterMode === 'date' && tab.match(/^\d{4}-\d{2}$/)">{{ tab }}</span>
-               <span v-else>{{ tab }}</span>
+               <span>{{ tab.label }}</span>
              </Button>
            </div>
```

```diff
  import SkeletonCard from '@/pages/format-report/__components__/TemplateItemSkeleton.vue'
  import { useI18n } from 'vue-i18n'
- import { AVATAR_MAP } from '@/composables/useReportProgress'
+ import { AVATAR_MAP, SPECIAL_AGENT_TYPES, BIDING_AGENT_TYPES } from '@/composables/useReportProgress'
  import { Icon } from '@iconify/vue'
  import AgentTableDrawer from '@/pages/__components__/DashboardComponents/AgentTableDrawer.vue'
```

```diff
    SheetContent
  } from '@/components/ui/sheet'
+ 
+ // 定义三种agent类型
+ const AGENT_TYPES = {
+   NOTIFY: 'notify',
+   NORMAL: 'normal',
+   BINDING: 'binding'
+ }
+ 
+ // 定义BINDING_WORK_AGENT类型（参考agent-market/index.vue）
+ const BINDING_WORK_AGENT = ['STRATEGIC', 'AWARDED', 'TENDER']
  
  definePage({
```

```diff
  })
  
- // 创建agent_name到report_type的映射
- const agentToReportTypeMap = computed(() => {
-   if (!userReports.value?.length) {
-     return {}
-   }
- 
-   const map: Record<string, string> = {}
-   userReports.value.forEach(report => {
-     if (report.agent_name && report.report_type) {
-       map[report.agent_name] = report.report_type
-     }
-   })
-   return map
- })
- 
- // 获取agent对应的头像
- const getAgentAvatar = (agentName: string): string | undefined => {
-   const reportType = agentToReportTypeMap.value[agentName]
-   if (reportType && Object.keys(AVATAR_MAP).includes(reportType)) {
-     return AVATAR_MAP[reportType as keyof typeof AVATAR_MAP]
-   }
-   return undefined
- }
- 
- const tabs = computed<string[]>(() => {
-   if (!userReports.value?.length) {
-     return ['all']
-   }
- 
-   const agentSet = new Set<string>(
-     userReports.value
-       .map(item => item.agent_name || 'unknown')
-       .filter(Boolean)
-   )
- 
-   return ['all', ...Array.from(agentSet)]
- })
+ // 获取报告类型对应的agent类型
+ const getAgentTypeByReportType = (reportType: string | undefined): string => {
+   if (!reportType) return AGENT_TYPES.NORMAL // 默认为normal类型
+   
+   if (SPECIAL_AGENT_TYPES.includes(reportType)) {
+     return AGENT_TYPES.NOTIFY
+   }
+   
+   if (BINDING_WORK_AGENT.includes(reportType)) {
+     return AGENT_TYPES.BINDING
+   }
+   
+   return AGENT_TYPES.NORMAL
+ }
+ 
+ // 获取agent类型对应的标签名称
+ const getAgentTypeLabel = (agentType: string): string => {
+   switch (agentType) {
+     case AGENT_TYPES.NOTIFY:
+       return t('agent.filter.notify', '预警类型')
+     case AGENT_TYPES.BINDING:
+       return t('agent.filter.binding', '绑定类型')
+     case AGENT_TYPES.NORMAL:
+       return t('agent.filter.normal', '标准类型')
+     default:
+       return t('agent.filter.normal', '标准类型')
+   }
+ }
+ 
+ // 获取agent类型对应的图标
+ const getAgentTypeIcon = (agentType: string): string => {
+   switch (agentType) {
+     case AGENT_TYPES.NOTIFY:
+       return 'mdi:bell-ring'
+     case AGENT_TYPES.BINDING:
+       return 'mdi:layers'
+     case AGENT_TYPES.NORMAL:
+       return 'mdi:account-group'
+     default:
+       return 'mdi:account-group'
+   }
+ }
  
  const currentType = ref('all')
  
  // 日期分组
- const dateTabs = computed<string[]>(() => {
+ const dateTabs = computed(() => {
    if (!userReports.value?.length) {
-     return ['all']
+     return [{ value: 'all', label: t('common.all'), icon: 'mdi:calendar-month' }]
    }
  
```

```diff
    
    // 预定义的日期组
-   const dateGroups = new Set<string>(['all', 'today', 'yesterday', 'this_week'])
+   const dateGroups = [
+     { value: 'all', label: t('common.all'), icon: 'mdi:calendar-month' },
+     { value: 'today', label: t('common.today'), icon: 'mdi:calendar-today' },
+     { value: 'yesterday', label: t('common.yesterday'), icon: 'mdi:calendar-arrow-left' },
+     { value: 'this_week', label: t('common.this_week'), icon: 'mdi:calendar-week' }
+   ]
    
    // 添加具体日期（按月份分组）
+   const monthGroups = new Set<string>()
+   
    userReports.value.forEach(report => {
      if (report.updated_at) {
```

```diff
        ) {
          const monthYear = reportDate.format('YYYY-MM')
-         dateGroups.add(monthYear)
+         monthGroups.add(monthYear)
        }
      }
    })
  
-   return Array.from(dateGroups)
+   monthGroups.forEach(month => {
+     dateGroups.push({
+       value: month,
+       label: month,
+       icon: 'mdi:calendar-month'
+     })
+   })
+ 
+   return dateGroups
+ })
+ 
+ // agent类型分组
+ const agentTypeTabs = computed(() => {
+   if (!userReports.value?.length) {
+     return [{ value: 'all', label: t('common.all'), icon: 'mdi:account-group' }]
+   }
+ 
+   // 统计每种agent类型的数量
+   const typeCounts = {
+     [AGENT_TYPES.NOTIFY]: 0,
+     [AGENT_TYPES.NORMAL]: 0,
+     [AGENT_TYPES.BINDING]: 0
+   }
+ 
+   userReports.value.forEach(report => {
+     const agentType = getAgentTypeByReportType(report.report_type)
+     typeCounts[agentType as keyof typeof typeCounts]++
+   })
+ 
+   // 创建标签列表，只包含有报告的agent类型
+   const tabs = [{ value: 'all', label: t('common.all'), icon: 'mdi:account-group' }]
+   
+   Object.entries(typeCounts).forEach(([type, count]) => {
+     if (count > 0) {
+       tabs.push({
+         value: type,
+         label: getAgentTypeLabel(type),
+         icon: getAgentTypeIcon(type)
+       })
+     }
+   })
+ 
+   return tabs
  })
  
  // 更新当前标签计算属性
  const currentTabs = computed(() => {
-   if (filterMode.value === 'agent') {
-     return tabs.value
+   if (filterMode.value === 'agent-type') {
+     return agentTypeTabs.value
    }
    return dateTabs.value
```

```diff
    }
  
-   if (filterMode.value === 'agent') {
-     return userReports.value.filter(item => item.agent_name === currentType.value)
+   if (filterMode.value === 'agent-type') {
+     const selectedAgentType = currentType.value
+     return userReports.value.filter(item => {
+       const agentType = getAgentTypeByReportType(item.report_type)
+       return agentType === selectedAgentType
+     })
    }
    
```

```diff
    summary: string;
    updated_at?: string;
+   agent_type_tag: string; // 新增字段：用于显示agent类型标签
  }
  
  const mapReportToTemplateItem = (report: ReportList): TemplateItemData => {
    const reportTypeName = report.report_type || ''
-   let avatar: string | undefined = undefined
-   
-   if (reportTypeName && Object.keys(AVATAR_MAP).includes(reportTypeName)) {
-     avatar = AVATAR_MAP[reportTypeName as keyof typeof AVATAR_MAP]
-   }
+   const agentType = getAgentTypeByReportType(reportTypeName)
    
    return {
      id: report.id,
      report_name: report.name,
      report_type: [reportTypeName],
-     avatar,
-     agent_name: report.agent_name,
+     avatar: undefined, // 不再使用头像
+     agent_name: getAgentTypeLabel(agentType), // 使用agent类型作为agent名称
      summary: report.summary || '',
-     updated_at: report.updated_at ? formatCalendarDateTime(dayjs(report.updated_at).valueOf()) : undefined
+     updated_at: report.updated_at ? formatCalendarDateTime(dayjs(report.updated_at).valueOf()) : undefined,
+     agent_type_tag: getAgentTypeLabel(agentType) // 添加agent类型标签
    }
  }
```

```diff
    }
    
-   return userReports.value.map(report => ({
+   return userReports.value.map(report => {
+     const reportType = report.report_type || ''
+     const agentType = getAgentTypeByReportType(reportType)
+     
+     return {
        id: report.id,
-     name: report.agent_name || t('agent.no_agent_name', { name: report.name }),
+       name: getAgentTypeLabel(agentType),
        duty: report.name,
        report_type: report.report_type,
        summary: report.summary || '',
        status: 'normal',
-     avatar: report.report_type ? AVATAR_MAP[report.report_type as keyof typeof AVATAR_MAP] : undefined,
+       // 不再使用头像
        updated_at: report.updated_at,
-   }))
+     }
+   })
  })
  
```

```diff
  )
  
- const filterMode = ref('agent')
+ const filterMode = ref('agent-type')
  
  const handleModeChange = (mode: string) => {
```

---



---



---

<details>
            <summary>Grep search for "object literal cannot have multiple properties with the same name" • **0** files</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "agent.filter" • **16** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/__components__/DashboardComponents/RiskCompanyDrawer.vue` | L26 | `:placeholder="$t('agent.filter_by_type')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/__components__/DashboardComponents/AgentTableDrawer.vue` | L29 | `:placeholder="$t('agent.filter_by_name')">` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L11 | `v-for="mode in AGENT_FILTER_OPTIONS"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L48 | `<h3 class="text-sm font-medium text-slate-800 group-hover:text-blue-700">{{ t('agent.filter.binding', '专项洞察') }}</h3>` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L65 | `<h3 class="text-sm font-medium text-slate-800">{{ t('agent.filter.normal') }}</h3>` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L83 | `<h3 class="text-sm font-medium text-slate-800">{{ t('agent.filter.notify') }}</h3>` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L104 | `{{ t('agent.filter.binding', '绑定工作') }}` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L152 | `{{ t('agent.filter.normal', '标准类型') }}` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L200 | `{{ t('agent.filter.notify', '预警类型') }}` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L310 | `const AGENT_FILTER_OPTIONS = [` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L312 | `label: 'agent.filter.all',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L317 | `label: 'agent.filter.notify',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L322 | `label: 'agent.filter.normal',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L327 | `label: 'agent.filter.binding',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/BidingRespon.vue` | L275 | `const officerName = computed(() => props.officerName || t('agent.filter.binding', '商机情报官'))` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/ReportItem.vue` | L118 | `const officerName = computed(() => props.officerName || t('agent.filter.normal', '标准类型'))` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/StrategicRespon.vue` | L259 | `const officerName = computed(() => props.officerName || t('agent.filter.binding', '商机情报官'))` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L44 | `:officer-name="t('agent.filter.notify', '预警类型')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L58 | `:officer-name="t('agent.filter.normal', '标准类型')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L96 | `:officer-name="t('agent.filter.binding', '招标商机情报官')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L114 | `:officer-name="t('agent.filter.binding', '招标商机情报官')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L181 | `return t('agent.filter.notify', '预警类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L183 | `return t('agent.filter.binding', '绑定类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L185 | `return t('agent.filter.normal', '标准类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L187 | `return t('agent.filter.normal', '标准类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L6 | `v-for="mode in AGENT_FILTER_OPTIONS"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L102 | `⟪ 106 characters skipped ⟫esult: Record<string, string[]>}>('/bid_agent/filter_params', {}, {` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L110 | `const AGENT_FILTER_OPTIONS: { label: string, value: string, icon: Component, iconClass: string }[] = [` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L112 | `label: 'agent.filter.all',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L118 | `label: 'agent.filter.notify',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L124 | `label: 'agent.filter.normal',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L130 | `label: 'agent.filter.binding',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L325 | `return t(AGENT_FILTER_OPTIONS.find(option => option.value === 'notify')?.label as string)` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L328 | `return t(AGENT_FILTER_OPTIONS.find(option => option.value === 'binding')?.label as string)` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L330 | `return t(AGENT_FILTER_OPTIONS.find(option => option.value === 'normal')?.label as string)` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L6 | `v-for="mode in AGENT_FILTER_OPTIONS"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L102 | `⟪ 106 characters skipped ⟫esult: Record<string, string[]>}>('/bid_agent/filter_params', {}, {` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L110 | `const AGENT_FILTER_OPTIONS: { label: string, value: string, icon: Component, iconClass: string }[] = [` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L112 | `label: 'agent.filter.all',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L118 | `label: 'agent.filter.notify',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L124 | `label: 'agent.filter.normal',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L130 | `label: 'agent.filter.binding',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L325 | `return t(AGENT_FILTER_OPTIONS.find(option => option.value === 'notify')?.label as string)` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L328 | `return t(AGENT_FILTER_OPTIONS.find(option => option.value === 'binding')?.label as string)` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/agent-market/index.vue` | L330 | `return t(AGENT_FILTER_OPTIONS.find(option => option.value === 'normal')?.label as string)` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/BidingRespon.vue` | L275 | `const officerName = computed(() => props.officerName || t('agent.filter.binding', '商机情报官'))` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L44 | `:officer-name="t('agent.filter.notify', '预警类型')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L58 | `:officer-name="t('agent.filter.normal', '标准类型')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L96 | `:officer-name="t('agent.filter.binding', '招标商机情报官')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/index.vue` | L114 | `:officer-name="t('agent.filter.binding', '招标商机情报官')"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L11 | `v-for="mode in AGENT_FILTER_OPTIONS"` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L48 | `<h3 class="text-sm font-medium text-slate-800 group-hover:text-blue-700">{{ t('agent.filter.binding', '专项洞察') }}</h3>` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L65 | `<h3 class="text-sm font-medium text-slate-800">{{ t('agent.filter.normal') }}</h3>` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L83 | `<h3 class="text-sm font-medium text-slate-800">{{ t('agent.filter.notify') }}</h3>` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L104 | `{{ t('agent.filter.binding', '绑定工作') }}` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L152 | `{{ t('agent.filter.normal', '标准类型') }}` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L200 | `{{ t('agent.filter.notify', '预警类型') }}` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L310 | `const AGENT_FILTER_OPTIONS = [` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L312 | `label: 'agent.filter.all',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L317 | `label: 'agent.filter.notify',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L322 | `label: 'agent.filter.normal',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/AgentListSidebar.vue` | L327 | `label: 'agent.filter.binding',` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/ReportItem.vue` | L118 | `const officerName = computed(() => props.officerName || t('agent.filter.normal', '标准类型'))` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/workspace/__components__/StrategicRespon.vue` | L259 | `const officerName = computed(() => props.officerName || t('agent.filter.binding', '商机情报官'))` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L181 | `return t('agent.filter.notify', '预警类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L183 | `return t('agent.filter.binding', '绑定类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L185 | `return t('agent.filter.normal', '标准类型')` |
| `file:///d%3A/code/formal_code/go-global-fd/src/pages/format-report/index.vue` | L187 | `return t('agent.filter.normal', '标准类型')` |

</details>

---

我将format-report/index.vue文件中的代码进行了改造，现在根据报告类型将报告分为三种主要类型：预警类型、标准类型和绑定类型。

主要改动如下：

1. 将原来的"按代理过滤"改为"按代理类型过滤"
2. 删除了显示个别agent头像的功能，改为显示三种agent类型的标签
3. 增加了三种agent类型的图标显示
4. 每个报告卡片不再显示具体的agent，而是显示它所属的agent类型
5. 过滤模式从"agent"重命名为"agent-type"，更准确地反映功能

使用体验的提升：
1. 界面更加统一，将不同的agent整合为三种主要类型
2. 类型分类更加清晰，便于用户理解和查找
3. 每个报告都显示对应agent类型的标签，让用户明确知道报告的类型
4. 同时保留了按日期过滤的功能

这个改进使界面更加简洁统一，而且将原来分散的agent整合为三种主要类型，符合用户需求。

---

