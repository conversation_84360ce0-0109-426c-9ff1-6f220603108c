import type { Item } from '../index'

export interface Projects {
  [key: string]: Project[];
}

interface Project {
  created_at: string;
  updated_at: string;
  name: string;
  name_cn: string;
  description: string;
  user_profile: Userprofile;
  industry: string;
  industry_cn: string;
  nation: string;
  nation_cn: string;
  status: string;
  others: Others;
  id: string;
  // 添加 _nation 字段
  _nation?: string;
}

interface Others {}

interface Userprofile {
  style: string[];
  style_value: string;
  overseas: string[];
  overseas_value: string;
  profile_summary: string;
}
interface Others {}

export type ReportData = Content & {
  title: string;
  id: string;
};
export interface DailyReport {
  id: string;
  date: string;
  project_id: string;
  report: Report[];
}

export type Report = Record<MarkdownKeyType, string> & {
  news_list: Newslist[];
};

interface Content {
  summary: string;
  deep_analysis: string;
  short_term_impact: string;
  long_term_impact: string;
  potential_risks: string;
  potential_opportunities: string;
  suggestion_list: string[];
  topic_name: string;
  main_argument: string;
  article_id_list: string[];
  news_list: Newslist[];
}

interface Newslist {
  id: string;
  title: string;
  publish_time: number;
  url: string;
  web_site: string;
  brief_summary: string;
  favour: boolean;
}
export interface ProjectInfo {
  created_at: string;
  updated_at: string;
  name: string;
  name_cn: string;
  description: string;
  user_profile: Userprofile;
  industry: string;
  industry_cn: string;
  nation: string;
  nation_cn: string;
  status: string;
  others: Others;
  id: string;
}

interface Others {}

interface Userprofile {
  style: string[];
  style_value: string;
  overseas: string[];
  overseas_value: string;
  profile_summary: string;
}

export interface GoogleTrends {
  [key: string]: { x: string; y: number }[];
}
export interface GoogleTrendsSummary {
  analysis_list: string[];
  summary: string;
}

export interface CriticalData {
  keyinfo: KeyInfo[];
  keyinfo_data: KeyInfoData[];
}
interface KeyInfo {
  keyinfo: string;
  impact: Impact;
  web_site: string;
}

interface Impact {
  trend: string;
  reason: string;
}
interface KeyInfoData {
  statistics: string;
  timeframe: string;
  description: string;
  implications: string;
}

export interface BarChartData {
  [key: string]: _[];
}

interface _ {
  month: number;
  nation: string;
  total_amount: number;
}

export interface IndustryChart {
  created_at: string;
  updated_at: string;
  month: number;
  last_month: number;
  category: string;
  code: number;
  name: string;
  this_amounts: number;
  last_amounts: number;
  sort: number;
  increase_money: number;
  increase_percent: number;
  others: Others;
  id: string;
}

interface Others {}

export type RewriteProps = {
  daily_summary_id: string;
  topic_name: string;
  analysis_type: string;
  user_suggestion?: string;
};

export const MARKDOWN_KEYS = {
  TOPIC_NAME: 'topic_name',
  SUMMARY: 'summary',
  DEEP_ANALYSIS: 'deep_analysis',
  OPPORTUNITY_AND_RISK: 'opportunity_and_risk',
  SUGGESTIONS: 'suggestion_list',
} as const

export type MarkdownKeyType =
  (typeof MARKDOWN_KEYS)[keyof typeof MARKDOWN_KEYS];

export interface MarkdownData extends Record<MarkdownKeyType, string> {
  id: string;
  title: string;
  news_list: Newslist[];
}

export interface RewriteProps {
  topic_name: string;
  analysis_type: MarkdownKeyType;
  daily_summary_id?: string;
}

export interface RewriteResponse {
  analysis_type: MarkdownKeyType;
  content: string;
}

export type MarkdownUpdateEvent = {
  type: 'reset' | 'update';
  data?: any;
  source?: string;
};

export interface MarkdownSyncOptions {
  debounceTime?: number;
  retryTimes?: number;
  retryDelay?: number;
}
