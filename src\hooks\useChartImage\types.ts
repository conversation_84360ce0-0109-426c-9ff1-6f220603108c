export interface ChartImageOptions {
  maxWidth?: number
  quality?: number
  retries?: number
  devicePixelRatio?: number
  cacheKey?: string
  format?: 'jpeg' | 'png'
}

export interface ProcessedImage {
  id: string
  dataUrl: string
  width: number
  height: number
  originalSize: number
  compressedSize: number
}

export interface ChartImageError extends Error {
  chartId: string
  attempt: number
} 