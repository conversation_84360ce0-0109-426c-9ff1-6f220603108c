import { tryOnUnmounted } from '@vueuse/core'
import { computed, getCurrentInstance, onUnmounted, type ComputedRef } from 'vue'

type CacheKey = any;
type CacheEntry<T> = {
  value: ComputedRef<T>;
  count: number;
};
type SafeCacheEntry<T> = CacheEntry<T> & {
  cleanups: (() => any)[]
};
export type MemorizeComputedRef<T> = ComputedRef<T> & {
  dispose: () => void;
  cleanup: () => void;
};
type MemorizeCache<Key, Value> = {
  get: (key: Key) => CacheEntry<Value> | undefined;
  set: (key: Key, value: CacheEntry<Value>) => void;
  has: (key: Key) => boolean;
  delete: (key: Key) => void;
  clear: () => void;
};
export type UseComputedOptions<T, Args extends unknown[]> = {
  cache?: MemorizeCache<CacheKey, T>;
  getKey?: (...args: Args) => CacheKey;
  autoClear?: boolean;
};

function getMapCache<Value>(): MemorizeCache<CacheKey, Value> {
  const data = new Map<CacheKey, CacheEntry<Value>>()
  return {
    get: key => data.get(key),
    set: (key, value) => data.set(key, value),
    has: key => data.has(key),
    delete: key => data.delete(key),
    clear: () => {
      data.clear()
    }
  }
}

/**
 * Creates a computed value that is memoized based on the arguments passed to the getter function.
 * @param getter The getter function to memoize.
 * @param options Options for the computed value.
 * @returns A function that returns a computed value based on the arguments passed to the getter function.
 */
export function useComputed<T, Args extends unknown[]>(getter: (...args: Args) => T, options?: UseComputedOptions<T, Args>) {
  const cache = options?.cache || getMapCache<T>()
  const generateKey = (...args: Args) => options?.getKey?.(...args) || JSON.stringify(args)
  if (options?.autoClear !== false) {
    tryOnUnmounted(() => {
      cache.clear()
    })
  }

  return (...args: Args): MemorizeComputedRef<T> => {
    const key = generateKey(...args)
    let entry = cache.get(key) as SafeCacheEntry<T> | undefined
    if (!entry) {
      entry = { value: computed(() => getter(...args)), count: 0, cleanups: [] }
      cache.set(key, entry)
      const instance = getCurrentInstance()
      if (instance) {
        onUnmounted(() => {
          if (entry) {
            entry.cleanups.forEach(cleanup => cleanup())
            entry.cleanups = []
          }
        }, instance)
      } else {
        // 当前计算属性在外部环境，请注意内存回收问题，必要时手动调用dispose函数
        window.console.warn(`useComputed: ${key} is used outside of a component. please add a cleanup function to dispose the cache manually.`)
      }
    }
    entry.count += 1
    const cleanup = () => {
      // 因为computed函数在组件销毁的时候会清理deps依赖，所以需要在组件销毁的时候对计数器-1，当计数器为0的时候删除缓存，因为此时已经没有依赖了
      if (--entry.count <= 0) {
        cache.delete(key)
      }
    }
    entry.cleanups.push(cleanup)
    const result = entry.value as MemorizeComputedRef<T>
    result.cleanup = cleanup
    result.dispose = () => {
      cache.delete(key)
      entry.cleanups = []
    }
    return result
  }
}
