# 统一权限管理架构文档

## 概述

本项目已实现了完全统一的权限管理架构，将所有权限检查逻辑集中到 `usePermissions` composable 中，包括：

- 工作区功能权限
- 侧边栏菜单权限  
- 设置页面权限
- API 调用权限

## 架构优势

### 🎯 **统一管理**

- 所有权限配置集中在 `src/composables/usePermissions.ts`
- 消除了分散在各组件中的权限检查逻辑
- 提供了一致的权限检查接口

### 🔒 **类型安全**

- 完整的 TypeScript 类型定义
- 编译时权限配置验证
- 智能代码提示和错误检查

### 🚀 **性能优化**

- 权限检查结果使用 computed 缓存
- 避免重复的权限计算
- 响应式权限状态更新

## 权限类型

### 1. 功能权限 (Feature Permissions)

控制主要功能模块的访问：

```typescript
'feature.notify_insights'     // 通知洞察功能
'feature.standard_feed'       // 标准信息流功能  
'feature.special_analysis'    // 专项分析功能
```

### 2. 菜单权限 (Menu Permissions)

控制侧边栏菜单项的显示：

```typescript
'menu.information'    // 信息菜单
'menu.dashboard'      // 仪表板菜单
'menu.format_report'  // 格式化报告菜单
'menu.daily_report'   // 日报菜单
'menu.procurement'    // 采购菜单
'menu.chat_bot'       // 聊天机器人菜单
```

### 3. 设置权限 (Settings Permissions)

控制设置页面的访问：

```typescript
'settings.manage_users'   // 用户管理
'settings.company_info'   // 公司信息
'settings.personal_info'  // 个人信息
'settings.manage_billing' // 账单管理
'settings.access_plans'   // 访问计划
```

### 4. API权限 (API Permissions)

控制API调用的权限：

```typescript
'api.fetchBidingData'      // 获取招投标数据
'api.fetchStrategicData'   // 获取战略数据
'api.executeRefreshAgent'  // 执行代理刷新
'api.batchDeleteReports'   // 批量删除报告
'api.changeTime'           // 修改时间
```

### 5. 操作权限 (Operation Permissions)

控制具体操作的权限：

```typescript
'operation.refresh_agent'  // 刷新代理
'operation.delete_agent'   // 删除代理
'operation.change_time'    // 修改时间
```

## 使用方式

### 在组件中使用权限检查

```vue
<script setup>
import { usePermissions } from '@/composables/usePermissions'

const { hasPermission, canAccess } = usePermissions()

// 单个权限检查
const canViewDashboard = hasPermission('menu.dashboard')

// 使用快捷访问器
const canManageUsers = canAccess.manageUsers.value
</script>

<template>
  <!-- 使用 PermissionWrapper 组件 -->
  <PermissionWrapper permission="feature.special_analysis">
    <SpecialAnalysisComponent />
  </PermissionWrapper>
  
  <!-- 使用 v-if 进行简单检查 -->
  <div v-if="canViewDashboard">
    Dashboard Content
  </div>
</template>
```

### 在 AppSidebar 中的集成

AppSidebar 组件已完全集成统一权限管理：

```typescript
// 使用统一权限管理
const { getAvailableMenuItems, getSettingsMenuItems } = usePermissions()

// 菜单项过滤
const headerMenuItems = computed(() => {
  const availableMenuItems = getAvailableMenuItems(menuItems)
  return availableMenuItems.map(item => ({
    title: t(item.labelKey),
    url: item.route,
    icon: MENU_ICON_MAP[item.route],
    isActive: route.path === item.route
  }))
})

// 设置菜单项过滤
const settingsItems = computed(() => {
  const unifiedItems = getSettingsMenuItems()
  return unifiedItems.map(item => ({
    title: item.title,
    url: item.url,
    icon: getIconForUrl(item.url),
    isActive: isActive(item.url)
  }))
})
```

## 权限配置示例

```typescript
// src/composables/usePermissions.ts
const permissions: PermissionConfig = {
  // 菜单权限基于 menuConfig
  'menu.information': {
    check: () => hasMenuAccess('information')
  },
  
  // 设置权限基于用户类型
  'settings.manage_users': {
    check: () => Boolean(userStore.hasPermission('canManageUsers')),
    errorMessage: t('permission.no_user_management_access')
  },
  
  // 功能权限基于 featureConfig
  'feature.notify_insights': {
    check: () => userStore.canAccessNotifyFeature
  }
}
```

## 迁移指南

### 从分散权限检查迁移

**之前的方式：**

```vue
<!-- 分散在各个组件中 -->
<div v-if="userStore.menuConfig.find(m => m.id === 'dashboard')?.status">
  Dashboard Menu
</div>

<div v-if="userStore.hasPermission('canManageUsers')">
  User Management
</div>
```

**现在的方式：**

```vue
<!-- 统一的权限管理 -->
<PermissionWrapper permission="menu.dashboard">
  Dashboard Menu
</PermissionWrapper>

<PermissionWrapper permission="settings.manage_users">
  User Management
</PermissionWrapper>
```

## 测试策略

### 单元测试

```typescript
import { usePermissions } from '@/composables/usePermissions'

describe('usePermissions', () => {
  it('should check menu permissions correctly', () => {
    const { hasPermission } = usePermissions()
    expect(hasPermission('menu.dashboard')).toBe(true)
  })
  
  it('should filter available menu items', () => {
    const { getAvailableMenuItems } = usePermissions()
    const filtered = getAvailableMenuItems(mockMenuItems)
    expect(filtered).toHaveLength(2)
  })
})
```

### 集成测试

```typescript
describe('AppSidebar Integration', () => {
  it('should render only permitted menu items', () => {
    const wrapper = mount(AppSidebar)
    expect(wrapper.find('[data-testid="dashboard-menu"]')).toBeTruthy()
    expect(wrapper.find('[data-testid="restricted-menu"]')).toBeFalsy()
  })
})
```

## 最佳实践

### 1. 权限命名规范

- 使用点分隔的命名空间：`category.specific_permission`
- 保持命名的一致性和可读性
- 避免过于复杂的嵌套结构

### 2. 组件使用指南

- 优先使用 `PermissionWrapper` 组件
- 对于复杂的权限逻辑，使用 `hasPermission` 函数
- 避免在模板中直接访问 store 的权限属性

### 3. 错误处理

- 为重要权限配置错误消息
- 使用 try-catch 包装权限检查逻辑
- 提供优雅的降级体验

### 4. 性能考虑

- 权限检查结果使用 computed 缓存
- 避免在循环中进行复杂的权限计算
- 合理使用权限检查的粒度

## 故障排除

### 常见问题

1. **权限检查返回 undefined**
   - 检查权限配置是否正确
   - 验证 userStore 的状态
   - 确认权限字符串拼写正确

2. **菜单项不显示**
   - 检查 menuConfig 中的状态
   - 验证权限配置的 check 函数
   - 确认组件正确使用了权限检查

3. **权限状态不更新**
   - 检查权限依赖的响应式数据
   - 验证 computed 的依赖关系
   - 确认权限变化时的事件传播

## 未来扩展

- 支持基于角色的权限控制 (RBAC)
- 实现权限继承机制
- 添加权限审计日志
- 支持动态权限配置
