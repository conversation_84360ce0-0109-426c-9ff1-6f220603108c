<template>
  <el-dialog
    v-model="visible"
    width="600"
    align-center
    :class="$style['change-password-dialog']"
    :title="$t('setting.userInfo.changePassword')"
    @opened="onOpened"
    @closed="close">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             label-position="top">
      <el-form-item
        prop="oldPassword"
        :label="$t('setting.userInfo.oldPassword')">
        <el-input
          v-model.trim="form.oldPassword"
          ref="oldPasswordRef"
          required
          autofocus
          autocomplete="off"
          type="password"
          show-password/>
      </el-form-item>
      <el-form-item
        prop="newPassword"
        :label="$t('setting.userInfo.newPassword')"
        :class="$style['new-pass']">
        <el-input
          v-model.trim="form.newPassword"
          :maxlength="20"
          autocomplete="off"
          type="password"
          show-password
          @focus="setFocusState(true)"
          @blur="setFocusState(false)"
          @clear="resetValidate"
          @input="$event || resetValidate()"/>

      </el-form-item>
      <CheckPasswordValidate
        ref="passwordInit"
        :focusable="isPassFocus"
        :password="form.newPassword"
        :level-required="passwordValidate"
        :class="$style['password-init']"/>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">{{
          $t("button.cancel")
        }}</el-button>
        <el-button type="primary" @click="handleChangePassword">
          {{ $t("button.save") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { ref, reactive, type DefineComponent, nextTick } from 'vue'
import type { FormRules, FormInstance, ElInput } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { usePost } from '@/hooks/useHttp'
import CheckPasswordValidate from './checkPasswordValidate.vue'
import { isOverseasVersion } from '@/utils'
import { ElMessage } from 'element-plus'

interface RuleForm {
  oldPassword: string;
  newPassword: string;
}
interface ValidatePassword {
  text: string;
  active: boolean;
  check: (password: string) => boolean;
}
const isPassFocus = ref<boolean>(false)
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

const { t } = useI18n()

const emit = defineEmits<{
  'update:show': [value:boolean]
  'close':[value:boolean]
  'success':[]
}>()

const visible = useVModel(props, 'show', emit)
const formRef = ref<FormInstance>()

const form = reactive({
  oldPassword: '',
  newPassword: '',
})
const passwordInit = ref<DefineComponent<typeof CheckPasswordValidate> | null>(
  null
)
const oldPasswordRef = ref<DefineComponent<typeof ElInput> | null>(null)
const onOpened = ()=>{
  nextTick(()=>{
    oldPasswordRef.value?.focus()
  })
}
const setFocusState = (state: boolean) => {
  isPassFocus.value = state
}
const passwordValidate = ref<ValidatePassword[]>([
  {
    text: t('setting.reset.pwdRequired.number'),
    active: false,
    check: (password: string) => /[0-9]/.test(password),
  },
  {
    text: t('setting.reset.pwdRequired.lowerCaseAndUpperCase'),
    active: false,
    check: (password: string) => /^(?=.*?[a-zA-Z]).*$/.test(password),
  },
  {
    text: t('setting.reset.pwdRequired.str'),
    active: false,
    check: (password: string) => password.length >= 8,
  },
])
const validatorPassword = (rule: any, value: any, callback: any) => {
  passwordValidate.value?.forEach((item) => {
    item.active = item.check(value + '')
  })
  const check = passwordInit.value?.check(value)
  if (!check) {
    callback(new Error())
    return
  }
  callback()
}
const rules = reactive<FormRules<RuleForm>>({
  oldPassword: [
    {
      required: true,
      message: ' ',
      trigger: 'blur-sm',
    },
  ],
  newPassword: [
    {
      required: true,
      message: ' ',
      trigger: 'change',
    },
    { validator: validatorPassword, trigger: 'change' },
  ],
})
const close = ()=>{
  resetPasswordValidate()
  nextTick(()=>{
    visible.value = false
    emit('close', false)
  })
}
const resetValidate = ()=>{
  passwordValidate.value?.forEach((item) => {
    item.active = false
  })
}
const resetPasswordValidate = () => {
  resetValidate()
  form.oldPassword = ''
  form.newPassword = ''
  formRef.value?.resetFields()
}
const handleChangePassword = async () => {
  await formRef.value?.validate()
  const result = await usePost<boolean>('/user/change_password', {
    old_password: form.oldPassword,
    new_password: form.newPassword,
  }).promiseResult()
  if (result){
    ElMessage.success(t('message.modifiedSuccess'))
    close()
    emit('success')
  }
}
</script>
<style lang="scss" module>
.change-password-dialog {
  padding:0 !important;
  :global(.el-dialog__title) {
    font-weight: var(--weight-font-Blod) !important;
  }
  :global(.el-dialog__body){
    padding:0 32px !important;
  }
  :global(.el-dialog__headerbtn){
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :global(.dialog-footer .el-button){
    min-width:88px;
  }
  .new-pass {
    margin-bottom: 0 !important;
    :global(.el-form-item__error) {
      display: none;
    }
  }
  .password-init {
    margin-top: 12px;
    margin-bottom: 12px;
  }
  .view-icon{
    margin-right: 6px;
    margin-left: 5px;
  }
}
</style>
<style lang="scss" scope>
.el-dialog .el-dialog__body{
  padding: 0 32px;
}
</style>
