<!-- Generated by SpecStory -->

# 拆分Swiper组件为子组件 (2025-03-07 12:59:36)

_**User**_

请你帮我把 @index.vue 中对于swiper-container部分的UI以及逻辑拆分出来，变为一个子组件

---

_**Assistant**_

```thinking
好的，我需要帮助用户将dashboard/index.vue中的swiper-container部分拆分成一个独立的子组件。让我先理解当前代码中swiper相关的部分，然后设计一个合适的组件拆分方案。

首先，我需要找出与swiper相关的所有代码部分：

1. 模板部分：
- 在template中有一个`:class="$style['swiper-container']"`的div
- 内部包含Swiper和SwiperSlide组件
- 还有一个`:class="$style['swiper-navigation']"`的div用于导航按钮

2. 脚本部分：
- 导入了Swiper, SwiperSlide组件和SwiperType类型
- 导入了Pagination, Navigation模块
- 定义了swiperModules常量
- 定义了swiperInstance, currentSlideIndex, totalSlides等ref和computed属性
- 定义了onSwiperInit, onSlideChange, handlePrevSlide, handleNextSlide等方法
- 还有isFirstSlide, isLastSlide计算属性
- 使用了standardReports数据和isStandardReportsLoading加载状态
- 定义了mapAgentData方法将ReportList数据映射为Agent数据

3. 样式部分：
- 定义了.swiper-container, .swiper-slide-card, .vertical-swiper等样式
- 定义了.swiper-navigation, .swiper-button等导航按钮样式
- 定义了.pagination-indicator样式

现在，我需要设计一个新的组件，将这些相关代码提取出来。我会创建一个名为StandardReportsSwiper.vue的组件，它应该：

1. 接收standardReports数据作为props
2. 接收isStandardReportsLoading加载状态作为props
3. 提供点击报告时的事件回调
4. 包含所有swiper相关的逻辑和样式

让我开始设计这个组件：
```

# 拆分Swiper组件

我将帮您把dashboard/index.vue中的swiper-container部分拆分成一个独立的子组件。

## 新组件代码

首先，创建一个新的组件文件：

```vue:src/pages/__components__/DashboardComponents/StandardReportsSwiper.vue
<template>
  <div :class="$style['swiper-container']" v-loading="loading">
    <Swiper
      :modules="swiperModules"
      :direction="'vertical'"
      :pagination="{
        clickable: true
      }"
      class="w-full vertical-swiper"
      :style="{ height: '220px' }"
      @swiper="onSwiperInit"
      @slideChange="onSlideChange">
      <SwiperSlide 
        v-for="report in reports" 
        :key="report.id"
        class="swiper-slide-card">
        <AgentCompletedCard
          :key="report.id"
          class="w-full cursor-pointer overflow-hidden"
          v-bind="mapAgentData(report)"
          @click="handleReportClick(mapAgentData(report))">
          <template #duty>
            <div class="flex items-start gap-2 flex-col">
              <span class="text-main-color text-sm">{{ report.name }}</span>
              <div v-if="report.updated_at">
                <span class="text-gray-500 text-xs">{{ $t("agent.updated_at") }}: </span>
                <span class="text-main-color text-sm">{{
                  formatCalendarDateTime(dayjs(report.updated_at).valueOf())
                }}</span>
              </div>
            </div>
          </template>
          <template #summary>
            <div
              class="text-main-color text-sm text-start w-full overflow-hidden mt-2">
              <OverClampText
                :content="report.summary"
                :line-clamp="3"
                class="leading-6"/>
            </div>
          </template>
        </AgentCompletedCard>
      </SwiperSlide>
    </Swiper>
    
    <!-- 导航按钮 -->
    <div :class="$style['swiper-navigation']">
      <button 
        :class="[
          $style['swiper-button'], 
          $style['swiper-button-prev'],
          { [$style['swiper-button-disabled']]: isFirstSlide }
        ]" 
        @click="handlePrevSlide"
        :disabled="isFirstSlide">
        <Icon icon="mdi:chevron-up" class="w-6 h-6"/>
      </button>
      
      <!-- 页码指示器 -->
      <div :class="$style['pagination-indicator']">
        {{ currentSlideIndex }} / {{ totalSlides }}
      </div>
      
      <button 
        :class="[
          $style['swiper-button'], 
          $style['swiper-button-next'],
          { [$style['swiper-button-disabled']]: isLastSlide }
        ]" 
        @click="handleNextSlide"
        :disabled="isLastSlide">
        <Icon icon="mdi:chevron-down" class="w-6 h-6"/>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { AgentCompletedCard } from '@specificlab/ui'
import { AVATAR_MAP } from '@/composables/useReportProgress'
import { useI18n } from 'vue-i18n'
import OverClampText from '@/components/OverClampText.vue'
import { Icon } from '@iconify/vue'
import dayjs from 'dayjs'
import { formatCalendarDateTime } from '@/utils/filter'
import { Swiper, SwiperSlide } from 'swiper/vue'
import type { Swiper as SwiperType } from 'swiper/types'
import 'swiper/css'
import 'swiper/css/pagination'
import { Pagination, Navigation } from 'swiper/modules'
import type { ReportList } from '@/pages/format-report/type'
import type { Agent } from '@/pages/dashboard/type'

const { t } = useI18n()

const props = defineProps<{
  reports: ReportList[]
  loading: boolean
}>()

const emit = defineEmits<{
  (e: 'reportClick', agent: Agent): void
}>()

// Swiper相关
const swiperModules = [Pagination, Navigation]
const swiperInstance = ref<SwiperType | null>(null)

const currentSlideIndex = ref(1)
const totalSlides = computed(() => props.reports?.length || 0)

const onSwiperInit = (swiper: SwiperType) => {
  swiperInstance.value = swiper
  
  // 添加额外的事件监听
  swiper.on('slideChangeTransitionStart', () => {
    // 可以在这里添加幻灯片切换开始时的动画效果
  })
  
  swiper.on('slideChangeTransitionEnd', () => {
    // 可以在这里添加幻灯片切换结束时的动画效果
  })
}

const onSlideChange = (swiper: SwiperType) => {
  // 更新当前页码（Swiper的activeIndex是从0开始的，所以+1）
  currentSlideIndex.value = swiper.activeIndex + 1
}

// 将ReportList数据映射为Agent数据
const mapAgentData = (report: ReportList) => {
  return {
    id: report.id,
    name: report.agent_name || t('agent.no_agent_name', { name: report.name }),
    duty: report.name,
    report_type: report.report_type,
    summary: report.summary || '',
    status: 'normal', // 默认状态，因为ReportList中没有risk_status字段
    avatar: report.report_type ? AVATAR_MAP[report.report_type as keyof typeof AVATAR_MAP] : undefined,
    updated_at: report.updated_at,
  } as Agent
}

// 添加计算属性判断是否是第一页或最后一页
const isFirstSlide = computed(() => currentSlideIndex.value === 1)
const isLastSlide = computed(() => currentSlideIndex.value === totalSlides.value)

// 修改handlePrevSlide和handleNextSlide方法，添加禁用状态检查
const handlePrevSlide = () => {
  if (swiperInstance.value && !isFirstSlide.value) {
    swiperInstance.value.slidePrev()
  }
}

const handleNextSlide = () => {
  if (swiperInstance.value && !isLastSlide.value) {
    swiperInstance.value.slideNext()
  }
}

const handleReportClick = (agent: Agent) => {
  emit('reportClick', agent)
}
</script>

<style lang="scss" module>
.swiper-container {
  width: 100%;
  height: auto;
  min-height: 220px;
  position: relative;
  
  @media (max-width: 768px) {
    min-height: 220px;
    padding: 8px;
  }
}

:global(.swiper-slide-card) {
  width: 100%;
  height: auto;
  min-height: 220px;
  display: flex;
  justify-content: center;
  align-items: center;
}

:global(.vertical-swiper) {
  width: 100%;
  height: auto;
  position: relative;
}

.swiper-navigation {
  position: absolute;
  bottom: 5px;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.swiper-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--brand-color-3);
  border: 2px solid var(--brand-color-3);
  color: var(--brand-color-1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  
  &:hover {
    background-color: var(--brand-color-3);
    color: white;
    transform: scale(1.05);
  }
  
  &:focus {
    outline: none;
  }
  
  :global(.iconify) {
    width: 24px !important;
    height: 24px !important;
  }
}

.swiper-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e0e0e0;
  border-color: #d0d0d0;
  color: #a0a0a0;
  box-shadow: none;
  
  &:hover {
    background-color: #e0e0e0;
    color: #a0a0a0;
    transform: none;
  }
}

.swiper-button-prev, .swiper-button-next {
  margin: 0;
}

.pagination-indicator {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

## 修改主文件

现在，需要修改主文件来使用这个新组件：

```vue:src/pages/dashboard/index.vue
// ... existing code ...
<div :class="$style['scrollable-agents-container']" v-show="(standardReports?.length || 0) > 0">
  <div :class="$style['agent-section-header']" class="justify-between">
    <div class="flex items-center gap-2">
      <Icon icon="mdi:format-list-text" class="text-blue-500 w-5 h-5"/>
      <span>{{ $t("agent.regular_agents") }}</span>
      <el-tooltip :content="$t('agent.regular_agents_tooltip')" placement="top">
        <Icon icon="mdi:help-circle-outline" class="text-gray-400 w-4 h-4 cursor-help"/>
      </el-tooltip>
    </div>
    <div class="flex items-center gap-3">
      <router-link 
        to="/format-report" 
        class="flex items-center gap-2 text-sm text-main-color hover:underline">
        <span>{{ $t("agent.view_all_reports") }}</span>
        <Icon icon="mdi:arrow-right" class="w-4 h-4"/>
      </router-link>
    </div>
  </div>
  
  <StandardReportsSwiper 
    :reports="standardReports" 
    :loading="isStandardReportsLoading"
    @reportClick="handleAgentClick" />
</div>
// ... existing code ...

<script setup lang="ts">
// ... existing code ...
import StandardReportsSwiper from '@/pages/__components__/DashboardComponents/StandardReportsSwiper.vue'
// ... existing code ...

// 可以删除以下代码，因为它们已经移到了子组件中：
// - swiperModules
// - swiperInstance
// - currentSlideIndex, totalSlides
// - onSwiperInit, onSlideChange
// - isFirstSlide, isLastSlide
// - handlePrevSlide, handleNextSlide
// - mapAgentData方法（如果不在其他地方使用）

// 保留这些代码，因为它们仍然需要在主组件中使用：
// - standardReports状态
// - getStandardReports方法
// - isStandardReportsLoading状态
// - handleAgentClick方法
// ... existing code ...
</script>

<style lang="scss" module>
// ... existing code ...

// 可以删除以下样式，因为它们已经移到了子组件中：
// - .swiper-container
// - .swiper-slide-card
// - .vertical-swiper
// - .swiper-navigation
// - .swiper-button, .swiper-button-disabled
// - .swiper-button-prev, .swiper-button-next
// - .pagination-indicator

// ... existing code ...
</style>
```

## 说明

1. 我创建了一个新的组件 `StandardReportsSwiper.vue`，它包含了所有与Swiper相关的逻辑和样式。

2. 这个组件接收两个props：
   - `reports`: 标准报告数据列表
   - `loading`: 加载状态

3. 组件会发出一个事件：
   - `reportClick`: 当用户点击报告时触发，传递被点击的报告数据

4. 在主文件中，我们只需要：
   - 导入新组件
   - 传递必要的props
   - 监听reportClick事件
   - 删除已移至子组件的代码和样式

5. 这样拆分后，主组件变得更加简洁，而Swiper相关的逻辑被封装在一个专门的组件中，提高了代码的可维护性和复用性。

---

_**User**_

请你根据 @risk_manger.md 的接口文档在 @index.vue 的onMounted中请求/risk_company/list的接口去请求风险公司列表的数据

---

_**Assistant**_



````thinking
我需要根据risk_manger.md中的接口文档，在dashboard/index.vue的onMounted中添加请求/risk_company/list接口的代码，用于获取风险公司列表数据。

首先，让我分析一下risk_manger.md中的接口文档：

1. 接口URL: `/api/risk_company/list`
2. 方法: GET
3. 参数:
   - limit: 每次请求返回的记录数量，默认50
   - last_id: 上一页最后一条记录的ID，用于分页
   - search: 搜索关键词
   - agent_type: 按agent类型过滤
   - sort: 排序字段，默认-updated_at

4. 响应数据结构:
   - data: 风险公司记录列表
   - has_more: 是否还有更多数据
   - next_cursor: 下一页的游标值

现在，我需要在dashboard/index.vue的onMounted中添加请求这个接口的代码。我会使用useGet钩子函数，类似于现有的getStandardReports函数。

我需要:
1. 创建一个新的state来存储风险公司列表数据
2. 创建一个新的useGet钩子来请求接口
3. 在onMounted中调用这个接口
4. 可能需要添加相关的类型定义

让我开始编写代码：
````

# 添加风险公司列表接口请求

根据risk_manger.md的接口文档，我将在dashboard/index.vue中添加请求风险公司列表的代码。

````vue:src/pages/dashboard/index.vue
// ... existing code ...
<script setup lang="ts">
import { ref, computed, onUnmounted, onMounted, watch } from 'vue'
import { definePage, useRouter } from 'vue-router/auto'
import {
  AgentCompletedCard,
  AgentRunningCard,
  AgentsResponseCard,
} from '@specificlab/ui'
import { useReportProgress, AVATAR_MAP, SPECIAL_AGENT_TYPES } from '@/composables/useReportProgress'
import { useI18n } from 'vue-i18n'
import { usePost, useGet } from '@/hooks/useHttp'
import type { CompletedAgent, Agent } from './type'
import OverClampText from '@/components/OverClampText.vue'
import EmptyAgentToMarket from '@/pages/__components__/DashboardComponents/EmptyAgentToMarket.vue'
import { Icon } from '@iconify/vue'
import dayjs from 'dayjs'
import emitter from '@/utils/events'
import SpecialAgentCard from '@/pages/__components__/DashboardComponents/SpecialAgentCard.vue'
import AgentTableDrawer from '@/pages/__components__/DashboardComponents/AgentTableDrawer.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { formatCalendarDateTime } from '@/utils/filter'
import { Swiper, SwiperSlide } from 'swiper/vue'
import type { Swiper as SwiperType } from 'swiper/types'
import 'swiper/css'
import 'swiper/css/pagination'
import { Pagination, Navigation } from 'swiper/modules'

import type { ReportList } from '@/pages/format-report/type'

// 添加风险公司列表的接口类型定义
interface RiskCompany {
  id: string;
  name: string;
  description: string;
  risk_level: string;
  agent_type: string;
  updated_at: string;
  // 根据实际接口返回数据添加其他字段
}

interface RiskCompanyResponse {
  data: RiskCompany[];
  has_more: boolean;
  next_cursor: string | null;
}

const { t } = useI18n()

// ... existing code ...

// 添加风险公司列表状态
const riskCompanies = ref<RiskCompany[]>([])
const hasMoreRiskCompanies = ref(false)
const nextCursor = ref<string | null>(null)
const isRiskCompaniesLoading = ref(false)

// 添加获取风险公司列表的函数
const {
  state: riskCompanyResponse,
  execute: getRiskCompanies,
  isLoading: isRiskCompaniesLoadingState
} = useGet<RiskCompanyResponse>(
  '/api/risk_company/list',
  {
    limit: 20,
    sort: '-updated_at'
  },
  {
    immediate: false
  }
)

// 监听风险公司列表响应
watch(riskCompanyResponse, (response) => {
  if (response) {
    if (nextCursor.value === null) {
      // 首次加载或重置
      riskCompanies.value = response.data
    } else {
      // 加载更多
      riskCompanies.value = [...riskCompanies.value, ...response.data]
    }
    hasMoreRiskCompanies.value = response.has_more
    nextCursor.value = response.next_cursor
  }
}, { deep: true })

// 加载更多风险公司
const loadMoreRiskCompanies = async () => {
  if (hasMoreRiskCompanies.value && nextCursor.value) {
    await getRiskCompanies(0, {
      limit: 20,
      last_id: nextCursor.value,
      sort: '-updated_at'
    })
  }
}

// 搜索风险公司
const searchRiskCompanies = async (keyword: string, agentType?: string) => {
  // 重置分页状态
  nextCursor.value = null
  
  const params: Record<string, any> = {
    limit: 20,
    sort: '-updated_at'
  }
  
  if (keyword) {
    params.search = keyword
  }
  
  if (agentType) {
    params.agent_type = agentType
  }
  
  await getRiskCompanies(0, params)
}

// 修改onMounted函数，添加获取风险公司列表
onMounted(async () => {
  open()
  currentPage.value = 1
  await getAgentList(0, {
    page: currentPage.value,
    per_page: 3, // 修改为只请求3个fixed agent
  })
  await refreshAsyncReportList()
  
  // 获取风险公司列表
  isRiskCompaniesLoading.value = true
  try {
    await getRiskCompanies()
    console.log('风险公司列表:', riskCompanyResponse.value?.data)
  } catch (error) {
    console.error('获取风险公司列表失败:', error)
    ElMessage.error(t('common.request_failed'))
  } finally {
    isRiskCompaniesLoading.value = false
  }
})

// 监听isRiskCompaniesLoadingState
watch(isRiskCompaniesLoadingState, (loading) => {
  isRiskCompaniesLoading.value = loading
})

// ... existing code ...
</script>
// ... existing code ...
````

## 说明

我已经根据risk_manger.md中的接口文档，在dashboard/index.vue中添加了请求风险公司列表的代码：

1. 添加了接口相关的类型定义：
   - `RiskCompany` 接口定义了风险公司的数据结构
   - `RiskCompanyResponse` 接口定义了接口返回的完整数据结构

2. 添加了状态管理：
   - `riskCompanies` - 存储风险公司列表数据
   - `hasMoreRiskCompanies` - 是否还有更多数据
   - `nextCursor` - 下一页的游标值
   - `isRiskCompaniesLoading` - 加载状态

3. 使用`useGet`钩子创建了`getRiskCompanies`函数，用于请求风险公司列表接口

4. 添加了数据处理函数：
   - 监听`riskCompanyResponse`，处理返回的数据
   - `loadMoreRiskCompanies` - 加载更多数据
   - `searchRiskCompanies` - 搜索和筛选数据

5. 在`onMounted`中添加了请求风险公司列表的代码

这样，当页面加载时，就会自动请求风险公司列表数据。您可以根据需要在页面中展示这些数据，或者添加搜索、筛选和加载更多的功能。

---

