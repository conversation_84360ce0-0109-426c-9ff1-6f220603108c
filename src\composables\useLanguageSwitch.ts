import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import { getI18nInstance, updateLocaleResources } from '@/lang/i18n'
import type { Language } from '@/types/language'

interface LanguageConfirmMessages {
  chinese: {
    title: string
    message: string
    confirm: string
    cancel: string
  }
  english: {
    title: string
    message: string
    confirm: string
    cancel: string
  }
  japanese: {
    title: string
    message: string
    confirm: string
    cancel: string
  }
}

const CONFIRM_MESSAGES: LanguageConfirmMessages = {
  'chinese': {
    title: '切换语言',
    message: '确定要切换语言吗？页面将会刷新',
    confirm: '确定',
    cancel: '取消'
  },
  'english': {
    title: 'Switch Language',
    message: 'Are you sure you want to switch language? The page will refresh.',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  'japanese': {
    title: '言語切り替え',
    message: '言語を切り替えますか？ページが更新されます。',
    confirm: '確認',
    cancel: 'キャンセル'
  }
}

export function useLanguageSwitch() {
  const { locale } = useI18n()

  const showLanguageConfirm = async (targetLanguage: Language): Promise<boolean> => {
    const currentLang = locale.value as Language
    const messages = CONFIRM_MESSAGES[currentLang as keyof typeof CONFIRM_MESSAGES]

    try {
      await ElMessageBox.confirm(
        messages.message,
        messages.title,
        {
          confirmButtonText: messages.confirm,
          cancelButtonText: messages.cancel,
          type: 'warning',
        }
      )
      return true
    } catch {
      return false
    }
  }

  const switchLanguage = async (
    language: Language,
    onConfirmed?: (lang: Language) => Promise<void>
  ): Promise<void> => {
    // 如果目标语言与当前语言相同，直接返回
    if (language === locale.value) {return}

    const confirmed = await showLanguageConfirm(language)
    if (!confirmed) {return}

    try {
      if (onConfirmed) {
        await onConfirmed(language)
      }
      
      await updateLocaleResources(language)
      const i18n = getI18nInstance()
      i18n.global.locale.value = language
      
      window.location.reload()
    } catch (error) {
      console.error('Failed to switch language:', error)
      // 使用 Element Plus 的消息提示显示错误
      ElMessageBox.alert(
        CONFIRM_MESSAGES[locale.value as keyof typeof CONFIRM_MESSAGES].message,
        'Error',
        {
          type: 'error',
        }
      )
      throw error
    }
  }

  return {
    switchLanguage,
    currentLocale: locale
  }
}