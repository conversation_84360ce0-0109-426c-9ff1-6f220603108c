<template>
  <div class="h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-slate-50/90 p-4 md:p-6">
    <div class="grid grid-cols-1 lg:grid-cols-[1fr_320px] gap-6 h-full overflow-hidden relative">
      <!-- Main Content Area -->
      <div class="flex flex-col gap-4 min-h-0">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 flex-shrink-0">
          <h1 class="text-2xl font-bold bg-gradient-to-r from-violet-600 to-cyan-500 bg-clip-text text-transparent">
            AI Agent Reports
          </h1>
            
          <div class="relative">
            <Button 
              variant="outline" 
              class="flex items-center gap-2 bg-white/80 backdrop-blur-sm w-full sm:w-auto shadow-sm border-slate-200"
              @click="showReportTypeMenu = !showReportTypeMenu">
              <span>{{ selectedReportType || 'All Report Types' }}</span>
              <ChevronDownIcon class="h-4 w-4 text-slate-500"/>
            </Button>
              
            <div 
              v-if="showReportTypeMenu" 
              class="absolute z-10 mt-1 w-full bg-white/95 backdrop-blur-md rounded-lg shadow-lg py-1 border border-slate-100">
              <div 
                class="px-4 py-2 hover:bg-slate-50 cursor-pointer transition-colors"
                @click="selectedReportType = null; showReportTypeMenu = false">
                All Report Types
              </div>
              <div 
                v-for="type in reportTypes" 
                :key="type"
                class="px-4 py-2 hover:bg-slate-50 cursor-pointer transition-colors"
                @click="selectedReportType = type; showReportTypeMenu = false">
                {{ type }}
              </div>
            </div>
          </div>
        </div>
          
        <!-- Chat Feed Card -->
        <Card class="bg-white/70 backdrop-blur-md border-slate-100 shadow-sm flex flex-col flex-1 min-h-0 overflow-hidden">
          <CardHeader class="border-b border-slate-100 bg-white/50 backdrop-blur-sm flex-shrink-0">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <ActivityIcon class="h-4 w-4 text-violet-500"/>
                <h2 class="text-lg font-semibold text-slate-800">Agent Activity Feed</h2>
              </div>
              <Badge variant="outline" class="bg-white/80 shadow-sm text-slate-600 border-slate-200">
                {{ sortedReports.length }} Reports
              </Badge>
            </div>
          </CardHeader>
            
          <CardContent class="p-0 flex-1 min-h-0 overflow-hidden">
            <ScrollArea class="h-full">
              <div class="p-4">
                <!-- Empty State -->
                <div v-if="sortedReports.length === 0" class="flex flex-col items-center justify-center py-20 text-center">
                  <div class="bg-gradient-to-br from-slate-50 to-white p-6 rounded-full mb-4 shadow-md">
                    <div class="relative">
                      <MessageSquarePlusIcon class="h-12 w-12 text-violet-400"/>
                      <BellRingIcon class="h-6 w-6 text-amber-400 absolute -top-1 -right-1"/>
                    </div>
                  </div>
                  <h2 class="text-lg font-semibold mb-2 text-slate-800">No Reports Available</h2>
                  <p class="text-sm text-slate-500 mb-4 max-w-md">
                    Your AI agents will report their activities here. Assign a task to get started.
                  </p>
                  <Button class="flex gap-2 items-center bg-gradient-to-r from-violet-600 to-cyan-500 text-white shadow-md hover:shadow-lg transition-all">
                    <PlusIcon class="h-4 w-4"/>
                    Assign New Task
                  </Button>
                </div>
                  
                <!-- Chat Feed -->
                <div v-else class="space-y-8">
                  <!-- Group by date -->
                  <template v-for="(group, date) in groupedReports" :key="date">
                    <!-- Date Separator -->
                    <div class="relative flex items-center py-4">
                      <div class="flex-grow border-t border-slate-100"></div>
                      <span class="flex-shrink mx-4 text-sm font-medium text-slate-500 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full shadow-sm">
                        {{ formatDateHeader(date) }}
                      </span>
                      <div class="flex-grow border-t border-slate-100"></div>
                    </div>
                      
                    <!-- Reports for this date -->
                    <div v-for="report in group"
                         :key="report.id" 
                         class="flex gap-3 animate-in fade-in slide-in-from-bottom-5 duration-300"
                         :class="{'justify-end': !isMonitorAgent(report) && !isRunningAgent(report)}">
                        
                      <!-- Avatar (only for monitor agents and running agents) -->
                      <div v-if="isMonitorAgent(report) || isRunningAgent(report)" class="relative flex-shrink-0 mt-1">
                        <Avatar class="ring-2 ring-white shadow-sm h-10 w-10">
                          <AvatarImage v-if="getAgentAvatar(report)" :src="getAgentAvatar(report)" :alt="getAgentName(report)"/>
                          <AvatarFallback v-if="isMonitorAgent(report)" class="bg-gradient-to-br from-amber-100 to-orange-100 text-amber-600">
                            <BellRingIcon class="h-5 w-5"/>
                          </AvatarFallback>
                          <AvatarFallback v-else class="bg-gradient-to-br from-violet-100 to-slate-100 text-violet-600">
                            {{ getInitials(getAgentName(report)) }}
                          </AvatarFallback>
                        </Avatar>
                        <span 
                          class="absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white"
                          :class="getStatusIndicatorClass(report)">
                        </span>
                      </div>
                        
                      <!-- Message Content -->
                      <div class="flex-1 space-y-2" :class="{'max-w-[calc(100%-60px)]': isMonitorAgent(report) || isRunningAgent(report), 'max-w-[75%]': !isMonitorAgent(report) && !isRunningAgent(report)}">
                        <!-- Agent Info -->
                        <div class="flex justify-between items-center" :class="{'flex-row-reverse': !isMonitorAgent(report) && !isRunningAgent(report)}">
                          <div>
                            <span class="font-medium text-sm text-slate-800">
                              {{ getAgentName(report) }}
                            </span>
                            <span class="text-xs text-slate-500 ml-2">{{ getReportType(report) }}</span>
                            <span v-if="isMonitorAgent(report)" class="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                              <BellIcon class="h-3 w-3 mr-1"/>
                              Automated Monitoring
                            </span>
                          </div>
                          <span class="text-xs text-slate-500">{{ getFormattedTime(report) }}</span>
                        </div>
                          
                        <!-- Message Card - Monitor/Alert Agent -->
                        <Card 
                          v-if="isMonitorAgent(report)"
                          class="relative backdrop-blur-md shadow-sm hover:shadow-md transition-all overflow-hidden"
                          :class="[
                            getRiskStatusBorderClass(report),
                            getRiskStatusBgClass(report),
                            {'cursor-pointer': isCompleted(report)},
                          ]"
                          @click="isCompleted(report) && viewReport(report)">
                            
                          <!-- Left accent border for monitor agents -->
                          <div class="absolute left-0 top-0 bottom-0 w-1.5" :class="getRiskStatusAccentClass(report)"></div>
                            
                          <CardHeader class="py-3 px-4 bg-white/20 backdrop-blur-sm flex flex-row items-center justify-between pl-6">
                            <div class="flex items-center gap-2 max-w-[70%]">
                              <Badge :variant="getRiskStatusVariant(report)" class="shadow-sm shrink-0">
                                {{ getRiskStatusLabel(report) }}
                              </Badge>
                              <span class="text-sm font-medium text-slate-800 truncate">{{ getReportName(report) }}</span>
                            </div>
                            <Badge variant="outline" class="bg-white/60 text-slate-600 border-slate-200 shadow-sm shrink-0">
                              {{ getFrequencyLabel(report) }}
                            </Badge>
                          </CardHeader>
                            
                          <template v-if="isRunning(report)">
                            <CardContent class="p-4 pl-6">
                              <p class="text-sm mb-3 text-slate-700 break-words">
                                {{ getProgressMessage(report) }}
                              </p>
                              <div class="space-y-2">
                                <Progress :value="getReportProgress(report)"
                                          class="h-2"
                                          :class="{'bg-blue-100': isRunning(report)}"/>
                                <p class="text-xs text-right text-slate-500">
                                  Completion: {{ getReportProgress(report) }}%
                                </p>
                              </div>
                              <!-- Steps -->
                              <div v-if="hasSteps(report)" class="mt-4 space-y-2 max-h-[300px] overflow-y-auto pr-1">
                                <div 
                                  v-for="(step, index) in (report as any).steps" 
                                  :key="index" 
                                  class="p-3 rounded-lg border border-slate-100 bg-white/30 backdrop-blur-sm transition-all break-words"
                                  :class="{
                                    'border-l-4 border-l-blue-500 shadow-sm': step.status === 'PROCESSING',
                                    'border-l-4 border-l-amber-300': step.status === 'PENDING',
                                    'border-l-4 border-l-green-500': step.status === 'COMPLETED'
                                  }">
                                  <div class="flex justify-between items-center mb-1 gap-2">
                                    <span class="text-sm font-medium text-slate-800 truncate">{{ step.title }}</span>
                                    <Badge :variant="getStepStatusVariant(step.status)" class="text-xs shrink-0" :class="getStepStatusBadgeClass(step.status)">
                                      {{ step.status }}
                                    </Badge>
                                  </div>
                                  <p v-if="step.description" class="text-xs text-slate-500 break-words">
                                    {{ step.description }}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </template>
                          <template v-else>
                            <CardContent class="p-4 pl-6">
                              <!-- Summary Items as List -->
                              <div v-if="Array.isArray(report.summary) && report.summary.length > 0" class="space-y-2">
                                <div v-for="(item, index) in report.summary"
                                     :key="index" 
                                     class="flex items-start gap-2 p-2 rounded-md bg-white/40 backdrop-blur-sm border border-slate-100 shadow-sm">
                                  <AlertCircleIcon v-if="report.risk_status === 'Danger'" class="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0"/>
                                  <AlertTriangleIcon v-else-if="report.risk_status === 'Warning'" class="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0"/>
                                  <CheckCircleIcon v-else class="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0"/>
                                  <span class="text-sm text-slate-700 break-words">{{ item }}</span>
                                </div>
                              </div>
                                
                              <!-- Single Summary -->
                              <p v-else class="text-sm leading-relaxed text-slate-700 break-words">
                                {{ getSummary(report) }}
                              </p>
                                
                              <!-- View Full Report Button -->
                              <div class="flex justify-end mt-3">
                                <Button size="sm" variant="outline" class="flex items-center gap-1.5 text-xs bg-white/50 hover:bg-white/80 border-slate-200 text-slate-700">
                                  <ExternalLinkIcon class="h-3.5 w-3.5"/>
                                  View Full Report
                                </Button>
                              </div>
                            </CardContent>
                          </template>
                        </Card>
                          
                        <!-- Message Card - Running Agents -->
                        <Card 
                          v-else-if="isRunningAgent(report)"
                          class="backdrop-blur-md border-l-4 shadow-sm hover:shadow-md transition-all"
                          :class="[
                            getStatusBorderClass(report),
                            getStatusBgClass(report),
                          ]">
                            
                          <CardHeader class="py-3 px-4 bg-white/20 backdrop-blur-sm flex flex-row items-center justify-between">
                            <div class="flex items-center gap-2">
                              <Badge :variant="getStatusVariant(report)" :class="getStatusBadgeClass(report)">
                                {{ getStatusLabel(report) }}
                              </Badge>
                              <span class="text-sm font-medium text-slate-800">{{ getReportName(report) }}</span>
                            </div>
                          </CardHeader>
                            
                          <CardContent class="p-4">
                            <!-- Pending Status -->
                            <template v-if="isPending(report)">
                              <p class="text-sm text-slate-600 mb-3 break-words">{{ getReportDescription(report) }}</p>
                              <div class="flex justify-center space-x-2 py-2">
                                <span class="h-2 w-2 bg-amber-400 rounded-full animate-bounce" style="animation-delay: 0s"></span>
                                <span class="h-2 w-2 bg-amber-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></span>
                                <span class="h-2 w-2 bg-amber-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></span>
                              </div>
                            </template>
                              
                            <!-- Running Status -->
                            <template v-else-if="isRunning(report)">
                              <p class="text-sm mb-3 text-slate-700 break-words">
                                {{ getProgressMessage(report) }}
                              </p>
                              <div class="space-y-2">
                                <Progress :value="getReportProgress(report)"
                                          class="h-2 bg-slate-100" 
                                          :class="{'bg-blue-100': isRunning(report)}"/>
                                <p class="text-xs text-right text-slate-500">
                                  Completion: {{ getReportProgress(report) }}%
                                </p>
                              </div>
                                
                              <!-- Steps -->
                              <div v-if="hasSteps(report)" class="mt-4 space-y-2 max-h-[300px] overflow-y-auto pr-1">
                                <div 
                                  v-for="(step, index) in report.steps" 
                                  :key="index" 
                                  class="p-3 rounded-lg border border-slate-100 bg-white/30 backdrop-blur-sm transition-all break-words"
                                  :class="{
                                    'border-l-4 border-l-blue-500 shadow-sm': step.status === 'PROCESSING',
                                    'border-l-4 border-l-amber-300': step.status === 'PENDING',
                                    'border-l-4 border-l-green-500': step.status === 'COMPLETED'
                                  }">
                                  <div class="flex justify-between items-center mb-1 gap-2">
                                    <span class="text-sm font-medium text-slate-800 truncate">{{ step.title }}</span>
                                    <Badge :variant="getStepStatusVariant(step.status)" class="text-xs shrink-0" :class="getStepStatusBadgeClass(step.status)">
                                      {{ step.status }}
                                    </Badge>
                                  </div>
                                  <p v-if="step.description" class="text-xs text-slate-500 break-words">
                                    {{ step.description }}
                                  </p>
                                </div>
                              </div>
                            </template>
                          </CardContent>
                        </Card>
                          
                        <!-- Message Card - Completed Regular Reports (Chat Bubble Style) -->
                        <Card 
                          v-else
                          class="backdrop-blur-md shadow-sm hover:shadow-md transition-all cursor-pointer"
                          :class="[
                            'bg-violet-50/80 border border-violet-100',
                            {'ml-auto': !isMonitorAgent(report) && !isRunningAgent(report)},
                          ]"
                          @click="viewReport(report)">
                            
                          <CardContent class="p-4">
                            <p class="text-sm leading-relaxed text-slate-700">{{ getSummary(report) }}</p>
                              
                            <!-- View Full Report Button -->
                            <div class="flex justify-end mt-3">
                              <Button size="sm" variant="outline" class="flex items-center gap-1.5 text-xs bg-white/50 hover:bg-white/80 border-slate-200 text-slate-700">
                                <ExternalLinkIcon class="h-3.5 w-3.5"/>
                                View Full Report
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                        
                      <!-- Avatar for completed reports (right side) -->
                      <div v-if="!isMonitorAgent(report) && !isRunningAgent(report)" class="relative flex-shrink-0 mt-1">
                        <Avatar class="ring-2 ring-white shadow-sm h-10 w-10">
                          <AvatarImage v-if="getAgentAvatar(report)" :src="getAgentAvatar(report)" :alt="getAgentName(report)"/>
                          <AvatarFallback class="bg-gradient-to-br from-violet-100 to-slate-100 text-violet-600">
                            {{ getInitials(getAgentName(report)) }}
                          </AvatarFallback>
                        </Avatar>
                        <span 
                          class="absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white bg-green-500">
                        </span>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
        
      <!-- Toggle Sidebar Button (Mobile/Tablet) -->
      <Button 
        variant="outline" 
        size="icon" 
        class="fixed bottom-6 right-6 lg:hidden z-10 bg-white shadow-md border-slate-200 rounded-full h-12 w-12"
        @click="sidebarVisible = !sidebarVisible">
        <UsersIcon v-if="!sidebarVisible" class="h-5 w-5 text-violet-600"/>
        <XIcon v-else class="h-5 w-5 text-violet-600"/>
      </Button>
        
      <!-- Right Sidebar - Agent List -->
      <div 
        class="flex flex-col min-h-0 transition-all duration-300 ease-in-out">
        <Card class="bg-white/70 backdrop-blur-md border-slate-100 shadow-sm flex flex-col flex-1 min-h-0 overflow-hidden">
          <CardHeader class="border-b border-slate-100 bg-white/50 backdrop-blur-sm">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-slate-800">AI Agents</h2>
              <Badge variant="secondary" class="bg-violet-100 text-violet-700 border-0">{{ activeAgentsCount }} Active</Badge>
            </div>
              
            <div class="relative mt-2">
              <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"/>
              <Input placeholder="Search agents..." class="pl-9 bg-white/80 border-slate-200" v-model="agentSearch"/>
            </div>
          </CardHeader>
            
          <CardContent class="flex-1 min-h-0 overflow-hidden p-0">
            <ScrollArea class="h-full">
              <div class="p-3 space-y-1.5">
                <!-- All Agents Option -->
                <div 
                  class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors"
                  :class="{ 'bg-white/80 shadow-sm': !selectedAgent }" 
                  @click="filterByAgent(null)">
                  <div class="flex items-center gap-3">
                    <Avatar class="h-9 w-9 ring-2 ring-white/70">
                      <AvatarFallback class="bg-gradient-to-br from-violet-500 to-cyan-500 text-white">
                        <UsersIcon class="h-4 w-4"/>
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 class="text-sm font-medium text-slate-800">All Agents</h3>
                      <p class="text-xs text-slate-500">View all reports</p>
                    </div>
                  </div>
                </div>
                  
                <!-- Monitor/Alert Agents Group -->
                <div 
                  class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors"
                  :class="{ 'bg-white/80 shadow-sm': selectedAgent === 'monitor' }" 
                  @click="filterByAgent('monitor')">
                  <div class="flex items-center gap-3">
                    <Avatar class="h-9 w-9 ring-2 ring-white/70">
                      <AvatarFallback class="bg-gradient-to-br from-amber-400 to-orange-400 text-white">
                        <BellRingIcon class="h-4 w-4"/>
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 class="text-sm font-medium text-slate-800">Monitoring Agents</h3>
                      <p class="text-xs text-slate-500">Automated alerts & monitoring</p>
                      <div class="flex items-center gap-1.5 mt-1">
                        <Badge variant="outline" class="text-[10px] px-1 py-0 bg-amber-100 text-amber-800 border-0">
                          Automated
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
                  
                <!-- Agent List -->
                <div 
                  v-for="agent in filteredAgents"
                  :key="agent.name"
                  class="p-2 rounded-lg hover:bg-white/60 cursor-pointer transition-colors"
                  :class="{ 'bg-white/80 shadow-sm': selectedAgent === agent.name }"
                  @click="filterByAgent(agent.name)">
                  <div class="flex items-center gap-3">
                    <div class="relative">
                      <Avatar class="h-9 w-9 ring-2 ring-white/70">
                        <AvatarImage :src="agent.avatar" :alt="agent.name"/>
                        <AvatarFallback class="bg-gradient-to-br from-violet-100 to-slate-100 text-violet-600">{{ getInitials(agent.name) }}</AvatarFallback>
                      </Avatar>
                      <span 
                        class="absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white"
                        :class="getAgentStatusClass(agent.status)">
                      </span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <h3 class="text-sm font-medium truncate text-slate-800">{{ agent.name }}</h3>
                      <p class="text-xs text-slate-500">{{ agent.role }}</p>
                      <div class="flex items-center gap-1.5 mt-1">
                        <Badge :variant="getAgentStatusVariant(agent.status)" class="text-[10px] px-1 py-0" :class="getAgentStatusBadgeClass(agent.status)">
                          {{ agent.status.charAt(0).toUpperCase() + agent.status.slice(1) }}
                        </Badge>
                        <span class="text-xs truncate max-w-[140px] text-slate-500">
                          {{ agent.currentTask || 'No active task' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
          </CardContent>
            
          <CardFooter class="border-t border-slate-100 p-4 bg-white/50 backdrop-blur-sm flex-shrink-0">
            <div class="grid grid-cols-2 gap-2 w-full">
              <Button class="flex gap-2 items-center bg-gradient-to-r from-violet-600 to-cyan-500 text-white shadow-sm hover:shadow-md transition-all">
                <PlusIcon class="h-4 w-4"/>
                New Task
              </Button>
              <Button variant="outline" class="flex gap-2 items-center border-slate-200 shadow-sm hover:shadow-md transition-all">
                <BellPlusIcon class="h-4 w-4 text-amber-500"/>
                Setup Alert
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { 
  ActivityIcon, SearchIcon, UsersIcon, PlusIcon, ChevronDownIcon, 
  ExternalLinkIcon, RefreshCwIcon, DownloadIcon, XIcon,
  MessageSquarePlusIcon, BellIcon, BellRingIcon, BellPlusIcon,
  AlertCircleIcon, AlertTriangleIcon, CheckCircleIcon
} from 'lucide-vue-next'
import { 
  Card, CardHeader, CardContent, CardFooter 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Avatar, AvatarFallback, AvatarImage 
} from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
  
  // --- Type Definitions ---
  type BadgeVariant = 'default' | 'outline' | 'destructive' | 'secondary' | null | undefined
  
  interface AgentStep {
    title: string;
    description: string;
    status: string; // 'PENDING', 'PROCESSING', 'COMPLETED', 'ERROR'
  }
  
  // Interface for monitor agents
  interface MonitorAgent {
    id: string;
    agent_name?: string;
    report_name: string;
    report_type: string;
    summary: string | string[];
    risk_status?: string;
    frequency?: number;
    created_at?: string;
    updated_at?: string;
    status?: string;
  }
  
  // Interface for running agents
  interface RunningAgent {
    id: string; 
    name: string;
    report_name: string; 
    duty: string;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'ERROR';
    avatar: string; 
    steps: AgentStep[];
    progress: number; 
    agent_name?: string; 
    report_type?: string; 
    date?: string;
    description?: string;
  }
  
  // Interface for standard reports
  interface StandardReport {
    id: string;
    name: string;
    agent_name?: string;
    report_type?: string;
    summary: string;
    updated_at?: string;
    language?: string;
  }
  
  type Report = MonitorAgent | StandardReport | RunningAgent;
  
// --- Mock Data ---
// Sample data for demonstration
const mockMonitorAgents: MonitorAgent[] = [
  {
    id: 'monitor-1',
    agent_name: 'Risk Monitor',
    report_name: 'Market Risk Assessment',
    report_type: 'RiskMonitor',
    summary: [
      'TSLA stock showing unusual volatility patterns',
      'AAPL trading volume increased by 45% in the last hour',
      'MSFT options activity suggests potential market movement'
    ],
    risk_status: 'Warning',
    frequency: 1,
    updated_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    status: 'completed'
  },
  {
    id: 'monitor-2',
    agent_name: 'Security Alert',
    report_name: 'System Security Scan',
    report_type: 'SecurityMonitor',
    summary: 'All systems operating normally. No security threats detected in the last scan.',
    risk_status: 'Safe',
    frequency: 7,
    updated_at: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
    status: 'completed'
  },
  {
    id: 'monitor-3',
    agent_name: 'Critical Alert',
    report_name: 'Database Performance',
    report_type: 'PerformanceMonitor',
    summary: [
      'Database server CPU usage at 92% for over 15 minutes',
      'Query response time increased by 300%',
      'Memory usage approaching critical threshold'
    ],
    risk_status: 'Danger',
    frequency: 1,
    updated_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    status: 'completed'
  }
]
  
const mockRunningAgents: RunningAgent[] = [
  {
    id: 'running-1',
    name: 'Data Analyst',
    report_name: 'Quarterly Sales Analysis',
    duty: 'Analyzing Q3 sales data across all regions',
    status: 'RUNNING',
    avatar: '',
    steps: [
      {
        title: 'Data Collection',
        description: 'Gathering sales data from all regional databases',
        status: 'COMPLETED'
      },
      {
        title: 'Data Cleaning',
        description: 'Normalizing data formats and removing outliers',
        status: 'COMPLETED'
      },
      {
        title: 'Statistical Analysis',
        description: 'Running regression models on sales trends',
        status: 'PROCESSING'
      },
      {
        title: 'Report Generation',
        description: 'Creating visualizations and summary documents',
        status: 'PENDING'
      }
    ],
    progress: 65,
    report_type: 'Analysis',
    date: new Date().toISOString(),
    description: 'Currently analyzing regional sales patterns and identifying growth opportunities'
  },
  {
    id: 'running-2',
    name: 'Market Research',
    report_name: 'Competitor Analysis',
    duty: 'Researching competitor product offerings',
    status: 'PENDING',
    avatar: '',
    steps: [
      {
        title: 'Initial Research',
        description: 'Identifying key competitors in the market',
        status: 'PENDING'
      },
      {
        title: 'Data Collection',
        description: 'Gathering product information and pricing',
        status: 'PENDING'
      }
    ],
    progress: 0,
    report_type: 'Research',
    date: new Date().toISOString(),
    description: 'Preparing to start competitor analysis'
  }
]
  
const mockStandardReports: StandardReport[] = [
  {
    id: 'report-1',
    name: 'Weekly Performance Summary',
    agent_name: 'Performance Tracker',
    report_type: 'PerformanceReport',
    summary: 'Overall system performance has improved by 12% this week. Response times are down by 15ms on average, and user satisfaction scores have increased to 4.8/5.',
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1 day ago
  },
  {
    id: 'report-2',
    name: 'Customer Feedback Analysis',
    agent_name: 'Customer Insights',
    report_type: 'FeedbackReport',
    summary: 'Analysis of 1,247 customer feedback items shows positive sentiment around the new UI (87% approval), with some concerns about loading times on mobile devices (23% of mobile users).',
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString() // 2 days ago
  },
  {
    id: 'report-3',
    name: 'Code Quality Assessment',
    agent_name: 'Code Reviewer',
    report_type: 'CodeQualityReport',
    summary: 'Latest codebase analysis shows 94% test coverage, a 3% improvement. Identified 12 potential optimizations in the authentication module that could improve login times by up to 40%.',
    updated_at: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString() // 3 days ago
  }
]
  
// --- State Variables ---
const realReports = ref<StandardReport[]>(mockStandardReports)
const monitorAgents = ref<MonitorAgent[]>(mockMonitorAgents)
const runningAgents = ref<RunningAgent[]>(mockRunningAgents)
  
const selectedAgent = ref<string | null>(null)
const selectedReportType = ref<string | null>(null)
const showReportTypeMenu = ref(false)
const agentSearch = ref('')
const sidebarVisible = ref(true)
// const window = ref({ innerWidth: typeof window.value !== 'undefined' ? window.value.innerWidth : 1200 })
  
// --- Computed Properties ---
// Merge all report sources
const allReports = computed<Report[]>(() => {
  const monitors: MonitorAgent[] = monitorAgents.value || []
  const running: RunningAgent[] = runningAgents.value || []
  const completed: StandardReport[] = realReports.value || []
  
  const reportsWithDate: Report[] = [
    ...monitors.map(r => ({ ...r, date: r.updated_at || new Date(0).toISOString(), status: r.status || 'completed' } as MonitorAgent & { date: string, status: string })),
    ...running.map(r => ({ ...r, date: r.date || new Date().toISOString() } as RunningAgent & { date: string })), 
    ...completed.map(r => ({ ...r, date: r.updated_at || new Date(0).toISOString(), status: 'completed' } as StandardReport & { date: string, status: string }))
  ]
  
  return reportsWithDate
})
  
// Sort reports by date
const sortedReports = computed(() => {
  const sorted = [...allReports.value].sort((a, b) => {
    const dateA = new Date((a as any).date || 0)
    const dateB = new Date((b as any).date || 0)
    return dateB.getTime() - dateA.getTime()
  })
    
  // Apply filters
  return sorted.filter(report => {
    if (selectedAgent.value) {
      if (selectedAgent.value === 'monitor') {
        if (!isMonitorAgent(report)) {return false}
      } else {
        if (getAgentName(report) !== selectedAgent.value) {return false}
      }
    }
    if (selectedReportType.value && getReportType(report) !== selectedReportType.value) {
      return false
    }
    return true
  })
})
  
// Group reports by date
const groupedReports = computed(() => {
  const grouped: Record<string, Report[]> = {}
    
  sortedReports.value.forEach(report => {
    const reportDateStr = (report as any).date || new Date(0).toISOString()
    const date = new Date(reportDateStr).toLocaleDateString()
      
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(report)
  })
    
  return grouped
})
  
// Generate agent list from reports
const agents = computed(() => {
  const uniqueAgents = new Map<string, { name: string; role: string; status: string; avatar: string; currentTask: string | null }>()
    
  const addOrUpdateAgent = (name: string, details: Partial<{ role: string; status: string; avatar: string; currentTask: string | null }>) => {
    const validName = name || 'Unknown Agent'
    if (validName === 'Unknown Agent') {
      return
    }
      
    const existing = uniqueAgents.get(validName) || { name: validName, role: 'Unknown Role', status: 'idle', avatar: '', currentTask: null }
        
    const newStatus = details.status?.toLowerCase() ?? existing.status
    const finalStatus = (newStatus === 'running' || newStatus === 'pending') ? newStatus : existing.status
  
    uniqueAgents.set(validName, { 
      ...existing, 
      ...details,
      role: details.role || existing.role,
      status: finalStatus 
    })
  }
  
  // Process running agents first 
  runningAgents.value.forEach(agent => {
    addOrUpdateAgent(agent.name, {
      role: agent.report_type || 'Running Task',
      status: agent.status.toLowerCase(),
      avatar: agent.avatar,
      currentTask: agent.report_name 
    })
  })
  
  // Process monitor agents
  monitorAgents.value.forEach(agent => {
    const name = agent.agent_name || agent.report_name || 'Monitor Agent'
    addOrUpdateAgent(name, {
      role: agent.report_type || 'Monitoring Agent',
      status: 'idle', 
      avatar: '', 
      currentTask: `Monitoring (${getFrequencyLabel(agent)})` 
    })
  })
  
  // Process completed reports
  realReports.value.forEach(report => {
    const name = report.agent_name || report.name || 'Report Agent'
    addOrUpdateAgent(name, {
      role: report.report_type || 'Reporting Agent',
      status: 'idle', 
      avatar: '',
      currentTask: null 
    })
  })
  
  return Array.from(uniqueAgents.values())
})
  
// Get all unique report types
const reportTypes = computed(() => {
  const types = new Set<string>()
  allReports.value.forEach(report => {
    const type = getReportType(report)
    if (type) {types.add(type)}
  })
  return Array.from(types)
})
  
// Filter agents based on search
const filteredAgents = computed(() => {
  if (!agentSearch.value) {return agents.value}
  const search = agentSearch.value.toLowerCase()
  return agents.value.filter(agent => 
    agent.name.toLowerCase().includes(search) || 
      (agent.role && agent.role.toLowerCase().includes(search))
  )
})
  
// Count active agents
const activeAgentsCount = computed(() => {
  return agents.value.filter(agent => 
    agent.status === 'running' || agent.status === 'pending'
  ).length
})
  
// --- Helper Functions ---
// Type guards
function isMonitorAgent(report: Report): report is MonitorAgent {
  return 'risk_status' in report || (report as MonitorAgent).report_type?.includes('Monitor') === true
}
  
function isRunningAgent(report: Report): report is RunningAgent {
  return 'steps' in report && 'progress' in report
}
  
function isStandardReport(report: Report): report is StandardReport {
  return 'name' in report && !('risk_status' in report) && !('steps' in report)
}
  
// Status checks
function isPending(report: Report): boolean {
  if (isRunningAgent(report)) {return report.status === 'PENDING'}
  return false
}
  
function isRunning(report: Report): boolean {
  if (isRunningAgent(report)) {return report.status === 'RUNNING'}
  if (isMonitorAgent(report)) {
    return runningAgents.value.some(task => task.id === report.id && task.status === 'RUNNING')
  }
  return false
}
  
function isCompleted(report: Report): boolean {
  if (isStandardReport(report)) {return true}
  if (isRunningAgent(report)) {return report.status === 'COMPLETED'}
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    return !runningTask || runningTask.status === 'COMPLETED'
  }
  return false
}
  
function isError(report: Report): boolean {
  if (isRunningAgent(report)) {return report.status === 'ERROR'}
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    return runningTask?.status === 'ERROR' || (report.status || '').toLowerCase() === 'error'
  }
  return false
}
  
function hasSteps(report: Report): boolean {
  return isRunningAgent(report) && report.steps && report.steps.length > 0
}
  
// Data extraction functions
function getReportType(report: Report): string {
  if (isMonitorAgent(report)) {return report.report_type || 'Monitor'}
  if (isRunningAgent(report)) {return report.report_type || 'Task'}
  if (isStandardReport(report)) {return report.report_type || 'Report'}
  return 'Unknown'
}
  
function getAgentName(report: Report): string {
  if (isMonitorAgent(report)) {return report.agent_name || report.report_name || 'Monitor Agent'}
  if (isRunningAgent(report)) {return report.name || 'Running Agent'} 
  if (isStandardReport(report)) {return report.agent_name || report.name || 'Report Agent'} 
  return 'Unknown Agent'
}
  
function getAgentAvatar(report: Report): string {
  const agentName = getAgentName(report)
  const agentInfo = agents.value.find(a => a.name === agentName)
  return agentInfo?.avatar || ''
}
  
function getInitials(name: string | undefined): string {
  if (!name || name === 'Unknown Agent') {return '?'}
  if (typeof name === 'string' && name.length > 0) {
    const nameParts = name.trim().split(/[\s_-]+/)
    if (nameParts.length > 1) {
      if (nameParts[0].length > 1 && nameParts[1].toLowerCase() === 'agent') {return nameParts[0][0].toUpperCase()}
      return nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase()
    } 
    return name[0].toUpperCase()
  }
  return '?'
}
  
function getReportName(report: Report): string {
  if (isMonitorAgent(report)) {return report.report_name || 'Monitor Report'}
  if (isRunningAgent(report)) {return report.report_name || 'Running Task'}
  if (isStandardReport(report)) {return report.name || 'Standard Report'}
  return 'Unknown Report'
}
  
function getReportDescription(report: Report): string {
  if (isMonitorAgent(report)) {return report.report_name || 'Monitoring task in progress'} 
  if (isRunningAgent(report)) {return report.duty || 'Processing task'}
  if (isStandardReport(report)) {return report.summary || 'Completed report'} 
  return 'Processing'
}
  
function getReportProgress(report: Report): number {
  if (isRunningAgent(report)) {return report.progress || 0}
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    if (runningTask) {return runningTask.progress || 0}
    return isCompleted(report) ? 100 : 0
  }
  if (isStandardReport(report)) {return 100}
  return 0
}
  
function getSummary(report: Report): string {
  let summaryText = 'Summary unavailable'
  if (isMonitorAgent(report)) {
    if (Array.isArray(report.summary)) {
      summaryText = report.summary.join('\n')
    } else {
      summaryText = report.summary || 'No monitoring data available'
    }
  } else if (isRunningAgent(report)) {
    summaryText = report.description || 'Task in progress'
  } else if (isStandardReport(report)) {
    summaryText = report.summary || 'No summary available'
  }
  return summaryText
}
  
function getFrequencyLabel(report: Report): string {
  if (isMonitorAgent(report) && report.frequency !== undefined) {
    const freq = report.frequency
    if (freq === 1) {return 'Daily'}
    if (freq === 7) {return 'Weekly'}
    if (freq === 30) {return 'Monthly'}
    return `Every ${freq} days`
  }
  return ''
}
  
// --- Status Styling Functions --- 
function getStatusIndicatorClass(report: Report): string {
  if (isRunning(report)) {return 'bg-blue-500 animate-pulse'}
  if (isPending(report)) {return 'bg-amber-500'}
  if (isError(report)) {return 'bg-red-500'}
  if (isCompleted(report)) {
    if (isMonitorAgent(report)) {
      switch ((report as MonitorAgent).risk_status?.toLowerCase()) {
      case 'danger': return 'bg-red-500'
      case 'warning': return 'bg-amber-500'
      case 'safe': default: return 'bg-green-500'
      }
    } else {
      return 'bg-green-500'
    }
  }
  return 'bg-gray-500'
}
  
function getStatusBorderClass(report: Report): string {
  if (!isMonitorAgent(report)) {
    if (isRunning(report)) {return 'border-l-blue-500'}
    if (isPending(report)) {return 'border-l-amber-500'}
    if (isCompleted(report)) {return 'border-l-green-500'}
    if (isError(report)) {return 'border-l-red-500'}
  }
  return 'border-l-slate-200'
}
  
function getStatusBgClass(report: Report): string {
  if (!isMonitorAgent(report)) {
    if (isRunning(report)) {return 'bg-blue-50/80'}
    if (isPending(report)) {return 'bg-amber-50/80'}
    if (isCompleted(report)) {return 'bg-green-50/80'}
    if (isError(report)) {return 'bg-red-50/80'}
  }
  return 'bg-white/80'
}
  
function getStatusBadgeClass(report: Report): string {
  if (!isMonitorAgent(report)) {
    if (isRunning(report)) {return 'bg-blue-100 text-blue-700 border-0'}
    if (isPending(report)) {return 'bg-amber-100 text-amber-700 border-0'}
    if (isCompleted(report)) {return 'bg-green-100 text-green-700 border-0'}
    if (isError(report)) {return 'bg-red-100 text-red-700 border-0'}
  }
  return 'bg-slate-100 text-slate-700 border-0'
}
  
// Risk Status styles (specifically for MonitorAgent cards)
function getRiskStatusBorderClass(report: Report): string {
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    if (runningTask?.status === 'RUNNING') {return 'border border-blue-200'}
    if (runningTask?.status === 'ERROR') {return 'border border-red-200'}
  
    switch ((report as MonitorAgent).risk_status?.toLowerCase()) {
    case 'danger': return 'border border-red-200'
    case 'warning': return 'border border-amber-200'
    case 'safe': default: return 'border border-green-200'
    }
  }
  return 'border border-slate-200'
}
  
function getRiskStatusBgClass(report: Report): string {
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    if (runningTask?.status === 'RUNNING') {return 'bg-blue-50/80'}
    if (runningTask?.status === 'ERROR') {return 'bg-red-50/80'}
  
    switch ((report as MonitorAgent).risk_status?.toLowerCase()) {
    case 'danger': return 'bg-red-50/80'
    case 'warning': return 'bg-amber-50/80'
    case 'safe': default: return 'bg-green-50/80'
    }
  }
  return 'bg-white/80'
}
  
function getRiskStatusAccentClass(report: Report): string {
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    if (runningTask?.status === 'RUNNING') {return 'bg-gradient-to-b from-blue-400 to-blue-600'}
    if (runningTask?.status === 'ERROR') {return 'bg-gradient-to-b from-red-400 to-red-600'}
  
    switch ((report as MonitorAgent).risk_status?.toLowerCase()) {
    case 'danger': return 'bg-gradient-to-b from-red-400 to-red-600'
    case 'warning': return 'bg-gradient-to-b from-amber-400 to-amber-600'
    case 'safe': default: return 'bg-gradient-to-b from-green-400 to-green-600'
    }
  }
  return 'bg-gradient-to-b from-slate-400 to-slate-600'
}
  
function getRiskStatusVariant(report: Report): BadgeVariant {
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    if (runningTask?.status === 'RUNNING') {return 'secondary'} 
    if (runningTask?.status === 'ERROR') {return 'destructive'}
  
    switch ((report as MonitorAgent).risk_status?.toLowerCase()) {
    case 'danger': return 'destructive'
    case 'warning': return 'secondary'
    case 'safe': default: return 'default'
    }
  }
  return 'outline'
}
  
function getRiskStatusLabel(report: Report): string {
  if (isMonitorAgent(report)) {
    const runningTask = runningAgents.value.find(task => task.id === report.id)
    if (runningTask?.status === 'RUNNING') {return 'Running'}
    if (runningTask?.status === 'ERROR') {return 'Error'}
  
    switch ((report as MonitorAgent).risk_status?.toLowerCase()) {
    case 'danger': return 'High Risk'
    case 'warning': return 'Warning'
    case 'safe': return 'Safe'
    default: return 'Unknown'
    }
  }
  return 'Unknown'
}
  
function getStatusVariant(report: Report): BadgeVariant {
  if (!isMonitorAgent(report)) {
    if (isRunning(report)) {return 'secondary'}
    if (isPending(report)) {return 'secondary'} 
    if (isCompleted(report)) {return 'default'} 
    if (isError(report)) {return 'destructive'}
  }
  return 'outline'
}
  
function getStatusLabel(report: Report): string {
  if (!isMonitorAgent(report)) {
    if (isRunning(report)) {return 'Running'}
    if (isPending(report)) {return 'Pending'}
    if (isCompleted(report)) {return 'Completed'} 
    if (isError(report)) {return 'Error'}
  }
  return 'Unknown'
}
  
// Agent List Status Styling
function getAgentStatusClass(status: string): string {
  const normalizedStatus = status ? status.toLowerCase() : 'idle'
  switch (normalizedStatus) {
  case 'running': return 'bg-blue-500'
  case 'pending': return 'bg-amber-500'
  case 'completed': case 'success': case 'idle': return 'bg-green-500'
  case 'error': return 'bg-red-500'
  default: return 'bg-slate-400'
  }
}
  
function getAgentStatusVariant(status: string): BadgeVariant {
  const normalizedStatus = status ? status.toLowerCase() : 'idle'
  switch (normalizedStatus) {
  case 'running': return 'secondary'
  case 'pending': return 'secondary'
  case 'completed': case 'success': case 'idle': return 'default'
  case 'error': return 'destructive'
  default: return 'outline'
  }
}
  
function getAgentStatusBadgeClass(status: string): string {
  const normalizedStatus = status ? status.toLowerCase() : 'idle'
  switch (normalizedStatus) {
  case 'running': return 'bg-blue-100 text-blue-700 border-0'
  case 'pending': return 'bg-amber-100 text-amber-700 border-0'
  case 'completed': case 'success': case 'idle': return 'bg-green-100 text-green-700 border-0'
  case 'error': return 'bg-red-100 text-red-700 border-0'
  default: return 'bg-slate-100 text-slate-600 border-0'
  }
}
  
// Step Status Styling
function getStepStatusVariant(status: string): BadgeVariant {
  switch (status.toUpperCase()) {
  case 'COMPLETED': return 'default'
  case 'PROCESSING': return 'secondary'
  case 'PENDING': return 'outline'
  case 'ERROR': return 'destructive'
  default: return 'outline'
  }
}
  
function getStepStatusBadgeClass(status: string): string {
  switch (status.toUpperCase()) {
  case 'COMPLETED': return 'bg-green-100 text-green-700 border-0'
  case 'PROCESSING': return 'bg-blue-100 text-blue-700 border-0'
  case 'PENDING': return 'bg-slate-100 text-slate-600 border-0'
  case 'ERROR': return 'bg-red-100 text-red-700 border-0'
  default: return 'bg-slate-100 text-slate-600 border-0'
  }
}
  
function getProgressMessage(report: Report): string {
  if (isRunningAgent(report)) {
    return report.description || 'Processing task...'
  }
  return 'Processing...'
}
  
function getFormattedTime(report: Report): string {
  const reportDateStr = (report as any).date || new Date().toISOString()
  try {
    const date = new Date(reportDateStr)
    if (isNaN(date.getTime())) { return '--:--' }
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false })
  } catch (e) { return '--:--' }
}
  
function formatDateHeader(dateString: string): string {
  const today = new Date().toLocaleDateString()
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  const yesterdayString = yesterday.toLocaleDateString()
    
  if (dateString === today) {return 'Today'}
  if (dateString === yesterdayString) {return 'Yesterday'}
    
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) { return dateString }
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' }
    return date.toLocaleDateString(undefined, options)
  } catch (e) { 
    return dateString 
  }
}
  
// --- Methods ---
function filterByAgent(agentName: string | null) {
  selectedAgent.value = agentName
}
  
function viewReport(report: Report) {
  // In a real app, this would navigate to a detailed report view
  console.log('Viewing report:', report.id)
  // router.push(`/reports/${report.id}`);
}
  
// --- Lifecycle Hooks ---
onMounted(() => {
  // In a real app, this would fetch data from an API
  console.log('Component mounted')
    
  //   // Handle window resize for responsive sidebar
  //   const handleResize = () => {
  //     window.value.innerWidth = typeof window.value !== 'undefined' ? window.value.innerWidth : 1200
  //     if (window.value.innerWidth >= 1024) {
  //       sidebarVisible.value = true
  //     }
  //   }
    
  //   if (typeof window.value !== 'undefined') {
  //     window.value.addEventListener('resize', handleResize)
  //   }
    
//   handleResize()
})
  
onUnmounted(() => {
//   if (typeof window.value !== 'undefined') {
//     window.value.removeEventListener('resize', () => {})
//   }
})
</script>
  
  <style scoped>
  /* Animation and transition effects */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .animate-in {
    animation: fadeIn 0.3s ease-out forwards;
  }
  
  /* Custom scrollbar styles */
  :deep(.scrollbar) {
    width: 6px !important;
  }
  
  :deep(.thumb) {
    background-color: rgba(156, 163, 175, 0.5) !important;
    border-radius: 3px !important;
  }
  
  /* Internal scrollable area scrollbar styles */
  div[class*="overflow-y-auto"] {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }
  
  div[class*="overflow-y-auto"]::-webkit-scrollbar {
    width: 4px;
  }
  
  div[class*="overflow-y-auto"]::-webkit-scrollbar-track {
    background: transparent;
  }
  
  div[class*="overflow-y-auto"]::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .grid {
      grid-template-columns: 1fr;
    }
  }
  
  /* Ensure text content doesn't overflow */
  .break-words {
    word-break: break-word;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  /* Enhanced glassmorphism effects */
  :deep(.card) {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    overflow: hidden;
  }
  
  :deep(.card:hover) {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  }
  
  /* Button hover effects */
  :deep(.button) {
    transition: all 0.2s ease;
  }
  
  :deep(.button:hover) {
    transform: translateY(-1px);
  }
  
  /* Special effects for monitoring alert cards */
  :deep(.card[class*="bg-red-50"]) {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
  }
  
  :deep(.card[class*="bg-amber-50"]) {
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
  }
  
  :deep(.card[class*="bg-green-50"]) {
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
  }
  </style>