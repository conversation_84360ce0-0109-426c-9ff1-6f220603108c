<template>
  <div 
    :class="$style['last-page-indicator']"
    class="w-full flex flex-col items-center py-4 gap-2">
    <div class="text-gray-500 text-sm">{{ $t('news.bottom') }}</div>
    <div 
      class="cursor-pointer hover:text-primary transition-colors duration-300"
      @click="emit('to-top')">
      <Icon icon="mdi:arrow-collapse-up" class="text-[#333] text-[24px]"/>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { Icon } from '@iconify/vue'

  
const emit = defineEmits<{
    'to-top': []
  }>()
</script>
  
  <style lang="scss" module>
  .last-page-indicator {
    opacity: 0.8;
    transition: opacity 0.3s;
  
    &:hover {
      opacity: 1;
    }
  }
  </style>