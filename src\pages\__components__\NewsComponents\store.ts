import { defineStore } from 'pinia'
import { useGet } from '@/hooks/useHttp'
import { computed, ref } from 'vue'
import type { NewsDetailResponse, TopBtn, NewsDetail } from '@/types'
import { useSafeWebsocket } from '@/hooks/useSafeWebsocket'
import dayjs from 'dayjs'

interface PaginationState {
  currentPage: number
  maxPage: number
  total: number
  pageSize: number
}

export const useNewsStore = defineStore('news', () => {
  // 基础状态
  const currentCountry = ref<string>('china')
  
  // 内部状态管理
  const pagination = ref<PaginationState>({
    currentPage: 1,
    maxPage: 1,
    total: 0,
    pageSize: 10
  })
  
  const newsItems = ref<NewsDetail[]>([])
  const clientTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  const timezoneOffset = -(new Date().getTimezoneOffset() / 60)
  const dailyNews = computed(() => {
    return [...newsItems.value].sort((a, b) => {
      // 转换为本地时区进行比较
      const timeA = dayjs(a.publish_time).tz(clientTimezone)
      const timeB = dayjs(b.publish_time).tz(clientTimezone)
      return timeB.valueOf() - timeA.valueOf()
    })
  })
  const isLastPage = computed(() => 
    pagination.value.currentPage >= pagination.value.maxPage
  )
  
  // API 调用封装
  const { 
    execute: fetchNews, 
    isLoading: isDailyNewsLoading 
  } = useGet<NewsDetailResponse>('/crawl_info/get_daily_news', {
    timeout: 1000 * 100 // 100秒超时

  }, {
    immediate: false,
    resetOnExecute: false
  })

  // 内部数据处理方法
  const updatePaginationFromResponse = (response: NewsDetailResponse) => {
    if (response.meta) {
      pagination.value = {
        currentPage: response.meta.page,
        maxPage: response.meta.max_page,
        total: response.meta.total,
        pageSize: response.meta.page_size
      }
    }
  }
  
  const updateNewsItems = (items: NewsDetail[], isAppend: boolean) => {
    if (isAppend) {
      newsItems.value = [...newsItems.value, ...items]
    } else {
      newsItems.value = items
    }
  }

  // 业务逻辑方法
  const refreshDailyNews = async (isLoadMore = false, needResponse = false) => {
    if (!isLoadMore) {
      // 重置分页状态
      pagination.value.currentPage = 1
      newsItems.value = []
    }
    
    const response = await fetchNews(0, {
      params: {
        page: pagination.value.currentPage
      }
    })
    
    if (response) {
      updatePaginationFromResponse(response)
      updateNewsItems(response.items, isLoadMore)
    }
    
    return needResponse ? response : null
  }

  const loadMoreNews = async () => {
    if (isLastPage.value || isDailyNewsLoading.value) {return}
    
    pagination.value.currentPage++
    await refreshDailyNews(true)
  }

  // 地区按钮数据
  const { 
    state: locationButtons, 
    execute: refreshLocation, 
    isLoading: isLocationLoading 
  } = useGet<TopBtn>('/crawl_info/nations', {}, {
    immediate: false
  })

  // 新增闪烁状态管理
  const flashingNewsIds = ref<Set<string>>(new Set())

  // 处理新闻闪烁
  const startNewsFlash = (newsId: string) => {
    flashingNewsIds.value.add(newsId)
    setTimeout(() => {
      flashingNewsIds.value.delete(newsId)
    }, 3000) // 3秒后停止闪烁
  }

  // 使用 useSafeWebsocket
  const { isConnected } = useSafeWebsocket<NewsDetail>({
    'news:update': (newNewsDetail: NewsDetail) => {
      newsItems.value = [newNewsDetail, ...newsItems.value]
      startNewsFlash(newNewsDetail.id) // 开始闪烁效果
    }
  })

  return {
    // 保持原有的对外 API
    currentCountry,
    dailyNews, // 通过 computed 保持与原有 API 一致
    isDailyNewsLoading,
    isLastPage,
    currentPage: computed(() => pagination.value.currentPage),
    locationButtons,
    isLocationLoading,
    
    // 方法导出
    refreshDailyNews,
    loadMoreNews,
    refreshLocation,
    isConnected,
    flashingNewsIds: computed(() => flashingNewsIds.value),
  }
})