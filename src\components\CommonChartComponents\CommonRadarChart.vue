<template>
  <EchartsComponent
    class="w-full h-[300px]"
    data-pdf-chart
    :params="getChartOption"
    :auto-size="true"/>
</template>
  
<script setup lang="ts">
import { computed } from 'vue'
import EchartsComponent from '@/components/EchartsComponent.vue'
import { isObject, merge } from 'lodash-es'

  
  interface Indicator {
    name: string
    max: number
  }
  
  interface RadarData {
    name: string
    value: number[]
  }
  
  interface Props {
    data:{
        title:string
        indicators: Indicator[]
        data: RadarData[]
    }
    config?: Record<string, any>
  }
  
const props = defineProps<Props>()
  
  
// 雷达图配置
const getChartOption = computed(() => {
  const baseOption = {
    title:{
      text:props.data.title
    },
    tooltip: {
      trigger: 'item',
      appendToBody: true,
      formatter: (params: any) => {
        const indicators = props.data.indicators
        const values = params.value.map((val: number, index: number) => 
          `${indicators[index].name}: ${val}%`
        )
        return `${params.name}<br/>${values.join('<br/>')}`
      }
    },
    legend: {
      show:true,
      right:0,
      top: 5
    },
    grid: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20,
      containLabel: true
    },
    radar: {
      indicator: props.data?.indicators,
      center: ['50%', '55%'],
      radius: '60%',
      nameGap: 8,
      splitArea: {
        show: true,
        areaStyle: {
          color: ['#FFF']
        }
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisName: {
        formatter: (name: string, indicator: any) => {
          const index = props.data.indicators.findIndex(ind => ind.name === name)
          const values = props.data.data.map(item => ({
            value: item.value[index],
            name: item.name
          }))
          
          const valueTexts = values.map((item, i) => {
            const color = ['#3B82F6', '#10B981'][i]
            return `{value${i}|${item.value}%}`
          }).join('\n')
          
          return `${name}\n${valueTexts}`
        },
        rich: {
          value0: {
            color: '#3B82F6',
            fontSize: 12,
            padding: [1, 0]
          },
          value1: {
            color: '#10B981',
            fontSize: 12,
            padding: [1, 0]
          }
        },
        color: '#374151',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'radar',
        data: props.data?.data.map(item => ({
          name: item.name,
          value: item.value,
          symbol: 'circle',
          symbolSize: 6,
         
          lineStyle: {
            width: 2
          },
          areaStyle: {
            opacity: 0.2
          }
        }))
      }
    ],
    animation: true,
    color: [
      '#3B82F6',
      '#10B981',
      '#F59E0B',
      '#EF4444',
      '#8B5CF6'
    ]
  }
  return isObject(props.config) ? merge(baseOption, props.config) : baseOption
})
</script>