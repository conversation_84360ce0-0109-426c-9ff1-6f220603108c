import type { DynamicContent, Margins, Style, TDocumentDefinitions } from 'pdfmake/interfaces'
import dayjs from 'dayjs'
import type { PdfOptions, HeaderConfig, FooterConfig, TitleConfig } from '../types'

// 默认配置
export const DEFAULT_HEADER_CONFIG: HeaderConfig = {
  left: 'SPECIFIC AI ANALYTICS REPORT',
  center: 'Specific AI',
  right: undefined, // 将使用当前日期
}

export const DEFAULT_FOOTER_CONFIG: FooterConfig = {
  copyright: 'Copyright© 2024 by FC ALL RIGHTS RESERVED',
  confidential:
    'This file may be confidential and protected by legal privilege. If you are not the intended recipient, disclosure, copying, distribution and use are prohibited.',
}

// Add new default config
export const DEFAULT_TITLE_CONFIG: TitleConfig = {
  subtitle: 'INFORMATION SUMMARY REPORT',
  contact: {
    company: '深センオフィスケリーセンターT1',
    website: 'www.formulaconsulting.cc',
    email: '<EMAIL>'
  }
}

// Header 生成函数
export const createHeader =
  (config: HeaderConfig = DEFAULT_HEADER_CONFIG) =>
    () => ({
      margin: [40, 20, 40, 0] as Margins,
      table: {
        widths: ['*', '*', '*'],
        body: [
          [
            {
              text: config.left ?? DEFAULT_HEADER_CONFIG.left,
              style: 'header-text',
              alignment: 'left',
            },
            {
              text: config.center ?? DEFAULT_HEADER_CONFIG.center,
              style: 'header-text',
              alignment: 'center',
            },
            {
              text: config.right ?? dayjs().format('YYYY-MM-DD'),
              style: 'header-text',
              alignment: 'right',
            },
          ],
        ],
      },
      layout: 'noBorders',
    })

// Footer 生成函数
export const createFooter =
  (config: FooterConfig = DEFAULT_FOOTER_CONFIG) =>
    (currentPage: number, pageCount: number) => ({
      margin: [40, 0, 40, 20] as Margins,
      stack: [
        {
          canvas: [
            {
              type: 'line',
              x1: 0,
              y1: 0,
              x2: 515,
              y2: 0,
              lineWidth: 1,
            },
          ],
        },
        {
          text: config.copyright ?? DEFAULT_FOOTER_CONFIG.copyright,
          style: 'footer-text',
          margin: [0, 5, 0, 0] as Margins,
        },
        {
          text: config.confidential ?? DEFAULT_FOOTER_CONFIG.confidential,
          style: 'footer-confidential',
          margin: [0, 2, 0, 0] as Margins,
        },
        {
          text: `${currentPage} / ${pageCount}`,
          style: 'footer-page',
          alignment: 'right',
        },
      ],
    })

// 默认样式
const defaultStyles = {
  'header-text': {
    fontSize: 10,
    color: '#000000',
  } as Style,
  'footer-text': {
    fontSize: 8,
    color: '#000000',
  } as Style,
  'footer-confidential': {
    fontSize: 8,
    color: '#666666',
  } as Style,
  'footer-page': {
    fontSize: 8,
    color: '#000000',
  } as Style,
  'pdf-title':{
    fontSize: 14,
    color: '#000000',
    fontWeight: 'bold',
  },
  'html-mark': {
    color: '#FF0000',
    background: '#FFFFFF',
    fontSize: 10,
    lineHeight: 1.5
  } as Style,
}

export const createDocumentStyles = (
  language: 'chinese' | 'japanese' | 'english',
  options?: Partial<PdfOptions>
): Partial<TDocumentDefinitions> => ({
  pageSize: options?.pageSize || 'A4',
  pageMargins: options?.pageMargins as Margins || [40, 60, 40, 60] as Margins,
  pageOrientation: options?.pageOrientation || 'portrait',

  defaultStyle: {
    font: language === 'japanese' ? 'AlibabaSansJP' : 'fangzhen',
    fontSize: 10,
    lineHeight: 1.5,
  },

  styles: {
    ...defaultStyles,
    ...options?.styles,
  },

  header: createHeader(options?.headerConfig),
  footer: createFooter(options?.footerConfig) as DynamicContent,
})
