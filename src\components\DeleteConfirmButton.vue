<template>
  <el-tooltip
    :class="$style.root"
    :content="tips"
    :disabled="!tips"
    v-bind="tipsOptions">
    <div ref="container">
      <div
        v-show="!showConfirm"
        :class="[$style.ref, 'cursor-pointer','r']"
        @click.capture="refClick"
        @mouseenter="refEnter">
        <slot>
          <el-button
            type="danger"
            size="small">
            <em class="zkicon icondelete"></em>
            {{ buttonText }}
          </el-button>
        </slot>
      </div>
      <div
        v-show="showConfirm"
        :class="[$style.confirm, 'cursor-pointer','mr-2']"
        @mouseleave="confirmLeave"
        @click="execute()">
        <slot name="confirm" :loading="isLoading">
          <el-button
            :type="confirmButtonOption?.type || 'text'"
            size="small">
            {{ confirmButtonText }}
          </el-button>
        </slot>
      </div>
    </div>
  </el-tooltip>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'
import { ElButton, ElTooltip } from 'element-plus' // 确保这些组件已正确导入
import { onClickOutside, useAsyncState } from '@vueuse/core'
type AsyncFunction = (...args: any[]) => Promise<any>;

const props = defineProps({
  tips: String,
  buttonOption: Object,
  buttonTips: String,
  buttonText: String,
  tipsOptions: Object,
  confirmButtonText: String,
  confirmButtonOption: Object,
  hoverTrigger: Boolean,
  leaveCancel: Boolean,
  onConfirm: Function as PropType<AsyncFunction>
})


const emit = defineEmits(['cancel'])
const container = ref<HTMLElement>()
const showConfirm = ref(false)
const { execute, isLoading } = useAsyncState(async () => {
  if (props.onConfirm) {
    await props.onConfirm()
    cancel()
  }
}, undefined, { immediate: false })

onClickOutside(container, clickOutsideHandler)

function confirmLeave() {
  if (props.leaveCancel) {
    cancel()
  }
}

function refEnter() {
  if (props.hoverTrigger) {
    showConfirm.value = true
  }
}

function refClick() {
  showConfirm.value = true
}

function cancel() {
  showConfirm.value = false
  emit('cancel')
}

function clickOutsideHandler() {
  cancel()
}

</script>

<style lang="scss" module>
.ref{

}
.confirm{

}
</style>
