<!-- Generated by SpecStory -->

# 处理特殊代理的API请求逻辑 (2025-03-05 11:11:52)

_**User**_

如 @index.vue 的AGENT_API_MAP所示，特殊的agent.id需要使用usePost @useHttp.ts  去请求特殊的API的地址，你需要在handleConfirm除了isSpecialAgent之外的另一个分支中添加上AGENT_API_MAP的逻辑对应关系，而不是全部使用delayReport

---

_**Assistant**_

Read file: src/pages/agent-market/index.vue

---

Read file: src/hooks/useHttp.ts

---



```thinking
好的，我需要理解当前的代码结构和需要修改的部分。让我分析一下：

1. 在 `src/pages/agent-market/index.vue` 文件中，有一个 `AGENT_API_MAP` 对象，它将特定的 agent.id 映射到特定的 API 端点：
```javascript
const AGENT_API_MAP = {
  'FinancialPerformanceTrackingReport':'/formatted_report/delay_financial_report',
  'CompanyLegalAnalysisReport':'/formatted_report/delay_legal_report',
  'RecentNewsAndSocialOpinionTrackingReport':'/formatted_report/delay_social_report',
}
```

2. 当前的 `handleConfirm` 函数有两个分支：
   - 如果 `isSpecialAgent.value` 为 true，则使用 `uploadExcel` 函数上传 Excel 文件
   - 否则，使用 `delayReport` 函数发送请求

3. 用户的要求是：在非 `isSpecialAgent` 的情况下，根据 `activeAgent.value?.id` 来决定使用哪个 API 端点，而不是统一使用 `delayReport`。

4. 从 `useHttp.ts` 中可以看到，`usePost` 函数接受 URL、数据、选项和状态选项作为参数。

所以，我需要修改 `handleConfirm` 函数，在 `else` 分支中添加逻辑，根据 `activeAgent.value?.id` 来决定使用哪个 API 端点。

具体来说，我需要：
1. 检查 `activeAgent.value?.id` 是否在 `AGENT_API_MAP` 中
2. 如果是，使用对应的 API 端点创建一个新的 `usePost` 实例并执行
3. 如果不是，继续使用默认的 `delayReport`

让我来实现这个修改：
```

---

```diff
        }
      } else {
-       await delayReport(0, {
-         report_type: activeAgent.value?.id,
+       const agentId = activeAgent.value?.id || ''
+       const requestData = {
+         report_type: agentId,
          language: locale.value,
          token: getToken(),
          agent_name: activeAgent.value?.agent_name,
          ...formData.userInfo
-       })
+       }
+       
+       // 检查是否是特殊API映射中的agent
+       if (agentId in AGENT_API_MAP) {
+         const apiUrl = AGENT_API_MAP[agentId as keyof typeof AGENT_API_MAP]
+         // 为特定API创建一个临时的usePost实例
+         const { execute: specialDelayReport } = usePost(apiUrl, {}, {}, {
+           immediate: false,
+         })
+         await specialDelayReport(0, requestData)
+       } else {
+         // 使用默认的delayReport
+         await delayReport(0, requestData)
+       }
+       
        ElMessage.success(t('common.success'))
        dialogVisible.value = false
```

---



我已经根据您的需求修改了代码。现在，在处理非特殊代理（非Excel模式）的情况下，系统会根据代理ID检查是否需要使用特殊的API端点。

## 修改内容说明

我在`handleConfirm`函数的`else`分支中添加了以下逻辑：

1. 提取了当前代理的ID并创建了一个统一的请求数据对象
2. 检查该ID是否存在于`AGENT_API_MAP`中
3. 如果存在，则：
   - 获取对应的API URL
   - 创建一个针对该特定URL的临时`usePost`实例
   - 使用该实例发送请求
4. 如果不存在，则继续使用默认的`delayReport`函数

这样，当用户选择不同类型的代理时，系统会自动根据`AGENT_API_MAP`中的映射关系选择正确的API端点进行请求。

例如：
- 当选择`FinancialPerformanceTrackingReport`时，会使用`/formatted_report/delay_financial_report`端点
- 当选择`CompanyLegalAnalysisReport`时，会使用`/formatted_report/delay_legal_report`端点
- 当选择`RecentNewsAndSocialOpinionTrackingReport`时，会使用`/formatted_report/delay_social_report`端点
- 对于其他类型的代理，会继续使用默认的`/formatted_report/delay_report`端点

这样的实现方式保持了代码的灵活性，如果将来需要添加更多的特殊API映射，只需要在`AGENT_API_MAP`对象中添加新的映射关系即可。

---

_**User**_

我觉得handleConfirm里面的逻辑太复杂，我需要你进行逻辑优化，性能提升等

---

_**Assistant**_

Read file: src/pages/agent-market/index.vue

---

Read file: src/pages/agent-market/index.vue

---

Read file: src/pages/agent-market/index.vue

---



```thinking
现在我已经了解了handleConfirm函数的完整实现以及相关的API和变量。让我分析一下当前的实现，并思考如何优化它：

1. 当前的handleConfirm函数有两个主要分支：
   - isSpecialAgent.value为true时，处理Excel文件上传
   - 否则，根据agentId选择不同的API端点发送请求

2. 存在的问题和优化点：
   - 嵌套的try-catch结构增加了复杂性
   - Excel上传部分有冗余的日志输出
   - 创建临时usePost实例的方式不够高效
   - 代码结构可以更清晰

3. 优化思路：
   - 将Excel处理和API请求分离为独立的函数
   - 预先定义所有可能的API请求函数，而不是动态创建
   - 减少不必要的日志输出
   - 简化错误处理逻辑
   - 使用更现代的JavaScript/TypeScript特性

让我设计一个优化后的实现：
```

---

```diff
  })
  
+ // 预先定义特殊API的请求函数
+ const specialApiRequests = {
+   FinancialPerformanceTrackingReport: usePost('/formatted_report/delay_financial_report', {}, {}, { immediate: false }).execute,
+   CompanyLegalAnalysisReport: usePost('/formatted_report/delay_legal_report', {}, {}, { immediate: false }).execute,
+   RecentNewsAndSocialOpinionTrackingReport: usePost('/formatted_report/delay_social_report', {}, {}, { immediate: false }).execute,
+ }
  
  interface Agent {
```

```diff
  
  const handleAgentClick = (agent: Agent) => {
-   // emit('start', agent)
    activeAgent.value = agent
    dialogVisible.value = true
  }
  
- const handleConfirm = async (formData: { userInfo: Record<string, string>, excelFile: File | null }) => {
-   try {
-     if (isSpecialAgent.value) {
+ // 处理Excel文件上传
+ const handleExcelUpload = async (formData: { excelFile: File | null }) => {
    if (!formData.excelFile) {
      ElMessage.error(t('required.excel'))
-         return
+     return false
    }
    
-       // Validate file extension
-       const fileName = formData.excelFile.name
-       const fileExt = fileName.split('.').pop()?.toLowerCase()
+   // 验证文件扩展名
+   const fileExt = formData.excelFile.name.split('.').pop()?.toLowerCase()
    if (!fileExt || !['xls', 'xlsx'].includes(fileExt)) {
      ElMessage.error(t('upload.excel.type.error'))
-         return
+     return false
    }
  
    const formDataObj = new FormData()
-       // Use original file object directly
    formDataObj.append('file', formData.excelFile)
    formDataObj.append('id', activeAgent.value?.id || '')
    formDataObj.append('agent_name', activeAgent.value?.name || '')
    
-       // Log FormData contents for debugging
-       console.log('FormData contents:')
-       for (let pair of formDataObj.entries()) {
-         console.log(pair[0], pair[1])
-       }
-       
-       try {
-         // Let browser handle multipart/form-data
-         const response = await uploadExcel(0, formDataObj)
-         console.log('Upload response:', response)
-         ElMessage.success(t('common.success'))
-         dialogVisible.value = false
-         emitter.emit('async:update')
-         await getAgents()
-       } catch (uploadError: any) {
-         console.error('Upload Error:', uploadError)
-         ElMessage.error(uploadError?.message || t('upload.excel.error'))
-       }
-     } else {
-       const agentId = activeAgent.value?.id || ''
+   try {
+     await uploadExcel(0, formDataObj)
+     return true
+   } catch (error: any) {
+     console.error('Upload Error:', error)
+     ElMessage.error(error?.message || t('upload.excel.error'))
+     return false
+   }
+ }
+ 
+ // 处理API请求
+ const handleApiRequest = async (agentId: string, userData: Record<string, string>) => {
    const requestData = {
      report_type: agentId,
      language: locale.value,
      token: getToken(),
      agent_name: activeAgent.value?.agent_name,
-         ...formData.userInfo
+     ...userData
    }
    
-       // 检查是否是特殊API映射中的agent
-       if (agentId in AGENT_API_MAP) {
-         const apiUrl = AGENT_API_MAP[agentId as keyof typeof AGENT_API_MAP]
-         // 为特定API创建一个临时的usePost实例
-         const { execute: specialDelayReport } = usePost(apiUrl, {}, {}, {
-           immediate: false,
-         })
-         await specialDelayReport(0, requestData)
+   try {
+     // 使用预定义的API请求函数或默认的delayReport
+     if (agentId in specialApiRequests) {
+       await specialApiRequests[agentId as keyof typeof specialApiRequests](0, requestData)
      } else {
-         // 使用默认的delayReport
        await delayReport(0, requestData)
-       }
-       
-       ElMessage.success(t('common.success'))
-       dialogVisible.value = false
-       emitter.emit('async:update')
      }
+     return true
    } catch (error: any) {
      console.error('API Error:', error)
      ElMessage.error(error?.message || t('common.error'))
+     return false
+   }
+ }
+ 
+ // 主确认处理函数
+ const handleConfirm = async (formData: { userInfo: Record<string, string>, excelFile: File | null }) => {
+   let success = false
+   
+   if (isSpecialAgent.value) {
+     success = await handleExcelUpload(formData)
+   } else {
+     const agentId = activeAgent.value?.id || ''
+     success = await handleApiRequest(agentId, formData.userInfo)
+   }
+   
+   if (success) {
+     ElMessage.success(t('common.success'))
+     dialogVisible.value = false
+     emitter.emit('async:update')
+     
+     // 仅在Excel上传成功后刷新代理列表
+     if (isSpecialAgent.value) {
+       await getAgents()
+     }
    }
  }
```

---





## 代码优化说明

我已经对`handleConfirm`函数进行了重构和优化，主要改进如下：

### 1. 结构优化

将原本复杂的`handleConfirm`函数拆分为三个独立的函数：
- `handleExcelUpload`: 专门处理Excel文件上传逻辑
- `handleApiRequest`: 处理API请求逻辑
- `handleConfirm`: 作为主函数，根据条件调用上述两个函数

### 2. 性能优化

- **预先定义API请求函数**：创建了`specialApiRequests`对象，预先定义所有特殊API的请求函数，避免在每次调用时动态创建临时的`usePost`实例，减少了内存分配和垃圾回收的开销。
- **减少不必要的日志输出**：移除了FormData内容的循环打印和上传响应的日志输出，减少了不必要的控制台输出操作。
- **简化文件扩展名提取**：优化了文件扩展名提取的代码，使其更简洁。

### 3. 错误处理优化

- **统一的错误处理模式**：每个函数都返回布尔值表示操作是否成功，主函数根据返回值决定后续操作。
- **分离的try-catch块**：每个函数有自己的try-catch块，使错误处理更加局部化和明确。

### 4. 逻辑优化

- **条件刷新**：只在Excel上传成功后才刷新代理列表，避免不必要的网络请求。
- **统一的成功处理**：将成功消息提示、对话框关闭和事件触发集中在主函数中处理，保持一致性。

### 5. 代码可读性提升

- **函数命名更清晰**：每个函数的名称明确表示其功能。
- **注释更加明确**：添加了更清晰的注释说明每个函数的用途。
- **代码结构更加扁平**：减少了嵌套层级，使代码更易于阅读和维护。

这些优化使代码更加模块化、可维护，并提高了执行效率。每个函数都有明确的单一职责，使整体逻辑更加清晰。

---

_**User**_

在 @[id].vue 中关于delayReport的逻辑，也需要根据AGENT_API_MAP的逻辑进行修改，这个组件只涉及到RecentNewsAndSocialOpinionTrackingReport和CompanyLegalAnalysisReport两个报告的生成、

---

_**Assistant**_



```thinking
我需要分析dashboard/[id].vue中的delayReport逻辑，并根据AGENT_API_MAP进行优化。

首先，让我理解当前的代码：

1. 在dashboard/[id].vue中，有一个handleGenerateDetailReport函数，用于生成详细报告。
2. 目前它使用了通用的delayReport函数，但根据条件判断设置了不同的id参数：
   - 如果report_type是'SocialAgent'，则id设为'RecentNewsAndSocialOpinionTrackingReport'
   - 否则id设为'CompanyLegalAnalysisReport'
3. 这个逻辑应该改为使用AGENT_API_MAP中定义的特定API端点。

根据agent-market/index.vue中的优化方式，我应该：
1. 预定义特殊API的请求函数
2. 根据报告类型选择对应的API函数
3. 简化handleGenerateDetailReport函数的逻辑

让我设计一个优化方案：
```

---

```diff
    immediate: false,
  })
+ 
+ // 预定义特殊API的请求函数
+ const specialApiRequests = {
+   RecentNewsAndSocialOpinionTrackingReport: usePost('/formatted_report/delay_social_report', {}, {}, { immediate: false }).execute,
+   CompanyLegalAnalysisReport: usePost('/formatted_report/delay_legal_report', {}, {}, { immediate: false }).execute,
+ }
  
  definePage({
```

```diff
  const handleGenerateDetailReport = async (step: AgentStep) => {
    console.log('Generating detail report...', step, singleReportData.value)
-   const delay_params = {
+   
+   // 确定报告类型
+   const reportId = singleReportData.value?.report_type === 'SocialAgent' 
+     ? 'RecentNewsAndSocialOpinionTrackingReport' 
+     : 'CompanyLegalAnalysisReport'
+   
+   // 准备请求数据
+   const requestData = {
      language: locale.value,
      token: getToken(),
-     id: singleReportData.value?.report_type === 'SocialAgent' ? 'RecentNewsAndSocialOpinionTrackingReport' : 'CompanyLegalAnalysisReport',
+     id: reportId,
      company_name: step.title,
    }
-   await delayReport(0, delay_params)
+   
+   try {
+     // 使用对应的特殊API请求函数
+     await specialApiRequests[reportId](0, requestData)
      ElMessage.success(t('common.success'))
      emitter.emit('async:update')
+   } catch (error: any) {
+     console.error('API Error:', error)
+     ElMessage.error(error?.message || t('common.error'))
+   }
  }
  const handleRefresh = () => {
```

---





## 代码优化说明

我已经对`dashboard/[id].vue`中的`handleGenerateDetailReport`函数进行了优化，主要改进如下：

### 1. 添加预定义的特殊API请求函数

```typescript
// 预定义特殊API的请求函数
const specialApiRequests = {
  RecentNewsAndSocialOpinionTrackingReport: usePost('/formatted_report/delay_social_report', {}, {}, { immediate: false }).execute,
  CompanyLegalAnalysisReport: usePost('/formatted_report/delay_legal_report', {}, {}, { immediate: false }).execute,
}
```

这样可以避免每次调用时动态创建临时的`usePost`实例，提高性能。

### 2. 优化报告类型确定逻辑

将报告类型的确定逻辑提取为单独的变量，使代码更清晰：

```typescript
// 确定报告类型
const reportId = singleReportData.value?.report_type === 'SocialAgent' 
  ? 'RecentNewsAndSocialOpinionTrackingReport' 
  : 'CompanyLegalAnalysisReport'
```

### 3. 添加错误处理

增加了`try-catch`块来处理可能的API请求错误，并显示适当的错误消息：

```typescript
try {
  // 使用对应的特殊API请求函数
  await specialApiRequests[reportId](0, requestData)
  ElMessage.success(t('common.success'))
  emitter.emit('async:update')
} catch (error: any) {
  console.error('API Error:', error)
  ElMessage.error(error?.message || t('common.error'))
}
```

### 4. 变量命名更加一致

将`delay_params`重命名为`requestData`，使命名风格与其他部分保持一致。

### 5. 直接使用特定API

不再使用通用的`delayReport`函数，而是根据报告类型直接使用对应的特殊API请求函数：

```typescript
await specialApiRequests[reportId](0, requestData)
```

这些优化使代码更加清晰、健壮，并提高了执行效率。通过预定义API请求函数和添加适当的错误处理，代码的可维护性和可靠性都得到了提升。

---

