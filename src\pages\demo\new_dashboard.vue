<template>
  <div class="dashboard-container bg-gradient-to-br from-slate-900 to-slate-800 min-h-screen text-white">
    <!-- Geometric background elements -->
    <div class="geometric-bg"></div>
      
    <div class="dashboard-layout">
      <!-- Left Sidebar -->
      <aside class="sidebar left-sidebar">
        <div class="logo-container mb-8">
          <div class="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
            Insight<span class="text-cyan-400">AI</span>
          </div>
        </div>
          
        <nav class="nav-menu">
          <div class="menu-item active">
            <div class="menu-icon">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-layout-dashboard"><rect width="7"
                                                                height="9"
                                                                x="3"
                                                                y="3"
                                                                rx="1"/><rect width="7"
                                                                              height="5"
                                                                              x="14"
                                                                              y="3"
                                                                              rx="1"/><rect width="7"
                                                                                            height="9"
                                                                                            x="14"
                                                                                            y="12"
                                                                                            rx="1"/><rect width="7"
                                                                                                          height="5"
                                                                                                          x="3"
                                                                                                          y="16"
                                                                                                          rx="1"/></svg>
            </div>
            <span>Dashboard</span>
          </div>
          <div class="menu-item">
            <div class="menu-icon">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-file-bar-chart"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M12 18v-6"/><path d="M8 18v-1"/><path d="M16 18v-3"/></svg>
            </div>
            <span>Reports</span>
          </div>
          <div class="menu-item">
            <div class="menu-icon">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-users"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
            </div>
            <span>Agents</span>
          </div>
          <div class="menu-item">
            <div class="menu-icon">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-settings"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
            </div>
            <span>Settings</span>
          </div>
        </nav>
      </aside>
        
      <!-- Main Content -->
      <main class="main-content">
        <header class="main-header">
          <h1 class="text-2xl font-bold">Report Progress</h1>
          <div class="filter-tabs">
            <button class="filter-tab active">All</button>
            <button class="filter-tab">Financial</button>
            <button class="filter-tab">Legal</button>
            <button class="filter-tab">Competitor</button>
            <button class="filter-tab">Market</button>
          </div>
        </header>
          
        <!-- Completed Work Summary -->
        <section class="summary-section">
          <h2 class="section-title">Latest Completed Reports</h2>
            
          <div class="report-cards">
            <div class="report-card featured">
              <div class="card-header">
                <div class="avatar-container">
                  <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Agent" class="avatar"/>
                </div>
                <div class="report-meta">
                  <h3 class="report-title">Q2 Financial Analysis</h3>
                  <p class="report-time">Updated 2 hours ago</p>
                </div>
                <div class="card-actions">
                  <button class="action-btn">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="18"
                         height="18"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-star"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/></svg>
                  </button>
                  <button class="action-btn">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="18"
                         height="18"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-refresh-cw"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
                  </button>
                  <button class="action-btn">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="18"
                         height="18"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-more-horizontal"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>
                  </button>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Revenue increased by 18% compared to Q1, with significant growth in the APAC region.</p>
                <div class="tag-container">
                  <span class="tag financial">Financial</span>
                  <span class="tag">Quarterly</span>
                  <span class="tag">Growth</span>
                </div>
              </div>
            </div>
              
            <div class="report-card">
              <div class="card-header">
                <div class="avatar-container">
                  <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Agent" class="avatar"/>
                </div>
                <div class="report-meta">
                  <h3 class="report-title">Competitor Product Analysis</h3>
                  <p class="report-time">Updated 5 hours ago</p>
                </div>
                <div class="card-actions">
                  <button class="action-btn">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="18"
                         height="18"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-star"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/></svg>
                  </button>
                  <button class="action-btn">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="18"
                         height="18"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-refresh-cw"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
                  </button>
                  <button class="action-btn">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="18"
                         height="18"
                         viewBox="0 0 24 24"
                         fill="none"
                         stroke="currentColor"
                         stroke-width="2"
                         stroke-linecap="round"
                         stroke-linejoin="round"
                         class="lucide lucide-more-horizontal"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>
                  </button>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">New competitor product launch detected with similar features but higher pricing.</p>
                <div class="tag-container">
                  <span class="tag competitor">Competitor</span>
                  <span class="tag">Product</span>
                  <span class="tag">Market</span>
                </div>
              </div>
            </div>
          </div>
        </section>
          
        <!-- Key Companies Reports -->
        <section class="companies-section">
          <h2 class="section-title">Key Company Reports</h2>
            
          <div class="company-grid">
            <!-- Company Report Card 1 -->
            <div class="company-card">
              <div class="card-header">
                <h3 class="company-name">TechCorp Industries</h3>
                <div class="avatar-container small">
                  <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Expert" class="avatar"/>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Quarterly financial report shows 12% revenue growth with expansion into European markets.</p>
                <div class="tag-container">
                  <span class="tag financial">Financial</span>
                  <span class="tag">Growth</span>
                </div>
                <div class="card-footer">
                  <span class="date">June 15, 2023</span>
                  <div class="card-actions">
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-eye"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    </button>
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12"
                                                                                                                                                          x2="12"
                                                                                                                                                          y1="15"
                                                                                                                                                          y2="3"/></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
              
            <!-- Company Report Card 2 -->
            <div class="company-card">
              <div class="card-header">
                <h3 class="company-name">Global Legal Solutions</h3>
                <div class="avatar-container small">
                  <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Expert" class="avatar"/>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Regulatory compliance update with new data privacy laws affecting operations in APAC region.</p>
                <div class="tag-container">
                  <span class="tag legal">Legal</span>
                  <span class="tag">Compliance</span>
                </div>
                <div class="card-footer">
                  <span class="date">June 12, 2023</span>
                  <div class="card-actions">
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-eye"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    </button>
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12"
                                                                                                                                                          x2="12"
                                                                                                                                                          y1="15"
                                                                                                                                                          y2="3"/></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
              
            <!-- Company Report Card 3 -->
            <div class="company-card">
              <div class="card-header">
                <h3 class="company-name">Nexus Innovations</h3>
                <div class="avatar-container small">
                  <img src="https://randomuser.me/api/portraits/women/22.jpg" alt="Expert" class="avatar"/>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Competitor analysis shows new product launch in Q3 with advanced AI capabilities.</p>
                <div class="tag-container">
                  <span class="tag competitor">Competitor</span>
                  <span class="tag">Product</span>
                </div>
                <div class="card-footer">
                  <span class="date">June 10, 2023</span>
                  <div class="card-actions">
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-eye"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    </button>
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12"
                                                                                                                                                          x2="12"
                                                                                                                                                          y1="15"
                                                                                                                                                          y2="3"/></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
              
            <!-- Company Report Card 4 -->
            <div class="company-card">
              <div class="card-header">
                <h3 class="company-name">Market Dynamics Inc.</h3>
                <div class="avatar-container small">
                  <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Expert" class="avatar"/>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Market trend analysis indicates shift toward sustainable products with 23% YoY growth.</p>
                <div class="tag-container">
                  <span class="tag market">Market</span>
                  <span class="tag">Trends</span>
                </div>
                <div class="card-footer">
                  <span class="date">June 8, 2023</span>
                  <div class="card-actions">
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-eye"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    </button>
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12"
                                                                                                                                                          x2="12"
                                                                                                                                                          y1="15"
                                                                                                                                                          y2="3"/></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
              
            <!-- Company Report Card 5 -->
            <div class="company-card">
              <div class="card-header">
                <h3 class="company-name">FinTech Solutions</h3>
                <div class="avatar-container small">
                  <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Expert" class="avatar"/>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Financial analysis shows strong Q2 performance with 15% revenue growth and new market entry.</p>
                <div class="tag-container">
                  <span class="tag financial">Financial</span>
                  <span class="tag">Growth</span>
                </div>
                <div class="card-footer">
                  <span class="date">June 5, 2023</span>
                  <div class="card-actions">
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-eye"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    </button>
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12"
                                                                                                                                                          x2="12"
                                                                                                                                                          y1="15"
                                                                                                                                                          y2="3"/></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
              
            <!-- Company Report Card 6 -->
            <div class="company-card">
              <div class="card-header">
                <h3 class="company-name">Legal Compliance Corp</h3>
                <div class="avatar-container small">
                  <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="Expert" class="avatar"/>
                </div>
              </div>
              <div class="card-content">
                <p class="report-summary">Regulatory update on new data protection laws affecting operations in European markets.</p>
                <div class="tag-container">
                  <span class="tag legal">Legal</span>
                  <span class="tag">Compliance</span>
                </div>
                <div class="card-footer">
                  <span class="date">June 3, 2023</span>
                  <div class="card-actions">
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-eye"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    </button>
                    <button class="action-btn small">
                      <svg xmlns="http://www.w3.org/2000/svg"
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           stroke-width="2"
                           stroke-linecap="round"
                           stroke-linejoin="round"
                           class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12"
                                                                                                                                                          x2="12"
                                                                                                                                                          y1="15"
                                                                                                                                                          y2="3"/></svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
            
          <div class="pagination">
            <button class="pagination-btn">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="18"
                   height="18"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"/></svg>
            </button>
            <button class="pagination-btn active">1</button>
            <button class="pagination-btn">2</button>
            <button class="pagination-btn">3</button>
            <button class="pagination-btn">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="18"
                   height="18"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>
            </button>
          </div>
        </section>
          
        <section class="cta-section">
          <button class="cta-button">
            Explore AI Employee Market
            <svg xmlns="http://www.w3.org/2000/svg"
                 width="20"
                 height="20"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round"
                 class="lucide lucide-arrow-right"><path d="M5 12h14"/><path d="m12 5 7 7-7 7"/></svg>
          </button>
        </section>
      </main>
        
      <!-- Right Sidebar - Active Agents -->
      <aside class="sidebar right-sidebar">
        <div class="sidebar-header">
          <h2 class="text-xl font-semibold">Active Agents</h2>
          <span class="agent-count">8 Online</span>
        </div>
          
        <div class="active-agents">
          <!-- Agent 1 -->
          <div class="agent-card">
            <div class="agent-info">
              <div class="avatar-container">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Agent" class="avatar"/>
                <span class="status-indicator online"></span>
              </div>
              <div class="agent-details">
                <h3 class="agent-name">Emma Watson</h3>
                <p class="agent-role">Financial Analyst</p>
              </div>
            </div>
            <div class="agent-task">
              <div class="task-progress">
                <div class="progress-bar" style="width: 75%"></div>
              </div>
              <p class="task-description">Analyzing Q2 financial reports</p>
              <p class="task-time">Est. completion: 45 min</p>
            </div>
          </div>
            
          <!-- Agent 2 -->
          <div class="agent-card">
            <div class="agent-info">
              <div class="avatar-container">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Agent" class="avatar"/>
                <span class="status-indicator online"></span>
              </div>
              <div class="agent-details">
                <h3 class="agent-name">Michael Chen</h3>
                <p class="agent-role">Market Researcher</p>
              </div>
            </div>
            <div class="agent-task">
              <div class="task-progress">
                <div class="progress-bar" style="width: 60%"></div>
              </div>
              <p class="task-description">Competitor product analysis</p>
              <p class="task-time">Est. completion: 1h 20min</p>
            </div>
          </div>
            
          <!-- Agent 3 -->
          <div class="agent-card">
            <div class="agent-info">
              <div class="avatar-container">
                <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Agent" class="avatar"/>
                <span class="status-indicator online"></span>
              </div>
              <div class="agent-details">
                <h3 class="agent-name">Sarah Johnson</h3>
                <p class="agent-role">Legal Specialist</p>
              </div>
            </div>
            <div class="agent-task">
              <div class="task-progress">
                <div class="progress-bar" style="width: 90%"></div>
              </div>
              <p class="task-description">Regulatory compliance review</p>
              <p class="task-time">Est. completion: 15 min</p>
            </div>
          </div>
            
          <!-- Agent 4 -->
          <div class="agent-card">
            <div class="agent-info">
              <div class="avatar-container">
                <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Agent" class="avatar"/>
                <span class="status-indicator online"></span>
              </div>
              <div class="agent-details">
                <h3 class="agent-name">David Kim</h3>
                <p class="agent-role">Data Scientist</p>
              </div>
            </div>
            <div class="agent-task">
              <div class="task-progress">
                <div class="progress-bar" style="width: 40%"></div>
              </div>
              <p class="task-description">Market trend analysis</p>
              <p class="task-time">Est. completion: 2h 10min</p>
            </div>
          </div>
            
          <!-- Agent 5 -->
          <div class="agent-card">
            <div class="agent-info">
              <div class="avatar-container">
                <img src="https://randomuser.me/api/portraits/women/22.jpg" alt="Agent" class="avatar"/>
                <span class="status-indicator online"></span>
              </div>
              <div class="agent-details">
                <h3 class="agent-name">Jessica Lee</h3>
                <p class="agent-role">Strategic Analyst</p>
              </div>
            </div>
            <div class="agent-task">
              <div class="task-progress">
                <div class="progress-bar" style="width: 25%"></div>
              </div>
              <p class="task-description">Competitor strategy mapping</p>
              <p class="task-time">Est. completion: 3h 45min</p>
            </div>
          </div>
        </div>
      </aside>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { definePage } from 'vue-router/auto'

definePage({
  layout:false
})
</script>
  <style>
  /* Base Styles */
  :root {
    --primary: #6366f1;
    --primary-glow: rgba(99, 102, 241, 0.5);
    --secondary: #10b981;
    --accent: #8b5cf6;
    --accent-glow: rgba(139, 92, 246, 0.5);
    --background: #0f172a;
    --foreground: #f8fafc;
    --card-bg: rgba(30, 41, 59, 0.7);
    --card-border: rgba(148, 163, 184, 0.1);
    --card-hover: rgba(30, 41, 59, 0.9);
    --sidebar-bg: rgba(15, 23, 42, 0.8);
    --glass-bg: rgba(30, 41, 59, 0.6);
    --glass-border: rgba(148, 163, 184, 0.2);
    --glass-highlight: rgba(255, 255, 255, 0.05);
    --financial: #10b981;
    --legal: #6366f1;
    --competitor: #f43f5e;
    --market: #f59e0b;
  }
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  body {
    background-color: var(--background);
    color: var(--foreground);
    font-size: 14px;
    line-height: 1.5;
  }
  
  /* Geometric Background */
  .geometric-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 20%),
      radial-gradient(circle at 90% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 20%),
      linear-gradient(to bottom right, rgba(99, 102, 241, 0.02) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
  }
  
  .geometric-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(to right, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 40px 40px;
    pointer-events: none;
  }
  
  /* Layout */
  .dashboard-container {
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
  }
  
  .dashboard-layout {
    display: grid;
    grid-template-columns: 240px 1fr 300px;
    min-height: 100vh;
    position: relative;
    z-index: 1;
  }
  
  /* Sidebar Styles */
  .sidebar {
    padding: 1.5rem;
    height: 100vh;
    overflow-y: auto;
    position: sticky;
    top: 0;
  }
  
  .left-sidebar {
    background: var(--sidebar-bg);
    border-right: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .right-sidebar {
    background: var(--sidebar-bg);
    border-left: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .logo-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 2rem;
  }
  
  .nav-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.7);
  }
  
  .menu-item:hover {
    background: var(--glass-highlight);
    color: var(--foreground);
  }
  
  .menu-item.active {
    background: var(--glass-highlight);
    color: var(--foreground);
    box-shadow: 0 0 0 1px var(--glass-border);
  }
  
  .menu-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Main Content */
  .main-content {
    padding: 1.5rem;
    overflow-y: auto;
  }
  
  .main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .filter-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  
  .filter-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--foreground);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }
  
  .filter-tab:hover {
    background: var(--glass-highlight);
    border-color: var(--glass-border);
  }
  
  .filter-tab.active {
    background: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 0 10px var(--primary-glow);
  }
  
  /* Section Styles */
  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .summary-section, .companies-section {
    margin-bottom: 2rem;
  }
  
  /* Report Cards */
  .report-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .report-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 0.75rem;
    padding: 1.25rem;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
  }
  
  .report-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom right, var(--glass-highlight), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  .report-card:hover::before {
    opacity: 1;
  }
  
  .report-card.featured {
    border-color: var(--primary);
    box-shadow: 0 0 15px var(--primary-glow);
  }
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  .avatar-container {
    position: relative;
  }
  
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--glass-border);
  }
  
  .avatar-container.small .avatar {
    width: 32px;
    height: 32px;
  }
  
  .report-meta {
    flex: 1;
  }
  
  .report-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .report-time {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  
  .card-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .action-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
  }
  
  .action-btn:hover {
    background: var(--glass-highlight);
    color: var(--foreground);
  }
  
  .action-btn.small {
    width: 24px;
    height: 24px;
  }
  
  .card-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .report-summary {
    font-size: 0.875rem;
    line-height: 1.5;
  }
  
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .tag {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
  }
  
  .tag.financial {
    background: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.4);
    color: #34d399;
  }
  
  .tag.legal {
    background: rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.4);
    color: #818cf8;
  }
  
  .tag.competitor {
    background: rgba(244, 63, 94, 0.2);
    border-color: rgba(244, 63, 94, 0.4);
    color: #fb7185;
  }
  
  .tag.market {
    background: rgba(245, 158, 11, 0.2);
    border-color: rgba(245, 158, 11, 0.4);
    color: #fbbf24;
  }
  
  /* Company Grid */
  .company-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .company-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 0.75rem;
    padding: 1.25rem;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .company-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-color: var(--glass-highlight);
  }
  
  .company-name {
    font-weight: 600;
    font-size: 1rem;
    flex: 1;
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
  }
  
  .date {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  
  /* Pagination */
  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }
  
  .pagination-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--foreground);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .pagination-btn:hover {
    background: var(--glass-highlight);
  }
  
  .pagination-btn.active {
    background: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 0 10px var(--primary-glow);
  }
  
  /* CTA Section */
  .cta-section {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
  }
  
  .cta-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(to right, var(--primary), var(--accent));
    border: none;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px var(--primary-glow);
  }
  
  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 20px var(--accent-glow);
  }
  
  /* Active Agents */
  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .agent-count {
    padding: 0.25rem 0.75rem;
    background: var(--primary);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .active-agents {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .agent-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.2s ease;
  }
  
  .agent-card:hover {
    background: var(--glass-highlight);
  }
  
  .agent-info {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }
  
  .status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid var(--background);
  }
  
  .status-indicator.online {
    background: var(--secondary);
  }
  
  .agent-details {
    flex: 1;
  }
  
  .agent-name {
    font-weight: 600;
    font-size: 0.875rem;
  }
  
  .agent-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  
  .agent-task {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0.5rem;
    padding: 0.75rem;
  }
  
  .task-progress {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    margin-bottom: 0.75rem;
    overflow: hidden;
  }
  
  .progress-bar {
    height: 100%;
    background: linear-gradient(to right, var(--primary), var(--accent));
    border-radius: 2px;
  }
  
  .task-description {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }
  
  .task-time {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
  }
  
  /* Responsive */
  @media (max-width: 1200px) {
    .dashboard-layout {
      grid-template-columns: 240px 1fr 250px;
    }
  }
  
  @media (max-width: 992px) {
    .dashboard-layout {
      grid-template-columns: 200px 1fr;
    }
    
    .right-sidebar {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .dashboard-layout {
      grid-template-columns: 1fr;
    }
    
    .left-sidebar {
      display: none;
    }
    
    .company-grid {
      grid-template-columns: 1fr;
    }
  }
  </style>