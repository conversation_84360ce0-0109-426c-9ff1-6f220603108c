/**
 * 通用Iframe消息处理 Composable
 * 
 * 🎯 核心功能：
 * 1. 统一的iframe消息类型定义和处理
 * 2. 可配置的消息处理逻辑
 * 3. 统一的加载状态管理
 * 4. 错误处理和重试机制
 * 5. 支持自定义消息处理器
 * 
 * 🔧 使用方式：
 * const { messageHandler, loading, error, ... } = useIframeMessages(config)
 */

import { ref, computed, readonly } from 'vue'

// ==================== 类型定义 ====================

/** 基础iframe消息接口 */
export interface IframeMessage {
  type: string
  data?: any
  source?: string
  timestamp: number
}

/** 通用错误消息类型 */
export type ErrorMessageType = 'API_ERROR' | 'ERROR' | 'AUTH_ERROR' | 'LOAD_ERROR'

/** 通用成功消息类型 */
export type SuccessMessageType = 'IFRAME_READY' | 'AUTH_SUCCESS' | 'LOAD_SUCCESS'

/** 通用通知消息类型 */
export type NotificationMessageType = 'RESIZE' | 'PAGE_LOADED' | 'API_CALL' | 'API_SUCCESS'

/** 所有支持的消息类型 */
export type AllMessageType = ErrorMessageType | SuccessMessageType | NotificationMessageType

/** 消息处理器函数类型 */
export type MessageHandler<T = any> = (message: IframeMessage, data?: T) => void | Promise<void>

/** 消息处理器配置 */
export interface MessageHandlerConfig {
  /** 错误消息处理器 */
  onError?: MessageHandler
  /** 成功消息处理器 */
  onSuccess?: MessageHandler  
  /** 通知消息处理器 */
  onNotification?: MessageHandler
  /** 自定义消息处理器 */
  onCustomMessage?: MessageHandler
}

/** iframe状态 */
export type IframeStatus = 'idle' | 'loading' | 'loaded' | 'error'

/** 配置选项 */
export interface UseIframeMessagesConfig {
  /** 是否启用调试模式 */
  debug?: boolean
  /** 支持的错误消息类型 */
  errorTypes?: ErrorMessageType[]
  /** 支持的成功消息类型 */
  successTypes?: SuccessMessageType[]
  /** 支持的通知消息类型 */
  notificationTypes?: NotificationMessageType[]
  /** 自定义支持的消息类型 */
  customTypes?: string[]
  /** 消息处理器配置 */
  handlers?: MessageHandlerConfig
  /** 是否自动管理加载状态 */
  autoManageLoading?: boolean
  /** 初始加载消息 */
  initialLoadingMessage?: string
}

// ==================== Hook实现 ====================

export function useIframeMessages(config: UseIframeMessagesConfig = {}) {
  // 解构配置选项，设置默认值
  const {
    debug = false,
    errorTypes = ['API_ERROR', 'ERROR', 'AUTH_ERROR'],
    successTypes = ['IFRAME_READY', 'AUTH_SUCCESS', 'LOAD_SUCCESS'],
    notificationTypes = ['RESIZE', 'PAGE_LOADED', 'API_CALL', 'API_SUCCESS'],
    customTypes = [],
    handlers = {},
    autoManageLoading = true,
    initialLoadingMessage = '正在初始化...'
  } = config

  // ==================== 响应式状态 ====================

  /** 是否加载中 */
  const isLoading = ref(true)
  
  /** 是否有错误 */
  const hasError = ref(false)
  
  /** 错误消息 */
  const errorMessage = ref<string>('')
  
  /** iframe状态 */
  const status = ref<IframeStatus>('idle')
  
  /** 加载消息 */
  const loadingMessage = ref(initialLoadingMessage)
  
  /** 消息计数 */
  const messageCount = ref(0)
  
  /** 是否开发环境 */
  const isDevelopment = computed(() => import.meta.env.DEV)

  // ==================== 计算属性 ====================

  /** 所有支持的消息类型 */
  const supportedTypes = computed(() => [
    ...errorTypes,
    ...successTypes,
    ...notificationTypes,
    ...customTypes
  ])

  /** 是否处于可用状态 */
  const isReady = computed(() => status.value === 'loaded' && !hasError.value)

  // ==================== 内部方法 ====================

  /** 日志记录 */
  const log = (...args: any[]) => {
    if (debug || isDevelopment.value) {
      console.log('[useIframeMessages]', ...args)
    }
  }

  /** 错误日志记录 */
  const logError = (...args: any[]) => {
    if (debug || isDevelopment.value) {
      console.error('[useIframeMessages]', ...args)
    }
  }

  /** 处理错误消息 */
  const handleErrorMessage = async (message: IframeMessage) => {
    logError('收到错误消息:', message)
    
    if (autoManageLoading) {
      isLoading.value = false
      hasError.value = true
      errorMessage.value = message.data?.message || message.data?.error || `${message.type}: 未知错误`
      status.value = 'error'
    }

    // 调用自定义错误处理器
    if (handlers.onError) {
      await handlers.onError(message)
    }
  }

  /** 处理成功消息 */
  const handleSuccessMessage = async (message: IframeMessage) => {
    log('收到成功消息:', message)
    
    if (autoManageLoading) {
      isLoading.value = false
      hasError.value = false
      status.value = 'loaded'
      loadingMessage.value = '加载完成'
    }

    // 调用自定义成功处理器
    if (handlers.onSuccess) {
      await handlers.onSuccess(message)
    }
  }

  /** 处理通知消息 */
  const handleNotificationMessage = async (message: IframeMessage) => {
    log('收到通知消息:', message)

    // 调用自定义通知处理器
    if (handlers.onNotification) {
      await handlers.onNotification(message)
    }
  }

  /** 处理自定义消息 */
  const handleCustomMessage = async (message: IframeMessage) => {
    log('收到自定义消息:', message)

    // 调用自定义消息处理器
    if (handlers.onCustomMessage) {
      await handlers.onCustomMessage(message)
    }
  }

  // ==================== 主要方法 ====================

  /** 主消息处理器 */
  const handleMessage = async (message: IframeMessage) => {
    messageCount.value++
    
    // 检查消息类型是否受支持
    if (!supportedTypes.value.includes(message.type)) {
      if (debug || isDevelopment.value) {
        log('忽略不支持的消息类型:', message.type, message)
      }
      return
    }

    try {
      // 根据消息类型分发处理
      if (errorTypes.includes(message.type as ErrorMessageType)) {
        await handleErrorMessage(message)
      } else if (successTypes.includes(message.type as SuccessMessageType)) {
        await handleSuccessMessage(message)
      } else if (notificationTypes.includes(message.type as NotificationMessageType)) {
        await handleNotificationMessage(message)
      } else if (customTypes.includes(message.type)) {
        await handleCustomMessage(message)
      }
    } catch (error) {
      logError('消息处理失败:', error, message)
    }
  }

  /** 重置状态 */
  const reset = () => {
    if (autoManageLoading) {
      isLoading.value = true
      hasError.value = false
      errorMessage.value = ''
      status.value = 'loading'
      loadingMessage.value = initialLoadingMessage
    }
    messageCount.value = 0
    log('状态已重置')
  }

  /** 设置加载状态 */
  const setLoading = (loading: boolean, message?: string) => {
    if (autoManageLoading) {
      isLoading.value = loading
      if (loading) {
        status.value = 'loading'
        hasError.value = false
      }
      if (message) {
        loadingMessage.value = message
      }
    }
  }

  /** 设置错误状态 */
  const setError = (error: string) => {
    if (autoManageLoading) {
      isLoading.value = false
      hasError.value = true
      errorMessage.value = error
      status.value = 'error'
    }
    logError('设置错误状态:', error)
  }

  /** 设置成功状态 */
  const setSuccess = (message?: string) => {
    if (autoManageLoading) {
      isLoading.value = false
      hasError.value = false
      status.value = 'loaded'
      if (message) {
        loadingMessage.value = message
      }
    }
    log('设置成功状态:', message)
  }

  // ==================== 返回接口 ====================

  return {
    // 状态
    isLoading: readonly(isLoading),
    hasError: readonly(hasError),
    errorMessage: readonly(errorMessage),
    status: readonly(status),
    loadingMessage: readonly(loadingMessage),
    messageCount: readonly(messageCount),
    isReady,
    supportedTypes,

    // 方法
    handleMessage,
    reset,
    setLoading,
    setError,
    setSuccess,

    // 配置信息
    config: {
      errorTypes,
      successTypes,
      notificationTypes,
      customTypes,
      debug,
      autoManageLoading
    }
  }
}

// ==================== 便捷类型导出 ====================

export type IframeMessagesComposable = ReturnType<typeof useIframeMessages>

// ==================== 预设配置 ====================

/** AI Dashboard 专用配置 */
export const AI_DASHBOARD_CONFIG: UseIframeMessagesConfig = {
  debug: false,
  errorTypes: ['API_ERROR', 'ERROR', 'AUTH_ERROR'],
  successTypes: ['IFRAME_READY'],
  notificationTypes: ['RESIZE', 'PAGE_LOADED', 'API_CALL', 'API_SUCCESS'],
  autoManageLoading: true,
  initialLoadingMessage: '正在初始化 AI Dashboard...'
}

/** 通用Embed页面配置 */
export const EMBED_PAGE_CONFIG: UseIframeMessagesConfig = {
  debug: false,
  errorTypes: ['ERROR', 'LOAD_ERROR'],
  successTypes: ['LOAD_SUCCESS'],
  notificationTypes: ['RESIZE', 'PAGE_LOADED'],
  autoManageLoading: true,
  initialLoadingMessage: '正在加载页面...'
}

/** 认证页面配置 */
export const AUTH_PAGE_CONFIG: UseIframeMessagesConfig = {
  debug: false,
  errorTypes: ['AUTH_ERROR', 'ERROR'],
  successTypes: ['AUTH_SUCCESS'],
  notificationTypes: ['PAGE_LOADED'],
  autoManageLoading: true,
  initialLoadingMessage: '正在进行身份验证...'
}