// src/components/STour/composables/useTour.ts

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useThrottleFn } from '@vueuse/core'
import type { Ref, ComputedRef } from 'vue'
import type { 
  STourStep, 
  ModuleTourConfig,
  STourThemeConfig
} from '../types'
import { THEME } from '../config'

interface UseTourOptions {
  visible: Ref<boolean>
  onFinish?: () => void
}

interface UseTourReturn {
  isVisible: Ref<boolean>
  currentStep: ComputedRef<number>
  steps: Ref<STourStep[]>
  isLastStep: ComputedRef<boolean>
  computedMask: ComputedRef<boolean | { color: string }>
  type: ComputedRef<'primary' | 'default'>
  handleStepChange: (step: number) => void
  handleClose: () => void
  handleFinish: () => Promise<void>
  start: () => void
  finish: () => Promise<void>
  close: () => void
  next: () => void
  prev: () => void
  updateConfig: (config: ModuleTourConfig) => void
}

export function useTour({ visible, onFinish }: UseTourOptions): UseTourReturn {
  // 基础状态
  const stepIndex = ref(0)
  const steps = ref<STourStep[]>([])
  const isLoading = ref(false)
  const currentTheme = ref<STourThemeConfig>(THEME)

  // 计算属性
  const currentStep = computed(() => stepIndex.value)
  const isLastStep = computed(() => currentStep.value === steps.value.length - 1)
  const canShowTour = computed(() => !isLoading.value && steps.value.length > 0)
  
  const computedMask = computed(() => {
    return currentTheme.value?.maskColor 
      ? { color: currentTheme.value.maskColor } 
      : true
  })

  const type = computed(() => {
    return currentTheme.value?.primaryColor ? 'primary' : 'default'
  })

  // 重置状态
  const resetState = () => {
    stepIndex.value = 0
    steps.value = []
    isLoading.value = false
  }

  // 更新配置
  const updateConfig = (config: ModuleTourConfig) => {
    isLoading.value = true
    
    try {
      // 重置状态
      resetState()
      
      // 更新配置
      steps.value = [...config.steps]
      currentTheme.value = {
        ...THEME,
        ...config.theme
      }
    } finally {
      isLoading.value = false
    }
  }

  // 更新可见性
  const updateVisibility = (value: boolean) => {
    if (value) {
      visible.value = canShowTour.value
    } else {
      visible.value = false
    }
  }

  // 添加滚动到目标元素的方法
  const scrollToTarget = (stepIndex: number) => {
    const step = steps.value[stepIndex]
    if (!step?.target) {
      return
    }
    
    const target = typeof step.target === 'string' 
      ? document.querySelector(step.target)
      : step.target
      
    if (target instanceof Element) {
      // 使用 scrollIntoView 确保元素在可视区域内
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  }

  // 增强 handleStepChange 方法
  const handleStepChange = (current: number) => {
    stepIndex.value = current
    // 切换步骤时滚动到目标元素
    scrollToTarget(current)
  }

  // 监听窗口大小变化
  const handleResize = useThrottleFn(() => {
    if (visible.value) {
      // 重新定位到当前步骤的目标元素
      scrollToTarget(currentStep.value)
    }
  }, 200)

  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  // 完成处理
  const finish = async () => {
    visible.value = false
    onFinish?.()
  }

  return {
    isVisible: visible,
    currentStep,
    steps,
    isLastStep,
    computedMask,
    type,
    handleStepChange,
    handleClose: () => updateVisibility(false),
    handleFinish: finish,
    start: () => updateVisibility(true),
    finish,
    close: () => updateVisibility(false),
    next: () => {
      if (isLastStep.value) {
        finish()
      } else {
        stepIndex.value++
      }
    },
    prev: () => {
      if (stepIndex.value > 0) {
        stepIndex.value--
      }
    },
    updateConfig
  }
}