import { usePost } from '@/hooks/useHttp'
import type { BarChartData } from '@/types/intelligence'
import type { IndustryChart } from '@/types/intelligence'

export const useChartComposable = () => {
  const {
    execute: refreshMonthlyChanges,
    state: monthlyData,
    isLoading: monthlyChangesLoading,
  } = usePost<BarChartData>(
    'customs_data/monthly_changes',
    {
      data: {
        notion: ['japan', 'korea', 'vietnam'],
      },
    },
    {
      showError:false
    },
    { immediate: false }
  )

  const {
    execute: refreshMaxIncreaseCountry,
    state: maxIncreaseCountryData,
    isLoading: maxIncreaseCountryLoading,
  } = usePost<IndustryChart[]>(
    'customs_data/top_increase_nations',
    {},
    {
      showError:false
    },
    {
      immediate: false,
    }
  )


  const {
    execute: refreshMaxIncreaseProduct,
    state: maxIncreaseProductData,
    isLoading: maxIncreaseProductLoading,
  } = usePost<IndustryChart[]>(
    'customs_data/top_increase_product',
    {},
    {
      showError: false,
    },
    {
      immediate: false,
    }
  )

  const fetchAllChartData = async () => {
    await Promise.all([
      refreshMonthlyChanges(),
      refreshMaxIncreaseCountry(),
      refreshMaxIncreaseProduct(),
    ])
  }
  return {
    monthlyData,
    monthlyChangesLoading,
    maxIncreaseCountryData,
    maxIncreaseCountryLoading,
    maxIncreaseProductData,
    maxIncreaseProductLoading,

    fetchAllChartData
  }
}

