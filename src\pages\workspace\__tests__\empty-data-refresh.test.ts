import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ref } from 'vue'

describe('Empty Data Refresh Logic', () => {
  let mockBidingResults: any
  let mockStrategicResults: any
  let mockDataLoadedFlags: any
  let mockDataCacheTimestamps: any

  beforeEach(() => {
    vi.clearAllMocks()
    
    // 模拟数据状态
    mockBidingResults = ref([])
    mockStrategicResults = ref([])
    mockDataLoadedFlags = ref({
      biding: false,
      strategic: false
    })
    mockDataCacheTimestamps = ref({
      biding: 0,
      strategic: 0
    })
  })

  // 模拟修复后的 shouldRefreshData 函数
  const simulateShouldRefreshData = (
    dataType: 'biding' | 'strategic', 
    forceRefresh = false,
    bidingResults: any[] = [],
    strategicResults: any[] = [],
    dataLoadedFlags = { biding: false, strategic: false },
    dataCacheTimestamps = { biding: 0, strategic: 0 }
  ) => {
    const now = Date.now()
    const lastUpdate = dataCacheTimestamps[dataType]
    const timeSinceUpdate = now - lastUpdate
    const FORCE_REFRESH_INTERVAL = 30 * 60 * 1000 // 30分钟
    
    // 强制刷新
    if (forceRefresh) {
      console.log(`${dataType} data: Force refresh requested`)
      return true
    }
    
    // 从未加载过
    if (!dataLoadedFlags[dataType] || lastUpdate === 0) {
      console.log(`${dataType} data: Never loaded, need to fetch`)
      return true
    }
    
    // 检查数据是否为空数组 - 防止接口报错后一直没有数据
    const currentResults = dataType === 'biding' ? bidingResults : strategicResults
    if (Array.isArray(currentResults) && currentResults.length === 0) {
      console.log(`${dataType} data: Current results are empty, need to refresh`)
      return true
    }
    
    // 检查缓存是否过期
    if (timeSinceUpdate > FORCE_REFRESH_INTERVAL) {
      console.log(`${dataType} data: Cache expired (${Math.round(timeSinceUpdate / 1000 / 60)} minutes old)`)
      return true
    }
    
    console.log(`${dataType} data: Using cached data (${Math.round(timeSinceUpdate / 1000)} seconds old)`)
    return false
  }

  // 模拟修复后的 handleSpecialTabChange 函数
  const simulateHandleSpecialTabChange = (
    tabKey: string,
    bidingResults: any[] = [],
    strategicResults: any[] = []
  ) => {
    console.log('Special analysis tab changed to:', tabKey)
    
    // 检查当前切换到的标签页是否有数据，如果没有则强制刷新
    let needsForceRefresh = false
    
    if (tabKey === 'biding_analysis') {
      // 检查招投标数据是否为空
      if (!bidingResults || bidingResults.length === 0) {
        console.log('Biding results are empty, forcing refresh')
        needsForceRefresh = true
      }
    } else if (tabKey === 'strategic_analysis') {
      // 检查战略分析数据是否为空
      if (!strategicResults || strategicResults.length === 0) {
        console.log('Strategic results are empty, forcing refresh')
        needsForceRefresh = true
      }
    }
    
    return {
      needsForceRefresh,
      action: needsForceRefresh ? 'forceReload' : 'useCache'
    }
  }

  describe('shouldRefreshData with Empty Data Check', () => {
    it('should refresh when biding results are empty even if data was loaded before', () => {
      const dataLoadedFlags = { biding: true, strategic: false }
      const dataCacheTimestamps = { biding: Date.now() - 5000, strategic: 0 } // 5秒前加载过
      const bidingResults: any[] = [] // 空数组
      
      const shouldRefresh = simulateShouldRefreshData(
        'biding', 
        false, 
        bidingResults, 
        [], 
        dataLoadedFlags, 
        dataCacheTimestamps
      )
      
      expect(shouldRefresh).toBe(true)
    })

    it('should refresh when strategic results are empty even if data was loaded before', () => {
      const dataLoadedFlags = { biding: false, strategic: true }
      const dataCacheTimestamps = { biding: 0, strategic: Date.now() - 5000 } // 5秒前加载过
      const strategicResults: any[] = [] // 空数组
      
      const shouldRefresh = simulateShouldRefreshData(
        'strategic', 
        false, 
        [], 
        strategicResults, 
        dataLoadedFlags, 
        dataCacheTimestamps
      )
      
      expect(shouldRefresh).toBe(true)
    })

    it('should not refresh when data exists and cache is fresh', () => {
      const dataLoadedFlags = { biding: true, strategic: false }
      const dataCacheTimestamps = { biding: Date.now() - 5000, strategic: 0 } // 5秒前加载过
      const bidingResults = [{ id: 1, data: 'some data' }] // 有数据
      
      const shouldRefresh = simulateShouldRefreshData(
        'biding', 
        false, 
        bidingResults, 
        [], 
        dataLoadedFlags, 
        dataCacheTimestamps
      )
      
      expect(shouldRefresh).toBe(false)
    })

    it('should refresh when data has never been loaded', () => {
      const dataLoadedFlags = { biding: false, strategic: false }
      const dataCacheTimestamps = { biding: 0, strategic: 0 }
      
      const shouldRefresh = simulateShouldRefreshData(
        'biding', 
        false, 
        [], 
        [], 
        dataLoadedFlags, 
        dataCacheTimestamps
      )
      
      expect(shouldRefresh).toBe(true)
    })

    it('should refresh when cache is expired even with data', () => {
      const dataLoadedFlags = { biding: true, strategic: false }
      const dataCacheTimestamps = { 
        biding: Date.now() - (31 * 60 * 1000), // 31分钟前，超过30分钟缓存期
        strategic: 0 
      }
      const bidingResults = [{ id: 1, data: 'some data' }] // 有数据但过期
      
      const shouldRefresh = simulateShouldRefreshData(
        'biding', 
        false, 
        bidingResults, 
        [], 
        dataLoadedFlags, 
        dataCacheTimestamps
      )
      
      expect(shouldRefresh).toBe(true)
    })
  })

  describe('handleSpecialTabChange with Empty Data Check', () => {
    it('should force refresh when switching to biding_analysis with empty data', () => {
      const bidingResults: any[] = []
      const strategicResults = [{ id: 1, data: 'strategic data' }]
      
      const result = simulateHandleSpecialTabChange('biding_analysis', bidingResults, strategicResults)
      
      expect(result.needsForceRefresh).toBe(true)
      expect(result.action).toBe('forceReload')
    })

    it('should force refresh when switching to strategic_analysis with empty data', () => {
      const bidingResults = [{ id: 1, data: 'biding data' }]
      const strategicResults: any[] = []
      
      const result = simulateHandleSpecialTabChange('strategic_analysis', bidingResults, strategicResults)
      
      expect(result.needsForceRefresh).toBe(true)
      expect(result.action).toBe('forceReload')
    })

    it('should use cache when switching to biding_analysis with existing data', () => {
      const bidingResults = [{ id: 1, data: 'biding data' }]
      const strategicResults = [{ id: 1, data: 'strategic data' }]
      
      const result = simulateHandleSpecialTabChange('biding_analysis', bidingResults, strategicResults)
      
      expect(result.needsForceRefresh).toBe(false)
      expect(result.action).toBe('useCache')
    })

    it('should use cache when switching to strategic_analysis with existing data', () => {
      const bidingResults = [{ id: 1, data: 'biding data' }]
      const strategicResults = [{ id: 1, data: 'strategic data' }]
      
      const result = simulateHandleSpecialTabChange('strategic_analysis', bidingResults, strategicResults)
      
      expect(result.needsForceRefresh).toBe(false)
      expect(result.action).toBe('useCache')
    })

    it('should handle null/undefined data gracefully', () => {
      const bidingResults = null as any
      const strategicResults = undefined as any
      
      const bidingResult = simulateHandleSpecialTabChange('biding_analysis', bidingResults, strategicResults)
      const strategicResult = simulateHandleSpecialTabChange('strategic_analysis', bidingResults, strategicResults)
      
      expect(bidingResult.needsForceRefresh).toBe(true)
      expect(strategicResult.needsForceRefresh).toBe(true)
    })
  })

  describe('Real-world Scenarios', () => {
    it('should handle API error recovery scenario', () => {
      // 场景：API之前报错，数据为空，用户切换标签页
      const dataLoadedFlags = { biding: true, strategic: false } // 标记为已加载但实际为空
      const dataCacheTimestamps = { biding: Date.now() - 10000, strategic: 0 } // 10秒前"加载"过
      const bidingResults: any[] = [] // API报错后为空
      
      // 用户切换到招投标分析标签页
      const tabChangeResult = simulateHandleSpecialTabChange('biding_analysis', bidingResults, [])
      expect(tabChangeResult.needsForceRefresh).toBe(true)
      
      // shouldRefreshData 也应该返回true
      const shouldRefresh = simulateShouldRefreshData(
        'biding', 
        false, 
        bidingResults, 
        [], 
        dataLoadedFlags, 
        dataCacheTimestamps
      )
      expect(shouldRefresh).toBe(true)
    })

    it('should handle successful data loading scenario', () => {
      // 场景：数据正常加载，用户在标签页间切换
      const dataLoadedFlags = { biding: true, strategic: true }
      const dataCacheTimestamps = { 
        biding: Date.now() - 10000, 
        strategic: Date.now() - 15000 
      }
      const bidingResults = [{ id: 1, data: 'biding data' }]
      const strategicResults = [{ id: 1, data: 'strategic data' }]
      
      // 用户切换到招投标分析标签页
      const bidingTabResult = simulateHandleSpecialTabChange('biding_analysis', bidingResults, strategicResults)
      expect(bidingTabResult.needsForceRefresh).toBe(false)
      
      // 用户切换到战略分析标签页
      const strategicTabResult = simulateHandleSpecialTabChange('strategic_analysis', bidingResults, strategicResults)
      expect(strategicTabResult.needsForceRefresh).toBe(false)
      
      // shouldRefreshData 应该返回false（使用缓存）
      expect(simulateShouldRefreshData('biding', false, bidingResults, strategicResults, dataLoadedFlags, dataCacheTimestamps)).toBe(false)
      expect(simulateShouldRefreshData('strategic', false, bidingResults, strategicResults, dataLoadedFlags, dataCacheTimestamps)).toBe(false)
    })
  })
})
