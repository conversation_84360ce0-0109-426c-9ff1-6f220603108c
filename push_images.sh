#!/bin/bash
set -e

# ð§© 构建环境（dev 或 pd），默认是 dev
BUILD_ENV=${1:-dev}

# ð·️ 镜像与仓库设置
DOCKERFILE_PATH="./Dockerfile"
BASE_IMAGE_NAME="web-ui"
PRIVATE_REGISTRY="10.4.4.12:5000"
CONTEXT_PATH="."
TAG="${BUILD_ENV}"
LATEST_TAG="latest"

# ð 镜像命名
LOCAL_IMAGE_FULL_NAME="${BASE_IMAGE_NAME}:${TAG}"
REMOTE_IMAGE_FULL_NAME="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"
REMOTE_IMAGE_LATEST="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${LATEST_TAG}"

echo "ð§ 开始构建并推送镜像 (${BUILD_ENV} 环境)..."

# 清理已有本地镜像（忽略失败）
docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true

# 构建镜像，传入构建参数
docker build \
  --no-cache \
  --build-arg BUILD_ENV="${BUILD_ENV}" \
  -f "${DOCKERFILE_PATH}" \
  -t "${LOCAL_IMAGE_FULL_NAME}" \
  "${CONTEXT_PATH}"

# 推送带环境标签的镜像
docker tag "${LOCAL_IMAGE_FULL_NAME}" "${REMOTE_IMAGE_FULL_NAME}"
docker push "${REMOTE_IMAGE_FULL_NAME}"

# 如果是 pd 环境，也打上 latest 标签
if [ "${BUILD_ENV}" = "pd" ]; then
  echo "ð·️ 同时打上 latest 标签（指向 pd）"
  docker tag "${LOCAL_IMAGE_FULL_NAME}" "${REMOTE_IMAGE_LATEST}"
  docker push "${REMOTE_IMAGE_LATEST}"
fi

# 清理本地镜像
docker rmi "${LOCAL_IMAGE_FULL_NAME}" || true
[ "${BUILD_ENV}" = "pd" ] && docker rmi "${REMOTE_IMAGE_LATEST}" || true

echo "✅ 镜像 ${REMOTE_IMAGE_FULL_NAME} 推送完成"
