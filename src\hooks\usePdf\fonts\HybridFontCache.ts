import { FONT_CACHE_KEY, FONT_VERSION } from './constants'
import type { FontCacheData } from '../types'

export class HybridFontCache {
  private memoryCache: WeakMap<object, any>
  private readonly CACHE_KEY: object
  private readonly SW_CACHE_NAME = 'pdf-fonts-cache-v1'
  private readonly DB_NAME = 'FontCache'
  private readonly STORE_NAME = 'fonts'

  constructor() {
    this.memoryCache = new WeakMap()
    this.CACHE_KEY = {}
    this.initServiceWorker()
  }

  private async initServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/font-service-worker.js')
        console.debug('ServiceWorker registration successful:', registration)
      } catch (error) {
        console.warn('ServiceWorker registration failed:', error)
      }
    }
  }

  private async initIndexedDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, 1)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          db.createObjectStore(this.STORE_NAME)
        }
      }
    })
  }

  async getFonts(): Promise<FontCacheData | null> {
    // 1. 检查内存缓存
    const memCached = this.memoryCache.get(this.CACHE_KEY)
    if (memCached) {
      console.debug('Using fonts from memory cache')
      return memCached
    }

    // 2. 检查 Service Worker Cache
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.SW_CACHE_NAME)
        const response = await cache.match(FONT_CACHE_KEY)
        if (response) {
          const data = await response.json() as FontCacheData
          if (data.version === FONT_VERSION) {
            console.debug('Using fonts from Service Worker cache')
            this.memoryCache.set(this.CACHE_KEY, data)
            return data
          }
        }
      } catch (error) {
        console.warn('Failed to read from Cache Storage:', error)
      }
    }

    // 3. 检查 IndexedDB
    try {
      const db = await this.initIndexedDB()
      const data = await this.getFromIndexedDB(db, FONT_CACHE_KEY)
      if (data && data.version === FONT_VERSION) {
        console.debug('Using fonts from IndexedDB')
        this.memoryCache.set(this.CACHE_KEY, data)
        return data
      }
    } catch (error) {
      console.warn('Failed to read from IndexedDB:', error)
    }

    return null
  }

  async saveFonts(fonts: any): Promise<void> {
    const cacheData: FontCacheData = {
      version: FONT_VERSION,
      fonts
    }

    // 1. 更新内存缓存
    this.memoryCache.set(this.CACHE_KEY, cacheData)

    // 2. 更新 Service Worker Cache
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.SW_CACHE_NAME)
        await cache.put(
          FONT_CACHE_KEY,
          new Response(JSON.stringify(cacheData))
        )
        console.debug('Fonts cached in Service Worker')
      } catch (error) {
        console.warn('Failed to cache in Service Worker:', error)
      }
    }

    // 3. 更新 IndexedDB
    try {
      const db = await this.initIndexedDB()
      await this.saveToIndexedDB(db, FONT_CACHE_KEY, cacheData)
      console.debug('Fonts cached in IndexedDB')
    } catch (error) {
      console.warn('Failed to cache in IndexedDB:', error)
    }
  }

  private async getFromIndexedDB(db: IDBDatabase, key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.STORE_NAME, 'readonly')
      const store = transaction.objectStore(this.STORE_NAME)
      const request = store.get(key)
      
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  private async saveToIndexedDB(db: IDBDatabase, key: string, value: any): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.STORE_NAME, 'readwrite')
      const store = transaction.objectStore(this.STORE_NAME)
      const request = store.put(value, key)
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  async clearCache(): Promise<void> {
    // 1. 清除内存缓存
    this.memoryCache.delete(this.CACHE_KEY)

    // 2. 清除 Service Worker Cache
    if ('caches' in window) {
      try {
        await caches.delete(this.SW_CACHE_NAME)
      } catch (error) {
        console.warn('Failed to clear Cache Storage:', error)
      }
    }

    // 3. 清除 IndexedDB
    try {
      const db = await this.initIndexedDB()
      const transaction = db.transaction(this.STORE_NAME, 'readwrite')
      const store = transaction.objectStore(this.STORE_NAME)
      await store.delete(FONT_CACHE_KEY)
    } catch (error) {
      console.warn('Failed to clear IndexedDB:', error)
    }
  }
} 