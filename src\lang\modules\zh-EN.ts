export default {
  menu: {
    topInfoMenu: 'Information',
    topIntelligenceMenu: 'Reports',
    topRobotMenu: 'Robot',
    topSearchMenu: 'Search',
    topProcurementMenu: 'Business Opportunity',
    topDashboardMenu: 'Dashboard',
    dailyReport: 'Daily Briefing',
    formatReport: 'Format Report',
    topWorkspaceMenu: 'Workspace',
  },
  button: {
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    refresh: 'Refresh',
    myself: 'Myself',
    clear: 'Clear',
    selectAll: 'Select All',
    load_more: 'Load More',
  },
  message: {
    copySuccess: 'Copy Success',
  },
  robot: {
    title: 'Choose a robot as your assistant',
    helper: {
      scrollbarTip:
        'Please move the mouse into the area below the card, use the mouse wheel (shift+wheel) to slide, view more assistants',
    },
    select: 'Select me',
  },
  intelligence: {
    keyEventsMenu: 'Messages',
    dashboardMenu: 'Dashboard',
    summaryMenu: 'Summary',
    createdIntelligence: 'Report Tracking',
    searchPlaceholder: 'Input country/industry to search',
    projectListTitle: 'Industry Briefing',
    emptyProjectsTitle: 'No data',
    outline:{
      title:'Outline',
    },
    report: {
      viewDetail: 'View Details',
      viewDetailTip: 'Click view details, you will see a complete report',
    },
    dashboard: {
      businessMoneyTitle: 'Industry Export Amount Percentage',
      keyEventsTitle: 'Google Keyword Trends',
      monthlyDataTitle: 'Monthly Export Trade Amount',
      maxIncreaseCountryTitle: '{time} Export Amount Increased Most Countries',
      maxIncreaseProductTitle: '{time} Export Amount Increased Most Products',
    },
    markdown: {
      topic: 'Topic',
      summary: 'Summary',
      analysis: 'Analysis',
      shorttermimpact: 'Short-term Impact',
      longtermimpact: 'Long-term Impact',
      suggestions: 'Suggestions',
      downloadpdf: 'Download PDF',
      previewpdf: 'Preview PDF',
      pdfnotready: 'PDF function is initializing...',
      settings: 'PDF Settings',
      pdfsettings: 'PDF Settings',
      pageSize: 'Page Size',
      orientation: 'Orientation',
      portrait: 'Portrait',
      landscape: 'Landscape',
      margins: 'Page Margins',
      marginLeft: 'Left Margin',
      marginTop: 'Top Margin',
      marginRight: 'Right Margin',
      marginBottom: 'Bottom Margin',
      title: 'Title',
      subtitle: 'Subtitle',
      headerSettings: 'Header Settings',
      headerLeft: 'Left Text',
      headerCenter: 'Center Text',
      headerRight: 'Right Text (Default: Current Date)',
      headerRightPlaceholder: 'Leave empty for current date',
      footerSettings: 'Footer Settings',
      footerCopyright: 'Footer Title',
      footerConfidential: 'Footer Subtitle',
      preview: 'PDF Preview',
      company: 'Company',
      website: 'Website',
      email: 'Contact Email',
      potentialRisks: 'Potential Risks',
      potentialOpportunities: 'Potential Opportunities',
      suggestionPlaceholder: 'You can provide specific suggestions for the current {type}',
      streaming: 'The report content is being generated, please try again later',
    },
    table: {
      keyinfo: 'Key Information',
      importantdata: 'Important Data',
      implications: 'Implications',
      description: 'Description',
      statistics: 'Statistics',
      timeframe: 'Timeframe',
      form: 'Form',
      source: 'Source',
      trend: 'Trend',
      rewrite: 'Rewrite',
    },
    swiper: {
      paused: 'The swiper is paused, {text} later will autoplay',
      toggleChartSwiper: 'Display/Hide Chart Swiper',
      fullscreen: 'Fullscreen',
      exitFullscreen: 'Exit Fullscreen',
      refresh: 'Handle Refresh',
      refreshRemainingTime: '{time} later refresh latest news',
      togglePlay: 'Pause/Resume Autoplay',
    },
  },
  page: {
    report_chat: {
      title: 'Report Chat',
      feed_title: 'Report Feed',
      report_count: 'Total {count} reports',
      agent_list_title: 'AI Assistant List',
      active_agent_count: '{count} active agents'
    }
  },
  workspace:{
    special_reports:'Business Insight',
    report_feed:'Report Feed',
    binding_analysis:'Binding Analysis',
    strategic_analysis:'Strategic Analysis',
    strategic_opportunities:'Strategic Opportunities',
    filter_by_country:'Filter by Country',
    all_countries:'All Countries',
    filter_by_industry:'Filter by Industry',
    all_industries:'All Industries',
    search_placeholder:'Search...',
    load_error:'Load Error',
    load_error_description:'Failed to load data',
    retry:'Retry',
    showing_results:'Showing {start}-{end} / {total} results',
    previous:'Previous',
    next:'Next',
    business_news_count:'{count} business news',
    unknown_title:'Unknown Title',
    news_status:'News',
    clear_search_filters:'Clear Search Filters',
    no_business_news_found:'No matching business news found',
    no_business_news_data:'No business news data available',
    no_business_news_try_keywords:'No business news containing "{searchText}" found, please try other keywords',
    no_business_news_system_updating:'No business news data available currently, system may be updating, please try again later',
    update_time:'Update Time',
    status:'Status',
    actions:'Actions',
    view_detail:'View Details',
    
    messages: {
      page_change_error: 'Failed to change page',
      filter_error: 'Failed to apply filters'
    }
  },
  step: {
    skip: 'skip',
    next: 'next',
  },
  poll: {
    start: 'Start(Enter)',
    loading: 'AI is analyzing your information...',
    projectCountLabel: 'projects',
    title: 'If you are willing to provide the following information, it will help AI serve you better',
    sector: 'Sector',
    sectorPlaceholder: 'For example: The industry I am in is the import and export of agricultural products.',
    region: 'Region',
    regionPlaceholder: 'For example: I focus on the North American market.',
    style: 'Style',
    stylePlaceholder: 'For example: I focus on cross-border e-commerce.',
    overseas: 'Enterprise Status',
    overseasPlaceholder: 'For example: I am a startup, just starting to go global.',
    escHint: 'Press ESC to skip directly',
    pollValidation: 'Please answer the following questions, if you do not want to answer, please press ESC to skip',
  },
  login: {
    require: 'Required fields cannot be empty',
    usernameRequire: 'Username must be at least 3 characters',
    passwordRequire: 'Password must be at least 6 characters',
    emailRequire: 'Please enter a valid email address',
    nameRequire: 'Name must be at least 2 characters',
    title: 'Specific AI',
    subtitle: 'Break the information barrier of enterprise overseas',
    description: 'Let everyone have their own AI strategic advisor',
    usernamePlaceholder: 'Please enter your Username/Email/Phone Number',
    emailPlaceholder: 'Please enter your Email',
    passwordPlaceholder: 'Please enter your Password',
    login: 'Login',
    emailVerification:'Email Verification',
    emailCodeSent:'Verification code sent to the following email address, please check',
    emailCodeInputTitle:'Please enter the email verification code',
    emailCodeVerifyError:'Verification code verification failed',
    emailCodeVerifySuccess:'Verification code verified successfully',
    emailCodeSendError:'Verification code sending failed',
    emailCodeSendSuccess:'Verification code sent successfully',
    resendCode:'Resend Code',
    verifying:'Verifying...',
    emailCodeNote:'Please check if the verification code exists in <junk mail>',
    captchaImageTitle: 'Captcha Image',
    captchaInputTitle: 'Please enter the captcha',
    captchaInputPlaceholder: 'Please enter the captcha',
    captchaNote: 'Please enter the captcha',
    verified: 'Verified',
    register: 'Register',
    haveAccount: 'Already have an account?',
    noAccount: 'No account?',
    forgetPassword: 'Forget Password',
    username: 'Username',
    email: 'Email',
    password: 'Password',
    logout: 'Logout',
    rememberPassword: 'Remember password for 30 days',
    tryNow: 'Try Now',
    createdAccount: 'Create Account',
    welcomeBack: 'Welcome Back',
    companyNamePlaceholder: 'Please enter your Company Name',
    inviteCodePlaceholder: 'Please enter your Invite Code',
    inviteCode: 'Code',
    companyName: 'Company Name',
    betaTestingTip:'Currently in beta testing. Please contact us for an invitation code.',
    createCompanyCodeError:'Invitation code does not exist'
  },
  global: {
    onlySeeSelf:'Only See',
    total_items:'{count} projects',
    bidding_filter_desc:'{count} projects, {country_num} countries',
    noData:'No data available',
    contactUsSuccess:'Thank you for your interest, we will contact you soon',
    appName: 'Go Global',
    back: 'Back',
    regenerate:'Regenerate',
    retrying: 'Retrying...',
    stop: 'Stop',
    generating: 'Generating...',
    error: 'Error, please try again later',
    thinking:'{label} writing...',
    searching:'{label} searching materials...',
    searchResults:'Reference Materials',
    expand:'Expand',
    collapse:'Collapse',
    mindmap:'Mind Map',
    currentSearchSection:'searching for {section} related materials',
    relatedMaterials:'Related Materials',
    averageScore:'Average Score',
    credibilityScore:'Credibility Score',
    importanceScore:'Importance Score',
    newsApiTitle:'Related News',
    confirmStop:'Are you sure you want to stop the report generation? We will generate the report asynchronously in the background',
    confirmBack:'Are you sure you want to return to the home page? We will generate the report asynchronously in the background',
    warning:'Warning',
    relevanceScore:'Relevance',
    agentMarket: 'Agent Market',
    mainMenu:'Main Menu',
    settingMenu:'Setting Menu',
    networkFast:'Network Fast',
    networkMedium:'Network Medium',
    networkSlow:'Network Slow',
    networkOffline:'Network Offline',
    networkUnknown:'Network Unknown',
    specific_ai_analysis:'SpecificAI Analysis',
    nextStepInfo:'Next Step Information',
    searchPlaceholder:'Search...',
    clearInput:'Clear input',
    selectAll:'Select All',
    deselectAll:'Deselect All',
    loading:'Loading...',
    networkSpeed:'Network Speed',
    networkGood:'Good',
    networkPoor:'Poor',
  },
  setting: {
    backBtn: 'Back',
    menuTitle: 'User Center',
    peopleManage: 'Member Management',
    subscriptionInfo: 'Subscription Information',
    personalInfo: 'Personal Information',
    companyInfo: 'Company Information',
    plansTitle: 'Subscription Plan',
    userInfo: {
      account: 'Account',
      password: 'Password',
      changePassword: 'Change Password',
      email: 'Email',
      oldPassword: 'Old Password',
      newPassword: 'New Password',
      loginAccount: 'Login Account',
      language: 'Language',
    },
    reset: {
      pwdRequired: {
        number: 'Password must contain numbers',
        lowerCaseAndUpperCase:
          'Password must contain uppercase and lowercase letters',
        str: 'Password length must be greater than or equal to 8',
      },
    },
    memberMgr: {
      invite: 'Copy Invite Link',
      remove: 'Delete',
      removeUser: 'Are you sure you want to delete member {name}?',
      batchRemoveUser: 'Are you sure you want to delete {name}, {count} members?',
      userName: 'User Name',
      mySelf: 'Myself',
      email: 'Email',
      createdAt: 'Created At',
      action: 'Action',
      role: 'Role',
      confirmChangeRole: 'Are you sure you want to change the role of {name} to {role}?',
      changeRole: 'Change Role',
      willDelete: 'You are about to delete the following members, <span style="font-weight: 600;font-size: 20px;color:red;">{count}</span> members',
      placeholder: {
        search: 'Search members',
      },
      msgboxRemove: {
        title: 'Delete member',
      },
      roleType:{
        admin:'Admin',
        member:'Member'
      }
    },
    companyMgr: {
      title: 'Company Information',
      name: 'Name',
      role: 'Role',
      createdAt: 'Created At',
      admin: 'Admin',
      user: 'Company Member',
      creator: 'Company Creator',
    },
    plans: {
      title: 'Subscription Plan',
      billingPeriod: {
        monthlyType: 'Monthly',
        annuallyType: 'Annually',
        monthly: 'mo',
        annually: 'yr',
      },
      free: 'Free Plan',
      pro: 'Pro Plan',
      business: 'Business Plan',
      upgrade: 'Upgrade',
      currentPlanInfo: 'You are currently on the {plan} plan, if you want to upgrade to enjoy more features, please:',
      currentPlan: 'Current Plan',
      contactUs: 'Contact Us',
    },
    news:{
      bottom:'is the bottom',
      update:'Latest News'
    },
    nav:{
      product:'Product',
      price:'Price',
      team:'Team'
    },
    home:{
      introduction:'Ready to start?',
      price:{
        introduction:'Choose the right plan for you'
      },
      team:{
        introduction:'Our Team'
      }
    }
  },
  required:{
    input:'Please enter {label}'
  },
  formatReport:{
    threadLoading:'AI is writing in multi-thread mode, please wait patiently...',
    filterLoading:'Filtering {count} materials, please wait patiently...',
    chartTitle:'Chart Analysis',
    timelineTitle:'Public Opinion Timeline',
    thinkingSummary:'Summarizing and refining content...',
    exportMindMap:'Export Mind Map',
    searchSource:'Searching information from internal {source}',
    citedReferences:'{count} cited references',
    unCitedReferences:'{count} uncited references',
    viewResults:'Search {count} materials',
    loadingStep:'Searching {section} related materials',
    rateLoading:'Scoring {count} materials based on relevance and importance',
    flowChartTip:'How is this report generated?',
    legalChartTitle:'Legal Risk Chart Analysis',
    legalTimelineTitle:'Legal Risk Timeline',
    legalTimeline:{
      tooltipTime:'Case Occurrence Month',
      tooltipContent:'Number of Legal Disputes',
      tooltipDetail:'Detailed Information'
    },
    legal:{
      text:'Analyzing {key} related case information...'
    },
    newsApi:{
      text:'Searching related news...',
      doneText:'Search {count} related news'
    },
    sidebar:{
      title:'My Standard Report',
      filterByCurrentLang:'Filter out reports that are not in the current system language'
    }
  },
  processFlow:{
    title:'Report Generation Process'
  },
  charts:{
    worldCloud:{
      title:'Keyword Cloud Chart'
    }
  },
  customerService:{
    title:'Customer Service',
    inputPlaceholder:'Please enter your message',
    welcomeMessage:'Hello, I am the customer service of Specific AI. How can I help you?'
  },
  landing: {
    navigation: {
      login: 'Login',
      register: 'Free Sign Up',
      features: 'Features',
      clients: 'Clients',
      pricing: 'Pricing',
      contact: 'Contact Us'
    },
    magicText: {
      description: 'SpecificAI focuses on real-time collection, structured organization, and intelligent analysis of global massive information, helping Chinese enterprises expand overseas efficiently, seize global opportunities, gain market insights, and achieve sustainable international growth.',
      titles: {
        collection: 'Intelligent Collection of Massive Information',
        coverage: 'Real-time Global Data Coverage',
        organization: 'Multi-dimensional Structured Organization',
        monitoring: 'Overseas Risk Monitoring & Early Warning',
        insights: 'Global Market Trend Insights',
        analysis: 'AI-driven Decision Analysis',
        platform: 'One-stop Overseas Intelligence Platform'
      }
    },
    hero: {
      title: 'Global Opportunities',
      titleAccent: 'Intelligent Decisions',
      subtitle: 'Build a knowledge engine covering {countries} countries, use {ai} to analyze global business data, and provide precise overseas expansion strategies for Chinese enterprises',
      cta: {
        primary: 'Try for Free',
        secondary: 'Learn More'
      }
    },
    stats: {
      countries: 'Countries Covered',
      languages: 'Languages Supported',
      dailyAnalysis: 'Daily Analysis',
      accuracy: 'Decision Accuracy'
    },
    features: {
      badge: 'Leading Features',
      title: 'Comprehensive Intelligent Overseas Expansion Decision Support',
      subtitle: 'Our platform combines big data and AI technology to provide comprehensive intelligent decision support for Chinese enterprises going global',
      items: {
        strategy: {
          title: 'Overseas Expansion Strategy Support',
          description: 'Built a "Country Knowledge Engine" covering 100+ countries, which can generate high-quality overseas expansion guides on demand to help enterprises formulate precise strategies.'
        },
        insights: {
          title: 'Global Trend Insights & Business Radar',
          description: 'The system analyzes global 50+ countries, 20+ languages, and over 10,000 overseas information daily, using AI to filter trend changes, policy signals, and potential opportunities that are substantive for Chinese enterprises.'
        },
        bidding: {
          title: 'Overseas Bidding Intelligent Matching',
          description: 'Intelligently match enterprise capabilities with overseas bidding requirements, precisely push the most matching business opportunities, and improve bidding success rates.'
        },
        projects: {
          title: 'Global Major Project Tracking',
          description: 'Real-time monitoring of global major infrastructure and commercial projects, providing complete project lifecycle tracking to help enterprises seize opportunities.'
        },
        monitoring: {
          title: 'Customer & Competitor Risk Monitoring',
          description: 'Comprehensive monitoring of overseas customers and competitor dynamics, timely risk warnings, and providing countermeasure strategy recommendations.'
        }
      }
    },
    contact: {
      badge: 'Contact Us',
      title: 'Ready to Start Your Intelligent Global Journey?',
      subtitle: 'Leave your contact information, our team will get in touch with you as soon as possible',
      emailPlaceholder: 'Your email address',
      cta: 'Get Demo',
      privacy: 'We respect your privacy and will never share your information',
      successMessage: 'Thank you for your subscription! We will contact you soon.',
      emailRequired: 'Please enter your email address'
    },
    footer: {
      sections: {
        product: {
          title: 'Product',
          features: 'Features',
          pricing: 'Pricing',
          tutorials: 'Tutorials',
          api: 'API Docs'
        },
        company: {
          title: 'Company',
          about: 'About Us',
          clients: 'Clients',
          careers: 'Join Us',
          contact: 'Contact Us'
        },
        resources: {
          title: 'Resources',
          guides: 'Global Guides',
          reports: 'Industry Reports',
          cases: 'Success Stories',
          blog: 'Tech Blog'
        },
        legal: {
          title: 'Legal',
          privacy: 'Privacy Policy',
          terms: 'Terms of Service',
          data: 'Data Processing',
          cookies: 'Cookie Policy'
        }
      },
      copyright: '© {year} Go Global. All rights reserved.',
      realTimeData: 'Real-time Data'
    }
  },
  procurement: {
    title: 'Procurement & Bidding Information',
    pagination: {
      layout: 'total, sizes, prev, pager, next'
    },
    detail: {
      title: 'Project Details',
      basicInfo: 'Basic Information',
      contactInfo: 'Contact Information',
      timeInfo: 'Time Information',
      procurementInfo: 'Procurement Details',
      supplierInfo: 'Supplier Requirements'
    },
    messages: {
      loadError: 'Failed to load data, please try again later',
      exportSuccess: 'Export successful'
    },
    fields: {
      name: 'Project Name',
      country: 'Country',
      category: 'Category',
      detail: 'Details',
      agency: 'Procurement Agency',
      contact_person: 'Contact Person',
      contact_phone: 'Contact Phone',
      contact_email: 'Contact Email',
      address: 'Address',
      release_date: 'Release Date',
      final_submission_date: 'Submission Deadline',
      procurement_type: 'Procurement Type',
      procurement_content: 'Procurement Content',
      procurement_industry: 'Industry',
      supplier_type: 'Supplier Type',
      supplier_qualifications: 'Supplier Qualifications',
      other_supplier_requirements: 'Other Requirements',
      aiScore: 'AI Score'
    },
    filters: {
      countryPlaceholder: 'Select Country',
      categoryPlaceholder: 'Select Category',
      industryPlaceholder: 'Select Industry',
      servicePlaceholder: 'Select Service'
    },
    options: {
      industry: {
        it: 'Information Technology',
        construction: 'Construction Engineering',
        medical: 'Healthcare',
        education: 'Education & Training',
        energy: 'Energy & Environment'
      },
      service: {
        consulting: 'Nuclear Energy',
        engineering: 'Engineering Services',
        maintenance: 'Maintenance Services',
        training: 'Training Services'
      }
    },
    actions: {
      export: 'Export Data'
    },
    settings: {
      columnTitle: 'Column Settings',
      reset: 'Reset'
    },
    aiScore: {
      credibility: 'Credibility',
      importance: 'Importance',
      authority: 'Authority'
    }
  },
  common: {
    view_details:'view detail',
    all: 'All',
    success:'Success',
    no_report:'No Report',
    newest_first:'Newest First',
    oldest_first:'Oldest First',

    view:'View',
    delete:'Delete',
    edit:'Edit',
    refresh:'Refresh',
    batch_delete:'Batch Delete',
    batch_view:'Batch View',
    batch_edit:'Batch Edit',

    summary:'Summary',
    items_selected:'Selected {count} items',
    search:'Search',
    filter_by_type:'Filter by type',

    batch_delete_confirm:'Are you sure you want to delete the following {count} tasks?',
    batch_delete_success:'Delete successfully',
    items_not_found:'{count} items not found',
    confirm:'Confirm',
    cancel:'Cancel',

    warning:'Warning',

    search_placeholder:'Search {label}',

    filter_by_name:'Filter by name',

    actions:'Actions',

    clear:'Clear',

    delete_success:'Delete successfully',

    filter_by_date:'Filter by date',

    today:'Today',
    yesterday:'Yesterday',
    this_week:'This Week',

    filter_by_agent:'Filter by agent',

    close:'Close',

    view_more:'View More',

    export_excel:'Export Excel',

    generate_report:'Generate Report',

    risk_monitor:'Risk Monitor',

    enable_risk_monitor_tip:'Risk monitoring class employees are working or not yet started, if you need to start, please click the button below',

    enable_now:'Enable Now',

    load_more:'Load More',

    back:'Back',

    risk_normal: 'No Risk',
    risk_warning: 'Warning',
    risk_danger: 'Has Risk',
    agent_status_unknown: 'Status Unknown',

    no_reports_desc: 'There are currently no reports or tasks. Start by assigning a new task!',
    assign_new_task: 'Assign New Task',
    date_today: 'Today',
    date_yesterday: 'Yesterday',
    all_report_types: 'All Report Types',
    all_agents: 'All Agents',
    view_all_reports: 'View all reports',
    automation: 'Automation',
    agent_status_running: 'Running',
    agent_status_pending: 'Pending',
    agent_status_idle: 'Idle',
    agent_status_completed: 'Completed',
    agent_status_success: 'Success',
    agent_status_error: 'Error',
    no_active_task: 'No active task',
    completion_degree: 'Completion',
    step_status_pending: 'Pending',
    step_status_processing: 'Processing',
    step_status_completed: 'Completed',
    step_status_error: 'Error',
    report_error_message: 'Error generating report',
    retry: 'Retry',
    frequency_daily: 'Daily',
    frequency_weekly: 'Weekly',
    frequency_monthly: 'Monthly',
    frequency_days: 'Every {count} days',
    unknown_type: 'Unknown Type',
    status_unknown: 'Status Unknown',

    filter: 'Filter',
    country: 'Country',
    news_type: 'News Type',
    compact_view: 'Compact View',
    card_view: 'Card View',
    loading_data: 'Loading Data',
    show_less: 'Show Less',
    read_more: 'Read More',
    and_more: 'and {count} more',
    collapse: 'Collapse',
    no_matching_results: 'No matching results',
    try_different_filters: 'Try different filters',
    reset_filters: 'Reset Filters',
    no_strategic_insights: 'No strategic insights available',
    news_source: 'News Source',
    no_data_available: 'No data available',
    none: 'None',
    processing_data: 'Processing data',
    unknown_date: 'Unknown Date',
    invalid_date: 'Invalid Date',
    locale_code: 'en-US',
    
    // Missing keys
    no_permission: 'No Permission',
    running_agents_group: 'Running Tasks',
    
    // Permission related error messages
    permission: {
      no_standard_access: 'You do not have permission to access standard reports',
      no_notify_access: 'You do not have permission to access notification insights',
      no_binding_access: 'You do not have permission to access bidding analysis',
      no_legal_access: 'You do not have permission to access legal-related features'
    }
  },
  strategic: {
    opportunity_details: 'Opportunity Details',
    entry_strategy: 'Entry Strategy',
    key_opportunities: 'Key Opportunities',
    opportunity: 'Opportunity',
    all_key_opportunities: 'All Key Opportunities',
    timeline: 'Timeline',
    summary: 'Summary',
    recommend_reason: 'Recommendation Reason',
    source_link: 'Source Link',
    loading_detail: 'Loading details...',
    loading_detail_description: 'Fetching detailed information, please wait',
    loading_tip: 'This may take a few seconds',
    detail_load_error: 'Failed to load details',
    detail_load_error_description: 'Unable to retrieve detailed project information',
    auto_return_message: 'Will automatically return to list in 3 seconds',
    return_to_list: 'Return to List',
    detail_title_fallback: 'Project Details',
    navigation: {
      quick_nav_title: 'Quick Navigation ({count} items)',
      unknown_title: 'Unknown Project',
    },
    shortcuts: {
      label: 'Shortcuts',
      switch: 'switch',
      back: 'back',
    },
    unknown_title: 'Unknown Title',
  },
  tools:{
    KeywordExtractionTool:'Keyword Extraction',
    ExaSearchTool:'Related News Search',
    RelevanceCalculatorTool:'Relevance Calculator',
    ContentScorer:'Content Scorer',
    Obtaininglegaldata:'Obtaining Legal Data',
    LegalAnalysisTool:'Legal Analysis',
    TimeLineTool:'Format Time Line',
    MarkdownTableTool:'Data Formatting',
    ReportGeneration:'Report Generation',
    Generate_table:'Format Table',
    SummaryTool:'Summary Report',
    KeywordExtractionToolDesc:'Extract keywords from report content',
    ExaSearchToolDesc:'Search related news based on keywords',
    RelevanceCalculatorToolDesc:'Score the relevance of materials based on report content',
    ContentScorerDesc:'Score the content of materials based on report content',
    ObtaininglegaldataDesc:'Get relevant legal data based on report content',
    LegalAnalysisToolDesc:'Analyze legal data based on report content',
    SummaryToolDesc:'Summarize report content',
    TianYanChaSearchTool:'Search legal data',
    TianYanChaSearchToolDesc:'Search relevant legal data based on report content',
    LegalScorerTool:'Analyze legal data',
    LegalScorerToolDesc:'Analyze legal data based on report content',
    TimeLineToolDesc:'Format time line information',
    MarkdownTableToolDesc:'Data formatting',
    ReportGenerationDesc:'Report generation',
    Generate_tableDesc:'Format table',
    TwitterScraperTool:'Search public opinion',
    TwitterScraperToolDesc:'Search relevant public opinion based on report content',
    SentimentAnalysisTool:'Public opinion analysis',
    SentimentAnalysisToolDesc:'Analyze public opinion based on report content',
    SocialMediaAnalysisTool:'Social media analysis',
    SocialScorerTool:'Social media score',

    AnalyzeLegalData:'Analyze legal data',
    AnalyzeLegalDataDesc:'Analyze legal data based on report content',
    WebSearchTool: 'Web Search Tool',
    WebSearchToolDesc: 'Performs web searches for information',
    DataAnalysisTool: 'Data Analysis Tool',
    DataAnalysisToolDesc: 'Analyzes provided data',
    ReportGenerationTool: 'Report Generation Tool',
    ReportGenerationToolDesc: 'Generates the final report document',
  },
  agent: {
    filter:{
      all:'All',
      notify:'Risk Monitoring Intelligence Officer',
      normal:'Overseas Market Intelligence Officer',
      binding:'Business Opportunity Intelligence Officer'
    },
    search_company_placeholder: 'Search company name...',
    status_filter: 'Status',
    risk_filter: 'Risk',
    all_status: 'All Status',
    all_risk: 'All Risk',
    status_completed: 'Completed',
    status_processing: 'Processing',
    status_pending: 'Pending',
    status_error: 'Error',
    risk_danger: 'Has Risk',
    risk_normal: 'No Risk',
    clear_filters: 'Clear',
    clear_all_filters: 'Clear All Filters',
    filter_results: 'Showing {total} / {all} results',
    active_filters: '{count} filters',
    shortcuts_tip: 'Shortcuts: ⌘K Search, ESC Clear',
    no_search_results: 'No matching results found',
    try_different_keywords: 'Try different keywords or adjust filter conditions',
    export_legal_og_excel:'Export {title} legal original data',
    start:'Start',
    loading:'Working',
    init:'Initializing...',
    completed_agents:'Completed Work',
    agent_performance:'Work Performance',
    agent_task_list:'Work Task List',
    no_completed_agent:'No completed work',
    start_by_creating_agent:'Start creating your AI strategic advisor',
    explore_market:'Explore AI employee market',
    no_agents_yet:'No tasks are running',
    processing:'Processing...',
    upload_excel_file:'Drag excel file here, or',
    upload_excel_file_button:'Select file',
    upload_excel_file_tip:'Support .xlsx, .xls format Excel files',
    upload_excel:'Upload excel file',
    regular_tasks:'Regular Tasks',
    scheduled_tasks:'Scheduled Tasks',
    no_summary:'No Summary',
    isRunning:'{name} is working for you, please continue to assign tasks after the employee completes the work',

    special_agent:'Scheduled Task (Daily 8:00 AM Automatically Executed)',

    social_tool_name:'Analyzing {name} for public opinion',
    legal_tool_name:'Analyzing {name} for legal analysis',
    special_agent_summary:'Discover a list of companies that need attention',

    no_agent_name:'{name} AI employee',
    generate_detail_report:'Generate a detailed report about {name}',

    confirm_generate_report:'Are you sure you want to generate a detailed report about {name}?',
    companies:'Total {count} companies',
    notify_agents:'Alert notification expert',
    normal_agents:'Deep report writing expert',
    edit_completed_agent:'Edit completed task',

    batch_operations:'Batch operations',
    filter_by_type:'Filter by type',
    summary:'Summary',
    batch_delete_confirm:'Are you sure you want to delete the following {count} tasks?',
    batch_delete_success:'Delete successfully',
    operation_failed:'Operation failed',

    name:'Employee Name',
    duty:'Task',
    updated_at:'Updated At',
    delete_confirm:'Are you sure you want to delete the task?',
    regular_agents_tooltip:'Regular tasks completed',
    special_agents_tooltip:'Special tasks completed',

    regular_agents:'Regular report writing class',
    special_agents:'Alert notification class',

    filter_by_name:'Filter by employee type',

    social_summary:'Public opinion',
    legal_summary:'Legal',

    view_all_reports:'View all deep report results',

    view_all_risk_companies:'View All Risk Companies',
    view_all_completed_companies:'View All Completed Companies',
    view_details_desc: 'View complete report content',
    view_risk_companies_desc: 'Filter and display all risk companies',
    view_completed_companies_desc: 'Filter and display all completed companies',
    filter_description: {
      all_companies: 'Showing details of all {total} companies',
      current_showing: 'Currently showing',
      search_company: 'Currently showing details of {company} company',
      status_companies: 'Currently showing all companies with {status} status',
      risk_companies: 'Currently showing all high-risk alert companies',
      company_part: '{company} company',
      status_part: '{status} status',
      risk_part: 'high-risk status',
      separator: ', ',
      combined: 'Currently showing details of {filters}'
    },
    risk_description:'Risk description',

    risk_alert:'Risk alert',

    alert_from:'Alert source',

    all_risk_companies:'Historical all risk companies',

    risk_indicators: 'Risk Indicators',

    set_interval_time:'Set interval time',
    interval_description_days:'Set the time interval (days) for the agent to automatically execute. The agent will update relevant information at the set frequency.',
    select_days:'Select interval days',
    custom_days:'Custom days',
    enter_days:'Please enter days',
    will_run_every:'The agent will run every {days} days',
    update_time_success:'Update time interval successfully',
    update_time_error:'Update time interval failed',
    days:'days',
    monitor_alert_tag: 'Monitor',
    refresh_agent: 'Refresh Agent',
    monitor_alert_group_title: 'Monitoring & Alerts',
    monitor_alert_group_desc: 'View automated monitoring tasks',
    change_monitor_frequency: 'Change Monitor Frequency',
    unknown_role: 'Unknown Role',
    monitor_agent_name: 'Monitor Agent',
    monitor_agent_role: 'Monitoring Task',
    monitor_task_desc: 'Monitoring every {frequency}',
    report_agent_name: 'Report Agent',
    report_agent_role: 'Reporting Task',
    running_agent_name: 'Running Agent',
    special_insight_group_title: 'Business Opportunity Insight',
    special_insight_group_desc: 'View business opportunity insight tasks',
    additional_info: 'Additional Information',
    industry_label: 'Industry (Optional)',
    industry_placeholder: 'Please select industry',
    nation_label: 'Country/Region (Optional)',
    nation_placeholder: 'Please select country',
    keywords_label: 'Keywords (Optional)',
    keywords_placeholder: 'Please enter keywords and press Enter',
    news_type: 'News Type (Optional)',
    select_news_type: 'Please select news type',
    view_all_risk_companies_desc: 'View detailed information of all current risk alert companies',
    view_all_completed_companies_desc: 'View detailed information of all current completed companies',
  },
  component:{
    selectedCount:'Selected {count} items'
  },
  report: {
    monitor_agent_type: 'Monitoring Task Report',
    running_task_type: 'Running Task Report',
    standard_report_type: 'Standard Report',
    CompositeAgent: 'Composite Analysis Report',
    SocialMediaAgent: 'Social Media Report',
    LegalAnalysisAgent: 'Legal Analysis Report',
    FinancialAnalysisAgent: 'Financial Analysis Report',
    MarketAnalysisAgent: 'Market Analysis Report',
    CompetitorAnalysisAgent: 'Competitor Analysis Report',
    monitor_summary_unavailable: 'Monitor summary unavailable',
    report_summary_unavailable: 'Report summary unavailable',
    task_running_summary: 'Task running, summary generating...',
    composite_summary_unavailable: 'Composite summary unavailable',
    no_legal_summary: 'No legal summary',
    no_social_summary: 'No social media summary',
    legal_summary: 'Legal Summary',
    social_summary: 'Social Media Summary',
    monitor_details_unavailable: 'Monitor details unavailable',
    unknown_report_title: 'Unknown Report Title',
    monitor_report_title: 'Monitoring Report',
    running_report_title: 'Running Report',
    standard_report_title: 'Standard Report',
    processing_default_desc: 'Report processing...',
    monitor_task_default_desc: 'Monitoring task running...',
    running_task_default_desc: 'Task running...',
    completed_default_desc: 'Report completed',
    summary_unavailable: 'Summary unavailable',
  },
  biding: {
    // AI analysis related
    ai_analysis_title: 'AI Deep Analysis',
    ai_analysis_description: 'Perform intelligent analysis for "{title}"',
    ai_analysis_confirm_title: 'AI Deep Analysis Confirmation',
    ai_analysis_will_include: '<strong>AI analysis will include:</strong><br/>• Project feasibility assessment<br/>• Competitive analysis<br/>• Bidding strategy recommendations<br/>• Risk assessment',
    ai_analysis_dashboard_message: 'About to perform deep analysis for "{title}" in AI Dashboard',
    start_ai_analysis: 'Start AI Analysis',
    start_analysis: 'Start Analysis',
    cancel: 'Cancel',
    
    // Detail page loading states
    loading_detail: 'Loading details...',
    loading_detail_description: 'Fetching complete project information, please wait',
    loading_tip: 'This may take a few seconds',
    detail_load_error: 'Failed to load details',
    detail_load_error_description: 'Unable to retrieve detailed project information, possibly due to network issues or the project no longer exists',
    auto_return_message: 'Will automatically return to the list in 2 seconds',
    return_to_list: 'Return to List',
    
    // Original texts
    detail_title_fallback: 'Bidding Project Details',
    field_title: 'Project Title',
    field_country: 'Country',
    field_publication_date: 'Publication Date',
    field_submission_deadline: 'Submission Deadline',
    field_estimated_value: 'Estimated Value',
    field_detailed_description: 'Detailed Description',
    field_keywords: 'Keywords',
    field_org_name: 'Organization Name',
    field_org_website: 'Organization Website',
    field_contact_info_group_label: 'Contact Information',
    field_contact_person_prefix: 'Person:',
    field_contact_phone_prefix: 'Phone:',
    field_contact_email_prefix: 'Email:',
    field_contact_address_prefix: 'Address:',
    card_title_procurement_type: 'Procurement Type',
    field_procurement_type_type: 'Type',
    field_procurement_type_confidence: 'Confidence',
    card_title_classification_info: 'Classification Info',
    field_classification_first: 'First Level',
    field_classification_second: 'Second Level',
    field_classification_third: 'Third Level',
    field_confidence_prefix: 'Confidence:',
    card_title_tender_requirements: 'Tender Requirements',
    field_tender_req_category: 'Category',
    field_tender_req_industry: 'Industry Sector',
    field_tender_req_items: 'Items/Services',
    field_supplier_reqs: 'Supplier Requirements',
    procurement_type_filter: 'Filter by Procurement Type',
    score_range_filter: 'Filter by Score Range',
    table_header_title: 'Project Title',
    table_header_deadline: 'Deadline',
    table_header_score: 'Score',
    agent_working_message: 'Agent is fetching latest progress...',
    no_scoring_data_radar: 'No detailed scoring data available for radar chart',
    comprehensive_score_radar_label: 'Comprehensive Score',
    original_score_radar_label: 'Original Score',
    selected_count: 'Selected {count} items',
    score: {
      client_authority: 'Client Authority',
      vendor_bias: 'Vendor Bias',
      project_amount: 'Project Amount',
      information_completeness: 'Info Completeness',
      future_collaboration: 'Future Collaboration'
    },
    filter_data: 'Filter Data',
    current_filters: 'Current Filters',
    clear_all_filters: 'Clear All Filters',
    remove_filter: 'Remove {name} filter',
    show_filters: 'Show Filter Conditions',
    hide_filters: 'Hide Filter Conditions',

    // Missing table related keys
    table_header_country: 'Country',
    table_header_recommend_score: 'Recommend Score',
    table_update_time: 'Update Time',
    table_header_action: 'Action',

    // Missing status related keys
    working: 'Working',
    expired: 'Expired',

    // Missing operation related keys
    batch_unlike: 'Batch Unlike',
    active_filters: 'Active Filters',

    // Missing message related keys
    no_items_selected: 'Please select items to operate',
    company_id_missing: 'Company ID is missing, cannot perform operation',
    batch_unlike_success: 'Batch unlike successful',
    batch_unlike_failed: 'Batch unlike failed',
    single_unlike_success: 'Unlike successful',
    single_unlike_failed: 'Unlike failed',

    // Confirmation related
    single_unlike_confirm: 'Are you sure you want to unlike this project?',
    batch_unlike_confirm_title: 'Batch Unlike Confirmation',
    batch_unlike_confirm_message: 'You are about to unlike {count} projects. This action cannot be undone. Do you want to continue?',
    batch_unlike_confirm_warning: '⚠️ Warning: After batch unlike, these projects will no longer appear in your watchlist',
    confirm_action: 'Confirm',
    cancel_action: 'Cancel',

    // Missing detail page related keys
    no_recommendation_data: 'No recommendation data available',
    tabs: {
      basic: 'Basic Info',
      organization: 'Organization',
      requirements: 'Requirements',
      recommendation: 'Recommendation',
      scoring: 'Scoring'
    },
    detail: {
      comprehensive_score_label: 'Comprehensive Score',
      recommendation_score_label: 'Recommendation Score'
    },
    recommendation: {
      overview: 'Recommendation Overview',
      score: 'Recommendation Score',
      field: 'Field',
      value: 'Value',
      score_label: 'Recommendation Score',
      update_time_label: 'Update Time',
      reason_title: 'Recommendation Reason'
    },
    navigation: {
      quick_nav_title: 'Quick Navigation ({count} items)',
      unknown_title: 'Unknown Project',
    },
    shortcuts: {
      label: 'Shortcuts',
      switch: 'switch',
      back: 'back',
    },
  }
}
// KeywordExtractionTool:'关键词提取',
// ExaSearchTool:'相关新闻定向检索',
// RelevanceCalculatorTool:'资料相关性评分',
// ContentScorer:'资料内容评分',
// Obtaininglegaldata:'获取相关法律数据',
// LegalAnalysisTool:'法律数据分析',
// TimeLineTool:'格式化时间线信息',
// MarkdownTableTool:'数据格式化',
// ReportGeneration:'报告生成'