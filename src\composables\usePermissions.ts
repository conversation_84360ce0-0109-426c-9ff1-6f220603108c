import { computed, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

// 权限配置类型
interface PermissionConfig {
  [key: string]: {
    check: () => boolean
    errorMessage?: string
  }
}

// 权限管理composable
export const usePermissions = () => {
  const userStore = useUserStore()
  const { t, locale } = useI18n()

  // 权限配置中心
  const permissions: PermissionConfig = {
    // API权限
    'api.fetchBidingData': {
      check: () => userStore.canAccessBusinessOpportunityFeature,
      errorMessage: t('common.permission.no_business_opportunity_access')
    },
    'api.fetchStrategicData': {
      check: () => userStore.canAccessStrategicFeature,
      errorMessage: t('common.permission.no_strategic_access')
    },
    'api.getStandardReports': {
      check: () => userStore.canAccessNormalFeature,
      errorMessage: t('common.permission.no_standard_access')
    },
    'api.getMonitorAgents': {
      check: () => userStore.canAccessNotifyFeature,
      errorMessage: t('common.permission.no_notify_access')
    },
    'api.executeRefreshAgent': {
      check: () => userStore.canAccessNotifyFeature,
      errorMessage: t('common.permission.no_notify_access')
    },
    'api.batchDeleteReports': {
      check: () => userStore.canAccessNotifyFeature,
      errorMessage: t('common.permission.no_notify_access')
    },
    'api.changeTime': {
      check: () => userStore.canAccessNotifyFeature,
      errorMessage: t('common.permission.no_notify_access')
    },
    
    // 功能权限
    'feature.notify_insights': {
      check: () => userStore.canAccessNotifyFeature
    },
    'feature.standard_feed': {
      check: () => userStore.canAccessNormalFeature
    },
    'feature.special_analysis': {
      check: () => userStore.canAccessSpecialAnalysisFeature
    },
    'feature.business_opportunity': {
      check: () => userStore.canAccessBusinessOpportunityFeature
    },
    'feature.strategic': {
      check: () => userStore.canAccessStrategicFeature
    },
    
    // 操作权限
    'operation.refresh_agent': {
      check: () => userStore.canAccessNotifyFeature
    },
    'operation.delete_agent': {
      check: () => userStore.canAccessNotifyFeature
    },
    'operation.change_time': {
      check: () => userStore.canAccessNotifyFeature
    },

    // 菜单权限 - 基于 menuConfig
    'menu.information': {
      check: () => hasMenuAccess('information')
    },
    'menu.dashboard': {
      check: () => hasMenuAccess('dashboard')
    },
    'menu.format_report': {
      check: () => hasMenuAccess('format_report')
    },
    'menu.daily_report': {
      check: () => hasMenuAccess('daily_report')
    },
    'menu.procurement': {
      check: () => hasMenuAccess('procurement')
    },
    'menu.chat_bot': {
      check: () => hasMenuAccess('chat_bot')
    },

    // 设置页面权限 - 基于用户类型
    'settings.manage_users': {
      check: () => Boolean(userStore.hasPermission('canManageUsers')),
      errorMessage: t('permission.no_user_management_access')
    },
    'settings.company_info': {
      check: () => Boolean(userStore.hasPermission('canAccessCompanyInfo')),
      errorMessage: t('permission.no_company_info_access')
    },
    'settings.personal_info': {
      check: () => true // 所有用户都可以访问个人信息
    },
    'settings.manage_billing': {
      check: () => Boolean(userStore.hasPermission('canManageBilling')),
      errorMessage: t('permission.no_billing_access')
    },
    'settings.access_plans': {
      check: () => Boolean(userStore.hasPermission('canAccessPlans')),
      errorMessage: t('permission.no_plans_access')
    },

    // Legal权限 - 基于语言设置
    'feature.legal': {
      check: () => locale.value !== 'english',
      errorMessage: t('permission.no_legal_access')
    }
  }

  // 菜单权限检查辅助函数
  const hasMenuAccess = (menuId: string): boolean => {
    try {
      const menuItem = userStore.menuConfig.find(menu => menu.id === menuId)
      return menuItem?.status ?? false
    } catch (error) {
      console.error(`Error checking menu access for ${menuId}:`, error)
      return false
    }
  }

  // 检查权限
  const hasPermission = (permissionKey: string): boolean => {
    const permission = permissions[permissionKey]
    return permission ? permission.check() : false
  }

  // 权限检查装饰器
  const withPermission = <T extends (...args: any[]) => any>(
    permissionKey: string,
    fn: T
  ): T => {
    return ((...args: any[]) => {
      if (!hasPermission(permissionKey)) {
        const permission = permissions[permissionKey]
        const message = permission?.errorMessage || t('common.no_permission')
        console.warn(`Permission denied: ${permissionKey}`)
        ElMessage.warning(message)
        return Promise.resolve()
      }
      return fn(...args)
    }) as T
  }

  // 权限守卫
  const guardPermission = (permissionKey: string): boolean => {
    if (!hasPermission(permissionKey)) {
      const permission = permissions[permissionKey]
      const message = permission?.errorMessage || t('common.no_permission')
      ElMessage.warning(message)
      return false
    }
    return true
  }

  // 获取可用的标签页
  const availableTabs = computed(() => {
    const tabs = []
    if (hasPermission('feature.standard_feed')) {
      tabs.push('standard_feed')
    }
    if (hasPermission('feature.notify_insights')) {
      tabs.push('notify_insights')
    }
    if (hasPermission('feature.special_analysis')) {
      tabs.push('special_analysis')
    }
    return tabs
  })

  // 获取默认标签页
  const defaultTab = computed(() => {
    const available = availableTabs.value
    return available[0] || 'standard_feed'
  })

  // 工作区标签页配置
  const workspaceTabsConfig = computed(() => [
    {
      key: 'standard_feed',
      label: t('agent.filter.normal'),
      permission: 'feature.standard_feed',
      default: true
    },
    {
      key: 'notify_insights',
      label: t('agent.filter.notify'),
      permission: 'feature.notify_insights'
    },
    {
      key: 'special_analysis',
      label: t('agent.filter.binding'),
      permission: 'feature.special_analysis'
    }
  ])

  // 权限检查快捷方法
  const canAccess = {
    notifyFeature: computed(() => hasPermission('feature.notify_insights')),
    normalFeature: computed(() => hasPermission('feature.standard_feed')),
    bindingFeature: computed(() => hasPermission('feature.special_analysis')),
    businessOpportunityFeature: computed(() => hasPermission('feature.business_opportunity')),
    strategicFeature: computed(() => hasPermission('feature.strategic')),
    specialAnalysisFeature: computed(() => hasPermission('feature.special_analysis')),
    refreshAgent: computed(() => hasPermission('operation.refresh_agent')),
    deleteAgent: computed(() => hasPermission('operation.delete_agent')),
    changeTime: computed(() => hasPermission('operation.change_time')),

    // 菜单访问权限
    informationMenu: computed(() => hasPermission('menu.information')),
    dashboardMenu: computed(() => hasPermission('menu.dashboard')),
    formatReportMenu: computed(() => hasPermission('menu.format_report')),
    dailyReportMenu: computed(() => hasPermission('menu.daily_report')),
    procurementMenu: computed(() => hasPermission('menu.procurement')),
    chatBotMenu: computed(() => hasPermission('menu.chat_bot')),

    // 设置页面权限
    manageUsers: computed(() => hasPermission('settings.manage_users')),
    companyInfo: computed(() => hasPermission('settings.company_info')),
    personalInfo: computed(() => hasPermission('settings.personal_info')),
    manageBilling: computed(() => hasPermission('settings.manage_billing')),
    accessPlans: computed(() => hasPermission('settings.access_plans')),

    // Legal权限
    legalFeature: computed(() => hasPermission('feature.legal'))
  }

  // 批量权限检查
  const hasAnyPermission = (...permissionKeys: string[]): boolean => {
    return permissionKeys.some(key => hasPermission(key))
  }

  const hasAllPermissions = (...permissionKeys: string[]): boolean => {
    return permissionKeys.every(key => hasPermission(key))
  }

  // 菜单配置相关方法
  const getAvailableMenuItems = (menuItems: any[]) => {
    return menuItems.filter(item => {
      // 检查父菜单权限
      const parentEnabled = hasMenuAccess(item.menuId)

      // 如果有子菜单，检查是否有任何子菜单可用
      if (item.children) {
        const hasEnabledChild = item.children.some((child: any) =>
          hasMenuAccess(child.menuId)
        )
        return parentEnabled || hasEnabledChild
      }

      return parentEnabled
    })
  }

  // 设置菜单项配置
  const getSettingsMenuItems = () => {
    const items = [
      {
        title: t('setting.peopleManage'),
        url: '/settings',
        permission: 'settings.manage_users',
      },
      {
        title: t('setting.companyInfo'),
        url: '/settings/company-info',
        permission: 'settings.company_info',
      },
      {
        title: t('setting.personalInfo'),
        url: '/settings/personal-info',
        permission: 'settings.personal_info',
      },
    ]

    return items.filter(item => hasPermission(item.permission))
  }

  return {
    hasPermission,
    withPermission,
    guardPermission,
    availableTabs,
    defaultTab,
    hasAnyPermission,
    hasAllPermissions,
    permissions,
    workspaceTabsConfig,
    canAccess,
    hasMenuAccess,
    getAvailableMenuItems,
    getSettingsMenuItems
  }
} 