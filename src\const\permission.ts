import type { AccessParams } from 'types'
import { UserType } from '@/stores/user'

// 让当前权限判断失败，并尝试使用后备权限，fallback 数组中的权限会按顺序尝试，直到找到第一个合适的权限为止。
const DISABLED_PERMISSION = '__disabled__'

// 基于用户类型的权限
export const companyAdminAccess: AccessParams = {
  userTypes: [UserType?.CompanyAdmin ?? 2]
} as const

export const memberAccess: AccessParams = {
  userTypes: [UserType?.CompanyUser ?? 1]
} as const

export const organizationOwner: AccessParams = {
  roles: ['organizationOwner'],
  userTypes: [UserType?.CompanyAdmin ?? 2]
} as const

export const organizationAdmin: AccessParams = {
  roles: ['organizationAdmin'],
  userTypes: [UserType?.CompanyAdmin ?? 2],
  fallback: [organizationOwner]
} as const

export const settingPage: AccessParams = {
  permissions: [DISABLED_PERMISSION],
  fallback: [organizationAdmin]
} as const

export const statisticsConfigPage: AccessParams = {
  permissions: [DISABLED_PERMISSION],
  // #if BUILD_REG === 'CN'
  fallback: [organizationAdmin]
  // #endif
} as const

// PDF相关权限定义
export const PDF_PERMISSIONS = {
  PDF_SETTING: 'pdf_setting',
  PDF_REVIEW: 'pdf_review',
  PDF_DOWNLOAD: 'pdf_download',
} as const

export type PdfPermissionType = typeof PDF_PERMISSIONS[keyof typeof PDF_PERMISSIONS]

// 权限映射到用户类型
export const PERMISSION_USER_TYPE_MAP: Record<PdfPermissionType, UserType[]> = {
  'pdf_setting': [UserType.CompanyAdmin, UserType.CompanyCreator],
  'pdf_review': [UserType.CompanyUser, UserType.CompanyAdmin, UserType.CompanyCreator],
  'pdf_download': [UserType.CompanyAdmin, UserType.CompanyCreator],
} as const

