import { createResourceStore } from './createResourceStore'
import type { UserPermission, UserRole } from './user'
import type { User } from '@/types/login'
export const useUserRoleResourceStore = createResourceStore<UserRole[]>('user/role', {
  parse: (data) => data?.filter((role) => role.enabled === 1).map((role) => role.roleCode) || [],
})

export const useUserPermissionsResourceStore = createResourceStore<UserPermission[]>('user/permissions', {
  parse: (data) => data?.map((permission) => permission.code) || [],
})

// {pdf_setting: true, pdf_review: true, pdf_download: true} => ['pdf_setting', 'pdf_review', 'pdf_download']
// {pdf_setting: false, pdf_review: true, pdf_download: false} => ['pdf_review']
export const useUserCompanyConfigResourceStore = createResourceStore<User['company']['config']['permissions']>('plan/permissions', {
  parse: (data) => Object.keys(data || {}).filter((key) => data?.[key as keyof typeof data]),
})
