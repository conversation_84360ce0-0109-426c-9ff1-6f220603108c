<template>
  <div :class="$style.inputWrapper">
    <div 
      :class="[
        $style.inputContainer,
        { [$style.isDisabled]: disabled }
      ]">
      <label :class="[$style.inlineLabel, { [$style.labelDisabled]: disabled }]">
        {{ label }}
      </label>
      <div class="relative flex-1 group">
        <input
          :type="showPassword ? 'text' : type"
          :value="modelValue"
          :disabled="disabled"
          @input="onInput"
          @blur="onBlur"
          :placeholder="placeholder"
          :class="[$style.input, { [$style.error]: showError }]"/>
        <Icon
          v-if="type === 'password'"
          :icon="showPassword ? 'mdi:eye-off' : 'mdi:eye'"
          :class="[
            'absolute right-0 top-1/2 -translate-y-1/2 w-5 h-5 cursor-pointer transition-all duration-300',
            'opacity-0 invisible group-hover:opacity-100 group-hover:visible',
            showPassword ? 'opacity-100 visible' : '',
            disabled ? 'text-gray-300' : 'text-gray-400 hover:text-gray-600'
          ]"
          @click="togglePassword"/>
      </div>
    </div>
    <div v-if="showError" :class="$style.errorMessage">{{ errorMessage }}</div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import { useDebounceFn } from '@vueuse/core'

interface Props {
  modelValue: string;
  placeholder?: string;
  type?: string;
  required?: boolean;
  validator?: (value: string) => string | true;
  validateOnBlur?: boolean;
  label?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  required: false,
  validateOnBlur: true,
  label: '',
  disabled: false,
})

const emit = defineEmits([
  'update:modelValue',
  'validation-error',
  'validation-success',
])

const touched = ref(false)
const errorMessage = ref('')
const showPassword = ref(false)

const showError = computed(() => touched.value && errorMessage.value !== '')
const { t } = useI18n()

const validate = (value: string): boolean => {
  if (props.required && !value) {
    errorMessage.value = t('login.require')
    emit('validation-error', errorMessage.value)
    return false
  }

  if (props.validator && value) {
    const validationResult = props.validator(value)
    if (validationResult !== true) {
      errorMessage.value = validationResult
      emit('validation-error', errorMessage.value)
      return false
    }
  }

  errorMessage.value = ''
  emit('validation-success')
  return true
}

const onInput = (e: Event) => {
  const value = (e.target as HTMLInputElement).value
  emit('update:modelValue', value)
  
  touched.value = true
  validate(value)
}

const onBlur = () => {
  touched.value = true
  validate(props.modelValue)
}

const togglePassword = useDebounceFn(() => {
  if (props.disabled) {
    return
  }
  showPassword.value = !showPassword.value
}, 300)
</script>

<style lang="scss" module>
.inputWrapper {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.inputContainer {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E5E7EB;
  transition: all 0.3s ease;
  padding-left: 4px;
  
  &:hover {
    border-bottom-color: #0045B5;
  }

  &:focus-within {
    border-bottom-color: #0045B5;
  }

  &.isDisabled {
    background-color: #f5f7fa;
    border-bottom-color: #dcdfe6;
    cursor: not-allowed;
    opacity: 0.7;
    
    &:hover {
      border-bottom-color: #dcdfe6;
    }
  }
}

.inlineLabel {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  line-height: 40px;
  width: 70px;
  text-align: left;
  shrink: 0;
  margin-right: 5px;
  
  overflow: hidden;
  text-overflow: ellipsis;
}

.input {
  border-bottom: none;
  width: 100%;
  height: 40px;
  padding: 8px 24px 8px 0;
  font-size: 14px;
  color: #333;
  background-color: transparent;

  &:focus {
    outline: none;
  }

  &:disabled {
    background-color: transparent;
    color: #a8abb2;
    cursor: not-allowed;
    
    &::placeholder {
      color: #c0c4cc;
    }
  }
}

.errorMessage {
  position: absolute;
  left: 0;
  bottom: -20px;
  font-size: 12px;
  color: #f56c6c;
}

.labelDisabled {
  color: #a8abb2;
  cursor: not-allowed;
}
</style>
