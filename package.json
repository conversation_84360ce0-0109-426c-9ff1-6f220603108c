{"name": "specific-ai", "version": "0.0.0", "private": true, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "prepare": "husky", "dev": "cross-env BUILD_REG=CN vite", "dev:intl": "cross-env BUILD_REG=INTL vite", "dev:private": "cross-env BUILD_REG=CN PRIVATE=true vite", "build": "cross-env BUILD_REG=CN run-p type-check \"build-only {@}\" --", "build:intl": "cross-env BUILD_REG=INTL run-p type-check \"build-only {@}\" --", "build:private": "cross-env BUILD_REG=CN PRIVATE=true run-p \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest run --coverage", "test:ui": "vitest --ui", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "preCommit": "lint-staged"}, "dependencies": {"@amplitude/analytics-browser": "2.6.2", "@antv/g6": "4.8.24", "@emotion/css": "11.11.2", "@iconify/vue": "4.2.0", "@sentry/vue": "8.2.1", "@specificlab/ui": "0.0.31", "@stripe/stripe-js": "3.5.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/vue-table": "^8.21.3", "@vueuse/core": "10.9.0", "@vueuse/integrations": "10.9.0", "@vueuse/shared": "10.9.0", "axios": "1.6.8", "broadcast-channel": "7.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.3", "date-fns-tz": "3.1.3", "dayjs": "1.11.11", "decimal.js": "10.4.3", "echarts": "5.5.0", "element-plus": "2.9.0", "html-to-pdfmake": "2.5.13", "lodash-es": "4.17.21", "lucide-vue-next": "^0.501.0", "markdown-it": "14.1.0", "markdown-it-container": "^4.0.0", "markdown-it-external-links": "0.0.6", "markdown-it-multimd-table": "^4.2.3", "mermaid": "^11.4.1", "mind-elixir": "^4.3.3", "mitt": "3.0.1", "pinia": "2.1.7", "pinyin-match": "1.2.5", "qs": "6.12.1", "reka-ui": "^2.2.0", "socket.io-client": "4.8.1", "swiper": "11.1.14", "tailwind-merge": "^3.2.0", "tippy.js": "^6.3.7", "tw-animate-css": "^1.2.5", "universal-cookie": "6.1.3", "uuid": "10.0.0", "vaul-vue": "0.4.1", "vue": "3.4.23", "vue-i18n": "9.13.1", "vue-router": "4.3.0", "vue-sonner": "^1.3.2", "vue-virtual-scroller": "2.0.0-beta.8", "vxe-table": "4.6.3"}, "devDependencies": {"@amplitude/analytics-types": "^2.5.0", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@rollup/plugin-replace": "^5.0.5", "@rushstack/eslint-patch": "^1.8.0", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.13", "@tsconfig/node20": "^20.1.4", "@types/echarts": "^4.9.22", "@types/jsdom": "^21.1.6", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.1", "@types/markdown-it-external-links": "^0.0.4", "@types/node": "^20.12.5", "@types/pdfmake": "^0.2.9", "@types/qs": "^6.9.15", "@types/socket.io-client": "^1.4.36", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vitest/coverage-v8": "^2.1.4", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.5.1", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "fast-glob": "^3.3.2", "husky": "^9.0.11", "jsdom": "^24.0.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "npm-run-all2": "^6.1.2", "postcss": "^8.4.38", "rollup-plugin-external-globals": "^0.12.0", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.75.0", "tailwindcss": "^4.1.4", "terser": "^5.36.0", "typescript": "~5.4.0", "unplugin-preprocessor-directives": "^1.0.3", "unplugin-vue-router": "^0.8.6", "vite": "^5.2.8", "vite-plugin-cdn2": "^1.1.0", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-env-types": "^0.1.4", "vite-plugin-mock": "^3.0.2", "vite-plugin-spa-loading": "^1.2.2", "vite-plugin-vue-devtools": "^7.0.25", "vite-plugin-vue-layouts": "^0.11.0", "vitest": "^2.1.4", "vue-tsc": "^2.0.11"}, "engines": {"node": ">=18", "pnpm": ">=8.14"}, "gitHooks": {"pre-commit": "lint-staged"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": ["eslint --fix --ignore-path .gitignore"]}, "pnpm": {"patchedDependencies": {"echarts@5.5.0": "patches/<EMAIL>", "@antv/g6@4.8.24": "patches/@<EMAIL>"}, "overrides": {"xe-utils": "3.5.25", "dayjs": "1.11.11"}}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.17.0"}, "packageManager": "pnpm@10.8.1+sha512.c50088ba998c67b8ca8c99df8a5e02fd2ae2e2b29aaf238feaa9e124248d3f48f9fb6db2424949ff901cffbb5e0f0cc1ad6aedb602cd29450751d11c35023677"}