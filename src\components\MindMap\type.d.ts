export interface MindElixirMethods {
  init(this: any, data: unknown): Error | undefined;
  exportSvg: (
    this: any,
    noForeignObject?: boolean,
    injectCss?: string | undefined
  ) => Blob;
  exportPng: (
    this: any,
    noForeignObject?: boolean,
    injectCss?: string | undefined
  ) => Promise<Blob | null>;
  createSummary: (this: any) => void;
  removeSummary: (this: any, id: string) => void;
  selectSummary: (this: any, el: any) => void;
  unselectSummary: (this: any) => void;
  renderSummary: (this: any) => void;
  editSummary: (this: any, el: any) => void;
  renderArrow(this: any): void;
  editArrowLabel(this: any, el: any): void;
  tidyArrow(this: any): void;
  createArrow: (this: any, from: any, to: any) => void;
  removeArrow: (this: any, linkSvg?: any) => void;
  selectArrow: (this: any, link: any) => void;
  unselectArrow: (this: any) => void;
  mainToSub: (this: any, tpc: any) => Promise<void>;
  reshapeNode: (this: any, tpc: any, patchData: any) => Promise<void>;
  insertSibling: (
    this: any,
    type: 'before' | 'after',
    el?: any,
    node?: any
  ) => Promise<void>;
  insertParent: (this: any, el?: any, node?: any) => Promise<void>;
  addChild: (this: any, el?: any, node?: any) => Promise<void>;
  copyNode: (this: any, node: any, to: any) => Promise<void>;
  copyNodes: (this: any, tpcs: any[], to: any) => Promise<void>;
  moveUpNode: (this: any, el?: any) => Promise<void>;
  moveDownNode: (this: any, el?: any) => Promise<void>;
  removeNode: (this: any, el?: any) => Promise<void>;
  removeNodes: (this: any, tpcs: any[]) => Promise<void>;
  moveNodeIn: (this: any, from: any[], to: any) => Promise<void>;
  moveNodeBefore: (this: any, from: any[], to: any) => Promise<void>;
  moveNodeAfter: (this: any, from: any[], to: any) => Promise<void>;
  beginEdit: (this: any, el?: any) => Promise<void>;
  setNodeTopic: (this: any, el: any, topic: string) => Promise<void>;
  selectNode: (
    this: any,
    targetElement: any,
    isNewNode?: boolean,
    e?: MouseEvent
  ) => void;
  unselectNode: (this: any) => void;
  selectNodes: (this: any, tpc: any[]) => void;
  unselectNodes: (this: any) => void;
  clearSelection: (this: any) => void;
  getDataString: (this: any) => string;
  getData: (this: any) => unknown;
  getDataMd: (this: any) => string;
  enableEdit: (this: any) => void;
  disableEdit: (this: any) => void;
  scale: (this: any, scaleVal: number) => void;
  toCenter: (this: any) => void;
  install: (this: any, plugin: (instance: any) => void) => void;
  focusNode: (this: any, el: any) => void;
  cancelFocus: (this: any) => void;
  initLeft: (this: any) => void;
  initRight: (this: any) => void;
  initSide: (this: any) => void;
  setLocale: (this: any, locale: string) => void;
  expandNode: (this: any, el: any, isExpand?: boolean) => void;
  refresh: (this: any, data?: unknown) => void;
  getObjById: (id: string, data: any) => any | null;
  generateNewObj: (this: any) => any;
  layout: (this: any) => void;
  linkDiv: (this: any, mainNode?: any) => void;
  editTopic: (this: any, el: any) => void;
  createWrapper: (
    this: any,
    nodeObj: any,
    omitChildren?: boolean
  ) => {
    grp: any;
    top: any;
    tpc: any;
  };
  createParent: (
    this: any,
    nodeObj: any
  ) => {
    p: any;
    tpc: any;
  };
  createChildren: (this: any, wrappers: any[]) => any;
  createTopic: (this: any, nodeObj: any) => any;
  findEle: (id: string, instance?: any) => any;
  changeTheme: (this: any, theme: any, shouldRefresh?: boolean) => void;
}
export interface MindMapInstance extends MindElixirMethods {
  disposable: Array<() => void>;
  isFocusMode: boolean;
  nodeDataBackup: NodeObj;
  mindElixirBox: HTMLElement;

  nodeData: NodeObj;
  arrows: Arrow[];
  summaries: Summary[];

  currentNode: Topic | null;
  currentNodes: Topic[] | null;
  currentSummary: SummarySvgGroup | null;
  currentArrow: CustomSvg | null;

  waitCopy: Topic[] | null;
  scaleVal: number;
  tempDirection: number | null;
  theme: Theme;
  userTheme?: Theme;
  direction: number;
  locale: Locale;
  draggable: boolean;
  editable: boolean;
  contextMenu: boolean;
  contextMenuOption: object;
  toolBar: boolean;
  keypress: boolean;
  mouseSelectionButton: 0 | 2;
  before: Before;
  newTopicName: string;
  allowUndo: boolean;
  overflowHidden: boolean;
  mainBranchStyle: number;
  subBranchStyle: number;
  generateMainBranch: (params: MainLineParams) => PathString;
  generateSubBranch: (params: SubLineParams) => PathString;

  container: HTMLElement;
  map: HTMLElement;
  root: HTMLElement;
  nodes: HTMLElement;
  lines: SVGElement;
  summarySvg: SVGElement;
  linkController: SVGElement;
  P2: HTMLElement;
  P3: HTMLElement;
  line1: SVGElement;
  line2: SVGElement;
  linkSvgGroup: SVGElement;
  /**
   * @internal
   */
  helper1?: LinkDragMoveHelperInstance;
  /**
   * @internal
   */
  helper2?: LinkDragMoveHelperInstance;

  bus: ReturnType<typeof Bus.create<EventMap>>;
  history: Operation[];
  undo: () => void;
  redo: () => void;

  selection: SelectionArea;
  selectionContainer?: string | HTMLElement;
}
type PathString = string;
/**
 * The MindElixir options
 *
 * @public
 */

export type Uid = string;

export type Left = 0;
export type Right = 1;

/**
 * MindElixir node object
 *
 * @public
 */
export type NodeObj = {
  topic: string;
  id: Uid;
  style?: {
    fontSize?: string;
    color?: string;
    background?: string;
    fontWeight?: string;
  };
  children?: NodeObj[];
  tags?: string[];
  icons?: string[];
  hyperLink?: string;
  expanded?: boolean;
  direction?: Left | Right;
  image?: {
    url: string;
    width: number;
    height: number;
    fit?: 'fill' | 'contain' | 'cover';
  };
  // main node specific properties
  branchColor?: string;
  // add programatically
  parent?: NodeObj; // root node has no parent!
  // TODO: checkbox
  // checkbox?: boolean | undefined
  dangerouslySetInnerHTML?: string;
};
export type Theme = {
  name: string;
  /**
   * Hint for developers to use the correct theme
   */
  type?: 'light' | 'dark';
  /**
   * Color palette for main branches
   */
  palette: string[];
  cssVar: Partial<{
    '--gap': string;
    '--main-color': string;
    '--main-bgcolor': string;
    '--color': string;
    '--bgcolor': string;
    '--selected': string;
    '--root-color': string;
    '--root-bgcolor': string;
    '--root-border-color': string;
    '--root-radius': string;
    '--main-radius': string;
    '--topic-padding': string;
    '--panel-color': string;
    '--panel-bgcolor': string;
    '--panel-border-color': string;
  }>;
};

export type Options = {
  el: string | HTMLElement;
  direction?: number;
  locale?: Locale;
  draggable?: boolean;
  editable?: boolean;
  contextMenu?: boolean;
  contextMenuOption?: any;
  toolBar?: boolean;
  keypress?: boolean;
  mouseSelectionButton?: 0 | 2;
  before?: Before;
  newTopicName?: string;
  allowUndo?: boolean;
  overflowHidden?: boolean;
  generateMainBranch?: (
    this: MindElixirInstance,
    params: MainLineParams
  ) => PathString;
  generateSubBranch?: (
    this: MindElixirInstance,
    params: SubLineParams
  ) => PathString;
  theme?: Theme;
  nodeMenu?: boolean;
  selectionContainer?: string | HTMLElement;
};

export interface MindMapNodeData {
  nodeData: NodeData;
}

interface NodeData {
  id: string;
  topic: string;
  root: boolean;
  children: Child[];
}

interface Child {
  topic: string;
  id: string;
}
