<template>
  <el-drawer
    v-model="visible"
    :title="t('processFlow.title')"
    :size="'50%'"
    :destroy-on-close="true"
    @open="initGraph">
    <ProcessFlowGraphContent :data="props.data" ref="graphRef"/>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useVModel } from '@vueuse/core'
import type { GraphData } from '@antv/g6'
import ProcessFlowGraphContent from './ProcessFlowGraphContent.vue'

interface Props {
  modelValue: boolean
  data: GraphData
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const { t } = useI18n()
const visible = useVModel(props, 'modelValue', emit)
const graphRef = ref<InstanceType<typeof ProcessFlowGraphContent>>()

const initGraph = () => {
  graphRef.value?.initGraph()
}
</script> 