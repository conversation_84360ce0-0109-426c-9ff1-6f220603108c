import { ref, watch } from 'vue'
import emitter from '@/utils/events'
import { ElMessage } from 'element-plus'
import { reloadWithRequest } from '@/hooks/useHttp'
import { getToken } from '@/hooks/useToken'

// Types
interface StreamOptions {
  url: string
  language: 'chinese' | 'english'
  timeout?: number
  retries?: number
  retryDelay?: number
}

interface StreamResponse {
  code: number | string
  msg?: string
  subMsg?: string
  data?: any
}

type ErrorCallback = (error: Error) => void

// Constants
const DEFAULT_TIMEOUT = 10 * 60 * 1000 // 10分钟超时
const MAX_RETRIES = 3
const RETRY_DELAY = 1000
const AUTH_ERROR_CODES = [601, 401, 402, 603]

function useHttpPostStream(
  options: StreamOptions,
  onDataReceived: (chunk: string) => void,
  onStreamFinished: () => void
) {
  const { 
    url, 
    language,
    timeout = DEFAULT_TIMEOUT, // 使用新的默认超时时间
    retries = MAX_RETRIES,
    retryDelay = RETRY_DELAY 
  } = options

  // State
  const output = ref('')
  const isLoading = ref(false)
  const isWaiting = ref(false)
  const error = ref<Error | null>(null)
  const errorCallbacks: ErrorCallback[] = []
  let controller: AbortController | null = null
  let timeoutId: NodeJS.Timeout | null = null

  const cleanup = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    if (controller) {
      controller.abort()
      controller = null
    }
  }

  const handleError = (e: unknown) => {
    const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred'
    const streamError = new Error(errorMessage)
    error.value = streamError
    errorCallbacks.forEach(callback => callback(streamError))
    // ElMessage.error(errorMessage)
  }

  async function processStream(response: Response, currentController: AbortController) {
    const reader = response.body!.getReader()

    try {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read()
        
        if (currentController.signal.aborted) {
          throw new Error('Stream has been aborted')
        }

        if (done) {
          onStreamFinished()
          break
        }

        const chunk = new TextDecoder('utf-8').decode(value)
        
        if (response.headers.get('content-type')?.includes('application/json')) {
          try {
            const res = JSON.parse(chunk)
            
            if (AUTH_ERROR_CODES.includes(Number(res.code))) {
              console.warn('Authentication token failed validation', url)
              emitter.emit('api:token-error', res)
              reloadWithRequest()
              throw new Error('Token validation failed')
            }

            if (res.code !== '200' && res.code !== 200) {
              throw new Error(res.subMsg || res.msg || 'Server error occurred')
            }
          } catch (parseError) {
            // 如果解析失败，视为普通文本处理
            console.warn('Failed to parse JSON chunk:', parseError)
          }
        }

        isWaiting.value = false
        output.value += chunk
        onDataReceived(chunk)
      }
    } finally {
      reader.releaseLock()
    }
  }

  async function startStreaming(data?: Record<string, any>, attemptCount = 0): Promise<void> {
    if (isLoading.value) {return}

    cleanup()
    controller = new AbortController()
    isLoading.value = true
    isWaiting.value = true
    error.value = null

    timeoutId = setTimeout(() => {
      if (controller) {
        controller.abort()
        handleError(new Error('Request timeout'))
      }
    }, timeout)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': getToken(),
          'Language': language,
        },
        body: JSON.stringify(data),
        signal: controller.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      if (response.body) {
        await processStream(response, controller)
      }
    } catch (e) {
      if (attemptCount < retries && !controller?.signal.aborted) {
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        return startStreaming(data, attemptCount + 1)
      }
      handleError(e)
    } finally {
      cleanup()
      isLoading.value = false
      isWaiting.value = false
    }
  }

  const abort = () => {
    if (isLoading.value) {
      cleanup()
      isLoading.value = false
      isWaiting.value = false
      error.value = null
      output.value = ''
      onStreamFinished()
    }
  }

  const onError = (callback: ErrorCallback) => {
    errorCallbacks.push(callback)
    return () => {
      const index = errorCallbacks.indexOf(callback)
      if (index > -1) {
        errorCallbacks.splice(index, 1)
      }
    }
  }

  watch(() => error.value, (newError) => {
    if (newError) {
      errorCallbacks.forEach(callback => callback(newError))
    }
  })

  const getController = () => controller

  return {
    startStreaming,
    abort,
    onError,
    getController,


    output,
    isLoading,
    isWaiting,
    error: error as Readonly<typeof error>
  }
}

export { useHttpPostStream }
