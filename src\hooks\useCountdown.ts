import { ref, shallowReadonly } from 'vue'
import { useIntervalFn, useEventListener } from '@vueuse/core'

export const useCountdown = (second: number, options?: {
  immediate?: boolean
}) => {
  const remaining = ref(second)
  let startTimestamp: number | undefined

  const checkAndStop = () => {
    if (remaining.value <= 0) {
      stop()
    }
  }
  const { resume, pause, isActive } = useIntervalFn(() => {
    remaining.value -= 1
    checkAndStop()
  }, 1000, { immediate: false })

  const stop = () => {
    pause()
    startTimestamp = undefined
  }
  const start = () => {
    remaining.value = second
    startTimestamp = Date.now()
    resume()
  }

  useEventListener(document, 'visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      pause()
      return
    }
    if (document.visibilityState === 'visible' && startTimestamp) {
      remaining.value = second - Math.floor((Date.now() - startTimestamp) / 1000)
      resume()
      checkAndStop()
    }
  })

  if (options?.immediate) {
    start()
  }

  return {
    start,
    stop,
    isActive,
    remaining: shallowReadonly(remaining),
  }
}
