import { defineStore } from 'pinia'
import { computed, getCurrentInstance, ref } from 'vue'

export const useKeepAliveRoutes = defineStore('keepAliveRoutes', () => {
  const _keepAliveNames = ref<Set<string>>(new Set())
  const keepAliveNames = computed(() => Array.from(_keepAliveNames.value))

  const current = getCurrentInstance()
  const addKeepAliveWithCurrent = () => {
    const name = current?.type.name
    if (name) {
      _keepAliveNames.value.add(name)
    }
  }

  const addKeepAlive = (name?: string) => {
    name && _keepAliveNames.value.add(name)
  }


  return {
    keepAliveNames,
    addKeepAliveWithCurrent,
    addKeepAlive,
  }
})
