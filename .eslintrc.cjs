/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
  root: true,
  'extends': [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
    'vue/multi-word-component-names': 0,
    'vue/html-closing-bracket-newline': ['error', {
      'singleline': 'never',
      'multiline': 'never'
    }],
    'vue/html-closing-bracket-spacing': ['error', {
      'startTag': 'never',
      'endTag': 'never',
      'selfClosingTag': 'never'
    }],
    'vue/html-indent': ['error', 2],
    'vue/max-attributes-per-line': ['error', {
      'singleline': 3,
      'multiline': 1
    }],
    'quotes': ['error', 'single'],
    'eqeqeq': 2,
    'no-else-return': ['error', { 'allowElseIf': false }],
    'comma-spacing': [2, { before: false, after: true }],
    'curly': [2, 'all'],
    'dot-notation': 2,
    'func-call-spacing': 1,
    'no-multi-spaces': 2,
    'space-infix-ops': 2,
    'object-curly-spacing': ['error', 'always'],
    'keyword-spacing': ['error', { before: true, after: true }],
    'no-multiple-empty-lines': 2,
    'indent': ['error', 2],
    'semi': ['error', 'never'],
    'no-useless-escape': 0,
  }
}
