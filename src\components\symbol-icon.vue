<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  size: {
    type: [Number, String],
    default: 16
  }
})

const getPx = (size: number | string) => size ? typeof size === 'string' ? size : `${size}px` : undefined


const style = computed(() => {
  return {
    fontSize: getPx(props.size),
    width: '1em',
    height: '1em',
    minWidth: '1em',
    minHeight: '1em',
  }
})
</script>

<template>
  <svg
    class="el-symbol-icon"
    aria-hidden="true"
    :style="style">
    <use :xlink:href="`#${icon}`"/>
  </svg>
</template>
