<!-- Generated by SpecStory -->

# 国际化中文文案匹配 (2025-05-29 17:49:25)

_**User**_

帮我将中写死的中文都使用 @modules 中的i18n国际化进行国际化文案匹配

---

_**Assistant**_



---

```thinking
用户希望我帮助将 landing.vue 文件中写死的中文文案使用国际化进行替换。我需要：

1. 首先查看 landing.vue 文件的内容，找出其中硬编码的中文文案
2. 查看 lang/modules 文件夹中的国际化文件，了解现有的国际化键值
3. 分析哪些中文文案可以直接使用现有的国际化键，哪些需要新增
4. 提供具体的替换方案

我需要按照Vue 3 + TypeScript的最佳实践，确保使用正确的国际化语法。
```

**Thinking**

我需要帮助您将 `landing.vue` 文件中的硬编码中文文案替换为国际化文案。让我先查看相关文件的内容，以便理解现有的国际化配置和需要替换的文案。

---

Read file: src/pages/landing.vue

---

我需要查看完整的文件内容来了解所有需要国际化的中文文案：

---

Read file: src/pages/landing.vue

---

让我继续查看完整的模板部分：

---

Read file: src/pages/landing.vue

---

让我继续查看剩余的模板部分：

---

Read file: src/pages/landing.vue

---

让我查看最后一小部分：

---

Read file: src/pages/landing.vue

---

现在让我查看国际化文件，了解现有的键值对：

---

Read file: src/lang/modules/zh-CN.ts

---

现在让我查看国际化文件的更多部分，特别是landing页面相关的内容：

---

<details>
            <summary>Grep search for "landing" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/code/formal_code/go-global-fd/typed-router.d.ts` | L28 | `'/landing': RouteRecordInfo<'/landing', '/landing', Record<never, never>, Record<never, never>>,` |
| `file:///d%3A/code/formal_code/go-global-fd/src/router/guards/auth.guard.0.ts` | L11 | `const redirectPath = to.meta.redirectIfUnauthorized as string || '/landing'` |

</details>

---

看起来国际化文件中还没有专门的landing页面配置。让我再查看一下国际化文件的完整结构：

---

Read file: src/lang/modules/zh-CN.ts

---

现在我已经理解了当前的情况。我需要给用户提供一个国际化改造方案。首先，我将向用户了解他们的具体需求：

---



---

好的，我现在将为您提供一个完整的国际化改造方案。

## **Thinking**

我已经分析了 `landing.vue` 文件中的所有中文文案，以及现有的国际化配置结构。根据分析，我需要：

1. **识别需要国际化的中文文案**：包括导航菜单、按钮文本、标题、描述文字、表单提示、页脚信息等
2. **在国际化文件中新增landing页面配置**：为landing页面创建专门的国际化配置分组
3. **修改Vue组件**：将硬编码的中文文案替换为 `t()` 函数调用

### 需要国际化的文案分析：

**导航相关：**
- 登录、免费注册、产品功能、客户案例、定价方案、联系我们

**Hero部分：**
- 全球商机、智能决策、免费试用、了解更多

**统计数据：**
- 覆盖国家、支持语种、每日分析资讯、决策准确率

**产品特性：**
- 领先功能、全方位智能出海决策支持、各个功能的标题和描述

**联系我们部分：**
- 联系我们、准备开始您的智能出海旅程？、获取演示等

**页脚：**
- 产品、公司、资源、法律等各个链接和版权信息

现在我将开始实施改造：

---

```diff
      welcomeMessage:'您好,我是Specific AI 出海易的客服，有什么可以帮您的吗？'
    },
+   landing: {
+     navigation: {
+       login: '登录',
+       register: '免费注册',
+       features: '产品功能',
+       clients: '客户案例',
+       pricing: '定价方案',
+       contact: '联系我们'
+     },
+     hero: {
+       title: '全球商机',
+       titleAccent: '智能决策',
+       subtitle: '构建覆盖{countries}国家的知识引擎，用{ai}分析全球商业数据，为中国企业提供精准出海战略',
+       cta: {
+         primary: '免费试用',
+         secondary: '了解更多'
+       }
+     },
+     stats: {
+       countries: '覆盖国家',
+       languages: '支持语种',
+       dailyAnalysis: '每日分析资讯',
+       accuracy: '决策准确率'
+     },
+     features: {
+       badge: '领先功能',
+       title: '全方位智能出海决策支持',
+       subtitle: '我们的平台结合大数据与AI技术，为中国企业出海提供全面的智能决策支持',
+       items: {
+         strategy: {
+           title: '出海战略决策支持',
+           description: '构建了覆盖100+国家的"国别知识引擎"，可按需定制生成高质量的出海指南，助力企业制定精准战略。'
+         },
+         insights: {
+           title: '全球趋势洞察与商机雷达',
+           description: '系统每日分析全球50+国家、20+语种、超万条海外资讯，通过AI筛选出对中国企业具有实质意义的趋势变化、政策信号和潜在机会。'
+         },
+         bidding: {
+           title: '海外招投标智能匹配',
+           description: '智能匹配企业能力与海外招投标需求，精准推送最匹配的商业机会，提高投标成功率。'
+         },
+         projects: {
+           title: '全球重大项目跟踪',
+           description: '实时监控全球重大基建和商业项目，提供完整项目周期跟踪，帮助企业抢占先机。'
+         },
+         monitoring: {
+           title: '客户与竞争对手风险监控',
+           description: '全面监控海外客户与竞争对手的动态，及时预警风险，提供应对策略建议。'
+         }
+       }
+     },
+     contact: {
+       badge: '联系我们',
+       title: '准备开始您的智能出海旅程？',
+       subtitle: '留下您的联系方式，我们的团队将尽快与您取得联系',
+       emailPlaceholder: '您的邮箱地址',
+       cta: '获取演示',
+       privacy: '我们尊重您的隐私，绝不会分享您的信息',
+       successMessage: '感谢您的订阅！我们将尽快与您联系。',
+       emailRequired: '请输入邮箱地址'
+     },
+     footer: {
+       sections: {
+         product: {
+           title: '产品',
+           features: '产品功能',
+           pricing: '定价方案',
+           tutorials: '使用教程',
+           api: 'API文档'
+         },
+         company: {
+           title: '公司',
+           about: '关于我们',
+           clients: '客户案例',
+           careers: '加入我们',
+           contact: '联系我们'
+         },
+         resources: {
+           title: '资源',
+           guides: '出海攻略',
+           reports: '行业报告',
+           cases: '成功案例',
+           blog: '技术博客'
+         },
+         legal: {
+           title: '法律',
+           privacy: '隐私政策',
+           terms: '服务条款',
+           data: '数据处理',
+           cookies: 'Cookie政策'
+         }
+       },
+       copyright: '© {year} 出海易. 保留所有权利',
+       realTimeData: '实时数据'
+     }
+   },
    procurement: {
      title: '采购与招标信息',
```

```diff
        authority: '权威性'
      }
- 
    },
    common: {
```

---

现在我需要同样在英文国际化文件中添加对应的配置：

---

Read file: src/lang/modules/zh-EN.ts

---

现在我需要找到合适的位置在英文国际化文件中添加landing页面配置：

---

<details>
            <summary>Grep search for "customerService" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/code/formal_code/go-global-fd/src/lang/modules/zh-EN.ts` | L365 | `customerService:{` |

</details>

---

Read file: src/lang/modules/zh-EN.ts

---

现在我在英文国际化文件中添加landing页面配置：

---

```diff
      welcomeMessage:'Hello, I am the customer service of Specific AI. How can I help you?'
    },
+   landing: {
+     navigation: {
+       login: 'Login',
+       register: 'Free Sign Up',
+       features: 'Features',
+       clients: 'Clients',
+       pricing: 'Pricing',
+       contact: 'Contact Us'
+     },
+     hero: {
+       title: 'Global Opportunities',
+       titleAccent: 'Intelligent Decisions',
+       subtitle: 'Build a knowledge engine covering {countries} countries, use {ai} to analyze global business data, and provide precise overseas expansion strategies for Chinese enterprises',
+       cta: {
+         primary: 'Try for Free',
+         secondary: 'Learn More'
+       }
+     },
+     stats: {
+       countries: 'Countries Covered',
+       languages: 'Languages Supported',
+       dailyAnalysis: 'Daily Analysis',
+       accuracy: 'Decision Accuracy'
+     },
+     features: {
+       badge: 'Leading Features',
+       title: 'Comprehensive Intelligent Overseas Expansion Decision Support',
+       subtitle: 'Our platform combines big data and AI technology to provide comprehensive intelligent decision support for Chinese enterprises going global',
+       items: {
+         strategy: {
+           title: 'Overseas Expansion Strategy Support',
+           description: 'Built a "Country Knowledge Engine" covering 100+ countries, which can generate high-quality overseas expansion guides on demand to help enterprises formulate precise strategies.'
+         },
+         insights: {
+           title: 'Global Trend Insights & Business Radar',
+           description: 'The system analyzes global 50+ countries, 20+ languages, and over 10,000 overseas information daily, using AI to filter trend changes, policy signals, and potential opportunities that are substantive for Chinese enterprises.'
+         },
+         bidding: {
+           title: 'Overseas Bidding Intelligent Matching',
+           description: 'Intelligently match enterprise capabilities with overseas bidding requirements, precisely push the most matching business opportunities, and improve bidding success rates.'
+         },
+         projects: {
+           title: 'Global Major Project Tracking',
+           description: 'Real-time monitoring of global major infrastructure and commercial projects, providing complete project lifecycle tracking to help enterprises seize opportunities.'
+         },
+         monitoring: {
+           title: 'Customer & Competitor Risk Monitoring',
+           description: 'Comprehensive monitoring of overseas customers and competitor dynamics, timely risk warnings, and providing countermeasure strategy recommendations.'
+         }
+       }
+     },
+     contact: {
+       badge: 'Contact Us',
+       title: 'Ready to Start Your Intelligent Global Journey?',
+       subtitle: 'Leave your contact information, our team will get in touch with you as soon as possible',
+       emailPlaceholder: 'Your email address',
+       cta: 'Get Demo',
+       privacy: 'We respect your privacy and will never share your information',
+       successMessage: 'Thank you for your subscription! We will contact you soon.',
+       emailRequired: 'Please enter your email address'
+     },
+     footer: {
+       sections: {
+         product: {
+           title: 'Product',
+           features: 'Features',
+           pricing: 'Pricing',
+           tutorials: 'Tutorials',
+           api: 'API Docs'
+         },
+         company: {
+           title: 'Company',
+           about: 'About Us',
+           clients: 'Clients',
+           careers: 'Join Us',
+           contact: 'Contact Us'
+         },
+         resources: {
+           title: 'Resources',
+           guides: 'Global Guides',
+           reports: 'Industry Reports',
+           cases: 'Success Stories',
+           blog: 'Tech Blog'
+         },
+         legal: {
+           title: 'Legal',
+           privacy: 'Privacy Policy',
+           terms: 'Terms of Service',
+           data: 'Data Processing',
+           cookies: 'Cookie Policy'
+         }
+       },
+       copyright: '© {year} Go Global. All rights reserved.',
+       realTimeData: 'Real-time Data'
+     }
+   },
    procurement: {
      title: 'Procurement & Bidding Information',
```

---

现在我也需要在日文国际化文件中添加相应的配置。首先查看日文文件的结构：

---

<details>
            <summary>Grep search for "customerService" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///d%3A/code/formal_code/go-global-fd/src/lang/modules/ja-JP.ts` | L363 | `customerService: {` |

</details>

---

Read file: src/lang/modules/ja-JP.ts

---

现在在日文国际化文件中添加landing页面配置：

---

```diff
      welcomeMessage: 'こんにちは、Specific AIのカスタマーサービスです。ご用件をお聞かせください。'
    },
+   landing: {
+     navigation: {
+       login: 'ログイン',
+       register: '無料登録',
+       features: '機能',
+       clients: 'お客様',
+       pricing: '料金',
+       contact: 'お問い合わせ'
+     },
+     hero: {
+       title: 'グローバル商機',
+       titleAccent: 'インテリジェント意思決定',
+       subtitle: '{countries}ヶ国をカバーする知識エンジンを構築し、{ai}でグローバルビジネスデータを分析し、中国企業に精密な海外展開戦略を提供',
+       cta: {
+         primary: '無料体験',
+         secondary: '詳細を見る'
+       }
+     },
+     stats: {
+       countries: 'カバー国数',
+       languages: '対応言語',
+       dailyAnalysis: '日次分析',
+       accuracy: '決定精度'
+     },
+     features: {
+       badge: '先進機能',
+       title: '包括的インテリジェント海外展開決定支援',
+       subtitle: '当プラットフォームはビッグデータとAI技術を組み合わせ、中国企業の海外展開に包括的なインテリジェント決定支援を提供します',
+       items: {
+         strategy: {
+           title: '海外展開戦略決定支援',
+           description: '100+ヶ国をカバーする「国別知識エンジン」を構築し、オンデマンドで高品質な海外展開ガイドを生成し、企業の精密戦略策定を支援します。'
+         },
+         insights: {
+           title: 'グローバルトレンド洞察とビジネスレーダー',
+           description: 'システムは毎日グローバル50+ヶ国、20+言語、1万件以上の海外情報を分析し、AIを通じて中国企業にとって実質的な意味のあるトレンド変化、政策シグナル、潜在的機会を選別します。'
+         },
+         bidding: {
+           title: '海外入札インテリジェントマッチング',
+           description: '企業能力と海外入札ニーズをインテリジェントにマッチングし、最適なビジネス機会を精密にプッシュし、入札成功率を向上させます。'
+         },
+         projects: {
+           title: 'グローバル重大プロジェクト追跡',
+           description: 'グローバルな重大インフラ・商業プロジェクトをリアルタイム監視し、完全なプロジェクトライフサイクル追跡を提供し、企業の先行機会獲得を支援します。'
+         },
+         monitoring: {
+           title: '顧客・競合リスク監視',
+           description: '海外顧客と競合の動向を包括的に監視し、リスクを適時警告し、対策戦略提案を提供します。'
+         }
+       }
+     },
+     contact: {
+       badge: 'お問い合わせ',
+       title: 'インテリジェントグローバル展開の旅を始める準備はできましたか？',
+       subtitle: '連絡先をお残しください。チームができるだけ早くご連絡いたします',
+       emailPlaceholder: 'メールアドレス',
+       cta: 'デモを取得',
+       privacy: 'プライバシーを尊重し、お客様の情報を共有することはありません',
+       successMessage: 'ご登録ありがとうございます！間もなくご連絡いたします。',
+       emailRequired: 'メールアドレスを入力してください'
+     },
+     footer: {
+       sections: {
+         product: {
+           title: '製品',
+           features: '機能',
+           pricing: '料金',
+           tutorials: 'チュートリアル',
+           api: 'API文書'
+         },
+         company: {
+           title: '会社',
+           about: '私たちについて',
+           clients: 'お客様',
+           careers: '採用情報',
+           contact: 'お問い合わせ'
+         },
+         resources: {
+           title: 'リソース',
+           guides: '海外展開ガイド',
+           reports: '業界レポート',
+           cases: '成功事例',
+           blog: 'テックブログ'
+         },
+         legal: {
+           title: '法的事項',
+           privacy: 'プライバシーポリシー',
+           terms: '利用規約',
+           data: 'データ処理',
+           cookies: 'Cookieポリシー'
+         }
+       },
+       copyright: '© {year} グローバル展開. 全権利保持.',
+       realTimeData: 'リアルタイムデータ'
+     }
+   },
    procurement: {
      title: '調達・入札情報',
```

---

现在，我需要修改 `landing.vue` 文件，将硬编码的中文文案替换为国际化函数调用。我将首先修改 `<script setup>` 部分的数据定义：

---

```diff
  const { t } = useI18n()
  
- 
- // 产品特性
- const features = [
+ // 产品特性 - 使用computed属性来获取国际化文案
+ const features = computed(() => [
    {
-     title: '出海战略决策支持',
+     title: t('landing.features.items.strategy.title'),
      icon: 'carbon:chart-relationship',
-     description: '构建了覆盖100+国家的"国别知识引擎"，可按需定制生成高质量的出海指南，助力企业制定精准战略。'
+     description: t('landing.features.items.strategy.description')
    },
    {
-     title: '全球趋势洞察与商机雷达',
+     title: t('landing.features.items.insights.title'),
      icon: 'carbon:radar',
-     description: '系统每日分析全球50+国家、20+语种、超万条海外资讯，通过AI筛选出对中国企业具有实质意义的趋势变化、政策信号和潜在机会。'
+     description: t('landing.features.items.insights.description')
    },
    {
-     title: '海外招投标智能匹配',
+     title: t('landing.features.items.bidding.title'),
      icon: 'lucide:file-text',
-     description: '智能匹配企业能力与海外招投标需求，精准推送最匹配的商业机会，提高投标成功率。'
+     description: t('landing.features.items.bidding.description')
    },
    {
-     title: '全球重大项目跟踪',
+     title: t('landing.features.items.projects.title'),
      icon: 'lucide:globe',
-     description: '实时监控全球重大基建和商业项目，提供完整项目周期跟踪，帮助企业抢占先机。'
+     description: t('landing.features.items.projects.description')
    },
    {
-     title: '客户与竞争对手风险监控',
+     title: t('landing.features.items.monitoring.title'),
      icon: 'carbon:security',
-     description: '全面监控海外客户与竞争对手的动态，及时预警风险，提供应对策略建议。'
+     description: t('landing.features.items.monitoring.description')
    }
- ]
- 
+ ])
  
- // 统计数据
- const stats = [
-   { value: '100+', label: '覆盖国家' },
-   { value: '50+', label: '支持语种' },
-   { value: '10K+', label: '每日分析资讯' },
-   { value: '85%', label: '决策准确率' }
- ]
+ // 统计数据 - 使用computed属性来获取国际化文案
+ const stats = computed(() => [
+   { value: '100+', label: t('landing.stats.countries') },
+   { value: '50+', label: t('landing.stats.languages') },
+   { value: '10K+', label: t('landing.stats.dailyAnalysis') },
+   { value: '85%', label: t('landing.stats.accuracy') }
+ ])
  
  // API调用
```

---

现在修改事件处理函数中的国际化文案：

---

```diff
  const handleSubscribe = async () => {
    if (!email.value) {
-     ElMessage.warning('请输入邮箱地址')
+     ElMessage.warning(t('landing.contact.emailRequired'))
      return
    }
    
    await addContact(0, { contact: email.value })
-   ElMessage.success('感谢您的订阅！我们将尽快与您联系。')
+   ElMessage.success(t('landing.contact.successMessage'))
    email.value = ''
  }
```

---

现在开始修改模板部分的硬编码中文文案。首先修改导航栏部分：

---

```diff
            <div class="flex-shrink-0">
              <a href="#" class="flex items-center">
-               <img src="@/assets/logo.svg" alt="出海易" class="h-8 md:h-10"/>
+               <img src="@/assets/logo.svg" :alt="t('global.appName')" class="h-8 md:h-10"/>
              </a>
            </div>
            
            <!-- 桌面导航 -->
            <!-- <nav class="hidden md:flex space-x-8">
-             <a @click="scrollToSection('features')" class="text-sm font-medium hover:text-primary cursor-pointer">产品功能</a>
-             <a @click="scrollToSection('clients')" class="text-sm font-medium hover:text-primary cursor-pointer">客户案例</a>
-             <a @click="scrollToSection('pricing')" class="text-sm font-medium hover:text-primary cursor-pointer">定价方案</a>
-             <a @click="scrollToSection('contact')" class="text-sm font-medium hover:text-primary cursor-pointer">联系我们</a>
+             <a @click="scrollToSection('features')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.features') }}</a>
+             <a @click="scrollToSection('clients')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.clients') }}</a>
+             <a @click="scrollToSection('pricing')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.pricing') }}</a>
+             <a @click="scrollToSection('contact')" class="text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.contact') }}</a>
            </nav> -->
            
```

```diff
              <Button @click="handleLoginClick"
                      variant="outline"
-                     size="sm">登录</Button>
-             <Button @click="handleRegisterClick" size="sm">免费注册</Button>
+                     size="sm">{{ t('landing.navigation.login') }}</Button>
+             <Button @click="handleRegisterClick" size="sm">{{ t('landing.navigation.register') }}</Button>
            </div>
            
```

---

现在修改移动端导航菜单：

---

```diff
      <div v-if="mobileMenuOpen" class="md:hidden bg-background/95 backdrop-blur-md border-b border-primary/5 shadow-md z-[999] fixed top-16 left-0 right-0" style="pointer-events: auto;">
        <div class="container mx-auto px-4 py-4 space-y-2">
-         <a @click="scrollToSection('features')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">产品功能</a>
-         <a @click="scrollToSection('clients')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">客户案例</a>
-         <a @click="scrollToSection('pricing')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">定价方案</a>
-         <a @click="scrollToSection('contact')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">联系我们</a>
+         <a @click="scrollToSection('features')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.features') }}</a>
+         <a @click="scrollToSection('clients')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.clients') }}</a>
+         <a @click="scrollToSection('pricing')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.pricing') }}</a>
+         <a @click="scrollToSection('contact')" class="block py-2 text-sm font-medium hover:text-primary cursor-pointer">{{ t('landing.navigation.contact') }}</a>
          <div class="pt-2 flex space-x-4">
            <Button @click="handleLoginClick"
                    variant="outline"
                    size="sm"
-                   class="w-full">登录</Button>
-           <Button @click="handleRegisterClick" size="sm" class="w-full">免费注册</Button>
+                   class="w-full">{{ t('landing.navigation.login') }}</Button>
+           <Button @click="handleRegisterClick" size="sm" class="w-full">{{ t('landing.navigation.register') }}</Button>
          </div>
        </div>
```

---

现在修改Hero部分的文案：

---

```diff
            <div class="lg:w-1/2 lg:pr-8 mb-10 lg:mb-0">
              <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary to-[#7490ff]">
-               全球商机<br/>
+               {{ t('landing.hero.title') }}<br/>
                <span class="relative text-primary">
-                 智能决策
+                 {{ t('landing.hero.titleAccent') }}
                  <span class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-primary to-transparent rounded-full"></span>
                </span>
              </h1>
              <p class="text-xl md:text-2xl text-muted-foreground mb-10 font-light">
-               构建覆盖<span class="text-primary font-medium">100+</span>国家的知识引擎，用<span class="text-primary font-medium">AI</span>分析全球商业数据，为中国企业提供精准出海战略
+               {{ t('landing.hero.subtitle', { countries: '100+', ai: 'AI' }) }}
              </p>
              <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6">
                <Button @click="handleRegisterClick" size="lg" class="px-8 py-6 text-lg rounded-full bg-gradient-to-r from-primary to-[#6384ff] hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
-                 免费试用
+                 {{ t('landing.hero.cta.primary') }}
                  <Icon icon="lucide:arrow-right" class="ml-2 h-5 w-5"/>
                </Button>
```

```diff
                        size="lg"
                        class="px-8 py-6 text-lg rounded-full border-primary/30 hover:bg-primary/5">
-                 了解更多
+                 {{ t('landing.hero.cta.secondary') }}
                </Button>
              </div>
```

---

我还需要修改Hero部分中的模拟界面中"实时数据"文案：

---

```diff
                        <div class="flex items-center bg-card/40 rounded-md px-2 py-1 text-xs text-muted-foreground">
                          <div class="w-3 h-3 rounded-full bg-primary/50 mr-2"></div>
-                         <span>实时数据</span>
+                         <span>{{ t('landing.footer.realTimeData') }}</span>
                        </div>
                      </div>
```

---

现在修改特性部分的文案：

---

```diff
        <div ref="animateFeatures" class="container mx-auto px-4 relative z-10">
          <div class="text-center mb-20">
-           <Badge variant="outline" class="mb-4 px-4 py-1.5 border-primary/20 text-primary font-medium">领先功能</Badge>
-           <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground to-primary/80">全方位智能出海决策支持</h2>
+           <Badge variant="outline" class="mb-4 px-4 py-1.5 border-primary/20 text-primary font-medium">{{ t('landing.features.badge') }}</Badge>
+           <h2 class="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground to-primary/80">{{ t('landing.features.title') }}</h2>
            <p class="text-xl text-muted-foreground max-w-3xl mx-auto font-light">
-             我们的平台结合大数据与AI技术，为中国企业出海提供全面的智能决策支持
+             {{ t('landing.features.subtitle') }}
            </p>
          </div>
```

---

现在修改联系我们部分的文案：

---

```diff
        <div ref="animateContact" class="container mx-auto px-4 relative z-10">
          <div class="max-w-3xl mx-auto text-center relative bg-background/50 backdrop-blur-sm p-8 rounded-2xl shadow-sm border border-primary/10">
-           <Badge variant="outline" class="mb-4 border-primary/20 text-primary">联系我们</Badge>
-           <h2 class="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground to-primary/80">准备开始您的智能出海旅程？</h2>
+           <Badge variant="outline" class="mb-4 border-primary/20 text-primary">{{ t('landing.contact.badge') }}</Badge>
+           <h2 class="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground to-primary/80">{{ t('landing.contact.title') }}</h2>
            <p class="text-lg text-muted-foreground mb-8">
-             留下您的联系方式，我们的团队将尽快与您取得联系
+             {{ t('landing.contact.subtitle') }}
            </p>
            
            <div class="flex flex-col sm:flex-row items-center gap-3 max-w-md mx-auto">
              <Input v-model="email"
-                    placeholder="您的邮箱地址"
+                    :placeholder="t('landing.contact.emailPlaceholder')"
                     type="email"
                     class="flex-grow"/>
              <Button @click="handleSubscribe" class="sm:flex-shrink-0 w-full sm:w-auto bg-primary hover:bg-primary/90">
-               获取演示
+               {{ t('landing.contact.cta') }}
              </Button>
            </div>
            
            <p class="text-xs text-muted-foreground mt-4">
-             我们尊重您的隐私，绝不会分享您的信息
+             {{ t('landing.contact.privacy') }}
            </p>
          </div>
```

---

现在修改页脚部分的文案：

---

```diff
          <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
            <div>
-             <h3 class="font-medium mb-4">产品</h3>
+             <h3 class="font-medium mb-4">{{ t('landing.footer.sections.product.title') }}</h3>
              <ul class="space-y-2">
-               <li><a href="#features" class="text-sm text-muted-foreground hover:text-foreground">产品功能</a></li>
-               <li><a href="#pricing" class="text-sm text-muted-foreground hover:text-foreground">定价方案</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">使用教程</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">API文档</a></li>
+               <li><a href="#features" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.features') }}</a></li>
+               <li><a href="#pricing" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.pricing') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.tutorials') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.product.api') }}</a></li>
              </ul>
            </div>
            <div>
-             <h3 class="font-medium mb-4">公司</h3>
+             <h3 class="font-medium mb-4">{{ t('landing.footer.sections.company.title') }}</h3>
              <ul class="space-y-2">
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">关于我们</a></li>
-               <li><a href="#clients" class="text-sm text-muted-foreground hover:text-foreground">客户案例</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">加入我们</a></li>
-               <li><a href="#contact" class="text-sm text-muted-foreground hover:text-foreground">联系我们</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.about') }}</a></li>
+               <li><a href="#clients" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.clients') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.careers') }}</a></li>
+               <li><a href="#contact" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.company.contact') }}</a></li>
              </ul>
            </div>
            <div>
-             <h3 class="font-medium mb-4">资源</h3>
+             <h3 class="font-medium mb-4">{{ t('landing.footer.sections.resources.title') }}</h3>
              <ul class="space-y-2">
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">出海攻略</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">行业报告</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">成功案例</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">技术博客</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.guides') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.reports') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.cases') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.resources.blog') }}</a></li>
              </ul>
            </div>
            <div>
-             <h3 class="font-medium mb-4">法律</h3>
+             <h3 class="font-medium mb-4">{{ t('landing.footer.sections.legal.title') }}</h3>
              <ul class="space-y-2">
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">隐私政策</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">服务条款</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">数据处理</a></li>
-               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">Cookie政策</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.privacy') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.terms') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.data') }}</a></li>
+               <li><a href="#" class="text-sm text-muted-foreground hover:text-foreground">{{ t('landing.footer.sections.legal.cookies') }}</a></li>
              </ul>
            </div>
```

```diff
            <div class="flex items-center mb-4 md:mb-0">
              <a href="#" class="flex items-center mr-3">
-               <img src="@/assets/logo.svg" alt="出海易" class="h-8"/>
+               <img src="@/assets/logo.svg" :alt="t('global.appName')" class="h-8"/>
              </a>
-             <span class="text-sm text-muted-foreground">© {{ new Date().getFullYear() }} 出海易. 保留所有权利</span>
+             <span class="text-sm text-muted-foreground">{{ t('landing.footer.copyright', { year: new Date().getFullYear() }) }}</span>
            </div>
            <!-- <div class="flex space-x-4">
```

---



---

_**User**_

还需要将 @MagicText.vue 中写死的中文更新到国际化文件中

---

_**Assistant**_



---

**Thinking**

用户希望我将 `MagicText.vue` 组件中的硬编码中文文案也进行国际化处理。从文件内容可以看到需要国际化的内容包括：

1. 主要描述文字
2. 动态切换的关键词数组

我需要先在国际化文件中添加相关配置，然后修改组件使用国际化。

首先，我在中文国际化文件中添加 MagicText 的配置：

---

```diff
        contact: '联系我们'
      },
+     magicText: {
+       description: 'SpecificAI专注于全球海量信息的实时搜集、结构化整理与智能分析，帮助中国企业高效出海，把握全球商机，洞察市场风险，实现可持续国际化增长。',
+       titles: [
+         '海量信息智能搜集',
+         '实时数据全球覆盖',
+         '多维度结构化整理',
+         '出海风险监测预警',
+         '海外市场趋势洞察',
+         'AI驱动决策分析',
+         '一站式出海情报平台'
+       ]
+     },
      hero: {
        title: '全球商机',
```

---

接下来在英文国际化文件中添加对应的配置：

---

```diff
        contact: 'Contact Us'
      },
+     magicText: {
+       description: 'SpecificAI focuses on real-time collection, structured organization, and intelligent analysis of global massive information, helping Chinese enterprises expand overseas efficiently, seize global opportunities, gain market insights, and achieve sustainable international growth.',
+       titles: [
+         'Intelligent Collection of Massive Information',
+         'Real-time Global Data Coverage',
+         'Multi-dimensional Structured Organization',
+         'Overseas Risk Monitoring & Early Warning',
+         'Global Market Trend Insights',
+         'AI-driven Decision Analysis',
+         'One-stop Overseas Intelligence Platform'
+       ]
+     },
      hero: {
        title: 'Global Opportunities',
```

---

现在在日文国际化文件中添加对应的配置：

---

```diff
        contact: 'お問い合わせ'
      },
+     magicText: {
+       description: 'SpecificAIは、グローバルな大量情報のリアルタイム収集、構造化整理、インテリジェント分析に専念し、中国企業の効率的な海外展開、グローバル商機の把握、市場リスクの洞察、持続可能な国際化成長の実現を支援します。',
+       titles: [
+         '大量情報インテリジェント収集',
+         'リアルタイムデータグローバルカバレッジ',
+         '多次元構造化整理',
+         '海外リスク監視・早期警告',
+         'グローバル市場トレンド洞察',
+         'AI駆動意思決定分析',
+         'ワンストップ海外情報プラットフォーム'
+       ]
+     },
      hero: {
        title: 'グローバル商機',
```

---

现在修改 `MagicText.vue` 组件，使其使用国际化：

---

```diff
            </h1>
            <p class="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl text-center font-[PingFang SC, HarmonyOS_Sans, Microsoft YaHei, sans-serif]">
-             SpecificAI专注于全球海量信息的实时搜集、结构化整理与智能分析，帮助中国企业高效出海，把握全球商机，洞察市场风险，实现可持续国际化增长。
+             {{ t('landing.magicText.description') }}
            </p>
          </div>
```

```diff
  <script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
+ import { useI18n } from 'vue-i18n'
  import { Button } from '@/components/ui/button'
  import { MoveRight } from 'lucide-vue-next'
  
- // 动态切换关键词（主题语）
- const titles = [
-   '海量信息智能搜集',
-   '实时数据全球覆盖',
-   '多维度结构化整理',
-   '出海风险监测预警',
-   '海外市场趋势洞察',
-   'AI驱动决策分析',
-   '一站式出海情报平台',
- ]
+ const { t } = useI18n()
+   
+ // 动态切换关键词（主题语） - 使用国际化
+ const titles = computed(() => t('landing.magicText.titles'))
  const titleNumber = ref(0)
- const currentTitle = computed(() => titles[titleNumber.value])
+ const currentTitle = computed(() => titles.value[titleNumber.value])
    
  onMounted(() => {
    setInterval(() => {
-     titleNumber.value = (titleNumber.value + 1) % titles.length
+     titleNumber.value = (titleNumber.value + 1) % titles.value.length
    }, 2000)
  })
```

---

现在让我验证一下修改是否完整，并检查是否还有其他需要国际化的地方。让我先检查一下现在的结果：

---



---

根据反馈，用户希望将动态文本数组直接写在组件中，而不是在国际化文件中定义数组。我需要调整实现方式：

---

```diff
  const { t } = useI18n()
    
- // 动态切换关键词（主题语） - 使用国际化
- const titles = computed(() => t('landing.magicText.titles'))
+ // 动态切换关键词（主题语） - 使用国际化键
+ const titleKeys = [
+   'landing.magicText.titles.collection',
+   'landing.magicText.titles.coverage',
+   'landing.magicText.titles.organization',
+   'landing.magicText.titles.monitoring',
+   'landing.magicText.titles.insights',
+   'landing.magicText.titles.analysis',
+   'landing.magicText.titles.platform'
+ ]
  const titleNumber = ref(0)
- const currentTitle = computed(() => titles.value[titleNumber.value])
+ const currentTitle = computed(() => t(titleKeys[titleNumber.value]))
    
  onMounted(() => {
    setInterval(() => {
-     titleNumber.value = (titleNumber.value + 1) % titles.value.length
+     titleNumber.value = (titleNumber.value + 1) % titleKeys.length
    }, 2000)
  })
```

---

现在我需要更新国际化文件，将数组形式改为对象形式：

---

```diff
      magicText: {
        description: 'SpecificAI专注于全球海量信息的实时搜集、结构化整理与智能分析，帮助中国企业高效出海，把握全球商机，洞察市场风险，实现可持续国际化增长。',
-       titles: [
-         '海量信息智能搜集',
-         '实时数据全球覆盖',
-         '多维度结构化整理',
-         '出海风险监测预警',
-         '海外市场趋势洞察',
-         'AI驱动决策分析',
-         '一站式出海情报平台'
-       ]
+       titles: {
+         collection: '海量信息智能搜集',
+         coverage: '实时数据全球覆盖',
+         organization: '多维度结构化整理',
+         monitoring: '出海风险监测预警',
+         insights: '海外市场趋势洞察',
+         analysis: 'AI驱动决策分析',
+         platform: '一站式出海情报平台'
+       }
      },
      hero: {
```

---

更新英文国际化文件：

---

```diff
      magicText: {
        description: 'SpecificAI focuses on real-time collection, structured organization, and intelligent analysis of global massive information, helping Chinese enterprises expand overseas efficiently, seize global opportunities, gain market insights, and achieve sustainable international growth.',
-       titles: [
-         'Intelligent Collection of Massive Information',
-         'Real-time Global Data Coverage',
-         'Multi-dimensional Structured Organization',
-         'Overseas Risk Monitoring & Early Warning',
-         'Global Market Trend Insights',
-         'AI-driven Decision Analysis',
-         'One-stop Overseas Intelligence Platform'
-       ]
+       titles: {
+         collection: 'Intelligent Collection of Massive Information',
+         coverage: 'Real-time Global Data Coverage',
+         organization: 'Multi-dimensional Structured Organization',
+         monitoring: 'Overseas Risk Monitoring & Early Warning',
+         insights: 'Global Market Trend Insights',
+         analysis: 'AI-driven Decision Analysis',
+         platform: 'One-stop Overseas Intelligence Platform'
+       }
      },
      hero: {
```

---

更新日文国际化文件：

---

```diff
      magicText: {
        description: 'SpecificAIは、グローバルな大量情報のリアルタイム収集、構造化整理、インテリジェント分析に専念し、中国企業の効率的な海外展開、グローバル商機の把握、市場リスクの洞察、持続可能な国際化成長の実現を支援します。',
-       titles: [
-         '大量情報インテリジェント収集',
-         'リアルタイムデータグローバルカバレッジ',
-         '多次元構造化整理',
-         '海外リスク監視・早期警告',
-         'グローバル市場トレンド洞察',
-         'AI駆動意思決定分析',
-         'ワンストップ海外情報プラットフォーム'
-       ]
+       titles: {
+         collection: '大量情報インテリジェント収集',
+         coverage: 'リアルタイムデータグローバルカバレッジ',
+         organization: '多次元構造化整理',
+         monitoring: '海外リスク監視・早期警告',
+         insights: 'グローバル市場トレンド洞察',
+         analysis: 'AI駆動意思決定分析',
+         platform: 'ワンストップ海外情報プラットフォーム'
+       }
      },
      hero: {
```

---

现在让我最终确认一下国际化改造是否完整：

---



---

完美！我已经成功完成了 `landing.vue` 和 `MagicText.vue` 组件的国际化改造工作。

## **总结**

我已经完成了完整的国际化改造，具体包括：

### **📂 国际化文件配置**

1. **中文配置** (`zh-CN.ts`) - 添加了 `landing` 配置块
2. **英文配置** (`zh-EN.ts`) - 添加了对应的英文翻译
3. **日文配置** (`ja-JP.ts`) - 添加了对应的日文翻译

### **🔧 组件修改**

#### **landing.vue**
- ✅ 导航栏文案（登录、注册、菜单项）
- ✅ Hero部分（标题、副标题、按钮）
- ✅ 统计数据标签
- ✅ 产品特性（标题、描述）
- ✅ 联系我们部分（所有文案和提示信息）
- ✅ 页脚链接和版权信息
- ✅ 将数据改为 `computed` 属性以响应语言变化

#### **MagicText.vue**
- ✅ 主要描述文字国际化
- ✅ 动态切换文本使用对象结构而非数组
- ✅ 引入 `useI18n` 并正确配置

### **🌐 国际化键值结构**

```typescript
landing: {
  navigation: { login, register, features, clients, pricing, contact },
  magicText: { 
    description, 
    titles: { collection, coverage, organization, monitoring, insights, analysis, platform }
  },
  hero: { title, titleAccent, subtitle, cta: { primary, secondary } },
  stats: { countries, languages, dailyAnalysis, accuracy },
  features: { badge, title, subtitle, items: { strategy, insights, bidding, projects, monitoring } },
  contact: { badge, title, subtitle, emailPlaceholder, cta, privacy, successMessage, emailRequired },
  footer: { sections: { product, company, resources, legal }, copyright, realTimeData }
}
```

现在所有的硬编码中文文案都已经被国际化键值替换，支持中文、英文、日文三种语言，并且能够响应语言切换实时更新显示内容。

---

