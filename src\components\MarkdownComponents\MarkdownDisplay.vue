<template>
  <div :class="$style.container" :data-pdf-section="type">
    <div v-if="!isHideTitle" :class="$style['title-container']" data-pdf-header>
      <span :class="$style['title']" data-pdf-title>{{ title }}</span>
      <slot name="rewrite-button" v-if="showRewriteButton">
        <el-button
          class="pdf-exclude"
          :class="$style['rewrite-btn']"
          type="primary"
          plain
          size="small"
          @click.stop="handleRewrite">
          {{ $t("intelligence.markdown.rewrite") }}
        </el-button>
      </slot>
    </div>
    <SearchResultsList
      v-if="searchResults?.length"
      :results="searchResults"/>
    <div class="markdown-content">
      <MarkdownContainer :content="content" :section-type="type"/>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useCamelCaseAttrs } from '@/hooks/useCamelCaseAttrs'
import MarkdownContainer from './MarkdownContainer.vue'
import SearchResultsList from '../SearchResultsList.vue'
import type { MarkdownKeyType } from '@/types/intelligence'
import type { url } from '@/pages/format-report/type'


const props = defineProps({
  title: {
    type: String,
  },
  content: {
    type: String,
    required: true,
  },
  type: {
    type: String as () => MarkdownKeyType | string,
    required: true,
  },
  showRewriteButton: {
    type: Boolean,
    default: false,
  },
  searchResults: {
    type: Array as () => url[],
    default: () => [],
  },
})

const emit = defineEmits<{
  rewrite: [type: MarkdownKeyType | string];
}>()

const handleRewrite = () => {
  emit('rewrite', props.type)
}

const { attrs } = useCamelCaseAttrs()
const isHideTitle = computed(() =>
  attrs.hideTitle === '' || !!attrs.hideTitle ? true : false
)
const isHideDivider = computed(() =>
  attrs.hideDivider === '' || !!attrs.hideDivider ? true : false
)
</script>

<style lang="scss" module>
.title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  margin-left: 12px;
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-font-color);
}

.rewrite-btn {
  margin-left: auto;
}

:global(.el-divider) {
  margin: 6px 0 !important;
}
</style>
