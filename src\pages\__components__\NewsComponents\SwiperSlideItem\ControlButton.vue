<template>
  <el-tooltip :content="tooltip" :open-delay="1000" :hide-after="0">
    <!-- <component
      :is="icon"
      class="cursor-pointer"
      theme="outline"
      size="24"
      :fill="fill"
      @click="$emit('click')"/> -->
    <Icon :icon="icon"
          class="text-[24px]"
          :fill="fill"
          @click="$emit('click')"/>
  </el-tooltip>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
defineProps<{
  tooltip: string;
  icon: any;
  fill?: string;
}>()

defineEmits<{
  click: [];
}>()
</script>
