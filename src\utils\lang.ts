// import type { I18nData } from '@/lang/i18n'
// const linkModInterpolate = /-\((.+?)\.\${(.+?)}\)/g // -(xx.${xx})
// const interpolate = /\$(\..+)?{(.+?)}/g // ${xx} or $.xx{xx}

// /**
//  * 替换key中的.和#为_和.
//  * a#b.name#c => a.b_name.c
//  * @param key
//  * @returns
//  */
// export const replaceKey = (key: string) => {
//   return key.replace(/\./g, '_').replace(/#/g, '.')
// }
// /**
//  * 将key中的${xx}替换为@:(xx.xx.xx)
//  * ${xx#xx#xx} => @:(xx.xx.xx)
//  * @param key
//  * @param scopeName
//  * @returns
//  */
// export const transformLinkKey = (key: string, scopeName?: string) => {
//   let str = key
//   if (linkModInterpolate.test(str)) {
//     str = str.replace(linkModInterpolate, (_, $1, $2) => `@${$1 ? `.${$1}` : ''}:{'${scopeName ? `${scopeName}.` : ''}${replaceKey($2)}'}`)
//   }
//   if (interpolate.test(str)) {
//     str = str.replace(interpolate, (_, $1, $2) => `@${$1 || ''}:{'${scopeName ? `${scopeName}.` : ''}${replaceKey($2)}'}`)
//   }
//   return str
// }
// export const processI18nData = (list: I18nData[]) => {
//   return list.reduce((acc, c) => {
//     const propKey = replaceKey(c.k)
//     acc[propKey] = transformLinkKey(c.v)
//     return acc
//   }, {} as Record<string, string>)
// }
