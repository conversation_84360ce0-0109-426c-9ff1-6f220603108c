import type { TooltipOptions } from '@/directives/overflow-tooltip/overflowTooltip'
import OverflowTooltip from '@/directives/overflow-tooltip/overflowTooltip'
import { tryOnUnmounted, unrefElement, useMutationObserver, type MaybeElementRef, type MaybeRefOrGetter } from '@vueuse/core'
import { computed, watch } from 'vue'

export const useOverflowTooltip = (target: MaybeRefOrGetter<HTMLElement | null | undefined>, opts: TooltipOptions) => {
  let instance: OverflowTooltip | undefined
  const cleanup = () => {
    if (instance) {
      instance.destroy()
      instance = undefined
    }
  }
  const targetEl = computed(() => unrefElement(target as unknown as MaybeElementRef<HTMLElement | null | undefined>))
  const stopWatch = watch(
    targetEl,
    (el) => {
      cleanup()
      if (!el) {
        return
      }
      instance = new OverflowTooltip(el, opts)
    },
    { immediate: true, flush: 'post' },
  )
  const { stop: stopMutationObserver } = useMutationObserver(() => targetEl.value ? targetEl.value.parentElement || document.body : null, (mutations) => {
    const el = targetEl.value
    mutations.forEach((mutation) => {
      mutation.removedNodes.forEach((node) => {
        if (node === el) {
          stop()
        }
      })
    })
  }, { childList: true, subtree: true })
  const updateOptions = (options: TooltipOptions) => {
    if (instance) {
      instance.update(options)
    }
  }

  const stop = () => {
    stopWatch()
    stopMutationObserver()
    cleanup()
  }

  tryOnUnmounted(stop)

  return { stop, updateOptions }
}
