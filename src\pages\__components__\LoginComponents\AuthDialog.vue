<template>
  <Dialog :open="dialogVisible" @update:open="dialogVisible = $event">
    <DialogContent
      class="p-0 w-full min-w-[40vw] min-h-[500px] h-[55vh] overflow-hidden rounded-lg border-none"
      :class="$style['auth-dialog']"
      :isHideCloseButton="true">
      <div class="w-full h-full overflow-hidden relative">
        <!-- 背景图 -->
        <img
          :src="loginBgImage"
          alt="Login Background"
          class="absolute inset-0 w-full h-full object-cover rounded-lg"/>

        <!-- 表单容器 -->
        <div 
          :class="[
            'relative z-10 h-full flex items-center overflow-hidden w-full',
            { 'lg:justify-end': currentMode === 'login' } // Justify right on lg+ for login mode
          ]">
          <Card
            :class="[
              'h-full w-full transition-all duration-500 ease-in-out', // Base mobile styles (full width) + transition
              'bg-background/95 backdrop-blur-[2px] shadow-lg', // Existing appearance styles
              'lg:w-1/2' // Width becomes 1/2 from lg breakpoint
            ]">
            <CardContent class="h-full flex flex-col items-start justify-center space-y-6 p-8">
              <div class="w-full flex items-center gap-2 flex-col">
                <img
                  :src="smallLogo"
                  class="w-[30px] h-[30px]"
                  alt="Logo"/>
                <span class="text-[16px] font-bold text-foreground">{{ dialogTitle }}</span>
              </div>
              <component
                class="h-full flex flex-col justify-center items-center gap-2 w-full"
                :is="currentForm"
                :loading="loading"
                @switchMode="handleSwitchMode"
                @toStepPage="handleRegisterStep"
                @toHome="handleToHome"
                @login="handleLogin"/>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <!-- 自定义关闭按钮插槽 -->
      <!-- <template #close-button> -->
      <Button
        :class="[
          'absolute top-4 z-20 transition-all duration-500 ease-in-out rounded-full', // Base styles + transition
          'hover:scale-110 hover:rotate-90', // Hover effects
          currentMode === 'login' ? 'left-4' : 'right-4' // Conditional positioning
        ]"
        variant="ghost"
        size="icon"
        @click="handleClose">
        <Icon icon="mdi:close" class="text-foreground text-[16px]"/>
      </Button>
      <!-- </template> -->
      
      <!-- 加载状态 -->
      <DialogOverlay v-if="loading" class="bg-background/50 flex items-center justify-center">
        <Loader2 class="h-8 w-8 text-primary animate-spin"/>
      </DialogOverlay>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useVModel } from '@vueuse/core'
import LoginForm from '@/pages/__components__/LoginComponents/LoginForm.vue'
import RegisterForm from '@/pages/__components__/LoginComponents/RegisterForm_og.vue'
import loginBgImage from '@/assets/home-bg.svg'
import smallLogo from '@/assets/images/sso/small-logo.svg'
import type { LoginMode } from '@/types/login'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
// shadcn组件
import {
  Dialog,
  DialogContent,
  DialogOverlay
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-vue-next'

const props = defineProps<{
  modelValue: boolean;
  mode: LoginMode;
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'update:mode': [value: LoginMode];
  toStepPage: [void];
  toHome: [void];
}>()

const dialogVisible = useVModel(props, 'modelValue', emit)
const currentMode = useVModel(props, 'mode', emit)

const currentForm = computed(() =>
  currentMode.value === 'login' ? LoginForm : RegisterForm
)

const { t } = useI18n()

const dialogTitle = computed(() =>
  currentMode.value === 'login'
    ? t('login.welcomeBack')
    : t('login.createdAccount')
)

const handleSwitchMode = (mode: LoginMode) => {
  currentMode.value = mode
}

function toStep() {
  emit('toStepPage')
}

const handleRegisterStep = () => {
  dialogVisible.value = false
  toStep()
}

const handleToHome = () => {
  dialogVisible.value = false
  emit('toHome')
}

const handleClose = () => {
  dialogVisible.value = false
}

const loading = ref(false)
const userStore = useUserStore()

const handleLogin = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await userStore.silentRetryLoadAccount()
    
    dialogVisible.value = false
    
    handleToHome()
  } catch (error) {
    console.error('Login failed:', error)
    ElMessage.error(t('login.failed'))
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" module>
/* Remove styles related to form container and close button positioning/transition */
/* Keep other styles if they exist and are necessary */
</style>
