<template>
  <div :class="$style.homeContainer" @scroll.passive="handleScroll" ref="containerRef">
    <!-- Product Section -->
    <section :class="[$style.section, $style.productSection]" id="product">
      <!-- Background -->
      <div class="absolute inset-0 -z-10">
        <img
          :src="bgImage"
          alt="background"
          class="w-full h-full object-cover"/>
      </div>
      <BreathingLights/>

      <!-- Header -->
      <TheHeader
        :navItems="navItems"
        :hasInviteCode="hasInviteCode"
        @login="handleLoginClick"
        @register="handleRegisterClick"/>

      <!-- Product Content -->
      <div class="h-[calc(100vh-80px)] flex items-center">
        <div class="container mx-auto px-4">
          <HeroContent :email="email" @try-now="handleTryNow"/>
        </div>
      </div>
    </section>

    <!-- Price Section -->
    <!-- <section :class="[$style.section, $style.priceSection]" id="price">
      <div :class="$style.priceSectionContent">
        <h2 class="text-3xl font-bold text-center mb-12 text-gray-200">{{ $t('home.price.introduction') }}</h2>
        <div class="container mx-auto px-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <PlanCard
              v-for="plan in plans"
              :key="plan.type"
              :plan="plan"
              :selected-billing-period="selectedBillingPeriod"
              @select="handlePlanSelect"/>
          </div>
        </div>
      </div>
    </section> -->

    <!-- Team Section -->
    <!-- <section :class="[$style.section, $style.teamSection]" id="team">
      <div class="container mx-auto px-4 h-full flex flex-col justify-center">
        <h2 class="text-3xl font-bold text-center mb-16">{{ $t('home.team.introduction') }}</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto">
          <div class="flex flex-col items-center" v-for="member in teamMembers" :key="member.name">
            <div
              class="w-[115px] h-[115px] rounded-full bg-gray-200 mb-4 overflow-hidden">
              <el-image :src="Emoji" fit="cover"/>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-gray-200">{{ member.name }}</h3>
            <p class="text-gray-300 text-center">{{ member.description }}</p>
          </div>


        </div>
      </div>
    </section> -->

    <!-- Scroll to Top Button -->
    <el-button
      v-show="showScrollTop"
      :class="$style.scrollTopBtn"
      circle
      type="primary"
      @click="scrollToTop">
      <Icon icon="mdi:arrow-up" class="text-[#fff] text-[24px]"/>
    </el-button>

    <!-- Overlay Layer -->
    <AuthDialog
      v-model="showDialog"
      v-model:mode="dialogMode"
      @toStepPage="toStep"
      @toHome="toHome"/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { definePage } from 'vue-router/auto'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router/auto'
import type { LoginMode } from '@/types/login'
import type { NavItem } from './__components__/LoginComponents/types/navigation'
import bgImage from '@/assets/home-bg.svg'

// Components
import TheHeader from './__components__/LoginComponents/TheHeader.vue'
import AuthDialog from './__components__/LoginComponents/AuthDialog.vue'
import HeroContent from './__components__/LoginComponents/HeroContent.vue'
import BreathingLights from './__components__/LoginComponents/BreathingLights.vue'
import PlanCard from '@/components/PlanCard.vue'
import { usePost } from '@/hooks/useHttp'
import { ElMessage } from 'element-plus'
import type { PlanConfig } from './settings/plans/type'
import Emoji from '@/assets/images/robot/emoji.png'
import { Icon } from '@iconify/vue'
import { getToken } from '@/hooks/useToken'
import { useUserStore } from '@/stores/user'

const { execute: addContact } = usePost(
  '/contact/add',
  {},
  {},
  {
    immediate: false,
  }
)
// Page Configuration
definePage({
  meta: {
    layout: false,
  },
})

// Composables
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// State Management
const email = ref('')
const showDialog = ref(false)
const dialogMode = ref<LoginMode>('login')
const currentPlan = ref('basic')
const selectedBillingPeriod = ref<'monthly' | 'annually'>('monthly')

// Navigation Items
const navItems = ref<NavItem[]>([
  { key: 'product', label: t('nav.product') },
  { key: 'price', label: t('nav.price') },
  { key: 'team', label: t('nav.team') },
])

// Team Members Data
const teamMembers = ref([
  { name: '小光', description: '小光' },
  { name: '小黄', description: '小黄' },
  { name: '小李', description: '小李' },
])

// Plans Data
const plans = ref<PlanConfig[]>([
  {
    'name': '免费版本',
    'type': 'free',
    'monthlyPrice': 0,
    'yearlyPrice': 0,
    'billingPeriod': 'monthly',
    'features': [
      '1位成员席位',
      '30份主题报告/月',
      '基础资讯数据',
      '基础PDF报告，不支持格式定制'
    ],
    'description': [
      '入门级版本，低门槛体验产品功能。',
      '适合探索基本出海机会。'
    ]
  },
  {
    'name': '普通收费版本',
    'type': 'pro',
    'monthlyPrice': 99,
    'yearlyPrice': 1070,
    'billingPeriod': 'monthly',
    'features': [
      '3位成员席位',
      '60份主题报告/月',
      '增强资讯数据',
      '高级PDF报告，支持PDF格式定制'
    ],
    'description': [
      '适合需要处理中等数量报告、希望深入分析数据的企业。',
      '提供增强的筛选和定制功能，支持企业团队协作。'
    ]
  },
  {
    'name': '高级收费版本',
    'type': 'business',
    'monthlyPrice': 299,
    'yearlyPrice': 3050,
    'billingPeriod': 'monthly',
    'features': [
      '10位成员席位',
      '不限制报告生成额度',
      '全面资讯数据',
      '高级PDF报告，含动态交互数据、定制化格式等'
    ],
    'description': [
      '为企业提供全方位信息支持，无限制报告生成，满足高强度需求。',
      '包括专属服务和动态分析功能，帮助企业制定战略决策。'
    ]
  }
])

// Event Handlers
const handleTryNow = async (emailValue: string) => {
  console.log('Try now with email:', emailValue)
  await addContact(0, { contact: emailValue })
  ElMessage.success(t('global.contactUsSuccess'))
}

const handlePlanSelect = (plan: any) => {
  currentPlan.value = plan.type
}

const handleLoginClick = () => {
  dialogMode.value = 'login'
  showDialog.value = true
}

const handleRegisterClick = () => {
  // if (!hasInviteCode.value) {
  //   return
  // }
  dialogMode.value = 'register'
  showDialog.value = true
}

const toStep = () => {
  router.push('/poll/finally')
}

const toHome = () => {
  // 直接导航，不重新加载账户
  router.push('/')
}

const userStore = useUserStore()

const checkLoginState = async () => {
  const token = getToken()
  if (token) {
    const result = await userStore.silentRetryLoadAccount()
    if (result) {
      toHome()
    }
  }
}

// Lifecycle
onMounted(() => {
  const inviteCode = route.query.invite_code as string
  const create_company_code = route.query.create_company_code as string
  if (inviteCode || create_company_code) {
    dialogMode.value = 'register'
    showDialog.value = true
  }
  
  // 异步检查登录状态
  checkLoginState()
})

// Refs
const containerRef = ref<HTMLElement | null>(null)
const showScrollTop = ref(false)
const isScrolling = ref(false)

// Enhanced scroll animation function
const scrollToTop = () => {
  if (isScrolling.value) {return}
  isScrolling.value = true
  
  const container = containerRef.value
  if (!container) {return}

  const startPosition = container.scrollTop
  const startTime = performance.now()
  const duration = 800 // Increased duration for smoother feel
  
  // Enhanced easing function for more natural movement
  const easeOutQuint = (t: number): number => {
    return 1 - Math.pow(1 - t, 5)
  }

  const scroll = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    const easedProgress = easeOutQuint(progress)
    container.scrollTop = startPosition * (1 - easedProgress)

    if (progress < 1) {
      requestAnimationFrame(scroll)
    } else {
      isScrolling.value = false
    }
  }

  requestAnimationFrame(scroll)
}

// Optimized scroll detection with RAF
let ticking = false
const handleScroll = () => {
  if (!ticking) {
    requestAnimationFrame(() => {
      if (!containerRef.value) {return}
      
      const productSection = document.getElementById('product')
      const productSectionHeight = productSection?.offsetHeight || 0
      
      showScrollTop.value = containerRef.value.scrollTop > productSectionHeight / 2
      ticking = false
    })
    ticking = true
  }
}

// Cleanup
onUnmounted(() => {
  isScrolling.value = false
  ticking = false
})

// Add computed for invite code check
const hasInviteCode = computed(() => {
  return Boolean(route.query.invite_code)
})
</script>

<style lang="scss" module>
.homeContainer {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: auto;
  scroll-snap-type: y mandatory;
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position; // 优化滚动性能
}

.section {
  position: relative;
  height: 100vh;
  width: 100%;
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

.productSection {
  background-color: transparent;
}

.priceSection {
  background: linear-gradient(135deg, rgba(239, 246, 255, 0.5) 0%, rgba(219, 234, 254, 0.5) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.priceSection {
  background: linear-gradient(135deg, #324C8C 0%, #5B7ED1 100%);
  align-items: center;
  justify-content: center;
}

.teamSection {
  background: linear-gradient(135deg, #4466BC 0%, #45A7C5 100%);
  padding: 4rem 0;
}

.ctaSection {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 6rem 0;
  background: linear-gradient(135deg, rgba(68, 102, 188, 0.05) 0%, rgba(68, 102, 188, 0.25) 100%);
  text-align: center;
}

.scrollTopBtn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 10;
  width: 3rem !important;
  height: 3rem !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, opacity; // 优化动画性能

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
