import { onMounted, onBeforeUnmount, type Ref, type ComputedRef } from 'vue'
import tippy, { type Instance as TippyInstance, type Props } from 'tippy.js'
import 'tippy.js/dist/tippy.css'
import 'tippy.js/themes/light.css'
import type { url } from '@/pages/format-report/type'

interface UseTippyReferenceOptions {
  containerRef: Ref<HTMLElement | null>;
  searchResultItems: ComputedRef<url[]>;
}

export function useTippyReference({ containerRef, searchResultItems }: UseTippyReferenceOptions) {
  const tippyInstances = new WeakMap<HTMLElement, TippyInstance>()
  const contentCache = new Map<string, HTMLElement>()
  
  let isInitializing = false
  let rafId: number | null = null

  const createTippyContent = (id: string) => {
    if (contentCache.has(id)) {
      return contentCache.get(id)!.cloneNode(true) as HTMLElement
    }
    
    if (!Array.isArray(searchResultItems.value)) {
      console.warn('searchResultItems is not an array:', searchResultItems.value)
      return document.createElement('div')
    }

    const result = searchResultItems.value.find(item => {
      const itemId = item.id?.toString()
      return itemId === id
    }) ?? searchResultItems.value.find((_, index) => index === Number(id))
    console.log('result', result)
    if (!result) {
      const emptyContent = document.createElement('div')
      emptyContent.textContent = '暂无内容'
      contentCache.set(id, emptyContent)
      return emptyContent.cloneNode(true) as HTMLElement
    }

    const content = document.createElement('div')
    content.className = 'p-1 max-w-[400px] tippy-content-wrapper'
    
    const fragment = document.createDocumentFragment()
    const wrapper = document.createElement('div')
    wrapper.className = 'flex flex-col gap-3'
    
    const innerWrapper = document.createElement('div')
    innerWrapper.className = 'flex items-center gap-2'
    
    if (result.favicon) {
      const img = document.createElement('img')
      img.src = result.favicon
      img.className = 'w-4 h-4'
      img.alt = 'favicon'
      innerWrapper.appendChild(img)
    }
    
    const link = document.createElement('a')
    link.href = result.url || '#'
    link.target = '_blank'
    link.className = 'text-blue-500 hover:text-blue-700 truncate'
    link.textContent = result.title || result.text || '无标题'
    
    innerWrapper.appendChild(link)
    wrapper.appendChild(innerWrapper)

    // if (result.description) {
    //   const description = document.createElement('div')
    //   description.className = 'text-sm text-gray-600'
    //   description.textContent = result.description
    //   wrapper.appendChild(description)
    // }
    
    fragment.appendChild(wrapper)
    content.appendChild(fragment)
    
    contentCache.set(id, content)
    return content.cloneNode(true) as HTMLElement
  }

  const createTippyConfig = (content: HTMLElement): Partial<Props> => ({
    content,
    allowHTML: true,
    placement: 'top',
    theme: 'light',
    animation: 'scale',
    arrow: true,
    interactive: true,
    maxWidth: 400,
    appendTo: document.body,
    duration: [200, 150],
    delay: [50, 50],
    inertia: true,
    hideOnClick: false,
    touch: true,
    zIndex: 9999,
    onCreate(instance) {
      instance.popper.addEventListener('transitionend', () => {
        if (!instance.state.isVisible) {
          instance.popper.style.willChange = 'auto'
        }
      })
    },
    onMount(instance) {
      instance.popper.style.willChange = 'transform'
    }
  })

  // const handleReferenceClick = (element: HTMLElement) => {
  //   if (!containerRef.value) {return}
    
  //   const referenceId = element.dataset.referenceId
  //   if (!referenceId) {return}
    
  //   const targetElement = containerRef.value.querySelector(`[data-reference-id="${referenceId}"]`)
  //   console.log('targetElement', targetElement)
  //   if (targetElement) {
  //     targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
  //     targetElement.classList.add('highlight-search-result')
  //     setTimeout(() => {
  //       targetElement.classList.remove('highlight-search-result')
  //     }, 3000)
  //   }
  // }

  const initializeReferenceLinks = () => {
    if (!containerRef.value || isInitializing) {return}

    isInitializing = true
    
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
    }

    rafId = requestAnimationFrame(() => {
      const referenceLinks = containerRef.value?.querySelectorAll<HTMLElement>('span[data-reference-id]')
      if (!referenceLinks) {return}

      referenceLinks.forEach(element => {
        // 使用元素的唯一ID作为tippy实例的键
        const uniqueId = element.id
        
        // 如果已经有实例且实例有效，跳过
        const existingInstance = tippyInstances.get(element)
        if (existingInstance?.state?.isEnabled) {return}

        // 如果有旧实例，先销毁
        if (existingInstance) {
          existingInstance.destroy()
          tippyInstances.delete(element)
        }
        
        const referenceId = element.dataset.referenceId
        if (!referenceId) {return}
        
        const content = createTippyContent(referenceId)
        const instance = tippy(element, createTippyConfig(content))
        tippyInstances.set(element, instance)
        
      })

      rafId = null
      isInitializing = false
    })
  }

  const cleanupReferenceLinks = () => {
    if (!containerRef.value) {return}
    
    const referenceLinks = containerRef.value.querySelectorAll<HTMLElement>('span[data-reference-id]')
    referenceLinks.forEach(element => {
      const instance = tippyInstances.get(element)
      if (instance) {
        instance.destroy()
        tippyInstances.delete(element)
      }
      
    })
  }

  let observer: MutationObserver | null = null

  onMounted(() => {
    initializeReferenceLinks()
    
    observer = new MutationObserver((mutations) => {
      const hasNewReferenceLinks = mutations.some(mutation => {
        return Array.from(mutation.addedNodes).some(node => {
          if (node instanceof HTMLElement) {
            return node.querySelector('span[id^="ref-"]') !== null
          }
          return false
        })
      })

      if (hasNewReferenceLinks) {
        initializeReferenceLinks()
      }
    })

    if (containerRef.value) {
      observer.observe(containerRef.value, {
        childList: true,
        subtree: true
      })
    }
  })

  onBeforeUnmount(() => {
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
    }
    
    cleanupReferenceLinks()
    contentCache.clear()
    
    if (observer) {
      observer.disconnect()
      observer = null
    }
  })

  return {
    initializeReferenceLinks,
    cleanupReferenceLinks
  }
} 