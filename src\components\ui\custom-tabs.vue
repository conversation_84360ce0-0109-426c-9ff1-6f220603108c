<template>
  <div class="custom-tabs-root flex items-center gap-4">
    <!-- 自定义标签页列表 - 样式与 shadcn-vue TabsList 一致 -->
    <div 
      :class="[
        'inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground',
        tabsListClass
      ]">
      <button
        v-for="tab in tabs"
        :key="tab.value"
        :class="[
          'inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
          modelValue === tab.value
            ? 'bg-background text-foreground shadow'
            : 'hover:bg-background/50 hover:text-foreground',
          tab.disabled ? 'opacity-50 pointer-events-none' : 'cursor-pointer'
        ]"
        :disabled="tab.disabled"
        @click="handleTabClick(tab.value)">
        <span class="flex items-center gap-1.5">
          {{ tab.label }}
          <!-- 加载指示器 -->
          <span 
            v-if="tab.loading" 
            class="w-2 h-2 bg-sky-400 rounded-full animate-ping">
          </span>
        </span>
      </button>
    </div>

    <!-- 子标签页 - 仅在指定标签页激活时显示 -->
    <div 
      v-if="subTabs && subTabs.length > 0 && showSubTabs" 
      class="flex items-center ml-4">
      <span class="text-sm text-muted-foreground mr-2">·</span>
      <div class="inline-flex h-8 w-fit items-center justify-center rounded-md bg-muted/50 p-[2px]">
        <button
          v-for="subTab in subTabs"
          :key="subTab.value"
          :class="[
            'inline-flex h-[calc(100%-1px)] items-center justify-center gap-1 rounded-sm border border-transparent px-2.5 py-1 text-xs font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[2px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50',
            subTabValue === subTab.value 
              ? 'bg-background text-foreground shadow-sm' 
              : 'text-muted-foreground hover:text-foreground cursor-pointer',
            subTab.disabled ? 'opacity-50 pointer-events-none' : 'cursor-pointer'
          ]"
          :disabled="subTab.disabled"
          @click="handleSubTabClick(subTab.value)">
          <span class="flex items-center gap-1">
            {{ subTab.label }}
            <!-- 子标签页加载指示器 -->
            <span 
              v-if="subTab.loading" 
              class="w-1.5 h-1.5 bg-sky-400 rounded-full animate-ping">
            </span>
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface TabItem {
  value: string
  label: string
  disabled?: boolean
  loading?: boolean
}

export interface SubTabItem {
  value: string
  label: string
  disabled?: boolean
  loading?: boolean
}

interface Props {
  modelValue: string
  tabs: TabItem[]
  subTabs?: SubTabItem[]
  subTabValue?: string
  subTabParent?: string // 哪个主标签页显示子标签页
  tabsListClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  subTabs: () => [],
  subTabParent: '',
  tabsListClass: ''
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'update:subTabValue': [value: string]
  'tab-change': [tab: TabItem]
  'sub-tab-change': [subTab: SubTabItem]
}>()

// 是否显示子标签页 - 修改逻辑以支持动态子标签页
const showSubTabs = computed(() => {
  // 如果没有设置 subTabParent，则根据 subTabs 是否有内容来决定
  if (!props.subTabParent) {
    return props.subTabs && props.subTabs.length > 0
  }
  // 如果设置了 subTabParent，则需要当前标签页匹配且有子标签页内容
  return props.modelValue === props.subTabParent && props.subTabs && props.subTabs.length > 0
})

// 处理主标签页点击
const handleTabClick = (value: string) => {
  if (value === props.modelValue) {return}
  
  const tab = props.tabs.find(t => t.value === value)
  if (!tab || tab.disabled) {return}
  
  emit('update:modelValue', value)
  emit('tab-change', tab)
}

// 处理子标签页点击
const handleSubTabClick = (value: string) => {
  if (value === props.subTabValue) {return}
  
  const subTab = props.subTabs?.find(t => t.value === value)
  if (!subTab || subTab.disabled) {return}
  
  emit('update:subTabValue', value)
  emit('sub-tab-change', subTab)
}

// 暴露方法给父组件
defineExpose({
  switchToTab: (value: string) => handleTabClick(value),
  switchToSubTab: (value: string) => handleSubTabClick(value)
})
</script>

<style scoped>
/* 确保动画效果与 shadcn-vue 一致 */
.custom-tabs-root {
}

/* 加载动画 */
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* 焦点样式 */
button:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* 过渡效果 */
button {
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 激活状态 */
button:active:not(:disabled) {
  transform: translateY(0);
}
</style>
