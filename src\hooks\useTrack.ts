import { storeToRefs } from 'pinia'
import { usePost } from './useHttp'
import dayjs from 'dayjs'
import { useDebounceFn } from '@vueuse/core'
import type { UnwrapRef } from 'vue'
import { onBeforeUnmount } from 'vue'

// 定义事件名称枚举
export enum TrackEventName {
  PAGE_VIEW = 'page_view',
  BUTTON_CLICK = 'button_click',
  FORM_SUBMIT = 'form_submit',
}

// 定义事件数据接口
export interface TrackEventData {
  page?: string
  component?: string
  action?: string
  value?: unknown
  timestamp?: number
  [key: string]: unknown
}

// 定义跟踪选项接口
interface TrackOptions {
  immediate: boolean
  retry: number
  debounce: number
}

// 创建重试工具函数
const createRetryableTask = async <T>(
  task: () => Promise<T>,
  retryCount: number,
  onError?: (error: unknown) => void
): Promise<T | undefined> => {
  let attempts = retryCount

  while (attempts > 0) {
    try {
      return await task()
    } catch (error) {
      attempts--
      if (attempts === 0 && onError) {
        onError(error)
      }
    }
  }
}

// 创建事件追踪器工厂
const createEventTracker = (
  track: (payload: unknown) => Promise<unknown>,
  options: TrackOptions
) => {
  // 存储不同事件和防抖时间组合的防抖函数
  const debouncedFunctions = new Map<string, ReturnType<typeof useDebounceFn>>()

  // 创建或获取防抖函数
  const getDebouncedTrack = (event: string, debounceTime: number) => {
    const key = `${event}_${debounceTime}`
    if (!debouncedFunctions.has(key)) {
      const debouncedFn = useDebounceFn(executeTrack, debounceTime)
      debouncedFunctions.set(key, debouncedFn)
    }
    return debouncedFunctions.get(key)!
  }

  const createTrackPayload = (event: string, data: TrackEventData) => ({
    event,
    data: {
      ...data,
      timestamp: dayjs().valueOf(),
      format_timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },
  })

  const executeTrack = async (event: string, data: TrackEventData) => {
    const payload = createTrackPayload(event, data)
    return createRetryableTask(
      () => track(payload),
      options.retry,
      (error) => console.error('Failed to track event after multiple retries:', error)
    )
  }

  // 清理函数
  const cleanup = () => {
    debouncedFunctions.clear()
  }

  const trackWithOptions = (event: string, data: TrackEventData, customOptions?: Partial<TrackOptions>) => {
    const debounceTime = customOptions?.debounce ?? options.debounce
    
    if (debounceTime > 0) {
      const debouncedFn = getDebouncedTrack(event, debounceTime)
      return debouncedFn(event, data)
    }
    
    return executeTrack(event, data)
  }

  return {
    trackWithOptions,
    cleanup
  }
}

export const useTrack = (defaultOptions: Partial<TrackOptions> = {}) => {
  const options: TrackOptions = {
    immediate: false,
    retry: 3,
    debounce: 0,
    ...defaultOptions,
  }

  const { execute: _track } = usePost('/user_record/add', {}, {
    showError: false,
    autoCancel: false
  }, {
    immediate: options.immediate,
  })
  
  const track = (payload: unknown) => _track(0, payload)
  const { trackWithOptions, cleanup } = createEventTracker(track, options)

  // 组件卸载时清理防抖函数
  onBeforeUnmount(() => {
    cleanup()
  })

  const trackEvent = async (
    event: TrackEventName | string,
    data: TrackEventData = {},
    customOptions?: Partial<TrackOptions>
  ) => {
    return trackWithOptions(event, data, customOptions)
  }

  const pageView = (pageName: string, extraData?: Partial<TrackEventData>) => {
    return trackEvent(TrackEventName.PAGE_VIEW, {
      page: pageName,
      ...extraData,
    })
  }

  const buttonClick = (componentName: string, extraData?: Partial<TrackEventData>) => {
    return trackEvent(TrackEventName.BUTTON_CLICK, {
      component: componentName,
      ...extraData,
    })
  }

  return {
    trackEvent,
    pageView,
    buttonClick,
  }
}