<template>
  <el-select-v2
    v-model="value"
    :options="options"
    v-bind="omit(attrs, ['options'])"
    :popperClass="[props.hiddenCheckedIcon ? $style.hideCheckedIcon : '', camelCaseAttrs.popperClass || '', isMultiple ? 'is-multiple' : '',].join(' ')"
    :filterable="enabledFilter"
    :filter-method="filterMethod"
    @change="handleChange">
    <template #header v-if="isMultiple">
      <div :class="$style.header">
        <div class="flex items-center gap-2">
          {{ $t("component.selectedCount", [val.length]) }}
          <el-link
            v-show="val.length > 0"
            type="primary"
            :underline="false"
            class="text-xs"
            @click.stop="clear">
            {{ $t("button.clear") }}
          </el-link>
        </div>
        <el-link
          type="primary"
          :underline="false"
          :disabled="isAllChecked"
          class="text-xs"
          @click.stop="selectAll">
          {{ $t("button.selectAll") }}
        </el-link>
      </div>
    </template>
    <template #default="{ item }">
      <div :class="$style.option">
        <div :class="$style.optionAvatar">
          <el-checkbox
            style="margin-right: 10px"
            v-model="item.checked"
            :disabled="item.disabled"
            @click.stop/>
          <!-- <Avatar v-if="hasAvatar" :url="item.avatar" class="mr-4" :key="item.avatar" /> -->
          <div :class="$style.optionLabel">
            <overflow-text>
              {{ item.label }}
            </overflow-text>
          </div>
        </div>

        <span
          :class="$style.onlySelf"
          @click.stop="onlySee(item.value)">
          {{ $t("global.onlySeeSelf") }}
        </span>
      </div>
    </template>
  </el-select-v2>
</template>
<script setup lang="ts">
import { computed, ref, watch, type PropType } from 'vue'
import { type ISelectV2Props } from 'element-plus'
import { useVModel } from '@vueuse/core'
import { useCamelCaseAttrs } from '@/hooks/useCamelCaseAttrs'
import { isNil, omit } from 'lodash-es'
// import Avatar from '@/pages/__components__/Avatar.vue'
import overflowText from '@/components/overflow-text.vue'
import { useSelectFilter } from '@/hooks/useSelectFilter'

type SelectModelValue = ISelectV2Props['modelValue']
type SelectOption = ISelectV2Props['options'][number]

defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  modelValue: {
    type: [String, Array, Object, Number, Boolean] as PropType<SelectModelValue>,
    default: () => [],
  },
  hiddenCheckedIcon: {
    type: Boolean,
    default: false,
  },
  hasAvatar:{
    type: Boolean,
    default: false,
  }
})
const emit = defineEmits<{
  'update:modelValue': [value: SelectModelValue]
  'change':[value:SelectModelValue]
}>()
const value = useVModel(props, 'modelValue', emit)
const val = computed(() => isNil(value.value) ? [] : Array.isArray(value.value) ? value.value : [value.value])

const { attrs, camelCaseAttrs } = useCamelCaseAttrs()
const options = ref<SelectOption[]>([])
const realOptions = computed(() => attrs.options as SelectOption[] || [])
const { enabledFilter, filterMethod, filterOptions } = useSelectFilter(realOptions, {
  filterMethod: (query: string) => {
    initialOptions(query)
  }
})
const isMultiple = computed(() => attrs.multiple === '' || !!attrs.multiple ? true : false)

const updateValue = (val: SelectModelValue) => {
  val = Array.isArray(val) ? [ ...new Set(val) ] : val
  if (isMultiple.value && Array.isArray(val) && camelCaseAttrs.multipleLimit) {
    val = val.slice(0, camelCaseAttrs.multipleLimit as number)
  }
  if (!Array.isArray(val) && isMultiple.value) {
    value.value = [val]
    return
  }
  value.value = val
  emit('change', val)
}
const clear = () => {
  updateValue([])
}
const handleChange = ()=>{
  emit('change', value.value)
}
const allOptionValues = computed(() => options.value.map(option => option.value))
const selectAll = () => {
  updateValue(allOptionValues.value)
}
const onlySee = (val:SelectModelValue)=>{
  updateValue([val])
}
const initialOptions = (filter?: string)=>{
  options.value = filterOptions((attrs.options || []) as SelectOption[], filter)

  options.value.forEach(option =>{
    option.checked = computed({
      get: () => value.value.includes(option.value),
      set: (v) => {
        if (option.disabled) {
          return
        }
        updateValue(v ? [...value.value, option.value] : value.value.filter((vv:number) => vv !== option.value))
      }
    })
    option.disabled = computed(()=> value.value.length >= (camelCaseAttrs.multipleLimit as number) && !value.value.includes(option.value))
  })
}
const isAllChecked = computed(() => val.value.length === allOptionValues.value.length)
watch(() => attrs.options, () => {
  // options.value = (attrs.options || []) as SelectOption[]
  initialOptions()
}, { immediate: true })
</script>
<style lang="scss" module>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.hideCheckedIcon {
  :global(
      .el-select-dropdown.is-multiple
        .el-select-dropdown__item.is-selected:after
    ) {
    display: none;
  }
}
.onlySelf {
  flex: initial;
  font-size: var(--size-font-9);
  visibility: hidden;
  color: var(--primary-color);
}
.option {
  display: flex;
  justify-content: space-between;

  :deep(.el-select-panel__item) {
    padding-left: 24px !important;
  }

  &:hover {
    .onlySelf {
      visibility: visible;
    }
  }

  .optionAvatar {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    .optionLabel{
      display: flex;
      overflow: hidden;
      :global(.el-text){
        color: var(--el-text-color) !important;
      }
    }
  }
}
</style>
