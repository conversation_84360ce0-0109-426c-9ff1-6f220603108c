import { ref } from 'vue'
import type { ChartImageOptions, ProcessedImage, ChartImageError } from './types'

const DEFAULT_OPTIONS: ChartImageOptions = {
  maxWidth: 1200,
  quality: 0.85,
  retries: 3,
  devicePixelRatio: window.devicePixelRatio || 1,
  format: 'jpeg'
}

export const useChartImage = () => {
  const processing = ref<Set<string>>(new Set())
  const imageCache = ref<Map<string, ProcessedImage>>(new Map())

  const processImage = async (
    base64: string,
    chartId: string,
    options: Partial<ChartImageOptions> = {}
  ): Promise<ProcessedImage> => {
    const mergedOptions = { ...DEFAULT_OPTIONS, ...options }
    const cacheKey = options.cacheKey || chartId

    // Check cache
    if (imageCache.value.has(cacheKey)) {
      return imageCache.value.get(cacheKey)!
    }

    processing.value.add(chartId)

    try {
      const result = await compressImage(base64, chartId, mergedOptions)
      imageCache.value.set(cacheKey, result)
      return result
    } finally {
      processing.value.delete(chartId)
    }
  }

  const compressImage = async (
    base64: string,
    chartId: string,
    options: ChartImageOptions,
    attempt = 1
  ): Promise<ProcessedImage> => {
    try {
      const img = await loadImage(base64)
      const { width, height } = calculateDimensions(img, options.maxWidth!)
      
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        throw new Error('Failed to get canvas context')
      }

      // Set canvas size considering device pixel ratio
      canvas.width = width * options.devicePixelRatio!
      canvas.height = height * options.devicePixelRatio!
      
      // Scale context for retina display
      ctx.scale(options.devicePixelRatio!, options.devicePixelRatio!)
      
      // Draw with high quality
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = 'high'
      
      ctx.drawImage(img, 0, 0, width, height)

      const compressedDataUrl = canvas.toDataURL(
        `image/${options.format}`,
        options.quality
      )

      return {
        id: chartId,
        dataUrl: compressedDataUrl,
        width,
        height,
        originalSize: base64.length,
        compressedSize: compressedDataUrl.length
      }
    } catch (error) {
      if (attempt < options.retries!) {
        // Retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100))
        return compressImage(base64, chartId, options, attempt + 1)
      }
      
      const chartError = new Error(
        `Failed to process chart image: ${(error as any).message}`
      ) as ChartImageError
      chartError.chartId = chartId
      chartError.attempt = attempt
      throw chartError
    }
  }

  const loadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = src
    })
  }

  const calculateDimensions = (
    img: HTMLImageElement,
    maxWidth: number
  ) => {
    const scale = maxWidth / img.width
    return {
      width: maxWidth,
      height: Math.round(img.height * scale)
    }
  }

  const clearCache = () => {
    imageCache.value.clear()
  }

  return {
    processImage,
    processing,
    clearCache
  }
} 