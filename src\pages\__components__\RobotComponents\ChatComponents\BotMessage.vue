<template>
  <div class="flex items-start justify-start w-full h-full mr-auto">
    <el-avatar :src="avatarSrc" class="bg-white mr-[20px]" :size="60"></el-avatar>
    <div :class="$style.botMessage">
      <div class="bg-[#fff] p-4 rounded-lg shadow-lg text-[16px] markdown-body prose w-full max-w-none" v-html="markdownToHtml(content)"/>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import { ElAvatar } from 'element-plus'
import robotAvatar from '@/assets/images/robot/emoji.png'
import { markdownToHtml } from '@/utils/markdown'

const props = defineProps<{
  content: string;
  avatar?: string;
}>()

const avatarSrc = computed(() => props.avatar || robotAvatar)
</script>

<style lang="scss" module>
.botMessage {
  width: 100%;
  height: 100%;

  :global(.markdown-body) {
    img {
      max-width: 800px;
      max-height: 600px;
      object-fit: contain;
    }
  }
}
</style>
