<template>
  <div :class="$style.specificIndex">
    <SidebarProvider>
      <el-container direction="horizontal" :class="$style.specificContainer">
        <AppSidebar/>
        <SidebarInset class="overflow-auto">
          <el-container direction="vertical" :class="$style.wrap">
            <header class="flex h-16 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear px-4">
              <div class="flex items-center gap-2 flex-1">
                <SidebarTrigger class="-ml-1"/>
              </div>

              <div class="ml-auto flex items-center gap-4">
                <TaskListPopover/>
              </div>
            </header>
            
            <el-main :style="{ background: '#fff' }" :class="[$style.main]" ref="mainRef">
              <RouterView v-slot="{ Component }">
                <Transition mode="out-in">
                  <KeepAlive :include="keepAliveNames">
                    <Suspense>
                      <!-- 主要内容 -->
                      <div class="w-full h-full overflow-auto">
                        <component :is="Component"></component>
                      </div>

                      <!-- 加载中状态 -->
                      <template #fallback>
                        <div class="h-full w-full flex justify-center items-center">
                          <div class="w-[50%]">
                            <el-skeleton :rows="5" animated/>
                          </div>
                        </div>
                      </template>
                    </Suspense>
                  </KeepAlive>
                </Transition>
              </RouterView>
            </el-main>
          </el-container>
        </SidebarInset>
      </el-container>
      <STour 
        v-model:visible="isVisible" 
        :config="currentConfig"
        @finish="handleFinish"
        v-if="currentConfig"/>
    </SidebarProvider>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useKeepAliveRoutes } from '@/stores/keepAliveRoutes'
import { STour } from '@/components/STour'
import { useTourState } from '@/components/STour/composables/useTourState'
import AppSidebar from '@/components/AppSidebar.vue'
import { useRoute, useRouter } from 'vue-router/auto'
import { useI18n } from 'vue-i18n'
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger
} from '@/components/ui/sidebar'
import TaskListPopover from '@/components/TaskListPopover.vue'

const { keepAliveNames } = useKeepAliveRoutes()
const mainRef = ref<HTMLElement | null>(null)
const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// 使用tour状态管理
const { isVisible, currentConfig, handleFinish } = useTourState()

// 处理面包屑数据
const breadcrumbItems = computed(() => {
  // 获取当前路径，但忽略查询参数和哈希
  const path = route.path
  const pathSegments = path.split('/').filter(Boolean)
  
  // 最多返回三级路由（不包括首页，因为首页已经单独处理）
  const items = []
  
  // 构建面包屑项，最多处理三层
  for (let i = 0; i < Math.min(pathSegments.length, 3); i++) {
    const segment = pathSegments[i]
    const segmentPath = '/' + pathSegments.slice(0, i + 1).join('/')
    
    items.push({
      name: t(`menu.${segment}`) || segment,
      path: segmentPath
    })
  }
  
  return items
})
</script>

<style lang="scss" module>
$menuBg: #33344b;
$defaultColor: #b7b6cd;
$activeColor: #fff;

.specificIndex {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.specificContainer {
  height: 100%;
  flex: 1;
}

.main {
  height: 100%;
  padding: 0 !important;
  overflow: auto;
}

.wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>

<style lang="scss">
</style>
