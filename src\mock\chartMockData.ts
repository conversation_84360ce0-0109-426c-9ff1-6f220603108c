import type { SSEMessage } from '@/hooks/useEnhancedEventSource'

// Word cloud mock data
export const mockWordCloudData: SSEMessage = {
  type: 'REPORT_CHARTS',
  chart_type: 'word_cloud',
  data: [
    // 技术相关 (Technology related)
    { name: 'Vue', value: 100 },
    { name: 'React', value: 85 },
    { name: 'TypeScript', value: 80 },
    { name: 'JavaScript', value: 75 },
    { name: 'Node.js', value: 70 },
    { name: 'Docker', value: 65 },
    { name: 'Kubernetes', value: 60 },
    { name: 'AWS', value: 55 },
    
    // 开发工具 (Development tools)
    { name: 'Git', value: 50 },
    { name: 'VSCode', value: 45 },
    { name: 'Webpack', value: 40 },
    { name: 'Vite', value: 35 },
    
    // 框架和库 (Frameworks and libraries)
    { name: 'Express', value: 30 },
    { name: 'NestJS', value: 28 },
    { name: '<PERSON><PERSON>windCSS', value: 25 },
    { name: 'Redux', value: 23 },
    
    // 数据库 (Databases)
    { name: 'MongoDB', value: 20 },
    { name: 'PostgreSQL', value: 18 },
    { name: 'MySQL', value: 15 },
    { name: 'Redis', value: 13 },
    
    // 测试工具 (Testing tools)
    { name: 'Jest', value: 12 },
    { name: 'Cypress', value: 10 },
    { name: 'Vitest', value: 8 },
    { name: 'Playwright', value: 7 },
    
    // 其他工具 (Other tools)
    { name: 'ESLint', value: 6 },
    { name: 'Prettier', value: 5 },
    { name: 'Babel', value: 4 },
    { name: 'NPM', value: 3 },
    { name: 'Yarn', value: 2 }
  ]
}

// 生成动态词云数据的函数 (Function to generate dynamic word cloud data)
export const generateWordCloudData = (
  baseWords: string[] = ['Vue', 'React', 'Angular', 'Svelte'],
  minValue = 1,
  maxValue = 100,
  count = 30
): SSEMessage => {
  const data = baseWords
    .map(name => ({
      name,
      value: Math.floor(Math.random() * (maxValue - minValue) + minValue)
    }))
    .sort((a, b) => b.value - a.value)
    
  // 生成额外的随机词条 (Generate additional random entries)
  const additionalWords = [
    'TypeScript', 'JavaScript', 'Python', 'Java', 'Go',
    'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP',
    'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'ElasticSearch',
    'Webpack', 'Vite', 'Rollup', 'Parcel', 'ESBuild',
    'TailwindCSS', 'SCSS', 'Less', 'Styled-Components', 'CSS-in-JS'
  ]
  
  while (data.length < count) {
    const name = additionalWords[Math.floor(Math.random() * additionalWords.length)]
    if (!data.find(item => item.name === name)) {
      data.push({
        name,
        value: Math.floor(Math.random() * (maxValue - minValue) + minValue)
      })
    }
  }
  
  return {
    type: 'REPORT_CHARTS',
    chart_type: 'word_cloud',
    data: data.sort((a, b) => b.value - a.value)
  }
}

// 使用示例 (Usage example):
/*
// 固定数据 (Fixed data)
const wordCloudData = mockWordCloudData

// 动态数据 (Dynamic data)
const dynamicWordCloudData = generateWordCloudData(
  ['Vue', 'React', 'Angular'], // 基础词条 (base words)
  10,  // 最小值 (min value)
  100, // 最大值 (max value)
  20   // 词条数量 (entry count)
)
*/ 