import { ref } from 'vue'
import { CUSTOM_FONTS } from './constants'
import { resolveUrl } from '@/utils'
import { HybridFontCache } from './HybridFontCache'

export const isFontLoaded = ref(false)
const FONT_LOAD_TIMEOUT = 10000
const MAX_RETRIES = 3

const createModuleWrapper = (fontText: string): string => `
  const exports = {};
  const module = { exports };
  ${fontText.replace('export default', 'module.exports =')}
  export default module.exports;
`

const loadFontModule = async (fontText: string) => {
  const blob = new Blob([createModuleWrapper(fontText)], {
    type: 'application/javascript',
  })
  const url = URL.createObjectURL(blob)

  try {
    const fontModule = await import(/* @vite-ignore */ url)
    return fontModule.default
  } finally {
    URL.revokeObjectURL(url)
  }
}

const fetchWithTimeout = async (url: string, timeout: number) => {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
      signal: controller.signal
    })
    return response
  } finally {
    clearTimeout(timeoutId)
  }
}

export const useFonts = () => {
  const fontCache = new HybridFontCache()

  const getPdfMake = (): typeof window.pdfMake => {
    if (!window.pdfMake) {
      throw new Error('pdfMake is not loaded')
    }
    return window.pdfMake
  }

  const loadFontsFromCDN = async (retryCount = 0): Promise<any> => {
    try {
      const response = await fetchWithTimeout(
        'https://www.goglobalsp.com/dist/font.js',
        FONT_LOAD_TIMEOUT
      )

      if (!response.ok) {
        throw new Error(`Failed to fetch fonts: ${response.statusText}`)
      }

      const fontText = await response.text()
      return await loadFontModule(fontText)
    } catch (error) {
      if (retryCount < MAX_RETRIES) {
        console.warn(`Retrying font load, attempt ${retryCount + 1}`)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000))
        return loadFontsFromCDN(retryCount + 1)
      }
      throw error
    }
  }

  const loadFonts = async (): Promise<void> => {
    const pdfMake = getPdfMake()

    if (isFontLoaded.value) {return}

    // 尝试从缓存加载
    const cachedData = await fontCache.getFonts()
    if (cachedData) {
      pdfMake.vfs = cachedData.fonts
      pdfMake.fonts = CUSTOM_FONTS
      isFontLoaded.value = true
      return
    }

    // 从 CDN 加载字体
    try {
      const vfsContent = await loadFontsFromCDN()

      if (vfsContent) {
        pdfMake.vfs = vfsContent
        pdfMake.fonts = CUSTOM_FONTS

        // 保存到缓存
        await fontCache.saveFonts(vfsContent)
        isFontLoaded.value = true
        console.debug('Fonts loaded and cached')
      }
    } catch (error) {
      console.error('Failed to load custom fonts:', error)
      isFontLoaded.value = false
    }
  }

  const clearFontCache = async (): Promise<void> => {
    await fontCache.clearCache()
    isFontLoaded.value = false
    console.debug('Font cache cleared')
  }

  return {
    loadFonts,
    clearFontCache,
    isFontLoaded,
  }
} 