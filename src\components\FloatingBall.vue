<template>
  <el-popover
    v-model:visible="showPopover"
    :width="380"
    trigger="click"
    placement="left"
    popper-class="task-popover"
    :show-arrow="false"
    :offset="12"
    :teleported="true"
    @show="handlePopoverShow">
    <template #reference>
      <div
        ref="ballRef"
        :style="{
          left: x + 'px',
          top: y + 'px',
          transition: isDragging ? 'none' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        }"
        tour-id="format_report-floating-ball"
        class="fixed cursor-move z-50">
        <div class="floating-ball w-[60px] h-[60px] rounded-full flex items-center justify-center relative" :class="{ 'cursor-not-allowed': !tasks.length }" v-loading="loading">
          <svg class="ball-icon w-6 h-6 relative z-[2]"
               viewBox="0 0 24 24"
               fill="none"
               stroke="white"
               stroke-width="2">
            <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
          </svg>
          <div v-if="tasks.length" class="notification-badge">{{ tasks.length }}</div>
        </div>
      </div>
    </template>

    <!-- Task List Content -->
    <div class="task-list">
      <div class="flex justify-between items-center p-4 border-b border-[var(--color-border-2)]">
        <h3 class="text-[var(--font-color-1)] text-base font-semibold">Task List</h3>
        <button
          class="w-6 h-6 rounded-full hover:bg-[var(--gray-transparent-hover)] transition-colors flex items-center justify-center"
          @click="showPopover = false">
          <svg viewBox="0 0 24 24"
               class="w-3.5 h-3.5"
               fill="none"
               stroke="currentColor"
               stroke-width="2">
            <path d="M18 6L6 18M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <div class="max-h-[400px] overflow-y-auto p-4">
        <div
          v-for="task in tasks"
          :key="task.id"
          class="task-item bg-[var(--gray-1)] rounded-lg p-4 mb-3 border border-[var(--color-border-2)] transition-all hover:shadow-hover">
          <div class="flex justify-between items-center mb-3">
            <span class="text-[var(--font-color-1)] text-sm">{{ task.report_name }}</span>
          </div>
          <div class="progress-container">
            <div class="flex justify-between items-center mb-1">
              <span class="text-xs text-[var(--font-color-2)]">{{ formatStatus(task.status) }}</span>
              <span class="text-xs font-medium text-[var(--font-color-1)]">{{ task.progress }}%</span>
            </div>
            <div class="h-1 bg-[var(--gray-3)] rounded-sm overflow-hidden">
              <div
                class="h-full transition-all duration-300 relative"
                :style="{
                  width: `${task.progress}%`,
                  background: getProgressBackground(task)
                }">
                <div class="absolute inset-0 shine-effect"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, watch } from 'vue'
import { useDraggable, useStorage } from '@vueuse/core'
import { useGet } from '@/hooks/useHttp'
import emitter from '@/utils/events'

interface Task {
  id: number
  report_name: string
  status: string
  date?: string
  priority: 'high' | 'medium' | 'low'
  progress: number
  sync_type: boolean
}

const props = defineProps<{
  initialX?: number
  initialY?: number
}>()

const tasks = ref<Task[]>([])
const showPopover = ref(false)
const isDragging = ref(false)

const { execute: fetchProgress, isLoading: loading } = useGet<Task[]>(
  '/formatted_report/check_report_list',
  {
    showError: false
  },
  { immediate: false }
)

emitter.on('async:update', () => {
  fetchProgressReports()
})

const fetchProgressReports = async () => {
  try {
    const data = await fetchProgress()
    if (Array.isArray(data)) {
      tasks.value = data
        .filter(task => !task.sync_type && task.status === 'running') 
        .map(task => ({
          ...task,
          priority: getPriority(task.status)
        }))
    }
  } catch (error) {
    console.error('Failed to fetch progress reports:', error)
    tasks.value = []
  }
}

const getPriority = (status: string): 'high' | 'medium' | 'low' => {
  switch (status?.toLowerCase()) {
  case 'failed':
    return 'high'
  case 'processing':
    return 'medium'
  default:
    return 'low'
  }
}

const handlePopoverShow = () => {
  // 显示时刷新数据
  fetchProgressReports()
}

// 使用 useStorage 持久化位置
const storedPosition = useStorage('floating-ball-position', {
  x: props.initialX ?? window.innerWidth - 90,
  y: props.initialY ?? window.innerHeight - 90
})

const x = ref(storedPosition.value.x)
const y = ref(storedPosition.value.y)
const ballRef = ref<HTMLElement>()

// 定义边缘吸附的阈值（距离边缘多少像素会触发吸附）
const SNAP_THRESHOLD = 50
// 定义边缘距离（最终吸附时距离边缘的像素）
const EDGE_DISTANCE = 20

// 计算吸附位置
const calculateSnapPosition = (position: { x: number, y: number }) => {
  const { innerWidth, innerHeight } = window
  const ballWidth = 60 // 悬浮球的宽度
  const ballHeight = 60 // 悬浮球的高度

  // 计算中心点
  const centerX = position.x + ballWidth / 2
  const centerY = position.y + ballHeight / 2

  // 计算到各边的距离
  const distanceToLeft = position.x
  const distanceToRight = innerWidth - (position.x + ballWidth)
  const distanceToTop = position.y
  const distanceToBottom = innerHeight - (position.y + ballHeight)

  // 确定水平位置
  let snapX
  if (distanceToLeft < SNAP_THRESHOLD) {
    snapX = EDGE_DISTANCE
  } else if (distanceToRight < SNAP_THRESHOLD) {
    snapX = innerWidth - ballWidth - EDGE_DISTANCE
  } else if (centerX < innerWidth / 2) {
    snapX = EDGE_DISTANCE
  } else {
    snapX = innerWidth - ballWidth - EDGE_DISTANCE
  }

  // 确定垂直位置
  let snapY = position.y
  if (distanceToTop < SNAP_THRESHOLD) {
    snapY = EDGE_DISTANCE
  } else if (distanceToBottom < SNAP_THRESHOLD) {
    snapY = innerHeight - ballHeight - EDGE_DISTANCE
  }

  return { x: snapX, y: snapY }
}

// 监听窗口大小变化，调整悬浮球位置
const handleResize = () => {
  if (x.value && y.value) {
    const snapPosition = calculateSnapPosition({ x: x.value, y: y.value })
    x.value = snapPosition.x
    y.value = snapPosition.y
    storedPosition.value = { x: x.value, y: y.value }
  }
}

onMounted(() => {
  // 初始化获取数据
  fetchProgressReports()

  if (ballRef.value) {
    const { style } = useDraggable(ballRef, {
      initialValue: {
        x: storedPosition.value.x,
        y: storedPosition.value.y
      },
      onStart() {
        isDragging.value = true
      },
      onMove(position) {
        // 边界检查
        const maxX = window.innerWidth - ballRef.value!.offsetWidth
        const maxY = window.innerHeight - ballRef.value!.offsetHeight

        const boundedX = Math.min(Math.max(0, position.x), maxX)
        const boundedY = Math.min(Math.max(0, position.y), maxY)

        // 拖动时自由移动，只做边界限制
        x.value = boundedX
        y.value = boundedY

        // 保存位置
        storedPosition.value = { x: x.value, y: y.value }
      },
      // 添加拖拽结束的处理
      onEnd() {
        // 先标记拖拽结束
        isDragging.value = false

        // 松开鼠标时进行边缘吸附
        const snapPosition = calculateSnapPosition({ x: x.value, y: y.value })
        x.value = snapPosition.x
        y.value = snapPosition.y
        storedPosition.value = { x: x.value, y: y.value }
      }
    })
  }

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const getProgressBackground = (task: Task) => {
  switch (task.status?.toLowerCase()) {
  case 'failed':
    return 'linear-gradient(90deg, var(--state-danger-color-1), var(--state-danger-color-2))'
  case 'completed':
    return 'linear-gradient(90deg, var(--state-done-color-1), var(--state-done-color-2))'
  case 'processing':
    return 'linear-gradient(90deg, var(--state-todo-color-1), var(--state-todo-color-2))'
  default:
    return 'linear-gradient(90deg, var(--state-todo-color-1), var(--state-todo-color-2))'
  }
}

const formatStatus = (status: string) => {
  switch (status.toLowerCase()) {
  case 'failed':
    return 'Failed'
  default:
    return status
  }
}
</script>

<style>
/* 全局样式 */
.task-popover {
  padding: 0 !important;
  border-radius: 8px !important;
  border: 1px solid var(--color-border-2) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}
</style>

<style scoped>
.floating-ball {
  background: linear-gradient(145deg, var(--brand-color-6), var(--brand-color-7));
  box-shadow: 0 4px 15px rgba(84, 120, 255, 0.3);
  transition: all 0.3s ease;
}

.cursor-not-allowed {
  opacity: 0.7;
  cursor: not-allowed;
}

.floating-ball::before {
  content: '';
  background: linear-gradient(145deg, var(--brand-color-5), var(--brand-color-7));
  z-index: -1;
}

.floating-ball:hover::before {
  opacity: 100;
}

.ball-icon {
}

.notification-badge {
}

.floating-ball::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  background: radial-gradient(circle at center, rgba(84, 120, 255, 0.2) 0%, transparent 70%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.5); opacity: 0; }
  100% { transform: scale(1); opacity: 0.3; }
}

.task-list {

    background-color: rgb(255 255 255 / var(--tw-bg-opacity)) ;
    border-radius: 0.5rem;
}

.shine-effect {
  background: linear-gradient(
    90deg,
    rgba(255,255,255,0.1) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0.1) 100%
  );
  animation: shine 2s infinite linear;
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.task-item:last-child {
  margin-bottom: 0;
}

.progress-container {
  margin-top: 0.5rem;
}
</style>
