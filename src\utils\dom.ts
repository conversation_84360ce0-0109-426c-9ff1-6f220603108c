/**
 * 判断el内容是否溢出（使用克隆方式）
 * @param el
 * @param attr
 * @returns {boolean}
 */
export function isOverflowOfClone(el: HTMLElement, attr?: any): boolean {
  const clone = el.cloneNode(true) as HTMLElement
  clone.style.visibility = 'hidden'
  clone.style.position = 'absolute'
  clone.style.top = '-9999px'
  clone.style.left = '-9999px'
  clone.style.width = 'auto'

  if (getComputedStyle) {
    const styles = getComputedStyle(el);
    ['fontSize', 'letterSpacing', 'fontWeight', 'fontFamily'].forEach((k: any) => clone.style[k] = styles[k])
    if (attr) {
      Object.keys(attr).forEach((k: any) => clone.style[k] = attr[k])
    }
  }
  document.body.appendChild(clone)
  const realWidth = clone.getBoundingClientRect().width
  clone.remove()
  const elWidth = el.getBoundingClientRect().width
  return realWidth > elWidth || el.scrollWidth > el.offsetWidth
}
