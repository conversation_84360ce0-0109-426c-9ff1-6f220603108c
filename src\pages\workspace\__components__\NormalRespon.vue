<template>
  <Card
    :class="[
      'relative backdrop-blur-md border-l-4 shadow-sm hover:shadow-md transition-all',
      logic.statusBorderClass.value,
      logic.statusBgClass.value,
    ]">
    <!-- Modify View Detail <PERSON>ton (Absolutely Positioned) - Only for Completed State -->
    <Button
      variant="ghost"
      size="sm"
      class="absolute top-2 right-2 h-7 px-2 text-xs text-slate-600 hover:text-main-color hover:bg-slate-100/50 z-10 flex items-center gap-1 cursor-pointer"
      @click.stop="viewReport(report as unknown as Report)">
      <ExternalLinkIcon class="h-3.5 w-3.5 shrink-0"/>
      <span>{{ $t('common.view_details') }}</span>
    </Button>

    <!-- Running/Pending Report -->
    <div v-if="logic.isRunning.value || logic.isPending.value">
      <CardHeader class="py-3 px-4 bg-white/20  flex flex-row items-center justify-between">
        <div class="flex items-center gap-2" v-if="logic.statusLabel.value">
          <Badge :variant="logic.statusVariant.value" :class="logic.statusBadgeClass.value">
            {{ logic.statusLabel.value }}
          </Badge>
          <!-- <span class="text-sm font-medium text-slate-800">{{ logic.reportName.value }}</span> -->
        </div>
      </CardHeader>

      <CardContent class="p-4">
        <!-- Pending Status -->
        <template v-if="logic.isPending.value">
          <p class="text-sm text-slate-600 mb-3 break-words">{{ logic.reportDescription.value }}</p>
          <div class="flex justify-center space-x-2 py-2">
            <span class="h-2 w-2 bg-amber-400 rounded-full animate-bounce" style="animation-delay: 0s"></span>
            <span class="h-2 w-2 bg-amber-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></span>
            <span class="h-2 w-2 bg-amber-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></span>
          </div>
        </template>

        <!-- Running Status -->
        <template v-else-if="logic.isRunning.value">
          <p class="text-sm mb-3 text-slate-700 break-words">
            {{ logic.progressMessage.value }}
          </p>
          <div class="space-y-2">
            <Progress v-model="progress"
                      class="h-2 bg-slate-100"/>
            <p class="text-xs text-right text-slate-500">
              {{ $t('common.completion_degree') }}: {{ logic.reportProgress.value.toFixed(0) }}%
            </p>
          </div>
                  
          <!-- Steps -->
          <div v-if="logic.hasSteps.value" class="mt-4 space-y-2 max-h-[300px] overflow-y-auto pr-1">
            <div 
              v-for="(step, index) in (logic.isRunningAgent ? (report as unknown as RunningAgent).steps : [])" 
              :key="index" 
              class="p-3 rounded-lg border border-slate-100 bg-white/30  transition-all break-words"
              :class="{
                'border-l-4 border-l-blue-500 shadow-sm': step.status === 'PROCESSING',
                'border-l-4 border-l-amber-300': step.status === 'PENDING',
                'border-l-4 border-l-green-500': step.status === 'COMPLETED'
              }">
              <div class="flex justify-between items-center mb-1 gap-2">
                <span class="text-sm font-medium text-slate-800 truncate">{{ step.title }}</span>
                <Badge :variant="logic.getStepStatusVariant(step.status)" class="text-xs shrink-0" :class="logic.getStepStatusBadgeClass(step.status)">
                  {{ $t(`common.step_status_${step.status.toLowerCase()}`, step.status) }}
                </Badge>
              </div>
              <p v-if="step.description" class="text-xs text-slate-500 break-words">
                {{ step.description }}
              </p>
            </div>
          </div>
        </template>
      </CardContent>
    </div>

    <!-- Completed Report -->
    <div v-else-if="logic.isCompleted.value">
      <CardContent class="p-4">
        <p class="text-sm leading-relaxed text-slate-700">{{ logic.summary.value }}</p>
      </CardContent>
    </div>

    <!-- Error Report -->
    <div v-else-if="logic.isError.value">
      <CardContent class="p-4">
        <p class="text-sm text-red-500 mb-3">{{ $t('common.report_error_message') }}</p>
        <div class="flex justify-end">
          <Button size="sm" variant="outline" class="flex items-center gap-1.5 text-xs bg-white/50 hover:bg-white/80 border-slate-200 text-slate-700">
            <RefreshCwIcon class="h-3.5 w-3.5"/>
            {{ $t('common.retry') }}
          </Button>
        </div>
      </CardContent>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { 
  Card, CardHeader, CardContent
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { RefreshCwIcon, ExternalLinkIcon } from 'lucide-vue-next'
import { toRefs, ref, watch } from 'vue'
import type { RunningAgent, Report } from '../types'
// Import the correct type for completed standard reports
import type { ReportList as CompletedStandardReport } from '@/pages/format-report/type'
import { useReportDisplayLogic } from '@/composables/useReportDisplayLogic'

// Define Props - Use the correct types
const props = defineProps<{
  report: CompletedStandardReport | RunningAgent; // Corrected type
  viewReport:(report: Report) => void;
}>()

// Use the composable
const logic = useReportDisplayLogic(toRefs(props).report)
const progress = ref(0)
watch(logic.reportProgress, (newVal) => {
  progress.value = newVal
})
// Expose t and viewReport for the template

</script>
