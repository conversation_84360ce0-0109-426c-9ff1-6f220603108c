import { ref, computed, onMounted, type Ref } from 'vue'
import { useGet } from '@/hooks/useHttp'
import { AVATAR_MAP, SPECIAL_AGENT_TYPES, type ReportData, type ToolResult } from '@/composables/useReportProgress'
import { useI18n } from 'vue-i18n'
import type { CompletedAgent } from '@/pages/dashboard/type' // For Monitor Agent type
import type { AgentMarket } from '@/pages/agent-market/type' // For Market Agent type
// import type { RawSSEData } from '@/pages/dashboard/type' // Added for dashboardData type

// Define and EXPORT the unified structure for workspace agents list
// This might need adjustment based on AgentListSidebar requirements
export interface WorkspaceAgent {
  id: string; // Use report_type or id from agent/report
  name: string;
  role: string;
  status: string; // 'running' or 'idle' based on market data initially
  avatar: string;
  currentTask: string | null;
  isMonitor: boolean;
  report_type: string; // Keep the original report_type/id
  frequency?: number; // Added by lookup later
  progress?: number; // Added for progress
}

export function useWorkspaceAgents(
  monitorAgentsFromReports: Ref<CompletedAgent[]>,
  dashboardDataFromReports: Ref<ReportData[]>
) {
  const { t } = useI18n()

  // --- Fetch Market Agents ---
  const {
    state: marketAgentsData,
    execute: getAgentMarket,
    isLoading: isLoadingMarket,
  } = useGet<AgentMarket[]>(
    '/formatted_report/agent_market',
    {},
    { immediate: true } // Fetch immediately
  )

  // --- Combined Loading State ---

  // --- Transform Market Agents (This is the base list) ---
  const baseMarketAgents = computed<WorkspaceAgent[]>(() => {
    if (!marketAgentsData.value) { return [] }
    return marketAgentsData.value.map(item => {
      const isMonitorType = SPECIAL_AGENT_TYPES.includes(item.id)
      return {
        id: item.id, // Use market item id (report_type)
        name: item.agent_name || t('agent.no_agent_name', { name: item.report_name }),
        role: item.report_name || t('agent.unknown_role'),
        // Use market status directly. Assumes this reflects if *any* task of this type is running.
        status: item.status ? 'running' : 'idle',
        avatar: AVATAR_MAP[item.id as keyof typeof AVATAR_MAP] || '',
        currentTask: item.report_description || null,
        isMonitor: isMonitorType,
        report_type: item.id,
        // Frequency will be added later
      }
    })
  })

  // Create a lookup map for frequency from monitor agent reports
  const frequencyLookup = computed(() => {
    const lookup = new Map<string, number>()
    monitorAgentsFromReports.value.forEach(agent => {
      if (agent.report_type && agent.frequency !== undefined) {
        lookup.set(agent.report_type, agent.frequency)
      }
    })
    return lookup
  })

  // --- Final Combined List (Market agents + Frequency) ---
  const combinedAgentsList = computed<WorkspaceAgent[]>(() => {
    const freqLookup = frequencyLookup.value
    
    // 创建两个映射 - 一个基于id，一个基于report_type
    const dashboardIdMap = new Map(
      (dashboardDataFromReports.value || []).map(task => [task.id, task])
    )
    
    // 额外创建一个基于report_type的映射
    const dashboardTypeMap = new Map(
      (dashboardDataFromReports.value || [])
        .map(task => [task.report_type?.toLowerCase(), task])
    )
    
    return baseMarketAgents.value.map(agent => {
      const result = { ...agent }
      
      if (agent.isMonitor && agent.report_type) {
        const frequency = freqLookup.get(agent.report_type)
        if (frequency !== undefined) {
          result.frequency = frequency
        }
      }
      
      // 优先尝试通过id匹配，然后尝试通过report_type匹配
      let sseTask = dashboardIdMap.get(agent.id)
      
      // 如果通过id找不到，尝试通过report_type查找
      if (!sseTask && agent.report_type) {
        sseTask = dashboardTypeMap.get(agent.report_type?.toLowerCase())
      }
      
      if (sseTask) {
        result.status = sseTask.status?.toLowerCase() || 'idle'
        result.progress = Math.ceil(sseTask.progress || 0)
        
        const processingTool = sseTask.tools_results?.find(tool => 
          ['running', 'pending', 'processing'].includes(tool.status?.toLowerCase() || '')
        )
        if (processingTool) {
          result.currentTask = processingTool.name
        }
      }
      
      return result
    })
  })
  
  const rawAgentDescription = computed(() => {
    return marketAgentsData.value?.map(agent => {
      return {
        report_type: agent.id,
        description: agent.report_description,
        report_name: agent.report_name,
      }
    })
  })
  // --- Exposed API ---
  return {
    isLoading:isLoadingMarket, // Reflects market loading primarily now
    combinedAgentsList,
    rawAgentDescription,
  }
} 