import type { Fonts } from '../types'

export const DEFAULT_FONTS: Fonts = {

}

export const CUSTOM_FONTS: Fonts = {
  ...DEFAULT_FONTS,
  fangzhen: {
    normal: 'fzhei-jt.TTF',
    bold: 'fzhei-jt.TTF',
    italics: 'fzhei-jt.TTF',
    bolditalics: 'fzhei-jt.TTF',
  },
  AlibabaSansJP: {
    normal: 'AlibabaSansJP-Regular.ttf',
    bold: 'AlibabaSansJP-Regular.ttf',
    italics: 'AlibabaSansJP-Regular.ttf',
    bolditalics: 'AlibabaSansJP-Regular.ttf',
  },
}

export const CONTENT_STYLES = {
  // 段落样式
  paragraph: {
    fontSize: 10,
    lineHeight: 1.5,
    margin: [0, 5, 0, 10],
    unbreakable: true,
  },

  // 列表容器样式
  listContainer: {
    fontSize: 10,
    lineHeight: 1.5,
    margin: [0, 5, 0, 10],
  },

  // 列表项样式
  listItem: {
    fontSize: 10,
    lineHeight: 1.5,
    margin: [0, 2, 0, 2],
    unbreakable: true,
  },
} as const

export const FONT_CACHE_KEY = 'pdf_fonts_cache'
export const FONT_VERSION = '1.0.0'
