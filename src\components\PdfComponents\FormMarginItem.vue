<template>
  <el-form-item
    :label="$t(field.label)"
    :class="$style.marginItem">
    <el-input-number
      :model-value="modelValue"
      :min="20"
      :max="100"
      @change="$emit('update:modelValue', $event)"
      @update:model-value="$emit('change')"/>
  </el-form-item>
</template>

<script setup lang="ts">
defineProps<{
  field: {
    key: string
    label: string
  }
  modelValue: number
}>()

defineEmits<{
  'update:modelValue': [value: number]
  'change': []
}>()
</script>

<style lang="scss" module>
.marginItem {
  margin-bottom: 0;

  :global(.el-form-item__label) {
    font-size: 14px;
  }

  :global(.el-input-number) {
    width: 100%;
  }
}
</style>
