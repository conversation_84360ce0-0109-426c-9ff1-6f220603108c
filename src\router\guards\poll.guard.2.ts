import router from '@/router'
import { redirectToPath } from '@/utils/router'
import { useUserStore } from '@/stores/user'

interface RedirectStrategy {
  handle(to: any, from: any, next: any, stepNum: number): boolean;
}

const isPoll = (to:string) => to.includes('/poll')
const isPollFinally = (to:string) => to === '/poll'

class CompletedPollRedirect implements RedirectStrategy {
  handle(to: any, from: any, next: any, stepNum: number): boolean {
    if (stepNum === 5 && isPoll(to.fullPath)) {
      console.warn('Poll already completed. Redirecting to home.')
      redirectToPath('/', to, from, next, { replaceRedirectFromQuery: true })
      return true
    }
    return false
  }
}

class IncompletePollRedirect implements RedirectStrategy {
  handle(to: any, from: any, next: any, stepNum: number): boolean {
    if (!isPoll(to.fullPath) && stepNum === 0) {
      console.warn('Poll not completed. Redirecting to poll page.')
      redirectToPath('/poll', to, from, next, { replaceRedirectFromQuery: true })
      return true
    }
    return false
  }
}

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const currentStep = userStore.userInfo?.step || 0
  const stepNum = currentStep

  const strategies: RedirectStrategy[] = [
    new CompletedPollRedirect(),
    new IncompletePollRedirect(),
  ]

  for (const strategy of strategies) {
    if (strategy.handle(to, from, next, stepNum)) {
      return // check redirectToPath, just return
    }
  }

  next() // no redirectToPath, just next router
})
