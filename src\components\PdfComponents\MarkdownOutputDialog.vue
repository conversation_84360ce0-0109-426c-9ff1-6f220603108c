<template>
  <div ref="elRef" :class="$style.container">
    <el-dialog
      v-model="show"
      :id="`markdown-output-dialog-${title}`"
      :custom-class="$style.dialog"
      :modal-append-to-body="true"
      align-center
      fullscreen
      :show-close="true"
      @closed="handleDialogClosed">
      <template #header>
        <div :class="$style.header">
          <span :class="$style.title">{{ title }}</span>
          <div class="flex items-center gap-2">
            <template v-if="isReadyToPdf && !isStreaming">
              <el-tooltip :content="$t('intelligence.markdown.settings')">
                <Icon icon="mdi:settings-outline"
                      class="text-[#333] text-[20px]"
                      @click="handleShowSettings"
                      :class="{
                        'cursor-not-allowed': !userStore.hasPermission(PDF_PERMISSIONS.PDF_SETTING),
                        'opacity-50': !userStore.hasPermission(PDF_PERMISSIONS.PDF_SETTING),
                        'cursor-pointer': userStore.hasPermission(PDF_PERMISSIONS.PDF_SETTING),
                      }"/>
              </el-tooltip>
              <el-tooltip :content="$t('intelligence.markdown.previewpdf')">
                <Icon icon="fontisto:preview"
                      class="text-[#333] text-[20px]"
                      @click="previewPDF"
                      :class="{
                        'cursor-pointer': userStore.hasPermission(PDF_PERMISSIONS.PDF_REVIEW),
                        'opacity-50': !userStore.hasPermission(PDF_PERMISSIONS.PDF_REVIEW),
                        'cursor-not-allowed': !userStore.hasPermission(PDF_PERMISSIONS.PDF_REVIEW),
                      }"/>
              </el-tooltip>
              <el-tooltip :content="$t('intelligence.markdown.downloadpdf')">
                <Icon  icon="mdi:file-pdf"
                       class="text-[#333] text-[20px]"
                       @click="downloadPDF"
                       :class="{
                         'cursor-pointer': userStore.hasPermission(PDF_PERMISSIONS.PDF_DOWNLOAD),
                         'opacity-50': !userStore.hasPermission(PDF_PERMISSIONS.PDF_DOWNLOAD),
                         'cursor-not-allowed': !userStore.hasPermission(PDF_PERMISSIONS.PDF_DOWNLOAD),
                       }"/>
              </el-tooltip>
            </template>
            <template v-else>
              <span class="text-gray-500">{{
                isStreaming
                  ? $t("intelligence.markdown.streaming")
                  : $t("intelligence.markdown.pdfnotready")
              }}</span>
            </template>
            <el-tooltip v-if="isSyncing" content="Syncing...">
              <Icon icon="mdi:loading" class="text-[#333] text-[20px] animate-spin"/>
            </el-tooltip>
          </div>
        </div>
      </template>
      <div
        ref="reportContainer"
        :class="$style.reportContainer"
        data-pdf-container>
        <div ref="contentWrapper" :class="$style.contentWrapper">
          <slot name="markdown-display"/>
        </div>
      </div>
    </el-dialog>
    <PdfSettingsDialog
      v-model="showSettings"
      :content="reportContainer!"
      :title="title"
      @confirm="handleSettingsConfirm"/>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, nextTick, type PropType } from 'vue'
import { Icon } from '@iconify/vue'
import { useVModel } from '@vueuse/core'
import { usePdf } from '@/hooks/usePdf/index'
import { isFontLoaded } from '@/hooks/usePdf/fonts/useFonts'
import { useI18n } from 'vue-i18n'
import PdfSettingsDialog from './PdfSettingsDialog.vue'
import type { PdfOptions } from '@/hooks/usePdf/types'
import { useTrack } from '@/hooks/useTrack'
import type { MarkdownKeyType } from '@/types/intelligence'
import { useUserStore } from '@/stores/user'
import { PDF_PERMISSIONS } from '@/const/permission'

const userStore = useUserStore()

const { trackEvent } = useTrack()
const { locale } = useI18n()
const isReadyToPdf = computed(
  () => isFontLoaded.value || locale.value === 'us'
)
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  visible: {
    type: Boolean,
    default: false,
  },
  isStreaming: {
    type: Boolean,
    default: false,
  },
  currentStreamingKey: {
    type: String as PropType<MarkdownKeyType>,
    default: '',
  },
  isSyncing: {
    type: Boolean,
    default: false,
  },
})

const reportContainer = ref<HTMLElement | null>(null)
const contentWrapper = ref<HTMLElement | null>(null)
const scrollContainer = computed(() =>
  elRef.value?.querySelector('.el-dialog__body')
)
const showSettings = ref(false)
const pdfSettings = ref<Partial<PdfOptions>>({})

const handleSettingsConfirm = (settings: Partial<PdfOptions>) => {
  pdfSettings.value = settings
}

const createPDF = async () => {
  if (!reportContainer.value) {
    return
  }
  const { generatePdf } = usePdf()
  return await generatePdf(props.title, reportContainer.value, pdfSettings.value)
}

const downloadPDF = async () => {
  const hasPermission = userStore.hasPermission(PDF_PERMISSIONS.PDF_DOWNLOAD)
  if (props.isStreaming || !hasPermission) {
    return
  }
  trackEvent('report_download', {
    topic: props.title,
  })
  const pdfMaker = await createPDF()
  pdfMaker?.download(props.title)
}

const previewPDF = async () => {
  const hasPermission = userStore.hasPermission(PDF_PERMISSIONS.PDF_REVIEW)
  if (props.isStreaming || !hasPermission) {
    return
  }
  trackEvent('report_review', {
    topic: props.title,
  })
  const pdfMaker = await createPDF()
  pdfMaker?.open()
}

const handleShowSettings = () => {
  if (userStore.hasPermission(PDF_PERMISSIONS.PDF_SETTING)) {
    showSettings.value = true
  }
}
const emit = defineEmits(['update:visible'])
const show = useVModel(props, 'visible', emit)
const elRef = ref<HTMLElement | null>(null)

const handleDialogClosed = () => {
  showSettings.value = false
  pdfSettings.value = {}
}

const scrollToSection = async (key: string) => {
  await nextTick()
  if (!scrollContainer.value) {
    return
  }

  const targetElement = contentWrapper.value?.querySelector(`#section-${key}`)
  if (!targetElement) {
    return
  }

  const inputWrapper = document.querySelector('#summary-suggestion-input')
  const inputHeight = inputWrapper
    ? inputWrapper.getBoundingClientRect().height
    : 0

  targetElement.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  })

  if (inputHeight) {
    await nextTick()
    scrollContainer.value.scrollBy({
      top: -inputHeight / 2,
      behavior: 'smooth',
    })
  }
}

defineExpose({
  scrollToSection,
})
</script>

<style lang="scss" module>
.container {
  :global(.el-overlay-dialog) {
    top: 0;
    bottom: 0;
  }
}
.header {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  padding-right: 20px;
  .title {
    color: var(--primary-font-color);
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
    letter-spacing: 0.2px;
  }
}

.dialog {
  :global(.el-dialog__body) {
    padding: 0;
    overflow: hidden;
  }
}

.reportContainer {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  padding-right: 15px;
}
</style>
