<template>
  <div :class="$style['main-content']">
    <div>
      <span class="text-[40px] mb-[20px]">{{ title }}</span>
      <ol class="mt-[20px]">
        <li
          v-for="(item, index) in contentItems"
          :key="index"
          class="flex items-start">
          <span class="text-[20px] font-bold mr-[10px] shrink-0">{{ index + 1 }}.</span>
          <span class="text-[18px] leading-[1.5]">{{ item }}</span>
        </li>
      </ol>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  title: string;
  contentItems: string[];
}>()
</script>

<style scoped lang="scss" module>
.main-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  :global(ol) {
    padding-left: 0;
    list-style-type: none;
  }

  :global(li) {
    margin-bottom: 20px;
  }
}

.img-box {
  border-radius: 8px;
}
</style>
