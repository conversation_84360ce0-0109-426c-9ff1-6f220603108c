import { useFonts } from './fonts/useFonts'
import { createDocumentStyles } from './styles/documentStyles'
import {
  processContent,
  createTitleSection,
  createTableOfContents,
} from './utils/contentProcessor'
import type { TDocumentDefinitions } from 'pdfmake/interfaces'
// @ts-expect-error
import htmlToPdfmake from 'html-to-pdfmake'
import type { PdfOptions } from './types'
import { getI18nInstance } from '@/lang/i18n'
import type { CreatedReport } from '@/pages/format-report/type'
import { onUnmounted } from 'vue'

export interface HTMLProcessorOptions {
  removeDetails: boolean
  processHeadings: boolean
  processList: boolean
  processTable: boolean
  removeHr: boolean 
  removeSearchResults: boolean
}

// Add cache mechanism
const contentCache = new WeakMap<HTMLElement, HTMLElement>()

// Add batch processing utility
const batchProcessElement = (element: HTMLElement, processors: Array<(el: HTMLElement) => void>): HTMLElement => {
  const fragment = document.createDocumentFragment()
  const clone = element.cloneNode(true) as HTMLElement
  fragment.appendChild(clone)

  processors.forEach(processor => processor(clone))
  
  return clone
}

export const processHTMLContent = (element: HTMLElement, options: Partial<HTMLProcessorOptions> = {}): HTMLElement => {
  // Check cache first
  const cached = contentCache.get(element)
  if (cached) {
    return cached.cloneNode(true) as HTMLElement
  }

  const {
    removeDetails = true,
    processHeadings = true,
    processList = false,
    processTable = true,
    removeHr = true,
    removeSearchResults = true,
  } = options

  const processors: Array<(el: HTMLElement) => void> = []

  if (removeDetails) {
    processors.push(el => {
      el.querySelectorAll('details, summary').forEach(node => node.remove())
    })
  }

  if (removeSearchResults) {
    processors.push(el => {
      el.querySelectorAll('search-results-list').forEach(node => node.remove())
    })
  }

  if (removeHr) {
    processors.push(el => {
      el.querySelectorAll('hr').forEach(node => node.remove())
    })
  }

  if (processHeadings) {
    processors.push(el => {
      el.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
        const level = parseInt(heading.tagName[1])
        const headingDiv = document.createElement('div')
        headingDiv.className = `heading-${level}`
        headingDiv.innerHTML = heading.innerHTML
        headingDiv.setAttribute('data-heading-level', String(level))
        heading.parentNode?.replaceChild(headingDiv, heading)
      })
    })
  }

  if (processList) {
    processors.push(el => {
      el.querySelectorAll('ul, ol').forEach(list => {
        const isOrderedList = list.tagName.toLowerCase() === 'ol'
        const listContainer = document.createElement(isOrderedList ? 'ol' : 'ul')
        
        listContainer.className = list.className
        Array.from(list.attributes).forEach(attr => {
          listContainer.setAttribute(attr.name, attr.value)
        })

        list.querySelectorAll(':scope > li').forEach((li, index) => {
          const listItem = document.createElement('li')
          
          const nestedLists = li.querySelectorAll(':scope > ul, :scope > ol')
          if (nestedLists.length > 0) {
            const textContent = document.createElement('div')
            textContent.innerHTML = li.innerHTML.replace(/<(ul|ol)[\s\S]*<\/(ul|ol)>/g, '')
            listItem.appendChild(textContent)
            
            nestedLists.forEach(nestedList => {
              const processedNestedList = nestedList.cloneNode(true) as HTMLElement
              listItem.appendChild(processedNestedList)
            })
          } else {
            listItem.innerHTML = li.innerHTML
          }

          listContainer.appendChild(listItem)
        })

        list.parentNode?.replaceChild(listContainer, list)
      })
    })
  }

  if (processTable) {
    processors.push(el => {
      el.querySelectorAll('table').forEach(table => {
        // 1. 获取表头数量作为标准列数
        const headerRow = table.querySelector('thead tr') as HTMLTableRowElement
        const headerCells = headerRow?.cells?.length || 0
        
        // 2. 处理每一行
        table.querySelectorAll('tbody tr').forEach(row => {
          // 检查行是否缺少单元格
          const currentCells = (row as HTMLTableRowElement).cells.length
          if (currentCells < headerCells) {
            // 补充缺失的单元格
            for (let i = currentCells; i < headerCells; i++) {
              const td = document.createElement('td')
              td.textContent = '-'
              row.appendChild(td)
            }
          }

          // 处理每个单元格
          Array.from((row as HTMLTableRowElement).cells).forEach(cell => {
            // 如果单元格为空或只包含空白字符,设置默认值
            if (!cell.textContent?.trim()) {
              cell.textContent = '-'
            }
            
            // 处理链接样式
            const links = cell.querySelectorAll('a')
            links.forEach(link => {
              const classes = [...new Set(link.className.split(/\s+/))]
              link.className = classes.join(' ')
            })
          })
        })
      })
    })
  }

  // Process everything in one batch
  const processed = batchProcessElement(element, processors)
  
  // Cache the result
  contentCache.set(element, processed.cloneNode(true) as HTMLElement)
  
  return processed
}

// Add processing result cache
const processingCache = new WeakMap<HTMLElement, {
  content: HTMLElement;
  timestamp: number;
}>()

const CACHE_DURATION = 5000 // 5 seconds cache duration


interface SectionProcessorOptions {
  images?: Record<string, string>;
  searchSource?: CreatedReport['search_result'];
  timelineData?: CreatedReport['time_line'];
  legalTimelineData?: CreatedReport['legal_data']['time_line'];
}

// 处理图片部分的函数
const processImageSection = (images: Record<string, string>): HTMLElement => {
  const i18nInstance = getI18nInstance()
  const language = i18nInstance.global.locale.value

  const titleMap = {
    chinese: '图表分析',
    english: 'Chart Analysis',
    japanese: 'グラフ分析' 
  }

  const fragment = document.createDocumentFragment()
  const imageSection = document.createElement('div')
  
  const titleDiv = document.createElement('div')
  titleDiv.className = 'pdf-title'
  titleDiv.innerHTML = titleMap[language]
  
  const contentDiv = document.createElement('div')
  contentDiv.className = 'markdown-content'
  
  Object.values(images).forEach(imageData => {
    const imgContainer = document.createElement('div')
    imgContainer.className = 'image-container'
    
    const img = document.createElement('img')
    img.src = imageData
    imgContainer.appendChild(img)
    contentDiv.appendChild(imgContainer)
  })
  
  imageSection.appendChild(titleDiv)
  imageSection.appendChild(contentDiv)
  fragment.appendChild(imageSection)
  
  return fragment.firstElementChild as HTMLElement
}

// 处理章节部分的函数
const processSections = (node: HTMLElement): HTMLElement[] => {
  return Array.from(node.querySelectorAll('[data-pdf-section]')).map(section => {
    const sectionDiv = document.createElement('div')
    
    const titleNode = section.querySelector('[data-pdf-title]')
    const contentNode = section.querySelector('[data-markdown-content]')
    
    if (titleNode) {
      const titleDiv = document.createElement('div')
      titleDiv.className = 'pdf-title'
      titleDiv.innerHTML = titleNode.innerHTML
      sectionDiv.appendChild(titleDiv)
    }
    
    if (contentNode) {
      const contentDiv = document.createElement('div')
      contentDiv.className = 'markdown-content'
      contentDiv.innerHTML = contentNode.innerHTML.replace(
        /<span[^>]*?data-reference-id="(\d+)"[^>]*?>.*?<\/span>/g,
        '[$1]'
      )
      sectionDiv.appendChild(contentDiv)
    }
    
    return sectionDiv
  })
}

const getLanguage = (): 'chinese' | 'japanese' | 'english' => {
  const i18nInstance = getI18nInstance()
  const language = i18nInstance.global.locale.value
  return language
}
// 处理参考资料部分的函数
const processReferences = (searchSource: CreatedReport['search_result']): HTMLElement => {
  const titleMap = {
    chinese: '参考资料',
    english: 'References',
    japanese: '参考資料'
  }
  const language = getLanguage()
  
  const fragment = document.createDocumentFragment()
  const referencesSection = document.createElement('div')
  
  const titleDiv = document.createElement('div')
  titleDiv.className = 'pdf-title'
  titleDiv.innerHTML = titleMap[language]
  
  const contentDiv = document.createElement('div')
  contentDiv.className = 'markdown-content'
  
  const urlList = document.createElement('ol')
  urlList.style.cssText = 'list-style-type: decimal; padding-left: 20px;'
  
  const urlElements = Object.values(searchSource)
    .flat()
    .map(item => {
      const li = document.createElement('li')
      li.style.cssText = 'margin-bottom: 8px; line-height: 1.5;'
      
      const link = document.createElement('a')
      link.href = item.url
      link.style.cssText = 'text-decoration: none !important; color: #0066cc;'
      link.textContent = item.title || item.url
      
      li.appendChild(link)
      return li
    })
  
  urlElements.forEach(element => urlList.appendChild(element))
  contentDiv.appendChild(urlList)
  referencesSection.appendChild(titleDiv)
  referencesSection.appendChild(contentDiv)
  fragment.appendChild(referencesSection)
  
  return fragment.firstElementChild as HTMLElement
}

// 处理timeline为ol -> ul -> li
const processTimeline = (timelineData: CreatedReport['time_line'], title: string): HTMLElement => {
  const fragment = document.createDocumentFragment()
  const timelineSection = document.createElement('div')
  
  // Create title section
  const titleDiv = document.createElement('div')
  titleDiv.className = 'pdf-title'
  titleDiv.innerHTML = title
  timelineSection.appendChild(titleDiv)
  
  // Create time_line list
  const timelineList = document.createElement('ol')
  timelineList.style.cssText = 'list-style-type: decimal; padding-left: 20px;'
  
  // Process each time_line item
  timelineData.forEach(item => {
    const li = document.createElement('li')
    li.style.cssText = 'margin-bottom: 16px;'
    
    // Time element
    const timeDiv = document.createElement('div')
    timeDiv.style.cssText = 'font-weight: bold; color: #333; margin-bottom: 8px; font-size: 14px;'
    timeDiv.textContent = item.time
    // Create unordered list for content items
    const contentList = document.createElement('ul')
    contentList.style.cssText = 'list-style-type: disc; margin-left: 20px;'
    
    // Split content by double newlines and create list items
    const contentItems = item.content.split('\n\n')
    contentItems.forEach(content => {
      const contentLi = document.createElement('li')
      contentLi.style.cssText = 'margin-bottom: 8px; line-height: 1.6;'
      contentLi.textContent = `${content}(${item.time})`
      contentList.appendChild(contentLi)
    })
    
    li.appendChild(timeDiv)
    li.appendChild(contentList)
    timelineList.appendChild(li)
  })
  
  timelineSection.appendChild(timelineList)
  fragment.appendChild(timelineSection)
  
  return fragment.firstElementChild as HTMLElement
}

export const extractContent = async (
  node: HTMLElement,
  options: SectionProcessorOptions = {}
): Promise<HTMLElement> => {
  try {
    // 检查缓存
    const cached = processingCache.get(node)
    if (cached && (Date.now() - cached.timestamp < CACHE_DURATION)) {
      return cached.content.cloneNode(true) as HTMLElement
    }

    // 创建容器
    const container = document.createElement('div')

    // 批量处理各个部分
    const processors: Promise<HTMLElement | HTMLElement[]>[] = []

    // 处理图片部分
    if (options.images && Object.keys(options.images).length > 0) {
      processors.push(Promise.resolve(processImageSection(options.images)))
    }

    // // 处理timeline部分
    if (options.timelineData && options.timelineData.length > 0) {
      const titleMap = {
        chinese: '舆论时间线',
        english: 'Time Line',
        japanese: '輿論時間線'
      }
      const language = getLanguage()
      processors.push(Promise.resolve(processTimeline(options.timelineData, titleMap[language])))
    }
    // 处理legalData部分
    if (options.legalTimelineData && options.legalTimelineData.length > 0) {
      const language = getLanguage()
      const titleMap = {
        chinese: '法律纠纷时间线',
        english: 'Legal Dispute Time Line',
        japanese: '法律紛争時間線'
      }
      processors.push(Promise.resolve(processTimeline(options.legalTimelineData, titleMap[language])))
    }
    // 处理章节部分
    processors.push(Promise.resolve(processSections(node)))

    // 处理参考资料部分
    if (options.searchSource && Object.keys(options.searchSource).length > 0) {
      processors.push(Promise.resolve(processReferences(options.searchSource)))
    }

    // 等待所有处理器完成
    const results = await Promise.all(processors)
    
    // 按顺序添加所有处理后的元素
    results.flat().forEach(element => {
      if (element) {
        container.appendChild(element)
      }
    })

    // 为缓存创建一个深拷贝
    processingCache.set(node, {
      content: container.cloneNode(true) as HTMLElement,
      timestamp: Date.now()
    })

    // 直接返回 container
    return container
  } catch (error) {
    console.error('Error in extractContent:', error)
    throw new Error(`Failed to extract content: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// 添加资源管理
const resourceManager = {
  workers: new Set<Worker>(),
  blobs: new Set<string>(),
  
  cleanup() {
    // 清理 workers
    this.workers.forEach(worker => worker.terminate())
    this.workers.clear()
    
    // 清理 blobs
    this.blobs.forEach(url => URL.revokeObjectURL(url))
    this.blobs.clear()
  }
}
export const usePdf = () => {
  const { isFontLoaded } = useFonts()
  
  // 使用 onUnmounted 清理资源
  onUnmounted(() => {
    resourceManager.cleanup()
  })

  const generatePdf = async (
    title: string,
    node: HTMLElement,
    options?: Partial<PdfOptions>,
    images?: Record<string, string>,
    searchSource?: CreatedReport['search_result'],
    timelineData?: CreatedReport['time_line'],
    legalTimelineData?: CreatedReport['legal_data']['time_line']
  ) => {
    if (!node || !isFontLoaded.value) {
      throw new Error(node ? 'Fonts failed to load' : 'HTML node is required')
    }

    const pdfMake = window.pdfMake
    const language = getLanguage()
    try {
      // 1. 预处理图片资源
      // const processedImages = images ? await processImages(images) : undefined

      // 2. 优化内容处理：使用 DocumentFragment 和并行处理
      const [structuredNode, documentStyles] = await Promise.all([
        (async () => {
          const fragment = document.createDocumentFragment()
          const clonedNode = node.cloneNode(true) as HTMLElement
          fragment.appendChild(clonedNode)
          return extractContent(clonedNode, { images, searchSource, timelineData, legalTimelineData })
        })(),
        createDocumentStyles(language, options)
      ])

      // 3. 优化内存使用：分块处理大型内容
      const processedNode = processHTMLContent(structuredNode)
      console.log('processedNode', processedNode)
      const htmlContent = await convertToPdfContent(processedNode)
      // 4. 优化 PDF 内容处理
      const processedContent = await processContentEfficiently(htmlContent)

      // 5. 创建最终的 PDF 定义
      const pdfDefinition = createPdfDefinition(
        title,
        documentStyles,
        processedContent,
        options
      )

      // 6. 创建 PDF 并确保资源清理
      const pdf = pdfMake.createPdf(pdfDefinition)
      
      // 包装 getBuffer 方法以确保资源清理
      const originalGetBuffer = pdf.getBuffer
      pdf.getBuffer = async function(...args: any[]) {
        try {
          const buffer = await originalGetBuffer.apply(this, args)
          resourceManager.cleanup() // 清理资源
          return buffer
        } catch (error) {
          resourceManager.cleanup() // 确保错误时也清理资源
          throw error
        }
      }

      return pdf

    } catch (error) {
      resourceManager.cleanup() // 确保错误时清理资源
      console.error('PDF generation failed:', error)
      throw error
    }
  }

  // 优化：高效处理内容
  const processContentEfficiently = async (content: any[]): Promise<any[]> => {
    const BATCH_SIZE = 100 // 每批处理的项目数
    const results: any[] = []
    
    for (let i = 0; i < content.length; i += BATCH_SIZE) {
      const batch = content.slice(i, i + BATCH_SIZE)
      const processedBatch = batch.map(item => processContent([item])[0])
      results.push(...processedBatch)
      
      // 允许其他任务执行
      if (i + BATCH_SIZE < content.length) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
    }
    
    return results
  }

  // 优化：分块处理大型内容
  const convertToPdfContent = async (node: HTMLElement) => {
    const content = node.innerHTML
    const chunkSize = 50000 // 50KB chunks
    const chunks = []
    
    for (let i = 0; i < content.length; i += chunkSize) {
      const chunk = content.slice(i, i + chunkSize)
      chunks.push(chunk)
    }

    return htmlToPdfmake(
      chunks.join(''),
      {
        removeExtraBlanks: true,
        tableAutoSize: true,
        preserveStyles: true
      }
    )
  }

  // 优化：减少对象创建和合并
  const createPdfDefinition = (
    title: string,
    documentStyles: Partial<TDocumentDefinitions>,
    processedContent: any[],
    options?: Partial<PdfOptions>
  ): TDocumentDefinitions => {
    const baseDefinition = {
      ...documentStyles,
      content: [
        createTitleSection(title, options?.titleSection),
        createTableOfContents(),
        { text: '', pageBreak: 'after' },
      ],
      info: {
        title,
        author: 'Specific AI',
        subject: title,
        keywords: title,
      },
    }

    // 直接修改数组而不是创建新数组
    baseDefinition.content.push(...processedContent)
    console.log('baseDefinition', baseDefinition)
    return baseDefinition as TDocumentDefinitions
  }

  return {
    generatePdf,
    isFontLoaded,
  }
}

export type { FontConfig, Fonts } from './types'
