/**
 * CDN插件封装
 * 封装vite-plugin-cdn2库，便于统一管理CDN配置
 */
import { cdn } from 'vite-plugin-cdn2'
import { defineResolve } from 'vite-plugin-cdn2/resolve'
import type { Plugin } from 'vite'

// CDN源池 - 优先使用jsdelivr，然后是unpkg
const CDN_POOL = [
  'https://cdn.jsdelivr.net/npm/',
  'https://unpkg.com/',
  'https://fastly.jsdelivr.net/npm/',
  'https://gcore.jsdelivr.net/npm/'
]

// 插件接口定义
interface Module {
  name: string
  var?: string
  path?: string
  css?: string
  relativeModule?: string
  aliases?: string[]
  global?: string
}

interface CDN2Options {
  modules: Module[]
}

/**
 * 封装CDN插件，统一配置内部CDN源和相关设置
 */
export function vitePluginCDN2(options: CDN2Options): Plugin {
  // vite-plugin-cdn2实际返回的是一个插件数组，但我们需要返回单个插件
  const cdnPlugin = {
    name: 'vite-plugin-cdn2-wrapper',
    // 将vite-plugin-cdn2的插件内容整合到这个插件中
    ...cdn({
      modules: options.modules.map(module => {
        // 如果已经提供了完整路径，则使用提供的路径
        if (module.path) {
          return {
            name: module.name,
            var: module.var || module.name,
            path: module.path,
            css: module.css
          }
        }

        // 否则使用原有的配置方式
        return {
          name: module.name,
          var: module.var,
          global: module.global,
          relativeModule: module.relativeModule,
          aliases: module.aliases
        }
      }),
      resolve: defineResolve({
        name: 'resolve:cdn-fallback',
        setup: ({ extra }) => {
          const { version, name, relativeModule } = extra
          
          // 构建URL路径
          const path = `${name}@${version}/${relativeModule || ''}`
          
          // 使用第一个CDN源（jsdelivr）
          return {
            url: `${CDN_POOL[0]}${path}`,
            injectTo: 'head-prepend',
            attrs: { 
              crossOrigin: 'anonymous',
              referrerpolicy: 'no-referrer'
            }
          }
        }
      })
    })
  } as unknown as Plugin
  
  return cdnPlugin
} 