import type { Ref } from 'vue'
import { getToken } from '@/hooks/useToken'
import { useEnhancedEventSource } from '@/hooks/useEnhancedEventSource'
import type { AgentMarket } from '../type'
import emitter from '@/utils/events'
// 状态更新接口定义
export interface AgentStatusUpdate {
  id: string
  status: boolean
}

export interface SSEMessage {
  type: string
  data: AgentStatusUpdate[]
}

/**
 * 处理Agent状态更新的SSE连接
 * @param agentsState Agent状态的响应式引用
 * @returns SSE连接控制方法
 */
export function useAgentStatusUpdates(agentsState: Ref<AgentMarket[] | null>) {
  const { close, open } = useEnhancedEventSource(
    () => {
      const url = new URL('/api/formatted_report/update_agent_status', window.location.origin)
      url.searchParams.append('token', getToken())
      return url
    },
    ['on_message'],
    {
      onMessage: (message) => {
        if (message.type !== 'update' || !Array.isArray(message.data) || !agentsState.value) {
          return
        }
        
        const updateMap = new Map(
          (message.data as AgentStatusUpdate[]).map(update => [update.id, update.status])
        )
        
        // 检查是否有任何状态变化
        let hasChanges = false
        const updatedAgents = agentsState.value.map(item => {
          const newStatus = updateMap.get(item.id)
          if (newStatus !== undefined && newStatus !== item.status) {
            hasChanges = true
            return { ...item, status: newStatus }
          }
          return item
        })
        
        // 只在有变化时更新状态
        if (hasChanges) {
          agentsState.value = updatedAgents as AgentMarket[]
          setTimeout(() => {
            emitter.emit('async:update')
          }, 1000)
        }
      },
      onError: (error) => {
        console.error('SSE连接错误:', error)
      },
      immediate: false
    }
  )

  return {
    openConnection: open,
    closeConnection: close
  }
} 