import { createI18n, type I18n } from 'vue-i18n'
import { set, get } from 'lodash-es'
import zh_CN from './modules/zh-CN'
import zh_EN from './modules/zh-EN'
import ja_JP from './modules/ja-JP'
import type { User } from '@/types/login'
import tour from './modules/tour'
import { ref } from 'vue'


const mergeZh = {
  ...zh_CN,
  ...tour.zh
}
const mergeEn = {
  ...zh_EN,
  ...tour.en
}
const mergeJa = {
  ...ja_JP,
  ...tour.ja
}
// 动态导入语言包
const loadLocaleResources = async (language: User['language']) => {
  let elementLocale, tableLocale

  if (language === 'english') {
    elementLocale = await import('element-plus/es/locale/lang/en')
    tableLocale = await import('vxe-table/lib/locale/lang/en-US')
    set(elementLocale.default, 'el.datepicker.currentMonth', 'This month')
  }
  else if (language === 'japanese') {
    elementLocale = await import('element-plus/es/locale/lang/ja')
    tableLocale = await import('vxe-table/lib/locale/lang/ja-JP')
    set(elementLocale.default, 'el.datepicker.currentMonth', '今月')
  }
  else {
    elementLocale = await import('element-plus/es/locale/lang/zh-cn')
    tableLocale = await import('vxe-table/lib/locale/lang/zh-CN')
    set(elementLocale.default, 'el.datepicker.currentMonth', '本月')
  }

  return {
    elementLocale: elementLocale.default,
    tableLocale: tableLocale.default
  }
}

export const elementPlusLocale = ref<any>(null)
let VXETableLocale: any

// 初始化默认语言资源
const initDefaultLocale = async () => {
  const defaultLanguage = 'chinese'
  const { elementLocale, tableLocale } = await loadLocaleResources(defaultLanguage as User['language'])
  elementPlusLocale.value = elementLocale
  VXETableLocale = tableLocale
}

// 确保在应用启动时加载默认语言资源
initDefaultLocale()

// 更新语言资源
export const updateLocaleResources = async (language: User['language']) => {
  const { elementLocale, tableLocale } = await loadLocaleResources(language)
  elementPlusLocale.value = elementLocale
  VXETableLocale = tableLocale
}

const pluralizationRule = (choice: number, choicesLength: number) => {
  choice = Math.abs(choice)

  if (choicesLength === 2) {
    return choice ? (choice > 1 ? 1 : 0) : 1
  }

  return choice ? Math.ceil(Math.min(choice, 2)) : 0
}

function flattenObject(obj: Record<string, any>, prefix = ''): Record<string, any> {
  return Object.keys(obj).reduce((acc, key) => {
    const newKey: string = prefix ? `${prefix}.${key}` : key
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      Object.assign(acc, flattenObject(obj[key], newKey))
    } else {
      acc[newKey] = obj[key]
    }
    return acc
  }, {} as Record<string, any>)
}

const messages = {
  chinese: flattenObject(mergeZh),
  english: flattenObject(mergeEn),
  japanese: flattenObject(mergeJa),
}

export function createI18nInstance() {
  const i18n = createI18n({
    legacy: false,
    locale: localStorage.getItem('specific-locale') || 'chinese',
    fallbackLocale: 'chinese',
    pluralizationRules: {
      chinese: pluralizationRule,
      english: pluralizationRule,
      japanese: pluralizationRule,
    },
    missing: (locale, key) => {
      return key
    },
    messages: messages,
    messageResolver: (obj, key) => {
      const message = (obj as any)[key]
      if (!message && key.startsWith('el.')) {
        return get(elementPlusLocale.value, key)
      }
      if (!message && key.startsWith('vxe.')) {
        return get(VXETableLocale, key)
      }
      return message
    },
  })
  return i18n
}

let i18nInstancePromise: I18n<{
  [key in User['language']]: Record<string, string>
}, {}, {}, User['language'], false>

export const getI18nInstance = () => {
  if (!i18nInstancePromise) {
    i18nInstancePromise = createI18nInstance()
  }
  return i18nInstancePromise
}