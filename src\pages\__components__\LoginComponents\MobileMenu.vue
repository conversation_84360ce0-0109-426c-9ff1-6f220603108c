<template>
  <Transition name="slide-fade">
    <div 
      v-if="modelValue" 
      class="md:hidden fixed inset-0 z-50">
      <!-- Backdrop -->
      <div 
        class="absolute inset-0 bg-black/50 backdrop-blur-xs"
        @click="emit('update:modelValue', false)"/>
      
      <!-- Menu Content -->
      <div 
        ref="menuRef"
        class="absolute right-0 top-0 bottom-0 w-[80vw] max-w-[300px] bg-white"
        @click.stop>
        <div class="container h-full flex flex-col">
          <!-- Close Button -->
          <div class="h-[60px] flex items-center justify-end mt-[20px] px-4">
            <el-button @click="emit('update:modelValue', false)">
              <i-mdi-close class="text-xl"/>
            </el-button>
          </div>
          
          <!-- Mobile Navigation -->
          <nav class="flex-1 px-4 py-4">
            <a 
              v-for="item in navItems" 
              :key="item.key"
              class="block py-3 text-lg font-bold hover:text-primary transition-colors"
              @click="handleNavClick(item)">
              {{ item.label }}
            </a>
          </nav>

          <!-- Mobile Actions -->
          <div class="flex flex-col gap-4 mt-auto mb-8 px-4">
            <el-button link @click="handleLogin">
              {{ t('login.login') }}
            </el-button>
            <el-tooltip
              :content="hasInviteCode ? '' : t('login.betaTestingTip')"
              placement="left"
              :disabled="hasInviteCode">
              <el-button 
                type="primary" 
                round 
                @click="handleRegister"
                :disabled="!hasInviteCode">
                {{ t('login.register') }}
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { NavItem } from './types/navigation'

const props = defineProps<{
  modelValue: boolean
  navItems: NavItem[]
  hasInviteCode: boolean
}>()

const emit = defineEmits<{
  'update:modelValue':[value:boolean]
  'login':[void]
  'register':[void]
  'nav-click':[item:NavItem]
}>()

const { t } = useI18n()
const menuRef = ref<HTMLElement | null>(null)

const handleNavClick = (item: NavItem) => {
  emit('nav-click', item)
  emit('update:modelValue', false)
}

const handleLogin = () => {
  emit('login')
  emit('update:modelValue', false)
}

const handleRegister = () => {
  emit('register')
  emit('update:modelValue', false)
}
</script>

<style lang="scss" module>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 添加背景动画 */
:global(.backdrop-enter-active),
:global(.backdrop-leave-active) {
  transition: opacity 0.3s ease;
}

:global(.backdrop-enter-from),
:global(.backdrop-leave-to) {
  opacity: 0;
}
</style>
