<template>
  <aside :class="$style['sidebar']" v-loading="props.loading">
    <div :class="$style['sidebar-header']">
      <div class="flex items-center gap-2">
        <Icon icon="material-symbols:description-outline-rounded" class="text-2xl text-main-color"/>
        <h2 :class="$style['sidebar-title']">{{ $t('formatReport.sidebar.title') }}</h2>
      </div>
      <div class="flex items-center gap-2 ml-auto">
        <el-tooltip :content="$t('formatReport.sidebar.filterByCurrentLang')" placement="bottom">
          <el-switch
            v-model="filterByCurrentLang"
            :active-icon="'material-symbols:translate'"
            inline-prompt
            class="mr-2"/>
        </el-tooltip>
      </div>
    </div>
    <div :class="$style['sidebar-list']">
      <div v-for="(languages, type) in groupedReports" :key="type" :class="$style['report-group']">
        <div :class="$style['group-header']">
          <Icon :icon="getReportTypeIcon(type)" :class="$style['group-icon']"/>
          <span :class="$style['group-title']">{{ type }}</span>
        </div>
        <div :class="$style['group-content']">
          <div v-for="(reports, lang) in languages" :key="lang" :class="$style['language-group']">
            <div :class="$style['language-header']">
              <Icon :icon="getLanguageIcon(lang)" :class="$style['language-icon']"/>
              <span :class="$style['language-title']">{{ getLanguageLabel(lang) }}</span>
            </div>
            <div :class="$style['language-content']">
              <div
                v-for="report in reports"
                :key="report.id"
                :class="$style['sidebar-item']"
                @click="handleReportClick(report)">
                <div :class="$style['sidebar-item-content']">
                  <h3 :class="$style['sidebar-item-title']" :title="report.name">{{ report.name }}</h3>
                  <!-- <div  v-if="!isEmpty(report.params)">
                      <p :class="$style['sidebar-item-description']">{{ {{ report.params.company_name }} }}</p>
                    </div> -->
                  <div :class="$style['sidebar-item-footer']">
                    <span :class="$style['sidebar-item-date']">
                      <Icon icon="material-symbols:calendar-today-outline" class="mr-1"/>
                      {{ formatReportDate(report) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed, ref, type PropType } from 'vue'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import type { ReportList, ProgressReport } from '../type'
import { useGet } from '@/hooks/useHttp'

const { locale } = useI18n()
const filterByCurrentLang = ref(false)

const props = defineProps<{
  reports: ReportList[]
  loading?: boolean
}>()

const emit = defineEmits<{
  select: [report: ReportList]
}>()

const getCurrentLangCode = computed(() => {
  switch (locale.value) {
  case 'en':
    return 'english'
  case 'zh-cn':
    return 'chinese'
  case 'ja':
    return 'japanese'
  default:
    return locale.value
  }
})

const groupedReports = computed(() => {
  const filteredReports = filterByCurrentLang.value
    ? props.reports?.filter(report => report.language === getCurrentLangCode.value)
    : props.reports

  return filteredReports?.reduce((acc, report) => {
    const type = report.agent_name
    const lang = report.language

    if (!acc[type]) {
      acc[type] = {}
    }
    if (!acc[type][lang]) {
      acc[type][lang] = []
    }

    acc[type][lang].push(report)
    return acc
  }, {} as Record<string, Record<string, ReportList[]>>)
})

const getReportTypeIcon = (type: string) => {
  switch (type) {
  case 'ProductTrackingAnalysisReport':
    return 'material-symbols:analytics-outline'
  default:
    return 'material-symbols:description-outline'
  }
}

const getLanguageIcon = (lang: string) => {
  switch (lang.toLowerCase()) {
  case 'english':
    return 'emojione:flag-for-united-states'
  case 'chinese':
    return 'emojione:flag-for-china'
  case 'japanese':
    return 'emojione:flag-for-japan'
  default:
    return 'material-symbols:language'
  }
}

const getLanguageLabel = (lang: string) => {
  switch (lang.toLowerCase()) {
  case 'english':
    return 'English'
  case 'chinese':
    return '简体中文'
  case 'japanese':
    return '日本語'
  }
}

const formatReportDate = (report: ReportList | ProgressReport) => {
  if (!report || typeof report !== 'object') { return '--' }

  // 检查是否为 ReportList 类型
  if ('name' in report && report.updated_at) {
    return dayjs(report.updated_at).format('YYYY-MM-DD HH:mm')
  }

  // 检查是否为 ProgressReport 类型且有 date 属性
  if ('date' in report && report.date) {
    return dayjs(report.date).format('YYYY-MM-DD HH:mm')
  }

  return dayjs().format('YYYY-MM-DD HH:mm')
}

const handleReportClick = (report: ReportList | ProgressReport) => {
  if ('name' in report) {
    emit('select', report)
  }
}
</script>

<style lang="scss" module>
.sidebar {
  width: 380px;
  height: 100%;
  border-right: 1px solid rgba(68, 102, 188, 0.1);
  display: flex;
  flex-direction: column;
}

.view-switch {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  background-color: var(--bg-color-1);

  :global(.el-switch__label) {
    color: var(--font-color-2);
    font-size: var(--size-font-8);
  }
}

.sidebar-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  border-bottom: 1px solid rgba(68, 102, 188, 0.1);
  background: linear-gradient(to right, rgba(68, 102, 188, 0.03), transparent 80%);
}

.sidebar-title {
  font-size: var(--size-font-5);
  font-weight: 600;
  color: var(--font-color-1);
  margin: 0;
  line-height: 1.4;
}

.sidebar-list {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
  overflow-y: auto;
  height: 100%;
}

.report-group {
  margin-bottom: 8px;
}

.group-header {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--font-color-2);
  font-weight: 500;
  background: rgba(68, 102, 188, 0.03);
}

.group-icon {
  font-size: 18px;
  color: #4466BC;
}

.group-title {
  font-size: var(--size-font-7);
}

.group-content {
  padding-left: 16px;
}

.sidebar-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border-left: 2px solid transparent;

  &:hover {
    background: linear-gradient(to right, rgba(68, 102, 188, 0.04), transparent 90%);
    border-left-color: rgba(68, 102, 188, 0.6);
  }

  &:active {
    background: linear-gradient(to right, rgba(68, 102, 188, 0.08), transparent 90%);
    border-left-color: rgba(68, 102, 188, 0.8);
  }
}

.sidebar-item-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item-title {
  font-size: var(--size-font-7);
  font-weight: 500;
  color: var(--font-color-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin: 0;
}

.sidebar-item-description {
  font-size: var(--size-font-8);
  color: var(--font-color-2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  margin: 0;
}

.sidebar-item-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: var(--size-font-8);
  margin-top: 4px;
}

.sidebar-item-date {
  color: var(--font-color-3);
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.language-group {
  margin-left: 8px;
  margin-bottom: 8px;
}

.language-header {
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--font-color-2);
  font-size: var(--size-font-8);
  background: rgba(68, 102, 188, 0.02);
  border-left: 2px solid rgba(68, 102, 188, 0.1);
}

.language-icon {
  font-size: 16px;
}

.language-title {
  font-weight: 500;
}

.language-content {
  padding-left: 8px;
}

.progress-info {
  margin: 8px 0;
  padding: 0 4px;
}

:global(.el-progress) {
  :global(.el-progress-bar__inner) {
    transition: all 0.3s ease;
  }

  :global(.el-progress__text) {
    font-size: var(--size-font-8) !important;
    color: var(--font-color-2);
  }
}
</style>
