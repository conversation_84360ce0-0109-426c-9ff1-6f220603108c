import axios, { isCancel, type AxiosInstance, type AxiosRequestConfig, type InternalAxiosRequestConfig } from 'axios'
import { useConfig } from '@/hooks/useConfig'
import { getToken, removeToken } from './useToken'
import { ElMessage } from 'element-plus'
import { tryOnUnmounted, useAsyncState, type UseAsyncStateOptions, type UseAsyncStateReturn } from '@vueuse/core'
import emitter from '@/utils/events'
// import { useI18n } from 'vue-i18n'
import { getI18nInstance } from '@/lang/i18n'
import { ERROR_CODE_USER_NOT_LOGIN, ERROR_CODE_UNAUTHORIZED, ERROR_CODE_USER_DELETED } from '@/const/errorCode'
// const { locale } = useI18n()

export enum ResultEnum {
  SUCCESS = 200,
  ERROR = -1,
  TIMEOUT = 401,
  NOTFOUND = 404,
}

export type ResponseData<T = any> = {
  code: number
  message: string
  success: boolean
  data: T | null
}
export type SelfHttpConfig = {
  validateCode: (code: number, data?: ResponseData) => boolean
  showError: boolean | ((error: any) => void)
  autoCancel: boolean
  beforeRequest(config: HttpOptions): HttpOptions;
  noAuthHandler: (responseData: ResponseData) => boolean
}
type Result<T, P = any> = UseAsyncStateReturn<T | null, [P?, HttpOptions?], true> & {
  promiseResult: () => Promise<T | null>
  cancel: () => void
}
export class BusinessError extends Error {
  constructor(public code: number, public message: string, public data: ResponseData) {
    super(message)
    this.name = 'BusinessError'
  }
}
type HttpOptions = AxiosRequestConfig & Partial<SelfHttpConfig>
const config = useConfig()

export const defaultConfig: SelfHttpConfig = {
  validateCode: (code: number) => code === ResultEnum.SUCCESS,
  showError: (error: any) => ElMessage.error(error),
  autoCancel: true,
  beforeRequest: (config) => config,
  noAuthHandler: () => {
    reloadWithRequest()
    return false
  },
}

export const client: AxiosInstance = axios.create({
  baseURL: config.VITE_API_BASE_URL || '/',
  timeout: 1000 * 15,
  headers: {
    timezone: -(new Date().getTimezoneOffset() / 60),
    timezoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
  }
})

client.interceptors.request.use((config) => {
  const token = config.headers.authorization || getToken()
  const i18nInstance = getI18nInstance()
  const language = i18nInstance.global.locale.value
  
  // Set default headers
  if (!config.headers['Content-Type'] && !(config.data instanceof FormData)) {
    config.headers['Content-Type'] = 'application/json'
  }
  
  if (token) {
    config.headers.authorization = token
    config.headers.language = language
  }
  const beforeRequest = (config as HttpOptions).beforeRequest || defaultConfig.beforeRequest
  console.log('beforeRequest(config)', beforeRequest(config))
  return beforeRequest(config) as InternalAxiosRequestConfig
})

let isReloading = false
const RELOAD_KEY = 'RELOAD_KEY'
const getReloadUrl = () => window.sessionStorage.getItem(RELOAD_KEY)
export const reloadWithRequest = () => {
  const url = window.location.href
  if (isReloading || getReloadUrl() === url) {
    return
  }
  isReloading = true
  window.sessionStorage.setItem(RELOAD_KEY, url)
  window.location.reload()
}
client.interceptors.response.use((response) => {
  if (response.status === 200) {
    if (response.headers['content-disposition'] || response.config.responseType === 'blob') {
      return response
    }
    if (response.config.responseType === 'text') {
      return response.data
    }
  }
  const responseData = response.data as ResponseData
  const config = response.config as HttpOptions
  const noAuthHandler = typeof config.noAuthHandler === 'function' ? config.noAuthHandler : defaultConfig.noAuthHandler
  const validateCode = config.validateCode || defaultConfig.validateCode
  if (validateCode(responseData.code, responseData)) {
    window.sessionStorage.removeItem(RELOAD_KEY)
    return responseData.data
  }
  window.console.warn(`request ${response.config.url} business failed, code: ${responseData.code}, message: ${responseData.message}`)
  // 601 没有登录
  const noAuth = [ERROR_CODE_USER_NOT_LOGIN, ERROR_CODE_UNAUTHORIZED, ERROR_CODE_USER_DELETED].includes(responseData.code)
  if (noAuth) {
    emitter.emit('api:token-error', responseData)
    if (noAuthHandler(responseData)) {
      return response.data
    }
  }
  const error = new BusinessError(responseData.code, responseData.message || `${responseData.code}`, responseData)
  const showError = config.showError === true || config.showError === undefined ? defaultConfig.showError : config.showError
  if (typeof showError === 'function' && !noAuth) {
    showError(error.message)
  }
  return Promise.reject(error)
}, (error) => {
  if (isCancel(error)) {
    return
  }
  window.console.error(`request ${error.config.url} failed, error: ${error.message}`)
  const showError = error.config.showError === true || error.config.showError === undefined ? defaultConfig.showError : error.config.showError
  if (typeof showError === 'function') {
    showError(error.message)
  }
  return Promise.reject(error)
})

export const useRequest = <T, P = any>(url: string, options: HttpOptions = {}, stateOptions: UseAsyncStateOptions<true, T | null> = {}): Result<T> => {
  const autoCancel = options.autoCancel === undefined ? defaultConfig.autoCancel : options.autoCancel
  let controller: AbortController | undefined = undefined

  const result = useAsyncState<T | null, [P?, HttpOptions?]>((data?: P, opts: HttpOptions = {}) => {
    console.log(opts, data, options)
    controller = autoCancel ? new AbortController() : undefined
    return client.request<P, T>({
      method: 'POST',
      responseType: 'json',
      signal: controller?.signal,
      url,
      ...options,
      data: typeof data === 'undefined' ? options.data : data,
      params: typeof data === 'undefined' ? options.params : (data as any)?.params, // 强制转换为 any
      ...opts,
    }).finally(() => controller = undefined)
  }, null, {
    immediate: true,
    ...stateOptions,
    throwError: false,
  })

  const cancel = () => controller?.abort()

  if (autoCancel) {
    tryOnUnmounted(() => cancel())
  }

  // 这里要包装useAsyncState的execute方法，在执行后如果有错误，则抛出错误（因为useAsyncState的throwError强制设置为false）
  // 主要是为了单独调用execute方法和promiseResult方法时，可以捕获到错误，且只抛出一次
  const execute = (...args: Parameters<typeof result.execute>) => {
    return new Promise<T | null>((resolve, reject) => {
      result.execute(...args).then(() => {
        if (result.error.value && stateOptions.throwError !== false) {
          reject(result.error.value)
          return
        }
        return resolve(result.state.value)
      }, reject)
    })
  }
  return {
    ...result,
    execute,
    promiseResult: () => new Promise<T | null>((resolve, reject) => {
      result.then((value) => {
        if (value.error.value && stateOptions.throwError !== false) {
          reject(value.error.value)
          return
        }
        resolve(value.state.value)
      }, reject)
    }),
    cancel
  }
}

export const useGet = <T>(url: string, options: HttpOptions = {}, stateOptions: UseAsyncStateOptions<true, T | null> = {}) => {
  return useRequest<T>(url, {
    method: 'GET',
    ...options,
  }, stateOptions)
}

export const usePost = <T, P = any>(url: string, data?: P, options: HttpOptions = {}, stateOptions: UseAsyncStateOptions<true, T | null> = {}) => {
  return useRequest<T, P>(url, {
    method: 'POST',
    data,
    ...options,
  }, stateOptions)
}
