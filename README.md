# Specific AI

一个基于 Vue 3 + TypeScript 的企业级AI应用框架。

## 🚀 技术栈

- **框架**: Vue 3
- **开发语言**: TypeScript
- **构建工具**: Vite
- **CSS框架**: TailwindCSS
- **组件库**: Element Plus
- **工具库**: VueUse
- **图标**: Iconify
- **图表**: ECharts

## 📁 项目结构

```
src/
├── assets/          # 静态资源
│   ├── images/      # 图片资源
│   └── styles/      # 样式文件
├── components/      # 通用组件
├── const/          # 常量定义
├── directives/     # 自定义指令
├── hooks/          # 组合式函数
├── pages/          # 页面组件
│   ├── __components__/     # 页面级组件 (被路由排除)
│   ├── demo/              # 开发环境示例页面 (生产环境被排除)
│   └── [id].vue          # 动态路由页面
├── utils/          # 工具函数
└── App.vue         # 根组件
```

### 路由系统说明

项目使用 `unplugin-vue-router` 进行基于文件系统的路由自动生成，并实现了完整的路由守卫系统。

#### 1. 文件系统路由

```ts
// vite.config.ts
VueRouter({
  exclude: [
    '**/__components__/**',   // 排除页面组件目录
    '**/composables/**',      // 排除组合式函数目录
    '**/demo/**'              // 生产环境排除demo页面
  ]
})
```

#### 2. 路由守卫系统

项目实现了多层路由守卫，按执行顺序排列：

1. **认证守卫** (auth.guard.ts):
```ts
// 处理路由的认证要求
meta: {
  requiresAuth: boolean     // 需要登录才能访问
  requiresGuest: boolean    // 仅游客可访问
  tryAuth: boolean          // 尝试认证但不强制
  redirectIfUnauthorized: string  // 未授权时重定向路径
}
```

2. **访问控制守卫** (access.guard.ts):
```ts
// 处理细粒度的权限控制
meta: {
  access: {
    features: string[]      // 需要的功能权限
    roles: string[]         // 需要的角色
    permissions: string[]   // 需要的权限
    rightSchemeType: string[] // 权限方案类型
    fallback: string        // 无权限时的降级方案
  }
  redirectIfNoAccess: string // 无权限时重定向路径
}
```

3. **页面缓存守卫** (keepAlive.guard.ts):
```ts
// 处理页面缓存
meta: {
  keepAlive: boolean       // 是否启用页面缓存
}
```

4. **业务流程守卫** (poll.guard.ts):
```ts
// 处理多步骤业务流程
// 例如: 问卷调查流程控制
- 防止跳过步骤
- 确保完成当前步骤
- 处理流程完成后的重定向
```

#### 3. 路由元数据使用示例

```ts
// 需要登录且具有管理员角色的页面
{
  path: '/admin',
  meta: {
    requiresAuth: true,
    access: {
      roles: ['admin'],
      redirectIfNoAccess: '/dashboard'
    }
  }
}

// 仅游客可访问的登录页
{
  path: '/login',
  meta: {
    requiresGuest: true,
    redirectIfUnauthorized: '/'
  }
}

// 需要缓存的复杂表单页面
{
  path: '/form',
  meta: {
    keepAlive: true,
    requiresAuth: true
  }
}
```

#### 4. 最佳实践

1. **路由命名规范**:
   - 使用kebab-case命名文件和路径
   - 动态参数使用`[param]`语法
   - 可选参数使用`[[param]]`语法

2. **权限控制**:
   - 优先使用`access`配置进行细粒度控制
   - 合理设置重定向路径
   - 使用`fallback`提供降级方案

3. **性能优化**:
   - 合理使用`keepAlive`缓存页面
   - 路由组件按需加载
   - 避免深层嵌套路由

4. **用户体验**:
   - 设置合适的重定向规则
   - 保留用户来源页面信息
   - 处理未授权场景

## ✨ 特性

- 🎨 集成 TailwindCSS，支持原子化 CSS
- 📦 基于 Vite 构建，开发体验极致
- 🔑 内置用户认证系统
- 📊 集成 ECharts 图表组件
- 🎯 TypeScript 支持
- 🌐 SVG 图标支持
- 📱 响应式设计

## 🛠️ 开发指南

### 环境要求

- Node.js >= 18
- pnpm >= 8

### IDE 推荐

- [Cursor](https://cursor.sh/) - 基于AI的新一代IDE
  - 内置项目级别的AI编码规范 (通过 `.cursorrules`)
  - 支持Vue 3 + TypeScript开发
  - 内置Git集成
  - 智能代码补全和重构
  - 项目上下文感知

> 💡 本项目包含 `.cursorrules` 配置文件，用于定义:
> - 技术栈规范 (Vue 3, TypeScript, TailwindCSS等)
> - Vue 3 Composition API最佳实践
> - TypeScript类型规范
> - 样式编写规范
> - 项目结构约定
> 
> 使用Cursor IDE可以获得基于这些规范的智能提示和代码质量检查。

### 安装依赖

```sh
pnpm install
```

### 开发服务器

```sh
pnpm dev
```

### 构建生产版本

```sh
pnpm build
```

### 单元测试

```sh
pnpm test:unit
```

### 代码检查

```sh
pnpm lint
```

## 📖 开发规范

### Vue 组件规范
- 尽量避免使用`watch`语法(尽量避免非线性的响应式语法)
- 使用 `<script setup lang="ts">` 语法
- 组件结构遵循 `<template>` -> `<script>` -> `<style>` 顺序
- 代码组织规范:
  ```ts
  //  Props/Emits定义
  const props = defineProps<{/*...*/}>()
  const emit = defineEmits<{/*...*/}>()

  ```

- Props 规范:
  - 使用TypeScript接口定义类型
  - 必须指定类型和默认值
  - 使用camelCase命名

- 事件处理:
  - 使用`defineEmits`定义事件
  - 事件名使用kebab-case
  - 处理函数使用handle前缀

- 样式规范:
  - 使用`<style lang="scss" module>`
  - 局部样式优先使用CSS Modules
  - 全局样式使用:global()修饰符
  - 避免深层选择器嵌套(最多3层)

- 状态管理:
  - 简单状态使用`ref()`
  - 复杂对象状态使用`reactive()`
  - 派生状态使用`computed()`
  - 副作用使用`watch()`/`watchEffect()`

- 性能优化:
  - 合理使用`computed`缓存
  - 大列表使用虚拟滚动
  - 组件按需加载
  - 避免不必要的响应式

### TypeScript 规范

- 明确声明类型，避免使用 any
- 使用接口定义数据结构
- 使用枚举定义常量

### 样式规范

- 优先使用 TailwindCSS 工具类
- 复杂样式使用 CSS Modules
- 遵循 BEM 命名规范

## 🔧 配置说明

### **环境变量配置**

项目使用分层配置系统，按以下优先级从高到低加载:

1. `window.$config` (public/config.js)
2. `.env.local` (本地开发配置)
3. `.env` (基础配置)
****
#### 基础环境变量 (.env)

```bash
# VITE_前缀的变量会暴露在客户端中
VITE_APP_ENV=development              # 环境标识
VITE_APP_TITLE=Specific AI           # 应用标题
VITE_APP_DESC=Specific AI Platform   # 应用描述

# API配置
SERVER_DOMAIN=dev.goglobalsp.com    # 服务器域名
VITE_API_URL=https://${SERVER_DOMAIN}
VITE_API_BASE_URL=/api
VITE_SERVER_API_URL=${VITE_API_URL}${VITE_API_BASE_URL}

# CDN配置
VITE_APP_CDN_URL=${VITE_API_URL}/dist

# WebSocket配置
VITE_WS_SERVER_URL=wss://${SERVER_DOMAIN}
VITE_WS_SERVER_URL_DEV=              # 开发环境WebSocket地址
```

#### 本地开发配置 (.env.local)

```bash
# 本地开发标识
VITE_IS_DEV=true

# 可覆盖.env中的任何配置
```

#### 运行时配置 (public/config.js)

可以在部署时动态修改的配置，会覆盖其他环境变量:

```js
window.$config = {
  // 可覆盖任何VITE_前缀的环境变量
  // VITE_WS_SERVER_URL_DEV: 'ws://localhost:8080'
}
```

### 配置使用说明

1. 环境变量使用:
   - 以`VITE_`开头的变量可在客户端代码中通过`import.meta.env`访问
   - 服务端环境变量不带`VITE_`前缀，仅在构建时可用

2. 本地开发:
   - 创建`.env.local`文件（已被git忽略）进行本地配置
   - 可覆盖默认环境变量

3. 生产部署:
   - 通过修改`public/config.js`进行运行时配置
   - 支持不同环境使用相同构建产物

4. 变量命名规范:
   - 客户端可访问的变量使用`VITE_`前缀
   - 服务端变量直接使用大写字母命名
   - 使用下划线分隔单词



### Pinia Store 规范

项目使用 Pinia 进行状态管理，遵循以下规范：

#### 1. Store 定义规范

```ts
// 使用 setup 语法定义 store
export const useExampleStore = defineStore('example', () => {
  // 1. State 定义
  const count = ref(0)
  const user = reactive({
    name: '',
    age: 0
  })

  // 2. Getters (computed)
  const doubleCount = computed(() => count.value * 2)

  // 3. Actions (methods)
  const increment = () => {
    count.value++
  }

  // 4. 返回store内容
  return {
    // state
    count,
    user,
    // getters
    doubleCount,
    // actions
    increment
  }
})
```

#### 2. 最佳实践

1. **State 管理**:
   ```ts
   // 简单数据类型使用 ref
   const count = ref(0)
   
   // 复杂对象使用 reactive
   const profileFields = reactive<ProfileFields>(defaults({}, emptyFields))
   
   // 使用 computed 处理派生状态
   const postStep = computed(() => activeStep.value + 1)
   ```

2. **API 调用集成**:
   ```ts
   // 使用自定义 hooks 处理 API 调用
   const { execute, isLoading } = useGet<ProfileFields>(
     '/api/endpoint',
     {},
     { immediate: false }
   )
   
   // API 调用方法
   const fetchData = async () => {
     const result = await execute()
     result && setData(result)
   }
   ```

3. **类型定义**:
   ```ts
   // 为 state 定义接口
   interface ProfileFields {
     industry: string[]
     area: string[]
     style: string[]
     overseas: string[]
   }
   
   // 为 post 数据定义类型
   interface PostData {
     step: number
     user_profile: {
       [key: string]: any
     }
   }
   ```

4. **Store 组合**:
   ```ts
   // 在一个 store 中使用其他 store
   const userStore = useUserStore()
   
   // 组合多个 store 的操作
   const submitAndRefresh = async () => {
     await submitData()
     await userStore.loadAccount()
   }
   ```

#### 3. 命名规范

1. **Store 命名**:
   - 使用 `use` 前缀
   - 使用 `Store` 后缀
   - 例如: `useUserStore`, `usePollStepStore`

2. **State 命名**:
   - 使用描述性名称
   - 避免缩写
   - 例如: `activeStep`, `profileFields`

3. **Action 命名**:
   - 使用动词开头
   - 描述行为意图
   - 例如: `setActiveStep`, `submitStepResult`

4. **Getter 命名**:
   - 使用名词或形容词
   - 描述计算结果
   - 例如: `postStep`, `isCompleted`

#### 4. 性能优化

1. **状态分割**:
   - 按功能模块分割 store
   - 避免过大的单一 store
   - 合理使用子 store

2. **缓存策略**:
   - 使用 `computed` 缓存计算结果
   - 避免不必要的响应式数据
   - 合理设置 API 缓存

3. **订阅优化**:
   - 精确订阅所需的状态
   - 避免不必要的监听
   - 及时清理订阅
