// var.scss

:root {
  --primary-color: #6984c9;
  --disabled-color: #d1d8ff;
  --danger-color: #cc3c39;
  --warning-color: #f29621;
  --success-color: #23b051;
  --info-color: #575872;
  --brand-color-1: #f0f2ff;
  --brand-color-2: #d1d8ff;
  --brand-color-3: #b2c0ff;
  --brand-color-4: #93a8ff;
  --brand-color-5: #7490ff;
  --brand-color-6: #5578ff;
  --brand-color-7: #6984c9;
  --brand-color-8: #35518f;
  --brand-color-9: #263c62;
  --gray-1: #fafafa;
  --gray-2: #f3f3f3;
  --gray-3: #eeeeee;
  --gray-4: #e7e7e7;
  --gray-5: #dcdcdc;
  --gray-6: #c5c5c5;
  --gray-7: #777777;
  --gray-8: #5e5e5e;
  --gray-9: #383838;
  --gray-10: #242424;
  --gray-11: #181818;
  --purpleGray-1: #fbfbfc;
  --purpleGray-2: #f2f1f5;
  --purpleGray-3: #e8e7f0;
  --purpleGray-4: #dddcea;
  --purpleGray-5: #cfcde0;
  --purpleGray-6: #b7b6cd;
  --purpleGray-7: #73728d;
  --purpleGray-8: #575872;
  --purpleGray-9: #33344b;
  --purpleGray-10: #1f2134;
  --purpleGray-11: #131523;

  --weight-font-Regular: 400;
  --weight-font-icon: 500;
  --weight-font-Semiblod: 600;
  --weight-font-Blod: 700;
  --weight-font-Black: 900;
  --primary-font-color: #6984c9;
  --font-color-1: #1f2134;
  --font-color-2: #797a85;
  --font-color-3: #a5a6ae;
  --font-color-4: #c5c6cb;
  --font-color-5: #ffffff;
  --font-color-6: #353748;
  --font-color-7: #8f8f99;
  --size-font-3: 34px;
  --size-font-4: 24px;
  --size-font-5: 20px;
  --size-font-6: 16px;
  --size-font-7: 14px;
  --size-font-8: 13px;
  --size-font-9: 12px;
  --letter-spacing-1: 0.3px;
  --letter-spacing-2: 0.25px;
  --letter-spacing-3: 0.2px;
  --letter-spacing-4: 0.15px;
  --letter-spacing-5: 0;
  --letter-spacing-6: -0.25px;
  --letter-spacing-7: -0.1px;

  --priority-color-Trivial: #eeeeee;
  --priority-color-Highest: #f04c37;
  --priority-color-High: #f07e2e;
  --priority-color-Medium: #f0b651;
  --priority-color-Low: #81cfe3;
  --priority-color-Lowest: #ceedf0;

  --state-todo-color-1: #5578ff;
  --state-todo-color-2: #6984c9;
  --state-todo-bg: #f0f2ff;
  --state-todo-progress: #93a8ff;
  --state-todo-disable: #d1d8ff;

  --state-doing-color-1: #4a7de2;
  --state-doing-color-2: #3d69bc;
  --state-doing-bg: #edf2ff;
  --state-doing-progress: #6b95f5;
  --state-doing-disable: #b7ceff;

  --state-done-color-1: #20a149;
  --state-done-color-2: #1e873f;
  --state-done-bg: #e2f5e1;
  --state-done-progress: #0ead58;
  --state-done-disable: #71bf8c;

  --state-waring-color-1: #f29621;
  --state-waring-color-2: #d9861e;
  --state-waring-bg: #fff5e9;
  --state-waring-icon: #ff9f23;
  --state-waring-disable: #ffd9a7;

  --state-danger-color-1: #cc3c39;
  --state-danger-color-2: #b33432;
  --state-danger-bg: #fee9e8;
  --state-danger-icon: #eb4542;
  --state-danger-disable: #f7b5b3;

  --link-primary-color: #5578ff;
  --link-primary-color-hover: #6984c9;
  --link-primary-color-active: #35518f;
  --link-primary-color-disabled: #d1d8ff;
  --link-danger-color: #eb4542;
  --link-danger-color-hover: #cc3c39;
  --link-danger-color-active: #b33432;
  --link-danger-color-disabled: #f7b5b3;
  --link-black-color: #1f2134;
  --link-black-color-hover: #797a85;
  --link-black-color-active: #a5a6ae;
  --link-black-color-disabled: #c5c6cb;

  --gray-transparent-hover: rgba(0, 0, 0, 0.04);
  --brand-transparent-hover: rgba(84, 120, 255, 0.04);
  --brand-transparent-active-1: rgba(84, 120, 255, 0.12);
  --brand-transparent-active-2: rgba(68, 102, 188, 0.06);
  --shadow-normal: 0px 4px 12px rgba(96, 97, 132, 0.08);
  --shadow-drag: 0px 8px 16px 0px rgba(96, 97, 132, 0.24);
  --shadow-hover: 0px 6px 12px rgba(96, 97, 132, 0.16);
  --shadow-dialog: 0px 8px 24px rgba(31, 33, 52, 0.3);

  --divider-height: 1px;
  --divider-height-2: 2px;
  --color-border-1: #d2d4d9;
  --color-border-2: #e4e6eb;

  --m-mini: 4px;
  --m-small: 8px;
  --m-extra: 12px;
  --m-default: 16px;
  --m-medium: 20px;
  --m-large: 24px;
  --m-oversize: 32px;
  --p-default: 10px;
  --p-larger: 20px;
  --p-lr-mini: 0 8px;
  --p-lr-small: 0 12px;
  --p-lr-default: 0 16px;
  --p-tb-mini: 4px 0;
  --p-tb-small: 8px 0;
  --radius-mini: 4px;
  --radius-small: 6px;
  --radius-default: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-oversize: 32px;

  --minW-button-mini: 72px;
  --minW-button-small: 80px;
  --minW-button-default: 88px;

  --default-height: 40px;
  --medium-height: 36px;
  --small-height: 30px;
  --mini-height: 26px;

  --icon-mr-smaller: 4px;
  --icon-mr-default: 8px;
  --icon-size-small: 24px;
  --icon-size-default: 32px;

  --scrollbar-track-background: transparent;
  --scrollbar-background-color: rgba(144, 147, 153, 0.3);
  --scrollbar-hover-background-color: rgba(144, 147, 153, 0.5);

  --main-bg-color: #ffffff;
  --bg-color: #f5f5f7;
  --table-header-bg-color: #fafafa;

  --checkbox-input-default-width: 16px;
  --checkbox-input-small-width: 14px;
  --checkbox-input-mini-width: 12px;
  --checkbox-input-default-height: 16px;
  --checkbox-input-small-height: 14px;
  --checkbox-input-mini-height: 12px;

  --switch-default-width: 44px;
  --switch-medium-width: 36px;
  --switch-small-width: 28px;
  --switch-default-height: 24px;
  --switch-medium-height: 20px;
  --switch-small-height: 16px;
  --switch-default-button-size: 20px;
  --switch-medium-button-size: 16px;
  --switch-small-button-size: 12px;

  --alert-icon-size: 14px;
  --alert-icon-large-size: 22px;

  --select-input-close-icon-bg: #f6f6f9;
}