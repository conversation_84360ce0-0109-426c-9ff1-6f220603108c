<template>
  <div class="flex flex-col flex-1 min-h-0">
    <template v-if="isLoading || apiLoading">
      <div class="flex flex-col items-center justify-center p-6 space-y-2 flex-1">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
        <p class="text-sm text-slate-600">{{ t('agent.loading') }}</p>
      </div>
    </template>
    <template v-else>
      <div v-if="items.length > 0" class="flex flex-col flex-1 min-h-0">
        <!-- Fixed Table Header -->
        <div class="flex-shrink-0 bg-slate-50/50 border-b border-slate-200 sticky top-0 z-10">
          <div class="flex px-3 py-2.5 text-xs font-medium text-slate-600 items-center">
            <!-- Checkbox Column -->
            <div class="w-10 flex-shrink-0 flex items-center justify-center">
              <el-checkbox
                :model-value="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
                :disabled="isUnliking"
                class="transition-all duration-200 hover:scale-110"/>
            </div>
            <div class="flex-grow min-w-0 pr-3" style="white-space: nowrap;">{{ t('biding.table_header_title') }}</div>
            <div class="w-20 md:w-24 flex-shrink-0 text-center px-2" style="white-space: nowrap;">{{ t('biding.table_header_country') }}</div>
            
            <!-- 投标截止时间 - 可排序 -->
            <div 
              class="w-28 md:w-32 flex-shrink-0 px-2 text-center cursor-pointer hover:text-blue-700 flex items-center justify-center space-x-1" 
              style="white-space: nowrap;" 
              @click="handleSort('extraction.basic_info.submission_deadline')">
              <span>{{ t('biding.table_header_deadline') }}</span>
              <component 
                :is="getSortIcon('extraction.basic_info.submission_deadline')" 
                :class="getSortIconClass('extraction.basic_info.submission_deadline')" 
                class="h-3 w-3 flex-shrink-0"/>
            </div>

            <!-- 推荐评分 - 可排序 -->
            <div 
              class="w-16 md:w-20 flex-shrink-0 text-center cursor-pointer hover:text-blue-700 flex items-center justify-center space-x-1" 
              style="white-space: nowrap;" 
              @click="handleSort('recommendation.recommend_score')">
              <span>{{ t('biding.table_header_recommend_score') }}</span>
              <component 
                :is="getSortIcon('recommendation.recommend_score')" 
                :class="getSortIconClass('recommendation.recommend_score')" 
                class="h-3 w-3 flex-shrink-0"/>
            </div>
            
            <!-- 评分 - 可排序 -->
            <!-- <div 
              class="w-16 md:w-20 flex-shrink-0 text-center cursor-pointer hover:text-blue-700 flex items-center justify-center space-x-1" 
              style="white-space: nowrap;" 
              @click="handleSort('scoring.comprehensive.score')">
              <span>{{ t('biding.table_header_score') }}</span>
              <component 
                :is="getSortIcon('scoring.comprehensive.score')" 
                :class="getSortIconClass('scoring.comprehensive.score')" 
                class="h-3 w-3 flex-shrink-0"/>
            </div> -->

            <!-- 更新时间 - 可排序 -->
            <div 
              class="w-16 md:w-20 flex-shrink-0 text-center cursor-pointer hover:text-blue-700 flex items-center justify-center space-x-1" 
              style="white-space: nowrap;" 
              @click="handleSort('update_time')">
              <span>{{ t('biding.table_update_time') }}</span>
              <component 
                :is="getSortIcon('update_time')" 
                :class="getSortIconClass('update_time')" 
                class="h-3 w-3 flex-shrink-0"/>
            </div>
            
            <!-- 操作列 -->
            <div class="w-20 flex-shrink-0 text-center" style="white-space: nowrap;">
              {{ t('biding.table_header_action') }}
            </div>
          </div>
        </div>

        <!-- Scrollable Table Body with DynamicScroller -->
        <DynamicScroller
          :items="items"
          :min-item-size="56"
          key-field="_id"
          class="flex-grow overflow-y-auto scrollbar-thin">
          <template v-slot="{ item, index, active }">
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :data-index="index">
              <div
                class="flex items-center px-3 py-3 border-b border-slate-100 min-h-[56px] transition-all duration-200 ease-in-out"
                :class="{
                  'bg-blue-50/80 border-blue-200/60 shadow-sm': selectedItems.includes(item._id),
                  'hover:bg-slate-50/80 hover:shadow-sm': !selectedItems.includes(item._id)
                }">
                <!-- Checkbox Column -->
                <div class="w-10 flex-shrink-0 flex items-center justify-center">
                  <el-checkbox
                    :model-value="selectedItems.includes(item._id)"
                    @change="(checked: boolean) => handleItemSelect(item._id, checked)"
                    :disabled="isUnliking"
                    class="transition-all duration-200 hover:scale-110"/>
                </div>
                <div
                  class="flex-grow min-w-0 pr-3 flex items-center space-x-2 cursor-pointer"
                  @click="() => handleViewItemDetail(item)">
                  <p class="text-sm text-slate-800 truncate shrink" :title="item.new_title">{{ item.new_title }}</p>
                  <Badge v-if="isDeadlineExpired(item.extraction?.basic_info?.submission_deadline || item.submission_deadline)"
                         variant="destructive"
                         class="text-xs px-1 py-0 font-normal h-fit whitespace-nowrap flex-shrink-0">
                    {{ t('biding.expired') }}
                  </Badge>
                </div>
                <div
                  class="w-20 md:w-24 flex-shrink-0 text-center px-2 cursor-pointer"
                  @click="() => handleViewItemDetail(item)">
                  <p class="text-xs text-slate-600 leading-tight">{{ item.extraction?.basic_info?.country || item.country }}</p>
                </div>
                <div
                  class="w-28 md:w-32 flex-shrink-0 px-2 text-center cursor-pointer"
                  style="white-space: nowrap;"
                  @click="() => handleViewItemDetail(item)">
                  <p class="text-xs text-slate-600 leading-tight">{{ formatDateString(item.extraction?.basic_info?.submission_deadline?.toString() || item.submission_deadline?.toString() || '') }}</p>
                </div>
                <!-- 推荐评分列 -->
                <div
                  class="w-16 md:w-20 flex-shrink-0 flex justify-center items-center gap-1 cursor-pointer"
                  @click="() => handleViewItemDetail(item)">
                  <Badge
                    v-if="item.recommendation?.recommend_score !== undefined && item.recommendation?.recommend_score !== null"
                    :class="getRecommendScoreClass(item.recommendation.recommend_score)"
                    class="text-xs">
                    {{ new Decimal(item.recommendation.recommend_score).toFixed(1) }}
                  </Badge>
                  <Badge
                    v-else-if="item.recommend_score !== undefined && item.recommend_score !== null"
                    :class="getRecommendScoreClass(item.recommend_score)"
                    class="text-xs">
                    {{ new Decimal(item.recommend_score).toFixed(1) }}
                  </Badge>
                  <span v-else class="text-xs text-slate-400">-</span>

                  <!-- 推荐理由图标 -->
                  <el-popover
                    v-if="item.recommendation?.recommend_reason || item.recommend_reason"
                    placement="top"
                    :width="280"
                    trigger="hover"
                    popper-class="recommend-reason-popover"
                    :show-after="300"
                    :hide-after="100">
                    <div class="recommend-reason-content">
                      {{ item.recommendation?.recommend_reason || item.recommend_reason }}
                    </div>
                    <template #reference>
                      <InfoIcon
                        class="h-3 w-3 text-slate-400 cursor-help flex-shrink-0 ml-0.5 recommend-reason-icon"></InfoIcon>
                    </template>
                  </el-popover>
                </div>
                <!-- <div
                  class="w-16 md:w-20 flex-shrink-0 flex justify-center cursor-pointer"
                  @click="() => handleViewItemDetail(item)">
                  <Badge :class="getScoreClass(item.scoring?.comprehensive?.score)">
                    {{ item.scoring?.comprehensive?.score !== undefined && item.scoring?.comprehensive?.score !== null ? new Decimal(item.scoring.comprehensive.score).toFixed(1) : '-' }}
                  </Badge>
                </div> -->
                <div
                  class="w-16 md:w-20 flex-shrink-0 text-center cursor-pointer"
                  style="white-space: nowrap;"
                  @click="() => handleViewItemDetail(item)">
                  <p class="text-xs text-slate-600 leading-tight">{{ formatDateString(item?.update_time?.toString() || item?.updated_at?.toString() || '') }}</p>
                </div>
                
                <!-- 操作列 -->
                <div class="w-20 flex-shrink-0 flex justify-center gap-1">
                  <!-- AI 分析按钮 with DropdownMenu -->
                  <DropdownMenu v-model:open="aiConfirmPopovers[item._id]">
                    <DropdownMenuTrigger as-child>
                      <Button
                        @click="(e) => { e.stopPropagation(); }"
                        variant="ghost"
                        size="sm"
                        :disabled="isUnliking"
                        class="h-6 w-6 p-0 text-slate-400 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200"
                        title="AI 深度分析">
                        <BrainIcon class="h-3 w-3"/>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent class="w-80 p-0" align="end">
                      <div class="p-4 space-y-3">
                        <div class="flex items-start gap-3">
                          <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <BrainIcon class="w-4 h-4 text-blue-600"/>
                          </div>
                          <div class="flex-1 min-w-0">
                            <h4 class="font-medium text-gray-900 text-sm">AI 深度分析</h4>
                            <p class="text-xs text-gray-600 mt-1">
                              为「{{ item.new_title }}」进行智能分析
                            </p>
                          </div>
                        </div>
                        <div class="flex gap-2 pt-1">
                          <Button 
                            @click="handleConfirmAiProcessing(item)"
                            class="flex-1 h-8 text-xs"
                            size="sm">
                            开始分析
                          </Button>
                          <Button 
                            @click="handleCancelAiProcessing(item._id)"
                            variant="outline" 
                            class="flex-1 h-8 text-xs"
                            size="sm">
                            取消
                          </Button>
                        </div>
                      </div>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  <!-- 不喜欢按钮 -->
                  <el-popconfirm
                    v-if="selectedItems.length <= 1"
                    :title="t('biding.single_unlike_confirm')"
                    :confirm-button-text="t('biding.confirm_action')"
                    :cancel-button-text="t('biding.cancel_action')"
                    confirm-button-type="danger"
                    cancel-button-type="default"
                    icon-color="#f56565"
                    placement="top"
                    :width="280"
                    @confirm="handleSingleUnlike(item._id)"
                    @cancel="() => {}">
                    <template #reference>
                      <Button
                        variant="ghost"
                        size="sm"
                        :disabled="isUnliking"
                        class="h-6 w-6 p-0 text-slate-400 hover:text-red-500 hover:bg-red-50 transition-colors duration-200">
                        <ThumbsDown class="h-3 w-3"/>
                      </Button>
                    </template>
                  </el-popconfirm>
                </div>
              </div>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </div>
      <!-- 无匹配结果显示 -->
      <div v-else-if="totalOriginalItems > 0 && items.length === 0" class="flex-1 flex flex-col items-center justify-center p-8 text-center">
        <BinocularsIcon class="h-12 w-12 text-slate-300 mx-auto mb-3"/>
        <h3 class="text-base font-medium text-slate-700 mb-1">{{ t('common.no_matching_results') }}</h3>
        <p class="text-sm text-slate-500 mb-3">{{ t('common.try_different_filters') }}</p>
        <Button variant="outline"
                size="sm"
                @click="handleResetFilters">
          {{ t('common.reset_filters') }}
        </Button>
      </div>
      <!-- 原始无数据显示 -->
      <div v-else-if="!isLoading && totalOriginalItems === 0" class="flex-1 flex items-center justify-center p-4 text-center">
        <span class="text-sm text-slate-500">{{ t('global.noData') }}</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import Decimal from 'decimal.js'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BinocularsIcon, ArrowUpDownIcon, ArrowDownIcon, ArrowUpIcon, ThumbsDown, InfoIcon, BrainIcon } from 'lucide-vue-next'
import type { BidingAnalysisItem } from '../types' // Adjust path if necessary
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc' // 导入 UTC 插件
import { useUserStore } from '@/stores/user'
import { usePost } from '@/hooks/useHttp'
import { ElMessage, ElMessageBox, ElPopconfirm, ElPopover } from 'element-plus'
import { useRouter } from 'vue-router'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

dayjs.extend(utc) // 使用 UTC 插件

// 定义排序相关的 Prop 类型
type SortOrder = 'asc' | 'desc' | null

const props = defineProps<{
  items: BidingAnalysisItem[]
  isLoading: boolean
  apiLoading: boolean
  totalOriginalItems: number
  getScoreClass:(score: number | undefined) => string
  formatDateString: (dateInput: any) => string
  // 新增 props 用于接收排序状态
  sortKey: string | null
  sortOrder: SortOrder
  // isAgentWorking?: boolean // Add if specific styling/logic needed here
}>()

const emit = defineEmits(['view-item-detail', 'reset-filters', 'sort', 'batch-unlike', 'selection-change', 'ai-processing'])
const { t } = useI18n()
const userStore = useUserStore()
const router = useRouter()

// 批量选择状态
const selectedItems = ref<string[]>([])

// AI 确认 Popover 状态
const aiConfirmPopovers = ref<Record<string, boolean>>({})

// 根据推荐分数获取Badge样式
const getRecommendScoreClass = (score: number | undefined): string => {
  if (score === undefined || score === null || isNaN(Number(score))) { 
    return 'bg-gray-100 text-gray-600' 
  }

  if (score >= 8) {return 'bg-green-100 text-green-800'}
  if (score >= 6) {return 'bg-blue-100 text-blue-800'}
  if (score >= 4) {return 'bg-yellow-100 text-yellow-800'}
  return 'bg-red-100 text-red-800'
}

// 批量不喜欢API调用
const { execute: batchUnlike, isLoading: isUnliking } = usePost<{
  success: boolean;
  message?: string;
}>(
  '/formatted_report/v1/recommendations/unlike',
  undefined,
  {},
  { immediate: false }
)

// 计算属性
const isAllSelected = computed(() => {
  return props.items.length > 0 && selectedItems.value.length === props.items.length
})

const isIndeterminate = computed(() => {
  return selectedItems.value.length > 0 && selectedItems.value.length < props.items.length
})

// 监听选中项变化，通知父组件
watch(selectedItems, (newSelection) => {
  console.log('🔍 BidingTableView: selectedItems changed:', {
    newSelection,
    length: newSelection.length,
    isAllSelected: isAllSelected.value,
    isIndeterminate: isIndeterminate.value,
    isUnliking: isUnliking.value
  })
  
  const selectionData = {
    selectedItems: newSelection,
    isAllSelected: isAllSelected.value,
    isIndeterminate: isIndeterminate.value,
    selectedCount: newSelection.length,
    isUnliking: isUnliking.value
  }
  
  console.log('🚀 BidingTableView: emitting selection-change:', selectionData)
  emit('selection-change', selectionData)
}, { deep: true })

// --- 新增：判断截止日期是否过期 ---
const isDeadlineExpired = (deadlineInput: string | Date | { $date: string } | undefined | null): boolean => {
  if (!deadlineInput) {
    return false // 没有截止日期，不算过期
  }

  let dateString: string | undefined

  // 尝试解析不同格式的日期输入
  if (typeof deadlineInput === 'string') {
    dateString = deadlineInput
  } else if (deadlineInput instanceof Date) {
    dateString = deadlineInput.toISOString()
  } else if (typeof deadlineInput === 'object' && deadlineInput !== null && '$date' in deadlineInput) {
    dateString = deadlineInput.$date
  } else if (typeof deadlineInput === 'number' && deadlineInput > 0) {
    try {
      dateString = new Date(deadlineInput).toISOString()
    } catch (e) { /* ignore */ }
  }

  if (!dateString) {
    return false // 无法解析日期字符串
  }

  const deadlineDate = dayjs.utc(dateString) // 按 UTC 解析
  if (!deadlineDate.isValid()) {
    console.warn('Invalid deadline date encountered:', deadlineInput)
    return false // 无效日期
  }

  // 与 UTC 当天的开始时间比较
  const todayUTCStart = dayjs.utc().startOf('day')
  return deadlineDate.isBefore(todayUTCStart)
}
// ------------------------------

const handleViewItemDetail = (item: BidingAnalysisItem) => {
  emit('view-item-detail', item)
}

const handleResetFilters = () => {
  emit('reset-filters')
}

// 批量选择相关方法
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedItems.value = props.items.map(item => item._id)
  } else {
    selectedItems.value = []
  }
}

const handleItemSelect = (itemId: string, checked: boolean) => {
  console.log('📋 BidingTableView: handleItemSelect called:', { itemId, checked, currentSelection: selectedItems.value })
  
  if (checked) {
    if (!selectedItems.value.includes(itemId)) {
      selectedItems.value.push(itemId)
    }
  } else {
    const index = selectedItems.value.indexOf(itemId)
    if (index > -1) {
      selectedItems.value.splice(index, 1)
    }
  }
  
  console.log('📋 BidingTableView: after handleItemSelect:', { 
    newSelection: selectedItems.value,
    length: selectedItems.value.length 
  })
}

const clearSelection = () => {
  selectedItems.value = []
}

// 批量不喜欢操作
const handleBatchUnlike = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning(t('biding.no_items_selected'))
    return
  }

  // 批量unlike的重量级确认交互
  try {
    await ElMessageBox.confirm(
      `
        <div style="margin-bottom: 16px;">
          ${t('biding.batch_unlike_confirm_message', { count: selectedItems.value.length })}
        </div>
        <div style="color: #f56565; font-size: 14px; padding: 12px; background-color: #fef5e7; border-radius: 6px; border-left: 4px solid #f56565;">
          ${t('biding.batch_unlike_confirm_warning')}
        </div>
      `,
      t('biding.batch_unlike_confirm_title'),
      {
        confirmButtonText: t('biding.confirm_action'),
        cancelButtonText: t('biding.cancel_action'),
        dangerouslyUseHTMLString: true,
        confirmButtonClass: 'el-button--danger',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showCancelButton: true,
        showConfirmButton: true,
        icon: ''
      }
    )
  } catch {
    // 用户取消操作
    return
  }

  const companyId = userStore.userInfo?.company?.id
  if (!companyId) {
    ElMessage.error(t('biding.company_id_missing'))
    return
  }

  try {
    const result = await batchUnlike(0, {
      bidding_ids: selectedItems.value,
      company_id: companyId
    })

    if (result?.success) {
      ElMessage.success(t('biding.batch_unlike_success'))
      clearSelection()
      // 通知父组件刷新数据
      emit('batch-unlike', selectedItems.value)
    } else {
      ElMessage.error(result?.message || t('biding.batch_unlike_failed'))
    }
  } catch (error) {
    console.error('Batch unlike failed:', error)
    ElMessage.error(t('biding.batch_unlike_failed'))
  }
}

// 单个项目不喜欢操作
const handleSingleUnlike = async (itemId: string) => {
  const companyId = userStore.userInfo?.company?.id
  if (!companyId) {
    ElMessage.error(t('biding.company_id_missing'))
    return
  }

  try {
    const result = await batchUnlike(0, {
      bidding_ids: [itemId],
      company_id: companyId
    })

    if (result?.success) {
      ElMessage.success(t('biding.single_unlike_success'))
      // 通知父组件刷新数据
      emit('batch-unlike', [itemId])
    } else {
      ElMessage.error(result?.message || t('biding.single_unlike_failed'))
    }
  } catch (error) {
    console.error('Single unlike failed:', error)
    ElMessage.error(t('biding.single_unlike_failed'))
  }
}

// 处理排序点击的方法
const handleSort = (key: string) => {
  let newOrder: SortOrder = 'desc' // 默认点击新列时降序

  if (props.sortKey === key) {
    // 如果点击的是当前排序列，则切换排序方向
    newOrder = props.sortOrder === 'desc' ? 'asc' : 'desc'
  }

  // 修正：确保提供的 key 与 BidingAnalysisItem 中的字段匹配
  // 假设评分字段是 'scoring.comprehensive.score'
  // 假设截止日期字段是 'extraction.basic_info.submission_deadline'
  // 假设更新时间字段是 'update_time'
  // 这些 key 需要在父组件中用于实际排序
  emit('sort', { key: key, order: newOrder })
}

// 辅助函数，用于获取排序图标组件
const getSortIcon = (key: string) => {
  if (props.sortKey !== key) {
    return ArrowUpDownIcon // 未排序状态图标
  }
  if (props.sortOrder === 'asc') {
    return ArrowUpIcon // 升序图标
  }
  return ArrowDownIcon // 降序图标
}

// 辅助函数，用于获取排序图标的 class
const getSortIconClass = (key: string) => {
  return props.sortKey === key ? 'text-blue-600' : 'text-slate-400'
}

// AI 流程处理方法
const handleAiProcessing = async (item: BidingAnalysisItem) => {
  try {
    await ElMessageBox.confirm(
      `
        <div style="margin-bottom: 16px; color: #374151;">
          即将在 AI Dashboard 中为「${item.new_title}」进行深度分析处理
        </div>
        <div style="color: #3b82f6; font-size: 14px; padding: 12px; background-color: #eff6ff; border-radius: 6px; border-left: 4px solid #3b82f6;">
          <strong>AI 分析将包括：</strong><br/>
          • 项目可行性评估<br/>
          • 竞争力分析<br/>
          • 投标策略建议<br/>
          • 风险评估
        </div>
      `,
      'AI 深度分析确认',
      {
        confirmButtonText: '开始 AI 分析',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        confirmButtonClass: 'el-button--primary',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        showCancelButton: true,
        showConfirmButton: true,
        icon: ''
      }
    )
    
    // 发射 AI 处理事件给父组件，让父组件打开 Sheet
    emit('ai-processing', {
      projectId: item._id,
      projectTitle: item.new_title,
      item: item
    })
    
  } catch {
    // 用户取消操作，不执行任何操作
    return
  }
}

// AI 确认处理方法
const confirmAiProcessing = (item: BidingAnalysisItem) => {
  // 关闭 popover
  aiConfirmPopovers.value[item._id] = false
  
  // 发射 AI 处理事件给父组件，让父组件打开 Sheet
  emit('ai-processing', {
    projectId: item._id,
    projectTitle: item.new_title,
    item: item
  })
}

const cancelAiProcessing = (itemId: string) => {
  // 关闭 popover
  aiConfirmPopovers.value[itemId] = false
}

// AI 确认处理方法
const handleConfirmAiProcessing = (item: BidingAnalysisItem) => {
  // 关闭 dropdown
  aiConfirmPopovers.value[item._id] = false
  
  // 发射 AI 处理事件给父组件，让父组件打开 Sheet
  emit('ai-processing', {
    projectId: item._id,
    projectTitle: item.new_title,
    item: item
  })
}

const handleCancelAiProcessing = (itemId: string) => {
  // 关闭 dropdown
  aiConfirmPopovers.value[itemId] = false
}

// 暴露方法给父组件
defineExpose({
  clearSelection,
  handleBatchUnlike
})
</script>

<style scoped>
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
}
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}

/* 自定义动画效果 */
.batch-operations-enter-active {
  transition: all 0.3s ease-out;
}

.batch-operations-leave-active {
  transition: all 0.2s ease-in;
}

.batch-operations-enter-from {
  opacity: 0;
  transform: translateY(-8px);
}

.batch-operations-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

/* 选中行的微妙动画 */
.row-selected {
  animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
  0% {
    background-color: rgb(239 246 255 / 0.5);
    transform: scale(1);
  }
  50% {
    background-color: rgb(219 234 254 / 0.8);
    transform: scale(1.002);
  }
  100% {
    background-color: rgb(239 246 255 / 0.8);
    transform: scale(1);
  }
}

/* Checkbox hover效果增强 */
:deep(.checkbox-root) {
  transition: all 0.2s ease-in-out;
}

:deep(.checkbox-root:hover) {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 批量操作按钮动画 */
.batch-button {
  transition: all 0.2s ease-in-out;
}

.batch-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.batch-button:active {
  transform: translateY(0);
}

/* el-popconfirm 样式定制 */
:deep(.el-popconfirm) {
  --el-popconfirm-border-radius: 8px;
  --el-popconfirm-title-font-size: 14px;
  --el-popconfirm-title-color: #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
}

:deep(.el-popconfirm__main) {
  padding: 12px 16px;
  margin-bottom: 8px;
}

:deep(.el-popconfirm__action) {
  padding: 0 16px 12px;
  text-align: right;
}

:deep(.el-popconfirm__action .el-button) {
  margin-left: 8px;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 6px;
}

:deep(.el-popconfirm__action .el-button--danger) {
  background-color: #ef4444;
  border-color: #ef4444;
  color: white;
}

:deep(.el-popconfirm__action .el-button--danger:hover) {
  background-color: #dc2626;
  border-color: #dc2626;
}

:deep(.el-popconfirm__action .el-button--default) {
  background-color: #f8fafc;
  border-color: #e2e8f0;
  color: #64748b;
}

:deep(.el-popconfirm__action .el-button--default:hover) {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

/* 推荐理由 popover 样式定制 */
:deep(.recommend-reason-popover) {
  max-width: 280px !important;
  min-width: 200px !important;
  width: auto !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e5e7eb !important;
  background-color: #ffffff !important;
}

:deep(.recommend-reason-popover .el-popover__content) {
  padding: 12px 16px !important;
  margin: 0 !important;
  max-width: none !important;
  width: auto !important;
}

/* 推荐理由内容样式 */
:deep(.recommend-reason-content) {
  max-width: 260px !important;
  width: auto !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  color: #374151 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 推荐理由图标动画效果 */
.recommend-reason-icon {
  transition: all 0.2s ease-in-out;
}

.recommend-reason-icon:hover {
  transform: scale(1.1);
  color: #3b82f6 !important;
}
</style>