<!-- Generated by SpecStory -->

# Optimizing Menu Logic in Vue Components (2025-02-21 15:34:37)

_**User**_

优化 @Header.vue 对于菜单显示的处理逻辑，注意，需要同步修改 @menu.guard.3.ts 的逻辑

---

_**Assistant**_

```thinking
Let me analyze this request carefully and think through the optimization needs...

First, let me understand the current implementation:
1. Header.vue handles menu display logic:
- Uses a menuConfig computed property to define all possible menu items
- Has a getMenuStatus helper to check if menu is enabled
- buildIntelligenceMenu specifically handles intelligence related menus
- Final buttons computed property filters and builds the menu array

2. menu.guard.3.ts handles route guards:
- Uses routeToMenuMap to map routes to menu IDs
- Checks menu access permissions
- Handles redirects for disabled menus

Some potential issues I notice:
- Menu configuration is duplicated between files (routeToMenuMap and menuConfig)
- Special handling for intelligence menus is scattered
- Route checking logic could be more centralized
- Menu structure could be more declarative/config driven

Key optimization opportunities:
1. Create a centralized menu configuration that both files can use
2. Make menu structure more declarative and easier to maintain
3. Improve type safety and reduce duplication
4. Simplify special case handling for nested menus

Let me think about the specific changes needed...

For the menu configuration:
- Should move to a separate config file that both can import
- Need to include all menu metadata: route, id, label key, etc
- Should support nested menu structure natively

For the guard logic:
- Can simplify by using the centralized config
- Make route checking more robust
- Improve type safety of the configuration

For the header component:
- Simplify menu building logic
- Make it more declarative
- Remove special case handling where possible

Let me plan out the specific implementation...

Implementation approach:
1. Create new menu config file
2. Update guard to use new config
3. Refactor header component
4. Add proper typing

This seems like a good optimization that will improve maintainability while preserving functionality.

Let me proceed with the implementation...
```

I'll help optimize the menu handling logic. Here's what I propose to do:

English:
1. Create a centralized menu configuration
2. Simplify the menu building logic
3. Improve type safety
4. Make the code more maintainable

中文：
1. 创建统一的菜单配置
2. 简化菜单构建逻辑
3. 提升类型安全
4. 提高代码可维护性

Let me implement these changes step by step:

1. First, I'll create a new menu configuration file:

---

```diff
- 
+ import { type RouteLocationNormalized } from 'vue-router'
+ 
+ export interface MenuItem {
+   key: string
+   route: string
+   menuId: string
+   labelKey: string
+   children?: MenuItem[]
+ }
+ 
+ export const menuItems: MenuItem[] = [
+   {
+     key: 'information',
+     route: '/',
+     menuId: 'information',
+     labelKey: 'menu.topInfoMenu'
+   },
+   {
+     key: 'intelligence',
+     route: '/intelligence',
+     menuId: 'daily_report',
+     labelKey: 'menu.topIntelligenceMenu',
+     children: [
+       {
+         key: 'dailyReport',
+         route: '/intelligence',
+         menuId: 'daily_report',
+         labelKey: 'intelligence.menu.dailyReport'
+       },
+       {
+         key: 'formatReport',
+         route: '/format-report',
+         menuId: 'format_report',
+         labelKey: 'intelligence.menu.formatReport'
+       }
+     ]
+   },
+   {
+     key: 'chatBot',
+     route: '/robot',
+     menuId: 'chat_bot',
+     labelKey: 'menu.topRobotMenu'
+   },
+   {
+     key: 'procurement',
+     route: '/procurement',
+     menuId: 'procurement',
+     labelKey: 'menu.topProcurementMenu'
+   },
+   {
+     key: 'dashboard',
+     route: '/dashboard',
+     menuId: 'dashboard',
+     labelKey: 'menu.topDashboardMenu'
+   }
+ ]
+ 
+ export const getMenuByRoute = (route: string): MenuItem | undefined => {
+   const findInItems = (items: MenuItem[]): MenuItem | undefined => {
+     for (const item of items) {
+       if (item.route === route) return item
+       if (item.children) {
+         const found = findInItems(item.children)
+         if (found) return found
+       }
+     }
+     return undefined
+   }
+   return findInItems(menuItems)
+ }
+ 
+ export const isRouteInMenu = (route: RouteLocationNormalized): boolean => {
+   const path = route.path
+   return menuItems.some(item => {
+     if (path === item.route || path.startsWith(`${item.route}/`)) return true
+     if (item.children) {
+       return item.children.some(child => 
+         path === child.route || path.startsWith(`${child.route}/`)
+       )
+     }
+     return false
+   })
+ }
+ 
+ export const getRouteToMenuMap = (): Record<string, string> => {
+   const map: Record<string, string> = {}
+   const addToMap = (items: MenuItem[]) => {
+     items.forEach(item => {
+       map[item.route] = item.menuId
+       if (item.children) {
+         addToMap(item.children)
+       }
+     })
+   }
+   addToMap(menuItems)
+   return map
+ } 
```

---



Let me fix the linter errors in the menu config file:

---

```diff
    const findInItems = (items: MenuItem[]): MenuItem | undefined => {
      for (const item of items) {
-       if (item.route === route) return item
+       if (item.route === route) { return item }
        if (item.children) {
          const found = findInItems(item.children)
-         if (found) return found
+         if (found) { return found }
        }
      }
```

```diff
    const path = route.path
    return menuItems.some(item => {
-     if (path === item.route || path.startsWith(`${item.route}/`)) return true
+     if (path === item.route || path.startsWith(`${item.route}/`)) { return true }
      if (item.children) {
        return item.children.some(child => 
```

---



Now let's update the menu guard to use the new configuration:

---

```diff
  import { useUserStore } from '@/stores/user'
  import { redirectToPath } from '@/utils/router'
- 
- // Map routes to menu IDs
- const routeToMenuMap: Record<string, string> = {
-   '/': 'information',
-   '/intelligence': 'daily_report',
-   '/format-report': 'format_report',
-   '/robot': 'chat_bot'
- }
+ import { menuItems, isRouteInMenu, getRouteToMenuMap } from '@/config/menu.config'
+ 
+ const routeToMenuMap = getRouteToMenuMap()
  
- // 获取第一个可访问的路由
+ // Get first accessible route
  const getFirstAccessibleRoute = (userStore: ReturnType<typeof useUserStore>) => {
-   const routeOrder = [
-     { path: '/', menuId: 'information' },
-     { path: '/intelligence', menuId: 'daily_report' },
-     { path: '/format-report', menuId: 'format_report' },
-     { path: '/robot', menuId: 'chat_bot' },
-   ]
- 
-   return routeOrder.find(route => 
-     userStore.menuConfig.find(menu => 
-       menu.id === route.menuId && menu.status
-     )
-   )?.path
- }
- 
- // 检查是否是需要菜单权限的路由
- const isMenuProtectedRoute = (path: string): boolean => {
-   // 直接检查是否在 routeToMenuMap 中或是其子路由
-   return Object.keys(routeToMenuMap).some(route => 
-     path === route || path.startsWith(`${route}/`)
-   )
+   for (const item of menuItems) {
+     if (userStore.menuConfig.find(menu => menu.id === item.menuId && menu.status)) {
+       return item.route
+     }
+     if (item.children) {
+       for (const child of item.children) {
+         if (userStore.menuConfig.find(menu => menu.id === child.menuId && menu.status)) {
+           return child.route
+         }
+       }
+     }
+   }
+   return undefined
  }
  
  router.beforeEach(async (to, from, next) => {
-   // 如果路由不在菜单映射表中，直接放行
-   if (!isMenuProtectedRoute(to.path)) {
+   // Skip check for non-menu routes
+   if (!isRouteInMenu(to)) {
      return next()
    }
```

```diff
    let menuId = routeToMenuMap[to.path]
    
-   // For nested routes under /intelligence
-   if (to.path.startsWith('/intelligence/')) {
-     menuId = 'daily_report'
-   }
-   if (to.path.startsWith('/format-report/')) {
-     menuId = 'format_report'
-   }
-   if (to.path.startsWith('/robot/')) {
-     menuId = 'chat_bot'
+   // For nested routes
+   if (!menuId) {
+     for (const [route, id] of Object.entries(routeToMenuMap)) {
+       if (to.path.startsWith(`${route}/`)) {
+         menuId = id
+         break
+       }
+     }
    }
  
```

---



Finally, let's update the Header component to use the new menu configuration:

---

```diff
  import LanguageSwitcher from './LanguageSwitch.vue'
  import { Icon } from '@iconify/vue'
+ import { menuItems, type MenuItem } from '@/config/menu.config'
  
  defineProps({
```

```diff
    },
  })
+ 
  const { locale, t } = useI18n()
  const userStore = useUserStore()
```

```diff
  }
  
- interface MenuItem {
-   key: string
-   label: string
-   menuId: string
-   selected: boolean
-   children?: MenuItem[]
- }
- 
- // 定义菜单配置
- const menuConfig = computed(() => ({
-   information: {
-     key: '/',
-     label: t('menu.topInfoMenu'),
-     menuId: 'information',
-     selected: route.path === '/',
-   },
-   dailyReport: {
-     key: '/intelligence',
-     label: t('intelligence.menu.dailyReport'),
-     menuId: 'daily_report',
-     selected: route.path.includes('/intelligence'),
-   },
-   formatReport: {
-     key: '/format-report',
-     label: t('intelligence.menu.formatReport'),
-     menuId: 'format_report',
-     selected: route.path.includes('/format-report'),
-   },
-   chatBot: {
-     key: '/robot',
-     label: t('menu.topRobotMenu'),
-     menuId: 'chat_bot',
-     selected: route.path.includes('/robot'),
-   },
-   procurement: {
-     key: '/procurement',
-     label: t('menu.topProcurementMenu'),
-     menuId: 'procurement',
-     selected: route.path.includes('/procurement'),
-   },
-   dashboard: {
-     key: '/dashboard',
-     label: t('menu.topDashboardMenu'),
-     menuId: 'dashboard',
-     selected: route.path.includes('/dashboard'),
-   },
- }))
- 
- // 获取菜单状态的函数保持不变
- const getMenuStatus = (menuId: string) => {
-   return userStore.menuConfig.find(item => item.id === menuId)?.status ?? false
- }
- 
- // 构建智能报告菜单
- const buildIntelligenceMenu = (dailyReport: MenuItem, formatReport: MenuItem) => {
-   const hasDailyReport = getMenuStatus(dailyReport.menuId)
-   const hasFormatReport = getMenuStatus(formatReport.menuId)
- 
-   if (!hasDailyReport && !hasFormatReport) {return []}
-   
-   if (hasDailyReport && hasFormatReport) {
-     return [{
-       key: '/intelligence',
-       label: t('menu.topIntelligenceMenu'),
-       menuId: 'daily_report',
-       selected: dailyReport.selected || formatReport.selected,
-       children: [dailyReport, formatReport]
-     }]
-   }
- 
-   return [
-     hasDailyReport && dailyReport,
-     hasFormatReport && formatReport
-   ].filter(Boolean) as MenuItem[]
- }
- 
- // 构建菜单按钮
+ // Transform menu item to button props
+ const transformMenuItem = (item: MenuItem, parentSelected = false): any => {
+   const isSelected = parentSelected || 
+     route.path === item.route || 
+     route.path.startsWith(`${item.route}/`)
+ 
+   return {
+     key: item.route,
+     label: t(item.labelKey),
+     menuId: item.menuId,
+     selected: isSelected,
+     children: item.children?.map(child => 
+       transformMenuItem(child, isSelected)
+     )
+   }
+ }
+ 
+ // Build menu buttons
  const buttons = computed(() => {
-   const config = menuConfig.value
-   const menuItems: MenuItem[] = []
- 
-   // 添加信息菜单
-   if (getMenuStatus(config.information.menuId)) {
-     menuItems.push(config.information)
-   }
- 
-   // 添加智能报告菜单（每日报告和制式报告）
-   menuItems.push(...buildIntelligenceMenu(config.dailyReport, config.formatReport))
- 
-   // 添加机器人菜单
-   if (getMenuStatus(config.chatBot.menuId)) {
-     menuItems.push(config.chatBot)
-   }
- 
-   // 添加采购菜单
-   if (getMenuStatus(config.procurement.menuId)) {
-     menuItems.push(config.procurement)
-   }
- 
-   // 添加仪表盘菜单
-   if (getMenuStatus(config.dashboard.menuId)) {
-     menuItems.push(config.dashboard)
+   return menuItems
+     .filter(item => {
+       // Check parent menu status
+       const parentEnabled = userStore.menuConfig.find(
+         menu => menu.id === item.menuId
+       )?.status
+ 
+       // For items with children, check if any child is enabled
+       if (item.children) {
+         const hasEnabledChild = item.children.some(child =>
+           userStore.menuConfig.find(
+             menu => menu.id === child.menuId
+           )?.status
+         )
+         return parentEnabled || hasEnabledChild
        }
  
-   return menuItems
+       return parentEnabled
+     })
+     .map(item => transformMenuItem(item))
  })
  
```

---



Let me explain the changes I've made:

English:
1. Created a new `menu.config.ts` file that centralizes all menu configuration:
   - Defined interfaces for menu items
   - Created a single source of truth for menu structure
   - Added utility functions for menu operations

2. Updated `menu.guard.3.ts`:
   - Removed hardcoded route mappings
   - Simplified route checking logic
   - Improved handling of nested routes
   - Made the code more maintainable

3. Refactored `Header.vue`:
   - Removed duplicate menu configuration
   - Simplified menu building logic
   - Improved type safety
   - Better handling of nested menus
   - More declarative approach to menu structure

Key benefits:
- Single source of truth for menu configuration
- Better type safety
- More maintainable code
- Simplified logic
- Better handling of nested menus

中文：
1. 创建了新的 `menu.config.ts` 文件，集中管理所有菜单配置：
   - 定义了菜单项接口
   - 创建了菜单结构的单一数据源
   - 添加了菜单操作的工具函数

2. 更新了 `menu.guard.3.ts`：
   - 移除了硬编码的路由映射
   - 简化了路由检查逻辑
   - 改进了嵌套路由的处理
   - 提高了代码可维护性

3. 重构了 `Header.vue`：
   - 移除了重复的菜单配置
   - 简化了菜单构建逻辑
   - 提高了类型安全性
   - 更好地处理嵌套菜单
   - 更声明式的菜单结构方法

主要优势：
- 菜单配置的单一数据源
- 更好的类型安全性
- 更易维护的代码
- 简化的逻辑
- 更好的嵌套菜单处理

Would you like me to make any adjustments to these changes?

---

