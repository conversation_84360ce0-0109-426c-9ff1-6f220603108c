<template>
  <div :class="$style['step-container']">
    <el-steps :active="active + 1" :align-center="true">
      <el-step 
        v-for="(step, index) in steps" 
        :key="index"
        :title="step.title"
        :description="step.description"
        :icon="step.icon"
        :status="step.status">
        <template #icon>
          <div 
            :class="[
              $style['step-icon'],
              active > index ? $style['step-finished'] : $style['step-waiting'],
              active === index ? $style['step-current'] : ''
            ]">
            <template v-if="active > index">
              <!-- <check theme="outline" size="12" fill="#fff"/> -->
              <Icon icon="mdi:check" class="text-[#fff] text-[12px]"/>
            </template>
            <template v-else>
              {{ index + 1 }}
            </template>
          </div>
        </template>
        <template #title>
          <span :class="[
            $style['step-title'],
            active > index ? $style['title-finished'] : $style['title-waiting'],
            active === index ? $style['title-current'] : ''
          ]">
            {{ step.title }}
          </span>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { Icon } from '@iconify/vue'

interface Step {
  title: string;
  description?: string;
  icon?: string;
  status?: 'wait' | 'process' | 'finish' | 'error' | 'success';
}

defineProps({
  steps: {
    type: Array as PropType<Step[]>,
    required: true
  },
  active: {
    type: Number,
    default: 1
  }
})
</script>

<style lang="scss" module>
// 变量定义
$primary-color: var(--primary-color);
$waiting-color: #E5E7EB;
$text-waiting: #6B7280;
$icon-size: 32px;

.step-container {
  
  :global {
    .el-step__line {
      background-color: $waiting-color !important;
      
      &.is-finish {
        background-color: $primary-color !important;
      }
    }
    
    .el-step__head {
      &.is-finish {
        color: $primary-color !important;
        border-color: $primary-color !important;
      }
      
      &.is-process {
        color: $primary-color !important;
        border-color: $primary-color !important;
      }
    }
  }
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: $icon-size;
  height: $icon-size;
  border-radius: 50%;
  font-weight: 500;
  transition: all 0.3s ease;
}

.step-waiting {
  background-color: $waiting-color;
  color: $text-waiting;
}

.step-finished,
.step-current {
  background-color: $primary-color;
  color: white;
}

.step-title {
  font-size: 14px;
  transition: color 0.3s ease;
}

.title-waiting {
  color: $text-waiting;
}

.title-finished,
.title-current {
  color: $primary-color;
  font-weight: 500;
}
</style>
