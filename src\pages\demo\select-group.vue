<script setup lang="ts">
import ElSelectV2Group from '@/components/ElSelectV2Group.vue'
import overflowText from '@/components/overflow-text.vue'
import { ref } from 'vue'
const mockGitProject = ref([{ 'label':'liuxiaoting', 'options':[{ 'label':'liuxiaoting/alex-demosadsadsadsasdas1212121asdas212121212121adsa2sd121212', 'value':29 }] }, { 'label':'ccy_pre', 'options':[{ 'label':'ccy_pre/ccy_zk', 'value':30 }] }, { 'label':'yiye', 'options':[{ 'label':'yiye/test_project_1', 'value':6 }, { 'label':'yiye/test_project_2', 'value':7 }] }, { 'label':'liga', 'options':[{ 'label':'liga/demo', 'value':3 }, { 'label':'liga/test', 'value':4 }, { 'label':'liga/springtest', 'value':5 }, { 'label':'liga/dddd', 'value':8 }, { 'label':'liga/yytest', 'value':20 }, { 'label':'liga/kongbai', 'value':37 }] }, { 'label':'root', 'options':[{ 'label':'root/hs_001', 'value':2 }, { 'label':'root/spring-test', 'value':9 }, { 'label':'root/bytebase', 'value':10 }, { 'label':'root/0421_testproject_001', 'value':11 }, { 'label':'root/0421008_test', 'value':12 }, { 'label':'root/0602001_test', 'value':15 }, { 'label':'root/project_0718001', 'value':16 }, { 'label':'root/20220919001', 'value':25 }, { 'label':'root/0921001', 'value':26 }, { 'label':'root/0922001', 'value':27 }, { 'label':'root/0928001', 'value':28 }, { 'label':'root/20221103001', 'value':31 }, { 'label':'root/integration_', 'value':33 }, { 'label':'root/test1', 'value':36 }] }, { 'label':'ccy', 'options':[{ 'label':'ccy/ccy_project', 'value':17 }, { 'label':'ccy/awesome_project_sample', 'value':18 }, { 'label':'ccy/new-project2022', 'value':21 }, { 'label':'ccy/happy', 'value':22 }, { 'label':'ccy/sync_update', 'value':23 }, { 'label':'ccy/yproject2', 'value':32 }] }, { 'label':'zhouxianjun', 'options':[{ 'label':'zhouxianjun/demo-vue', 'value':34 }] }, { 'label':'zhaohui', 'options':[{ 'label':'zhaohui/dddddddd', 'value':39 }] }, { 'label':'zyytest1', 'options':[{ 'label':'zyytest1/nerd4me', 'value':24 }] }, { 'label':'gitlab-instance-86afe7c0', 'options':[{ 'label':'gitlab-instance-86afe7c0/Monitoring', 'value':1 }] }, { 'label':'liujianqiao', 'options':[{ 'label':'liujianqiao/html-pages', 'value':38 }] }])

const initials = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j']

const currentProject = ref<number[]>([])

const value = ref([])
const value2 = ref([])
const normalValue = ref<number[]>([])
const options = Array.from({ length: 10 }).map((_, idx) => {
  const label = idx + 1
  return {
    value: `Group ${label}`,
    label: `Group ${label}`,
    options: Array.from({ length: 10 }).map((_, idx) => ({
      value: `Option ${idx + 1 + 10 * label}`,
      label: `${initials[idx % 10]}${idx + 1 + 10 * label}`,
    })),
  }
})
const normalOptions = Array.from({ length: 10 }).map((_, idx) => ({
  value: `Option ${idx + 1}`,
  label: `Option ${idx + 1}`,
}))
const handleOnlySelf = async (id: number) => {
  currentProject.value = [id]
}
const handleSelectProject = async (projectIds: number[]) => {
  currentProject.value = projectIds
}
const updateValue = (value: number[])=>{
  console.log('🚀 ~ updateValue ~ value:', value)
  normalValue.value = value
}
</script>

<template>
  <ElSelectV2Group
    v-model="value2"
    :options="options"
    style="width: 240px"
    :multiple-limit="15"
    multiple
    hidden-checked-icon
    disabled-group-hover
    group-divider>
    <template #group="{ item }">
      <el-checkbox :model-value="item.checked"
                   :disabled="item.disabled"
                   :indeterminate="item.indeterminate"
                   :label="item.label"/>
    </template>
    <template #item="{ item }">
      <overflow-text>
        {{ item.label }}
      </overflow-text>
    </template>
  </ElSelectV2Group>
  分组互斥：
  <ElSelectV2Group
    v-model="value"
    :options="options"
    style="width: 240px"
    :multiple-limit="15"
    multiple
    hidden-checked-icon
    disabled-group-hover
    group-divider
    isGroupExclusive>
    <template #group="{ item }">
      <el-checkbox :model-value="item.checked"
                   :disabled="item.disabled"
                   :indeterminate="item.indeterminate"
                   :label="item.label"/>
    </template>
    <template #item="{ item }">
      <overflow-text>
        {{ item.label }}
      </overflow-text>
    </template>
  </ElSelectV2Group>
</template>
