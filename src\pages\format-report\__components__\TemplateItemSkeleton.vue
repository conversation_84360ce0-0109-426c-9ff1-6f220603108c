<template>
  <div :class="$style['skeleton-card']">
    <div :class="$style['card-content']">
      <div :class="[$style['icon-wrapper']]">
        <div :class="[$style['skeleton'], $style['skeleton-icon']]"></div>
      </div>
      <div :class="$style['titles']">
        <div :class="[$style['skeleton'], $style['skeleton-title']]"></div>
      </div>
      <div :class="[$style['skeleton'], $style['skeleton-description']]"></div>
      <div :class="[$style['skeleton'], $style['skeleton-description']]"></div>
    </div>
    <div :class="$style['card-footer']">
      <div :class="[$style['skeleton'], $style['skeleton-tag']]"></div>
      <div :class="[$style['skeleton'], $style['skeleton-arrow']]"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  height?: string;
  width?: string;
}>()
</script>

<style lang="scss" module>
.skeleton-card {
  background: var(--main-bg-color);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(68, 102, 188, 0.1);
  box-shadow: 0 4px 6px rgba(68, 102, 188, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  min-height: 280px;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.icon-wrapper {
  background: rgba(68, 102, 188, 0.05);
  border-radius: 50%;
  padding: 16px;
  margin-bottom: 20px;
}

.skeleton {
  background: linear-gradient(
    90deg,
    rgba(68, 102, 188, 0.05) 25%,
    rgba(68, 102, 188, 0.1) 37%,
    rgba(68, 102, 188, 0.05) 63%
  );
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4px;
}

.skeleton-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.skeleton-title {
  width: 80%;
  height: 24px;
  margin: 0 auto 12px;
}

.skeleton-description {
  width: 100%;
  height: 16px;
  margin-bottom: 8px;

  &:last-child {
    width: 60%;
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid rgba(68, 102, 188, 0.1);
}

.skeleton-tag {
  width: 60px;
  height: 22px;
  border-radius: 12px;
}

.skeleton-arrow {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
</style>
