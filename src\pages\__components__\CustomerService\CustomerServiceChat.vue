<template>
  <div class="space-y-6">
    <template v-for="message in messages" :key="message.timestamp">
      <!-- 用户消息 -->
      <div v-if="message.sender === 'user'" class="flex justify-end space-x-2">
        <div class="max-w-[80%]">
          <div class="bg-[#4466BC] text-white px-4 py-2.5 rounded-2xl rounded-tr-sm hover:bg-[#3355AB] transition-colors shadow-xs">
            {{ message.content }}
          </div>
        </div>
      </div>

      <!-- 系统消息和客服消息 -->
      <div v-else class="flex justify-start space-x-2">
        <div class="max-w-[80%]">
          <div class="bg-gray-100 text-gray-800 px-4 py-2.5 rounded-2xl rounded-tl-sm hover:bg-gray-200 transition-colors shadow-xs">
            {{ message.content }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import type { CustomerServiceMessage } from '@/pages/robot/type'

defineProps<{
  messages: CustomerServiceMessage[]
}>()
</script> 