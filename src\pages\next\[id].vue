<template>
  <div :class="$style['next-iframe-page']" class="bg-gray-50">
    <!-- 页面无效状态 -->
    <div 
      v-if="!currentPageConfig" 
      class="flex items-center justify-center h-screen bg-gradient-to-br from-red-50 to-red-100">
      <div class="max-w-md text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon icon="mdi:alert-circle" class="w-8 h-8 text-red-600"/>
        </div>
        <h1 class="text-2xl font-bold text-red-900 mb-3">页面不存在</h1>
        <p class="text-red-700 mb-6">请求的页面 "{{ pageId }}" 不存在或不支持</p>
        <Button @click="navigateBack" class="bg-red-600 hover:bg-red-700 text-white">
          <Icon icon="mdi:arrow-left" class="w-4 h-4 mr-2"/>
          返回
        </Button>
      </div>
    </div>

    <!-- 使用 IframeWrapper 替代 IframeContainer -->
    <IframeWrapper
      v-else-if="iframeSrc"
      :src="iframeSrc"
      :token="authToken"
      :from-og="false"
      :locale="currentLocale"
      :title="pageTitle"
      :height="viewportHeight"
      :min-height="viewportHeight"
      :max-height="viewportHeight"
      :auto-height="false"
      :enable-communication="true"
      :debug="isDevelopment"
      :sandbox="sandboxPermissions"
      :allowed-origins="allowedOrigins"
      :retry-attempts="3"
      :load-timeout="30000"
      wrapper-class="h-screen w-full"
      container-class="h-full w-full"
      loading-title="正在加载 NextJS 页面"
      loading-icon="mdi:web"
      loading-hint="💡 正在初始化嵌入式页面"
      :show-loading-url="isDevelopment"
      :show-cancel-button="true"
      error-title="NextJS 页面加载失败"
      :show-error-details="isDevelopment"
      :error-details="errorDetails"
      :show-retry-button="true"
      :show-go-back-button="true"
      :show-close-button="false"
      :messages-config="iframeMessagesConfig"
      @loaded="handleIframeLoaded"
      @error="handleIframeError"
      @message="handleIframeMessage"
      @retry="handleIframeRetry"
      @cancel="navigateBack"
      @go-back="navigateBack"/>

    <!-- 调试信息（仅开发环境） -->
    <div 
      v-if="isDevelopment && showDebugInfo" 
      class="fixed bottom-4 right-4 bg-gray-900/90 text-white p-4 rounded-lg shadow-lg max-w-xs z-30">
      <div class="text-xs font-mono space-y-1">
        <div class="font-semibold text-green-400">NextJS Debug Info</div>
        <div>Environment: {{ currentEnv }}</div>
        <div>Page ID: {{ pageId }}</div>
        <div>Config: {{ currentPageConfig?.title }}</div>
        <div>Viewport: {{ viewportWidth }}x{{ viewportHeight }}</div>
        <div>Messages: {{ messageCount }}</div>
      </div>
      <Button 
        @click="showDebugInfo = false" 
        size="sm" 
        variant="ghost" 
        class="mt-2 h-6 px-2 text-xs text-gray-300 hover:text-white">
        Hide
      </Button>
    </div>

    <!-- 开发环境快捷键提示 -->
    <div 
      v-if="isDevelopment" 
      class="fixed bottom-4 left-4 text-xs text-gray-500 z-30">
      Press <kbd class="px-1 py-0.5 bg-gray-200 rounded text-gray-700">F12</kbd> for debug info
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import IframeWrapper from '@/components/IframeWrapper.vue'
import { getToken } from '@/hooks/useToken'
import { useEventListener, useWindowSize } from '@vueuse/core'
import { definePage } from 'vue-router/auto'
import { adaptIframeError, type IframeError } from '@/utils/iframeErrorAdapter'
import { type IframeMessage } from '@/composables/useIframeMessages'

// 定义页面元信息
definePage({
  meta: {
    layout: 'empty', // 使用空布局以获得全屏体验
    requiresAuth: true,
  },
})

// 类型定义
interface NextJsPageConfig {
  id: string
  title: string
  path: string
  description?: string
  requiresAuth?: boolean
  allowedEnvironments?: string[]
}

// Composables
const route = useRoute()
const router = useRouter()
const { width: viewportWidth, height: viewportHeight } = useWindowSize()

// 响应式状态
const messageCount = ref(0)
const showDebugInfo = ref(false)

// 配置
const pageId = computed(() => route.params.id as string)
const currentEnv = computed(() => import.meta.env.VITE_APP_ENV || 'development')
const isDevelopment = computed(() => import.meta.env.DEV)
const authToken = computed(() => getToken())
const currentLocale = computed(() => 'zh') // 默认中文，可以从i18n或其他地方获取

// Next.js 页面配置映射 - 根据iframe集成指南更新
const nextJsPages: Record<string, NextJsPageConfig> = {
  dashboard: {
    id: 'dashboard',
    title: 'Dashboard',
    path: '/zh/embed/dashboard', // 使用完整的embed路径格式
    description: 'NextJS Dashboard 嵌入页面',
    requiresAuth: true,
    allowedEnvironments: ['dev', 'production', 'test']
  },
  report: {
    id: 'report',
    title: 'Report',
    path: '/zh/embed/report',
    description: 'NextJS Report 嵌入页面',
    requiresAuth: true,
    allowedEnvironments: ['dev', 'production', 'test']
  },
  test: {
    id: 'test',
    title: 'Test',
    path: '/zh/embed/test',
    description: 'NextJS Test 嵌入页面',
    requiresAuth: true,
    allowedEnvironments: ['dev', 'production', 'test']
  }
  // 可以在这里添加更多页面配置
}

// 计算属性
const currentPageConfig = computed(() => {
  const config = nextJsPages[pageId.value]
  if (!config) {
    return null
  }
  return config
})

const pageTitle = computed(() => {
  return currentPageConfig.value?.title || `Next.js ${pageId.value}`
})

// iframe消息处理配置
const iframeMessagesConfig = computed(() => ({
  autoManageLoading: true,
  initialLoadingMessage: '正在初始化NextJS页面...',
  handlers: {
    onSuccess: async (message: IframeMessage) => {
      console.log('✅ NextJS页面: 收到成功消息', message)
    },
    onError: async (message: IframeMessage) => {
      console.error('🚨 NextJS页面: 收到错误消息', message)
    },
    onNotification: async (message: IframeMessage) => {
      console.log('📢 NextJS页面: 收到通知消息', message)
      
      // 处理特定的NextJS页面消息
      if (message.type === 'navigate' && message.data?.path) {
        router.push(message.data.path)
      }
    }
  }
}))

// 错误详情
const errorDetails = computed(() => {
  return [
    `页面ID: ${pageId.value}`,
    `配置: ${currentPageConfig.value?.title || '未找到'}`,
    `环境: ${currentEnv.value}`,
    `认证状态: ${authToken.value ? '已认证' : '未认证'}`,
    `时间: ${new Date().toLocaleString()}`
  ].join('\n')
})

// 根据环境构建URL
const baseUrl = computed(() => {
  const env = currentEnv.value.toLowerCase()
  
  // 环境URL映射
  const envUrls: Record<string, string> = {
    development: 'http://localhost:3000',
    dev: 'https://dev.goglobalsp.com',
    test: 'https://test.goglobalsp.com', 
    production: 'https://www.goglobalsp.com',
    prod: 'https://www.goglobalsp.com'
  }
  
  return envUrls[env] || envUrls.development
})

const iframeSrc = computed(() => {
  if (!currentPageConfig.value) {
    return ''
  }
  
  const url = new URL(currentPageConfig.value.path, baseUrl.value)
  
  // 添加认证token
  if (authToken.value) {
    url.searchParams.set('token', authToken.value)
  }
  
  // 添加页面信息
  url.searchParams.set('pageId', pageId.value)
  url.searchParams.set('title', currentPageConfig.value.title)
  
  // 添加环境标识
  url.searchParams.set('env', currentEnv.value)
  
  // 添加来源标识
  url.searchParams.set('source', 'nextjs-embed')
  
  // 添加时间戳防止缓存
  url.searchParams.set('t', Date.now().toString())
  
  return url.toString()
})

// 安全配置
const sandboxPermissions = computed(() => [
  'allow-scripts',
  'allow-same-origin',
  'allow-forms',
  'allow-popups',
  'allow-orientation-lock',
  'allow-pointer-lock',
  'allow-presentation'
])

const allowedOrigins = computed(() => {
  const origins = [
    'http://localhost:3000',
    'https://dev.goglobalsp.com',
    'https://test.goglobalsp.com',
    'https://www.goglobalsp.com'
  ]
  
  // 确保当前环境的URL在白名单中
  const currentOrigin = new URL(baseUrl.value).origin
  if (!origins.includes(currentOrigin)) {
    origins.push(currentOrigin)
  }
  
  return origins
})

// 事件处理 - 现在大大简化了！
const handleIframeLoaded = () => {
  console.log('✅ NextJS页面: 加载完成')
}

const handleIframeError = (error: IframeError | string) => {
  const errorObject = adaptIframeError(error)
  console.error('🚨 NextJS页面: 错误', errorObject)
  
  // 根据错误类型进行特殊处理
  switch (errorObject.type) {
  case 'AUTH_ERROR':
    console.error('🔐 认证错误，可能需要重新登录')
    // 可以在这里添加重定向到登录页的逻辑
    break
  case 'NETWORK_ERROR':
    console.error('🌐 网络连接错误')
    break
  case 'TIMEOUT_ERROR':
    console.error('⏰ 加载超时')
    break
  }
}

const handleIframeMessage = (message: IframeMessage) => {
  messageCount.value++
  console.log('📨 NextJS页面: 收到消息', message)
}

const handleIframeRetry = () => {
  console.log('🔄 NextJS页面: 重试加载')
}

// 操作方法
const navigateBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}

// 键盘快捷键
useEventListener('keydown', (e) => {
  if (isDevelopment.value && e.key === 'F12') {
    e.preventDefault()
    showDebugInfo.value = !showDebugInfo.value
  }
})

// 页面验证和初始化
const validatePageConfig = () => {
  if (!currentPageConfig.value) {
    console.error(`❌ NextJS页面配置不存在: ${pageId.value}`)
    return false
  }
  
  // 验证环境支持
  const config = currentPageConfig.value
  if (config.allowedEnvironments && !config.allowedEnvironments.includes(currentEnv.value)) {
    console.error(`❌ 当前环境 (${currentEnv.value}) 不支持此页面`)
    return false
  }
  
  // 验证认证状态
  if (config.requiresAuth && !authToken.value) {
    console.error('❌ 需要登录认证')
    return false
  }
  
  return true
}

// 监听路由变化
watch(() => route.params.id, () => {
  validatePageConfig()
}, { immediate: true })

// 生命周期
onMounted(() => {
  validatePageConfig()
  
  // 设置页面标题
  if (currentPageConfig.value) {
    document.title = `${currentPageConfig.value.title} - ${import.meta.env.VITE_APP_TITLE || 'App'}`
  }
})

onUnmounted(() => {
  // 恢复原始标题
  document.title = import.meta.env.VITE_APP_TITLE || 'App'
})
</script>

<style lang="scss" module>
// 确保全屏无缝体验
.next-iframe-page {
  margin: 0;
  padding: 0;
  
  // 隐藏滚动条但保持功能
  overflow: hidden;
  
  // 确保在所有设备上都是全屏
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

// 键盘快捷键样式
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-block;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  border-bottom: 1px solid;
}

// 加载动画优化
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}
</style> 