import type { PdfOptions, TitleConfig } from '../types'
import { DEFAULT_TITLE_CONFIG } from '../styles/documentStyles'

const createSingleImageLayout = (imageStack: any[]) => {
  return imageStack.map(item => {
    if (item.nodeName === 'DIV' && item.stack) {
      const imageData = item.stack.find((stackItem: any) => 
        stackItem.nodeName === 'IMG' && stackItem.image
      )
      
      return {
        stack: [
          {
            text: item.title || '',
            style: 'image-title',
            alignment: 'center',
            margin: [0, 0, 0, 5]
          },
          {
            image: imageData?.image || '',
            width: 500,
            alignment: 'center',
            margin: [0, 0, 0, 20]
          }
        ]
      }
    }
    return item
  })
}

const createMultiImageLayout = (imageStack: any[]) => {
  const rows = []
  for (let i = 0; i < imageStack.length; i += 3) {
    const rowItems = imageStack.slice(i, i + 3)
    rows.push({
      columns: rowItems.map(item => {
        const imageData = item.stack?.find((stackItem: any) => 
          stackItem.nodeName === 'IMG' && stackItem.image
        )
        
        return {
          stack: [
            {
              text: item.title || '',
              style: 'image-title',
              alignment: 'center',
              margin: [0, 0, 0, 5]
            },
            {
              image: imageData?.image || '',
              width: 160,
              alignment: 'center',
              margin: [0, 0, 0, 10]
            }
          ],
          width: `${100 / rowItems.length}%`
        }
      }),
      margin: [0, 0, 0, 20]
    })
  }
  return rows
}
const processNestedList = (listItem: any): any => {
  if (!listItem) {return null}

  const isOrderedList = !!listItem.ol
  const listContent = isOrderedList ? listItem.ol : listItem.ul
  
  if (!listContent) {return listItem}

  const processedItems = listContent.map((item: any) => {
    let result: any = {}

    if (item.stack) {
      // 处理text数组结构
      const textContent = item.stack[0]?.text
      if (Array.isArray(textContent)) {
        // 合并文本并保持样式
        result = {
          text: textContent.map(t => ({
            text: t.text,
            ...(t.bold && { bold: true }),
            ...(t.style?.includes('html-mark') && { style: 'html-mark' })
          })),
          margin: item.stack[0].margin
        }
      }
    }

    return result
  }).filter(Boolean)

  return {
    [isOrderedList ? 'ol' : 'ul']: processedItems,
    style: 'list-container',
    margin: listItem.margin || [0, 5, 0, 10]
  }
}
export const processContent = (content: any[]): any[] => {
  return content.map(item => {
    // 处理参考资料部分
    if (['参考资料', 'References', '参考資料'].includes(item.stack?.[0]?.text)) {
      return {
        pageBreak: 'before',
        stack: [
          {
            text: item.stack[0].text,
            style: 'pdf-title',
            tocItem: true,
            id: `section_${Math.random().toString(36).substr(2, 9)}`,
            margin: [0, 0, 0, 20]
          },
          ...item.stack.slice(1).map((stackItem: any) => ({
            ...stackItem,
            style: 'reference-item'
          }))
        ]
      }
    }

    // 处理图表分析部分
    if (['图表分析', 'Chart Analysis', 'グラフ分析'].includes(item.stack?.[0]?.text)) {
      
      // 获取图片数据
      const imageStack = item.stack[1]?.stack || []
      
      // 根据图片数量决定布局方式
      const imageLayout = imageStack.length <= 1 
        ? createSingleImageLayout(imageStack) 
        : createMultiImageLayout(imageStack)

      return {
        pageBreak: 'after',
        stack: [
          {
            text: item.stack[0].text,
            style: 'pdf-title',
            tocItem: true,
            id: `section_${Math.random().toString(36).substr(2, 9)}`,
            margin: [0, 0, 0, 20]
          },
          ...imageLayout
        ]
      }
    }

    // 处理纯文本
    if (typeof item === 'string') {
      return {
        text: item,
        style: 'normal-text'
      }
    }

    // 处理标题
    if (item.className?.startsWith('heading-') || 
        (Array.isArray(item.style) && item.style.some((s: string) => s.startsWith('heading-')))) {
      const level = item.className 
        ? parseInt(item.className.split('-')[1].substring(1))
        : parseInt(item.style.find((s: string) => s.startsWith('heading-')).split('-')[1].substring(1))
      return {
        text: item.text,
        style: `heading${level}`
      }
    }

    // 处理stack结构
    if (item.stack && Array.isArray(item.stack)) {
      const processedStack = {
        ...item,
        stack: item.stack.map((stackItem: any) => {
          // 特殊处理pdf-title样式
          if (stackItem.style?.includes('pdf-title') || 
              (Array.isArray(stackItem.style) && stackItem.style.includes('pdf-title'))) {
            return {
              ...stackItem,
              tocItem: true,
              id: `section_${Math.random().toString(36).substr(2, 9)}`,
              style: 'pdf-title'
            }
          }
          return processContent([stackItem])[0]
        })
      }
      
      if (item.margin) {
        processedStack.margin = item.margin
      }
      
      return processedStack
    }

    // 处理列表
    if (item.ul || item.ol || item.nodeName === 'UL' || item.nodeName === 'OL') {
      return processNestedList(item)
    }

    // 处理复杂文本
    if (Array.isArray(item.text)) {
      return {
        ...item,
        text: item.text.map((t: any) => ({
          text: t.text,
          bold: t.bold || false,
          style: Array.isArray(t.style) ? t.style[0] : t.style || 'normal-text'
        }))
      }
    }

    // 处理其他内容
    return {
      ...item,
      style: Array.isArray(item.style) ? item.style[0] : item.style || 'normal-text'
    }
  })
}

export const createTitleSection = (title: string, options?: TitleConfig) => ({
  stack: [
    {
      text: options?.title || title,
      style: 'mainTitle',
      alignment: 'center',
      margin: [0, 10, 0, 5],
    },
    {
      text: options?.subtitle || DEFAULT_TITLE_CONFIG.subtitle,
      style: 'subTitle',
      alignment: 'center',
      margin: [0, 0, 0, 15],
    },
    {
      table: {
        widths: ['100%'],
        body: [
          [
            {
              text: [
                options?.contact?.company || DEFAULT_TITLE_CONFIG.contact.company,
                '\n',
                'Web: ', options?.contact?.website || DEFAULT_TITLE_CONFIG.contact.website,
                '; ',
                'E-mail: ', options?.contact?.email || DEFAULT_TITLE_CONFIG.contact.email
              ],
              style: 'contactInfo',
              alignment: 'center',
            },
          ],
        ],
      },
      layout: {
        hLineWidth: () => 0,
        vLineWidth: () => 0,
        paddingLeft: () => 5,
        paddingRight: () => 5,
        paddingTop: () => 5,
        paddingBottom: () => 5,
      },
    },
  ],
  margin: [0, 0, 0, 15],
})

export const createTableOfContents = () => ({
  stack: [
    {
      text: 'TABLE OF CONTENTS',
      style: 'toc-header',
      margin: [0, 0, 0, 5],
    },
    {
      canvas: [
        {
          type: 'line',
          x1: 0,
          y1: 5,
          x2: 515,
          y2: 5,
          lineWidth: 1,
          lineColor: '#000000',
        },
      ],
      margin: [0, 5, 0, 5],
    },
    {
      toc: {
        title: { text: '' },
        numberStyle: { bold: true },
        textStyle: { fontSize: 12 },
        textMargin: [5, 5, 0, 5],
      },
    },
  ],
}) 