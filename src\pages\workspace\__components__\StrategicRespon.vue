<template>
  <div class="strategic-analysis-container h-full flex flex-col">
    <!-- 表格视图：首页显示所有列表项 -->
    <StrategicTableView 
      v-if="currentViewMode === 'table'"
      :items="strategicReportItems" 
      :isLoading="false"
      :apiLoading="apiLoading || false"
      :totalCount="totalCount"
      :hasError="hasError"
      :searchText="searchText"
      :t="t"
      :formatDateString="formatDateString"
      @view-detail="handleViewStrategicDetail"
      @search="handleSearch"
      @retry="handleRetry"/>

    <!-- 详情视图：显示单个项目的详细信息 -->
    <StrategicDetailView 
      v-else-if="currentViewMode === 'detail' && selectedStrategicItem"
      :selectedItem="selectedStrategicItem"
      :selectedItemBasicInfo="selectedItemBasicInfo"
      :detailLoadingState="detailLoadingState"
      :allItems="strategicReportItems"
      :currentIndex="currentDetailIndex"
      :formatDateString="formatDateString"
      :t="t"
      @close-detail="handleCloseDetail"
      @navigate-to-item="handleNavigateToItem"
      @navigate-previous="handleNavigatePrevious"
      @navigate-next="handleNavigateNext"/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineEmits, defineProps, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import StrategicTableView from './StrategicTableView.vue'
import StrategicDetailView from './StrategicDetailView.vue'
import { usePost } from '@/hooks/useHttp'

// 定义组件属性
const props = defineProps<{
  strategicReport?: any
  apiLoading?: boolean
}>()

// 定义组件事件
const emit = defineEmits(['update:loading'])

// 使用 i18n
const { t } = useI18n()

// 视图模式
const currentViewMode = ref<'table' | 'detail'>('table')

// Strategic 数据项接口
interface StrategicDataItem extends Record<string, any> {
  id: string | number
  country?: string
  first_level_industry?: string
  news_type?: string
  summary?: string
  opportunity_details?: string
  entry_strategy?: string
  key_opportunities?: string[]
  timeline?: string
  url?: string
  recommend_reason?: string
  updated_at?: string
  update_at?: string
  title?: string
}

// 搜索状态
const searchText = ref('')

// 错误状态
const hasError = ref(false)

// Strategic 数据
const strategicReportItems = ref<StrategicDataItem[]>([])
const totalCount = computed(() => strategicReportItems.value.length)

// 详情页相关状态
const selectedStrategicItem = ref<StrategicDataItem | null>(null)
const selectedItemBasicInfo = ref<StrategicDataItem | null>(null)
const detailLoadingState = ref<'idle' | 'loading' | 'success' | 'error'>('idle')
const currentDetailIndex = ref<number | undefined>(undefined)

// 详情接口相关
const { execute: fetchStrategicDetail } = usePost<any>(
  '/business_agent/get_business_news_detail', {}, {}, { immediate: false }
)

// 处理 strategic 数据的初始化
const initializeStrategicData = () => {
  console.log('初始化Strategic数据:', props.strategicReport)
  
  if (props.strategicReport?.results && Array.isArray(props.strategicReport.results)) {
    const rawItems = props.strategicReport.results
    
    // 使用新的数据结构，确保每个项目有唯一 ID
    strategicReportItems.value = rawItems.map((item: any, index: number) => ({
      ...item,
      id: item.id || `strategic-${index}`,
      title: item.title || item.summary || `Strategic Opportunity ${index + 1}`
    }))

    console.log('Strategic Report Items 初始化完成:', {
      items: strategicReportItems.value.length,
      total: totalCount.value
    })
  } else {
    // 处理空数据或无效数据的情况
    strategicReportItems.value = []
    
    console.log('Strategic数据为空或无效:', {
      hasStrategicReport: !!props.strategicReport,
      hasResults: !!props.strategicReport?.results,
      isResultsArray: Array.isArray(props.strategicReport?.results),
      total: totalCount.value
    })
  }
}

// 格式化日期字符串
const formatDateString = (dateInput: any): string => {
  if (!dateInput) {return '-'}
  
  try {
    const date = new Date(dateInput)
    if (isNaN(date.getTime())) {return '-'}
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 搜索处理
const handleSearch = (searchValue: string) => {
  searchText.value = searchValue
  // 在实际应用中，这里应该调用API进行搜索
  console.log('搜索Strategic项目:', searchValue)
}

// 重试处理
const handleRetry = () => {
  hasError.value = false
  console.log('重试加载Strategic数据')
}

// 查看 Strategic 详情
const handleViewStrategicDetail = async (item: StrategicDataItem, index: number) => {
  console.log('查看Strategic详情:', item.id, 'index:', index)
  
  // 设置基本信息和索引
  selectedItemBasicInfo.value = item
  currentDetailIndex.value = index
  detailLoadingState.value = 'loading'
  
  // 切换到详情视图
  currentViewMode.value = 'detail'
  
  try {
    // 调用真实的详情接口
    const detailResponse = await fetchStrategicDetail(0, {
      business_analysis_id: item.id
    })
    
    if (detailResponse && detailResponse.data) {
      // 合并列表数据和详情数据
      selectedStrategicItem.value = {
        ...item, // 列表中的基本信息
        ...detailResponse.data, // 详情信息
        id: item.id, // 确保 ID 保持一致
        title: item.title // 确保标题保持一致
      }
      detailLoadingState.value = 'success'
    } else {
      // 如果详情接口失败，仍然显示列表中的基本信息
      selectedStrategicItem.value = item
      detailLoadingState.value = 'success'
      console.warn('详情接口返回空数据，使用列表数据')
    }
    
  } catch (error) {
    console.error('加载Strategic详情失败:', error)
    detailLoadingState.value = 'error'
    
    // 错误情况下自动返回列表视图
    setTimeout(() => {
      handleCloseDetail()
    }, 3000)
  }
}

// 关闭详情视图
const handleCloseDetail = () => {
  currentViewMode.value = 'table'
  selectedStrategicItem.value = null
  selectedItemBasicInfo.value = null
  detailLoadingState.value = 'idle'
  currentDetailIndex.value = undefined
  console.log('关闭Strategic详情视图')
}

// 导航到指定项目
const handleNavigateToItem = async (index: number) => {
  if (index < 0 || index >= strategicReportItems.value.length) {
    console.warn('导航索引超出范围:', index)
    return
  }
  
  const targetItem = strategicReportItems.value[index]
  console.log('导航到Strategic项目:', targetItem.id, 'index:', index)
  
  // 更新基本信息和索引
  selectedItemBasicInfo.value = targetItem
  currentDetailIndex.value = index
  detailLoadingState.value = 'loading'
  
  try {
    // 模拟加载数据
    await new Promise(resolve => setTimeout(resolve, 200))
    
    selectedStrategicItem.value = targetItem
    detailLoadingState.value = 'success'
    
  } catch (error) {
    console.error('导航加载失败:', error)
    detailLoadingState.value = 'error'
  }
}

// 导航到上一个项目
const handleNavigatePrevious = () => {
  if (currentDetailIndex.value !== undefined && currentDetailIndex.value > 0) {
    handleNavigateToItem(currentDetailIndex.value - 1)
  }
}

// 导航到下一个项目
const handleNavigateNext = () => {
  if (currentDetailIndex.value !== undefined && currentDetailIndex.value < strategicReportItems.value.length - 1) {
    handleNavigateToItem(currentDetailIndex.value + 1)
  }
}

// 监听 strategicReport 变化
watch(() => props.strategicReport, (newReport) => {
  if (newReport) {
    initializeStrategicData()
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化
onMounted(() => {
  initializeStrategicData()
})
</script>

<style scoped>
.strategic-analysis-container {
  background: #fafafa;
}
</style> 