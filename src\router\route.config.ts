import { 
  companyAdminAccess,
} from '@/const/permission'
import { merge } from 'lodash-es'
import type { RouteRecordRaw } from 'vue-router/auto'
import type { RouteNamedMap } from 'vue-router/auto-routes'
import { UserType } from '@/stores/user'

type RouteName = keyof RouteNamedMap & string

const routeConfig: {[k in RouteName]?: Partial<RouteRecordRaw>} = {
  '/settings/': {
    meta: {
      requiresAuth: true,
      access: {
        userTypes: [UserType.CompanyUser, UserType.CompanyAdmin, UserType.CompanyCreator]
      }
    }
  },
  '/settings/personal-info/': {
    meta: {
      requiresAuth: true,
      access: {
        userTypes: [UserType.CompanyUser, UserType.CompanyAdmin, UserType.CompanyCreator]
      }
    }
  },
  '/settings/company-info/': {
    meta: {
      requiresAuth: true,
      access: {
        userTypes: [UserType.CompanyAdmin, UserType.CompanyCreator]
      }
    }
  },
  '/settings/plans/': {
    meta: {
      requiresAuth: true,
      access: {
        userTypes: [UserType.CompanyCreator]
      }
    }
  },
  '/settings/subscription-info/': {
    meta: {
      requiresAuth: true,
      access: {
        userTypes: [UserType.CompanyCreator]
      }
    }
  },
}

export const mergeRouteConfig = (route: RouteRecordRaw): RouteRecordRaw => {
  if (routeConfig[route.name as RouteName] && route.component) {
    return merge(route, routeConfig[route.name as RouteName])
  }
  return route
}
