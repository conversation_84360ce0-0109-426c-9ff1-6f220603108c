/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  /* color: var(--color-text); */
  /* background: var(--color-background); */
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    PingFang SC,
    Noto Sans,
    Hiragino Sans GB,
    Microsoft YaHei,
    微软雅黑,
    Arial,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

::-webkit-scrollbar {
  z-index: 11;
  width: 10px
}

::-webkit-scrollbar:horizontal {
  height: 10px
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 6px;
  background-color: var(--scrollbar-background-color);
  background-clip: padding-box;
  border: 2px dashed transparent
}

::-webkit-scrollbar-thumb:hover {
  background-clip: initial;
  background-color: var(--scrollbar-hover-background-color)
}

::-webkit-scrollbar-corner,
::-webkit-scrollbar-track {
  background: var(--scrollbar-track-background)
}

::-webkit-scrollbar-track-piece {
  background: var(--scrollbar-track-background);
  width: 6px
}

input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  opacity: 0;
  display: nome !important;
  pointer-events: none;
  position: absolute;
  right: 0;
}

.tippy-content {
  :focus {
    outline: none;
  }
}

.gitSubmitLoading .el-loading-spinner .path {
  stroke: #FFFFFF !important;
}

.gitSubmitLoading .el-loading-text {
  color: #FFFFFF !important;
}

.zk-date-time-picker .zk-date-display-input {
  background: #fff;
  border-radius: var(--radius-small);
}

.zk-tips-icon {
  color: var(--font-color-2);
}

/* [LSOTP-6724] 【研发洞察】LDAP认证登录 */
.vxe-table {
  --vxe-font-color: var(--font-color-1);
  .vxe-table--header-wrapper,
  th {
    background-color: var(--table-header-bg-color) !important;
  }

  .vxe-table--render-default .vxe-body--column.col--ellipsis,
  .vxe-table--render-default .vxe-footer--column.col--ellipsis,
  .vxe-table--render-default .vxe-header--column.col--ellipsis,
  .vxe-table--render-default.vxe-editable .vxe-body--column {
    height: 48px;
  }

  .vxe-header--column {
    height: 40px !important;
    padding: 0px !important;
    font-size: var(--size-font-9);
    color: var(--font-color-2);
    font-weight: normal;
  }

  .vxe-cell {
    padding: 0 12px;
  }

  .vxe-body--row.row--hover {
    background-color: var(--gray-transparent-hover) !important;
  }

  .vxe-table--render-wrapper .vxe-cell--sort .sort--active {
    color: var(--primary-color) !important;
  }

  .vxe-cell--title {
    display: flex;
    align-items: center;
  }
}
