<template>
  <div class="flex flex-col h-full">
    <Container class="grow overflow-hidden"/>
  </div>
</template>

<script lang="ts" setup>
import Container from './__components__/NewsComponents/Container.vue'
import { definePage } from 'vue-router/auto'

definePage({
  meta:{
    layout:'default',
    moduleId:'information',
    requiresAuth:true
  }
})

</script>

<style lang="scss" module>

</style>
