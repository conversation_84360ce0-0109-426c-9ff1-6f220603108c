// iframe错误类型定义
export interface IframeError {
  type: 'NETWORK_ERROR' | 'TIMEOUT_ERROR' | 'AUTH_ERROR' | 'LOAD_ERROR' | 'UNKNOWN_ERROR'
  message: string
  code?: string | number
  url?: string
  timestamp: number
  retryCount: number
  details?: any
}

// 错误适配器：将字符串错误转换为IframeError对象
export function adaptIframeError(error: IframeError | string): IframeError {
  if (typeof error === 'string') {
    return {
      type: 'UNKNOWN_ERROR',
      message: error,
      timestamp: Date.now(),
      retryCount: 0
    }
  }
  return error
}

// 获取错误消息：从IframeError对象或字符串中提取消息
export function getErrorMessage(error: IframeError | string): string {
  return typeof error === 'string' ? error : error.message
}

// 检查是否为详细错误对象
export function isDetailedError(error: IframeError | string): error is IframeError {
  return typeof error === 'object' && 'type' in error
} 