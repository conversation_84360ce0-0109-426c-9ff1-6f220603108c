<template>
  <div class="w-full h-full flex flex-col gap-1" ref="containerRef">
    <div class="flex items-center justify-between w-full pr-4">
      <h3 class="text-[16px] font-medium">{{ $t("global.mindmap") }}</h3>
      <div class="flex items-center gap-2">
        <!-- <button
          class="p-1 hover:bg-gray-100 rounded-sm transition-colors"
          @click="toggle"
          :title="$t('global.fullscreen')">
          <Icon
            :icon="isFullscreen ? 'material-symbols:fullscreen-exit' : 'material-symbols:fullscreen'"
            class="w-5 h-5 text-gray-600"/>
        </button> -->
        <button
          class="p-1 hover:bg-gray-100 rounded-sm transition-colors"
          @click="exportImage"
          :title="$t('formatReport.exportMindMap')">
          <Icon
            icon="mdi:download"
            class="w-5 h-5 text-main-color"/>
        </button>
      </div>
    </div>
    <div 
      :class="[
        $style['mind-map-container'],
        { 'fixed inset-0 z-50 w-screen! h-screen! bg-white': isFullscreen }
      ]">
      <div 
        :class="[
          $style['mind-map'],
          { 'w-screen! h-screen!': isFullscreen }
        ]" 
        ref="mapRef" 
        id="basic-mind-map"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MindElixir, { type NodeObj, type Options, type MindElixirInstance, type MindElixirMethods } from 'mind-elixir'
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useFullscreen } from '@vueuse/core'

interface MindMapData {
  title: string;
  outline: string[];
}

const props = defineProps<{
  data: MindMapData;
}>()

const emit = defineEmits<{
  'update:data': [data: MindMapData];
  'select-node': [node: NodeObj];
}>()

type Instance = MindElixirInstance & MindElixirMethods
const containerRef = ref<HTMLElement | null>(null)
const mapRef = ref<HTMLElement | null>(null)
const mindElixir = ref<Instance | null>(null)

// Fullscreen handling
const { isFullscreen, toggle } = useFullscreen(containerRef)

// Export image function
const exportImage = async () => {
  if (!mindElixir.value) {return}
  
  try {
    // 生成文件名
    const fileName = `${props.data.title}.png`
    
    // 导出为 PNG blob
    const blob = await mindElixir.value.exportPng(false)
    if (!blob) {
      console.error('Failed to generate image blob')
      return
    }
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    
    // 触发下载
    a.click()
    
    // 清理资源
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export image:', error)
  }
}

const convertToMindElixirData = (data: MindMapData): NodeObj => {
  const generateId = () => `_${Math.random().toString(36).substr(2, 9)}`

  const root: NodeObj = {
    topic: data.title || 'Root',
    id: generateId(),
    children: [],
    expanded: true,
    direction: MindElixir.LEFT,
  }

  if (data.outline && Array.isArray(data.outline)) {
    root.children = data.outline.map((item) => ({
      topic: item,
      id: generateId(),
      expanded: true,
      direction: MindElixir.LEFT,
    }))
  }

  return root
}

const convertFromMindElixirData = (node: NodeObj): MindMapData => {
  return {
    title: node.topic,
    outline: (node.children || []).map((child) => child.topic),
  }
}

const options = computed(() => {
  return {
    el: '#basic-mind-map',
    direction: MindElixir.RIGHT,
    nodeData: mindElixirData.value,
    draggable: false,
    contextMenu: false,
    toolBar: false,
    nodeMenu: false,
    editable: false,
    mouseSelectionButton: 2,
    theme: {
      name: 'Latte',
      palette: [
        '#dd7878', '#ea76cb', '#8839ef', '#e64553',
        '#fe640b', '#df8e1d', '#40a02b', '#209fb5',
        '#1e66f5', '#7287fd'
      ],
      cssVar: {
        '--main-color': '#444446',
        '--main-bgcolor': '#ffffff',
        '--color': '#777777',
        '--bgcolor': 'transparent',
        '--panel-color': '#444446',
        '--panel-bgcolor': '#ffffff',
        '--panel-border-color': '#eaeaea',
      },
    },
    background: {
      color: '#fff',
    },
  }
})

const mindElixirData = computed(() => {
  return convertToMindElixirData(props.data)
})

const initMindMap = () => {
  if (!mapRef.value) {
    console.error('Map container not found')
    return
  }

  mindElixir.value = new MindElixir(options.value as Options)
  if (mindElixir.value) {
    mindElixir.value.init({
      nodeData: mindElixirData.value,
    })
    mindElixir.value.scale(0.6)
    
    mindElixir.value.bus.addListener('selectNode', (node: NodeObj) => {
      emit('select-node', node)
    })

    mindElixir.value.bus.addListener('operation', () => {
      const currentData = mindElixir.value!.getData()
      const convertedData = convertFromMindElixirData(currentData as unknown as NodeObj)
      emit('update:data', convertedData)
    })
  }
}

// Watch fullscreen changes to adjust mind map
watch(isFullscreen, (full) => {
  if (!mindElixir.value) {return}
  
  nextTick(() => {
    // mindElixir.value?.resize()
    // 调整缩放比例
    console.log('mindElixirData.value', { nodeData: mindElixirData.value })
    // mindElixir.value?.refresh({ nodeData: mindElixirData.value })
    nextTick(()=>{
      mindElixir.value?.init(options.value)
      mindElixir.value?.toCenter()
      mindElixir.value?.scale(full ? 1 : 0.4)
    })
  })
})


onMounted(() => {
  nextTick(() => {
    initMindMap()
  })
})
</script>

<style lang="scss" module>
.mind-map-container {
  width: 100%;
  height: 600px;
  background: #fff;
  transition: all 0.3s ease;
  position: relative;
}

.mind-map {
  width: 100%;
  height: 600px;
  transition: all 0.3s ease;
}

:global {
  .mind-elixir {
    background: transparent;
  }
  
  // 全屏时的样式调整
  .fullscreen-enabled {
    .mind-map-container {
      padding: 1rem;
    }
  }
}
</style>
