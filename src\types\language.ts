export type Language = 'chinese' | 'english' | 'japanese'

export interface LanguageOption {
  value: Language
  label: string
  messageKey: keyof typeof LANGUAGE_MESSAGES
  next: Language
}

export const LANGUAGE_MESSAGES = {
  CHINESE_MESSAGE: '您确定要切换语言吗？这将刷新页面。',
  ENGLISH_MESSAGE: 'Are you sure you want to switch the language? This will refresh the page.',
  JAPANESE_MESSAGE: '言語を切り替えますか？これによりページが更新されます。',
} as const

export const SUPPORTED_LANGUAGES: LanguageOption[] = [
  { 
    value: 'chinese', 
    label: '中文', 
    messageKey: 'CHINESE_MESSAGE',
    next: 'english'
  },
  { 
    value: 'english', 
    label: 'English', 
    messageKey: 'ENGLISH_MESSAGE',
    next: 'chinese'
  },
  { 
    value: 'japanese', 
    label: '日本語', 
    messageKey: 'JAPANESE_MESSAGE',
    next: 'chinese'
  }
]

export const LanguageUtils = {
  getNextLanguage(currentLanguage: Language): Language {
    const currentLang = SUPPORTED_LANGUAGES.find(lang => lang.value === currentLanguage)
    return currentLang?.next || SUPPORTED_LANGUAGES[0].value
  },

  getLanguageOption(language: Language): LanguageOption | undefined {
    return SUPPORTED_LANGUAGES.find(lang => lang.value === language)
  },

  getLanguageLabel(language: Language): string {
    return this.getLanguageOption(language)?.label || language
  },

  getConfirmButtonText(language: Language): string {
    return language === 'english' ? 'Refresh' : '刷新'
  },

  getCancelButtonText(language: Language): string {
    return language === 'english' ? 'Cancel' : '取消'
  }
} 