import { useComputed } from '@/hooks/useComputed'
import { defineStore, type StoreDefinition } from 'pinia'
import { ref, computed } from 'vue'
import { type ComputedRef } from 'vue'

// 忽略资源
const IGNORE_RESPONSE = '*'

type ResourceStoreDefinition<T> = StoreDefinition<string, {
}, {
  resources: ComputedRef<string[]>;
}, {
  setResources: (newResources?: T) => void;
  hasResources: (needs?: string[], every?: boolean) => boolean;
  hasResourcesComputed: (needs?: string[], every?: boolean) => ComputedRef<boolean>;
  refreshResource: <Args extends unknown[]>(...args: Args) => Promise<void>;
}>;
type ResourceStore<T> = ReturnType<ResourceStoreDefinition<T>>;
export type ResourceOptions<T> = {
  // 加载权限资源
  load?: (...args: any[]) => Promise<T> | T;
  // 解析资源
  parse?: (resources?: T) => string[];
  dependencies?: ResourceStore<T>[];
}

export const hasAccess = (resources: string[], needs?: string[], every?: boolean) => {
  if (!needs?.length) {
    return true
  }
  // 需要的为忽略 直接返回true
  if (needs.includes(IGNORE_RESPONSE)) {
    return true
  }

  if (every) {
    return needs.every((resource) => resources.includes(resource))
  }
  return needs.some((resource) => resources.includes(resource))
}

/**
 * 创建一个通用的资源校验store
 * @param name
 * @param opts
 */
export const createResourceStore = <T = string[]>(name: string, opts?: ResourceOptions<T>) => {
  const { load, parse, dependencies } = opts || {}
  const doParseResource = parse || ((resources?: T) => (resources || []) as string[])
  return defineStore(name, () => {
    // state
    const selfResources = ref<string[]>([])

    // computed
    const resources = computed(() => selfResources.value.concat(...dependencies?.map(d => d.resources) || []))

    // actions
    const setResources = (newResources?: T) => {
      selfResources.value = doParseResource(newResources)
    }

    const hasResources = (needPermissions?: string[], every?: boolean) => {
      return hasAccess(resources.value, needPermissions, every)
    }

    const hasResourcesComputed = useComputed(hasResources)

    const refreshResource = async <Args extends unknown[]>(...args: Args) => {
      const newResources = await load?.(args)
      setResources(newResources)
    }

    return {
      resources,
      hasResourcesComputed,
      setResources,
      hasResources,
      refreshResource
    }
  })
}
