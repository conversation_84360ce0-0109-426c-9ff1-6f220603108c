import { nextTick, ref, shallowReadonly, unref, watch, type MaybeRef } from 'vue'

/**
 * 这个hook作用为修复日期选择器的同步问题，当用户选择日期时，如果没有真正的选择，而是切换年，月等视图时默认el-date-picker会自动选择当前日期，导致日期选择器的modelValue和真实值不一致。
 * 目前发现使用week和month视图时会出现这种问题。
 * 使用的时候把modelValue作为el-date-picker的v-model绑定到日期选择器上，并将handleChange作为el-date-picker的change事件绑定到日期选择器上。
 * 修复后不能使用v-model的值来触发数据更新，应该使用onChange来触发数据更新。
 * 有个问题是无法修改年或者月份，因为重置了value，必须把controlViewValue在el-date-picker的default-value属性上设置。
 * @param defaultValue
 * @param onChange
 * @returns
 */
export const useSyncedDatePicker = (defaultValue: MaybeRef<Date | null> = null, onChange?: (val: Date | null) => void) => {
  // v-model的值
  const modelValue = ref<Date | null>()
  // 真实选择的值
  const realValue = ref<Date | null>()
  // 控制面板显示的值
  const controlViewValue = ref<Date | null>()

  // 监听defaultValue变化，更新所有相关的ref
  watch(() => unref(defaultValue), (newVal) => {
    modelValue.value = newVal
    realValue.value = newVal
    controlViewValue.value = newVal
  }, { immediate: true })

  const handleChange = (val: Date | null) => {
    realValue.value = val
    onChange?.(val)
  }

  watch(modelValue, () => {
    // 这里需要2个nextTick，因为el-date-picker的change事件会在下一2个tick才触发，保持在change事件后执行
    nextTick(() => {
      nextTick(() => {
        if (realValue.value !== modelValue.value) {
          const newVal = modelValue.value
          modelValue.value = realValue.value
          // 设置viewValue，触发视图更新，不然会导致视图选择的年或者月回到真实值的位置
          nextTick(() => {
            controlViewValue.value = newVal
          })
          console.log('reset date value with no change')
        }
      })
    })
  })

  return {
    modelValue,
    realValue: shallowReadonly(realValue),
    controlViewValue: shallowReadonly(controlViewValue),
    handleChange,
  }
}
