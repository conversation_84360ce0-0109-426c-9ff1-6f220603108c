<template>
  <div class="relative flex-1 min-h-0">
    <!-- Loading 状态 -->
    <div v-if="detailLoadingState === 'loading'" class="h-full flex flex-col">
      <!-- 保持顶部导航结构 -->
      <div class="flex-shrink-0 p-6 border-b">
        <div class="flex items-center justify-between w-full mb-4">
          <div class="flex items-center gap-3 flex-1 min-w-0">
            <Button variant="ghost"
                    size="sm"
                    @click="handleCloseDetail"
                    class="h-8 w-8 p-0 flex-shrink-0">
              <ArrowLeftIcon class="h-4 w-4"/>
            </Button>
            
            <h2 class="text-xl font-bold flex-1 min-w-0 truncate">{{ selectedItemBasicInfo?.new_title || t('biding.loading_detail') }}</h2>
          </div>
          
          <!-- 右侧导航控件 - 在加载时也显示 -->
          <div v-if="allItems && allItems.length > 1" class="flex items-center gap-1 bg-white rounded-lg border border-gray-200 shadow-sm p-1 flex-shrink-0">
            <!-- 上一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigatePrevious"
              :disabled="currentIndex === 0"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronLeftIcon class="h-3.5 w-3.5"/>
            </Button>
            
            <!-- 列表选择器带icon -->
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" class="h-7 px-2 text-xs gap-1 hover:bg-gray-100">
                  <ListIcon class="h-3.5 w-3.5"/>
                  <span>{{ (currentIndex ?? 0) + 1 }}/{{ allItems.length }}</span>
                  <ChevronDownIcon class="h-3 w-3"/>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-80 max-h-60 overflow-y-auto" align="end">
                <div class="p-2">
                  <div class="flex items-center gap-2 text-xs font-medium text-gray-500 mb-2">
                    <ListIcon class="h-3.5 w-3.5"/>
                    <span>{{ t('biding.navigation.quick_nav_title', { count: allItems.length }) }}</span>
                  </div>
                  <div class="space-y-1">
                    <div 
                      v-for="(item, index) in allItems" 
                      :key="item._id"
                      @click="handleNavigateToItem(index)"
                      :class="[
                        'flex items-center justify-between p-2 rounded-md cursor-pointer text-xs hover:bg-gray-50 transition-colors',
                        currentIndex === index ? 'bg-blue-50 border border-blue-200' : ''
                      ]">
                      <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">
                          {{ item.new_title || item.extraction?.basic_info?.title || t('biding.navigation.unknown_title') }}
                        </div>
                        <div class="text-gray-500 mt-0.5 flex items-center gap-1">
                          <MapPinIcon class="h-3 w-3"/>
                          {{ item.extraction?.basic_info?.country || '-' }}
                        </div>
                      </div>
                      <div class="flex-shrink-0 ml-2">
                        <Badge 
                          :class="getScoreClass(item.recommendation?.recommend_score)" 
                          class="text-xs">
                          {{ item.recommendation?.recommend_score 
                            ? new Decimal(item.recommendation.recommend_score).toFixed(1) 
                            : '-' }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <!-- 下一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigateNext"
              :disabled="currentIndex === (allItems.length - 1)"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronRightIcon class="h-3.5 w-3.5"/>
            </Button>
          </div>
        </div>
      </div>
      
      <!-- 加载内容区域 -->
      <div class="flex-1 flex flex-col items-center justify-center p-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
        <h3 class="text-lg font-medium text-slate-700 mb-2">{{ t('biding.loading_detail') }}</h3>
        <p class="text-sm text-slate-500 text-center max-w-md">
          {{ selectedItemBasicInfo?.new_title || t('biding.loading_detail_description') }}
        </p>
        <div class="mt-4 flex items-center gap-2 text-xs text-slate-400">
          <span>{{ t('biding.loading_tip') }}</span>
        </div>
      </div>
    </div>
    
    <!-- Error 状态 -->
    <div v-else-if="detailLoadingState === 'error'" class="h-full flex flex-col">
      <!-- 保持顶部导航结构 -->
      <div class="flex-shrink-0 p-6 border-b">
        <div class="flex items-center justify-between w-full mb-4">
          <div class="flex items-center gap-3 flex-1 min-w-0">
            <Button variant="ghost"
                    size="sm"
                    @click="handleCloseDetail"
                    class="h-8 w-8 p-0 flex-shrink-0">
              <ArrowLeftIcon class="h-4 w-4"/>
            </Button>
            
            <h2 class="text-xl font-bold flex-1 min-w-0 truncate text-red-700">{{ t('biding.detail_load_error') }}</h2>
          </div>
          
          <!-- 右侧导航控件 - 在错误时也显示 -->
          <div v-if="allItems && allItems.length > 1" class="flex items-center gap-1 bg-white rounded-lg border border-gray-200 shadow-sm p-1 flex-shrink-0">
            <!-- 上一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigatePrevious"
              :disabled="currentIndex === 0"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronLeftIcon class="h-3.5 w-3.5"/>
            </Button>
            
            <!-- 列表选择器带icon -->
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" class="h-7 px-2 text-xs gap-1 hover:bg-gray-100">
                  <ListIcon class="h-3.5 w-3.5"/>
                  <span>{{ (currentIndex ?? 0) + 1 }}/{{ allItems.length }}</span>
                  <ChevronDownIcon class="h-3 w-3"/>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-80 max-h-60 overflow-y-auto" align="end">
                <div class="p-2">
                  <div class="flex items-center gap-2 text-xs font-medium text-gray-500 mb-2">
                    <ListIcon class="h-3.5 w-3.5"/>
                    <span>{{ t('biding.navigation.quick_nav_title', { count: allItems.length }) }}</span>
                  </div>
                  <div class="space-y-1">
                    <div 
                      v-for="(item, index) in allItems" 
                      :key="item._id"
                      @click="handleNavigateToItem(index)"
                      :class="[
                        'flex items-center justify-between p-2 rounded-md cursor-pointer text-xs hover:bg-gray-50 transition-colors',
                        currentIndex === index ? 'bg-blue-50 border border-blue-200' : ''
                      ]">
                      <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">
                          {{ item.new_title || item.extraction?.basic_info?.title || t('biding.navigation.unknown_title') }}
                        </div>
                        <div class="text-gray-500 mt-0.5 flex items-center gap-1">
                          <MapPinIcon class="h-3 w-3"/>
                          {{ item.extraction?.basic_info?.country || '-' }}
                        </div>
                      </div>
                      <div class="flex-shrink-0 ml-2">
                        <Badge 
                          :class="getScoreClass(item.recommendation?.recommend_score)" 
                          class="text-xs">
                          {{ item.recommendation?.recommend_score 
                            ? new Decimal(item.recommendation.recommend_score).toFixed(1) 
                            : '-' }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <!-- 下一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigateNext"
              :disabled="currentIndex === (allItems.length - 1)"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronRightIcon class="h-3.5 w-3.5"/>
            </Button>
          </div>
        </div>
      </div>
      
      <!-- 错误内容区域 -->
      <div class="flex-1 flex flex-col items-center justify-center p-8">
        <AlertCircleIcon class="h-12 w-12 text-red-400 mb-4"/>
        <h3 class="text-lg font-medium text-red-700 mb-2">{{ t('biding.detail_load_error') }}</h3>
        <p class="text-sm text-red-500 text-center max-w-md mb-4">
          {{ t('biding.detail_load_error_description') }}
        </p>
        <p class="text-xs text-slate-500">{{ t('biding.auto_return_message') }}</p>
        <Button 
          variant="outline" 
          size="sm" 
          @click="handleCloseDetail" 
          class="mt-4">
          {{ t('biding.return_to_list') }}
        </Button>
      </div>
    </div>
    
    <!-- 正常详情内容 -->
    <Card v-else-if="selectedItem" class="p-6 max-w-full h-full flex flex-col">
      <CardHeader>
        <div class="flex flex-col gap-3">
          <!-- 第一行：返回按钮、标题、导航控件 -->
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center gap-3 flex-1 min-w-0">
              <Button variant="ghost"
                      size="sm"
                      @click="handleCloseDetail"
                      class="h-8 w-8 p-0 flex-shrink-0">
                <ArrowLeftIcon class="h-4 w-4"/>
              </Button>
              
              <h2 class="text-xl font-bold flex-1 min-w-0 truncate">{{ sNewTitle || t('biding.detail_title_fallback') }}</h2>
            </div>
            
            <!-- 右侧导航控件 -->
            <div v-if="allItems && allItems.length > 1" class="flex items-center gap-1 bg-white rounded-lg border border-gray-200 shadow-sm p-1 flex-shrink-0">
              <!-- 上一个按钮 -->
              <Button 
                variant="ghost" 
                size="sm" 
                @click="handleNavigatePrevious"
                :disabled="currentIndex === 0"
                class="h-7 w-7 p-0 hover:bg-gray-100">
                <ChevronLeftIcon class="h-3.5 w-3.5"/>
              </Button>
              
              <!-- 列表选择器带icon -->
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" class="h-7 px-2 text-xs gap-1 hover:bg-gray-100">
                    <ListIcon class="h-3.5 w-3.5"/>
                    <span>{{ (currentIndex ?? 0) + 1 }}/{{ allItems.length }}</span>
                    <ChevronDownIcon class="h-3 w-3"/>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent class="w-80 max-h-60 overflow-y-auto" align="end">
                  <div class="p-2">
                    <div class="flex items-center gap-2 text-xs font-medium text-gray-500 mb-2">
                      <ListIcon class="h-3.5 w-3.5"/>
                      <span>{{ t('biding.navigation.quick_nav_title', { count: allItems.length }) }}</span>
                    </div>
                    <div class="space-y-1">
                      <div 
                        v-for="(item, index) in allItems" 
                        :key="item._id"
                        @click="handleNavigateToItem(index)"
                        :class="[
                          'flex items-center justify-between p-2 rounded-md cursor-pointer text-xs hover:bg-gray-50 transition-colors',
                          currentIndex === index ? 'bg-blue-50 border border-blue-200' : ''
                        ]">
                        <div class="flex-1 min-w-0">
                          <div class="font-medium truncate">
                            {{ item.new_title || item.extraction?.basic_info?.title || t('biding.navigation.unknown_title') }}
                          </div>
                          <div class="text-gray-500 mt-0.5 flex items-center gap-1">
                            <MapPinIcon class="h-3 w-3"/>
                            {{ item.extraction?.basic_info?.country || '-' }}
                          </div>
                        </div>
                        <div class="flex-shrink-0 ml-2">
                          <Badge 
                            :class="getScoreClass(item.recommendation?.recommend_score)" 
                            class="text-xs">
                            {{ item.recommendation?.recommend_score 
                              ? new Decimal(item.recommendation.recommend_score).toFixed(1) 
                              : '-' }}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <!-- 下一个按钮 -->
              <Button 
                variant="ghost" 
                size="sm" 
                @click="handleNavigateNext"
                :disabled="currentIndex === (allItems.length - 1)"
                class="h-7 w-7 p-0 hover:bg-gray-100">
                <ChevronRightIcon class="h-3.5 w-3.5"/>
              </Button>
            </div>
          </div>
          
          <!-- 第二行：推荐理由（独立区域） -->
          <div v-if="selectedItem?.recommendation?.recommend_reason" class="w-full">
            <div class="recommendation-reason-card">
              <div class="flex items-start gap-3">
                <div class="flex-1 min-w-0">
                  <div class="recommendation-reason-title">{{ t('global.specific_ai_analysis') }}：</div>
                  <div class="recommendation-reason-content">
                    {{ selectedItem.recommendation.recommend_reason }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 第三行：快捷键提示和评分徽章 -->
          <div class="w-full flex justify-between items-center gap-3">
            <!-- 左侧：快捷键提示 -->
            <div class="flex items-center gap-2 text-xs text-gray-500 flex-shrink-0">
              <span>{{ t('biding.shortcuts.label') }}：</span>
              <kbd class="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">←</kbd>
              <kbd class="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">→</kbd>
              <span>{{ t('biding.shortcuts.switch') }}</span>
              <kbd class="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">ESC</kbd>
              <span>{{ t('biding.shortcuts.back') }}</span>
            </div>
            
            <!-- 右侧：评分、时间徽章和AI分析按钮 -->
            <div class="flex items-center gap-2 flex-shrink-0">
              <Badge :class="getScoreClass(recommendItem?.recommend_score)" class="text-xs">
                {{ t('biding.detail.recommendation_score_label') }}: {{ recommendItem?.recommend_score !== undefined && recommendItem?.recommend_score !== null ? new Decimal(recommendItem.recommend_score).toFixed(1) : '-' }}
              </Badge>
              <Badge variant="outline" class="text-xs">
                {{ formatDateString(selectedItem?.processing_timestamp?.toString() || '') }}
              </Badge>
              
              <!-- AI 深度分析按钮 with DropdownMenu -->
              <DropdownMenu v-model:open="showAiConfirm">
                <DropdownMenuTrigger as-child>
                  <Button 
                    variant="outline" 
                    size="sm"
                    class="h-7 px-3 text-xs gap-1.5 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-purple-100 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md">
                    <BrainIcon class="w-3.5 h-3.5"/>
                    <span>AI 分析</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent class="w-80 p-0" align="end">
                  <div class="p-4 space-y-3">
                    <div class="flex items-start gap-3">
                      <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <BrainIcon class="w-4 h-4 text-blue-600"/>
                      </div>
                      <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-900 text-sm">AI 深度分析</h4>
                        <p class="text-xs text-gray-600 mt-1">
                          为「{{ sNewTitle }}」进行智能分析
                        </p>
                      </div>
                    </div>
                    <div class="bg-blue-50 rounded-md p-3">
                      <div class="text-xs text-blue-800 space-y-1">
                        <div class="flex items-center gap-1">
                          <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
                          <span>项目可行性评估</span>
                        </div>
                        <div class="flex items-center gap-1">
                          <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
                          <span>竞争力分析与策略</span>
                        </div>
                        <div class="flex items-center gap-1">
                          <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
                          <span>风险评估报告</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex gap-2 pt-1">
                      <Button 
                        @click="handleConfirmAiProcessing"
                        class="flex-1 h-8 text-xs"
                        size="sm">
                        开始分析
                      </Button>
                      <Button 
                        @click="handleCancelAiProcessing"
                        variant="outline" 
                        class="flex-1 h-8 text-xs"
                        size="sm">
                        取消
                      </Button>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent class="flex-1 min-h-0 overflow-y-auto scrollbar-thin">
        <Tabs defaultValue="recommendation" class="w-full">
          <TabsList class="grid w-full grid-cols-5 sticky top-0 bg-gray-100 z-10">
            <TabsTrigger value="recommendation" class="cursor-pointer">{{ t('biding.tabs.recommendation') }}</TabsTrigger>
            <TabsTrigger value="basic" class="cursor-pointer">{{ t('biding.tabs.basic') }}</TabsTrigger>
            <TabsTrigger value="organization" class="cursor-pointer">{{ t('biding.tabs.organization') }}</TabsTrigger>
            <TabsTrigger value="requirements" class="cursor-pointer">{{ t('biding.tabs.requirements') }}</TabsTrigger>
            <TabsTrigger value="scoring" class="cursor-pointer">{{ t('biding.tabs.scoring') }}</TabsTrigger>
          </TabsList>

          <!-- 推荐信息标签页 -->
          <TabsContent value="recommendation" class="overflow-x-auto max-w-full">
            <div v-if="selectedItem?.recommendation" class="space-y-4">
              <div class="bg-blue-50 p-3 rounded-lg">
                {{ t('biding.recommendation.score') }}: {{ new Decimal(selectedItem.recommendation.recommend_score).toFixed(1) }}
              </div>
              
              <Table>
                <!-- <TableHeader>
                  <TableRow>
                    <TableHead class="w-1/4">{{ t('biding.recommendation.field') }}</TableHead>
                    <TableHead>{{ t('biding.recommendation.value') }}</TableHead>
                  </TableRow>
                </TableHeader> -->
                <TableBody>
                  <TableRow>
                    <TableCell class="font-medium w-1/4">{{ t('biding.recommendation.score_label') }}</TableCell>
                    <TableCell>
                      <Badge :class="getScoreClass(selectedItem.recommendation.recommend_score)" class="text-sm">
                        {{ new Decimal(selectedItem.recommendation.recommend_score).toFixed(1) }}
                      </Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell class="font-medium w-1/4">{{ t('biding.recommendation.update_time_label') }}</TableCell>
                    <TableCell>
                      {{ formatDateString(selectedItem.recommendation.update_time) }}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
              
              <!-- <div class="space-y-2">
                <h3 class="text-base font-semibold">{{ t('biding.recommendation.reason_title') }}</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                  <p class="text-sm text-gray-700 whitespace-pre-wrap">
                    {{ selectedItem.recommendation.recommend_reason }}
                  </p>
                </div>
              </div> -->
            </div>
            <div v-else class="text-center text-slate-500 py-10">
              <ActivityIcon class="h-12 w-12 text-slate-300 mx-auto mb-3"/>
              <p>{{ t('biding.no_recommendation_data') }}</p>
            </div>
          </TabsContent>

          <!-- 基本信息标签页 -->
          <TabsContent value="basic" class="overflow-x-auto max-w-full">
            <div v-if="basicInfoDisplayFields.length > 0">
              <Table class="w-full table-fixed">
                <TableBody>
                  <TableRow v-for="field in basicInfoDisplayFields" :key="field.key">
                    <TableCell :class="[field.labelCellClass || 'font-medium w-1/4']">{{ field.label }}</TableCell>
                    <TableCell :class="field.cellClass">
                      <template v-if="field.type === 'date'">{{ formatDateString(field.value) }}</template>
                      <template v-else-if="field.type === 'currency'">{{ field.value ?? '-' }} {{ field.currencySymbol ?? '' }}</template>
                      <template v-else-if="field.type === 'badge-list' && Array.isArray(field.value)">
                        <div class="flex gap-2 flex-wrap">
                          <Badge v-for="kw in field.value" :key="kw" variant="secondary">{{ kw }}</Badge>
                        </div>
                        <span v-if="!field.value.length">-</span>
                      </template>
                      <template v-else-if="field.type === 'pre-wrap'">{{ field.value ?? '-' }}</template>
                      <template v-else>{{ field.value ?? '-' }}</template>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
            <div v-else class="text-center text-slate-500 py-10">
              <ActivityIcon class="h-12 w-12 text-slate-300 mx-auto mb-3"/>
              <p>{{ t('common.no_data_available') }}</p>
            </div>
          </TabsContent>

          <!-- 招标方信息标签页 -->
          <TabsContent value="organization" class="overflow-x-auto max-w-full">
            <div v-if="organizationMainDisplayFields.length > 0 || organizationContactDetails.length > 0">
              <Table class="w-full table-fixed">
                <TableBody>
                  <TableRow v-for="field in organizationMainDisplayFields" :key="field.key">
                    <TableCell :class="[field.labelCellClass || 'font-medium w-1/4']">{{ field.label }}</TableCell>
                    <TableCell :class="field.cellClass">
                      <template v-if="field.type === 'link'">
                        <a :href="field.value"
                           target="_blank"
                           class="text-blue-600 hover:underline"
                           v-if="field.value">{{ field.value }}</a>
                        <span v-else>-</span>
                      </template>
                      <template v-else>{{ field.value ?? '-' }}</template>
                    </TableCell>
                  </TableRow>
                  <TableRow v-if="organizationContactDetails.length > 0">
                    <TableCell class="font-medium align-top" :rowspan="organizationContactDetails.filter(cd => cd.value !== '-').length || 1">
                      {{ t('biding.field_contact_info_group_label') }}
                    </TableCell>
                    <template
                      v-if="organizationContactDetails[0] && organizationContactDetails[0].value !== '-'">
                      <TableCell :class="[organizationContactDetails[0].cellClass, {'border-b': organizationContactDetails.filter(cd => cd.value !== '-').length > 1 && organizationContactDetails[0].value !== '-'}]">
                        <div class="flex items-center">
                          <span class="font-medium text-sm mr-2">{{ organizationContactDetails[0].prefix }}</span>
                          <template v-if="organizationContactDetails[0].type === 'email-link'">
                            <a :href="`mailto:${organizationContactDetails[0].value}`" class="text-blue-600 hover:underline" v-if="organizationContactDetails[0].value">{{ organizationContactDetails[0].value }}</a>
                            <span v-else>-</span>
                          </template>
                          <template v-else>{{ organizationContactDetails[0].value ?? '-' }}</template>
                        </div>
                      </TableCell>
                    </template>
                    <template
                      v-else-if="organizationContactDetails.filter(cd => cd.value !== '-').length === 0">
                      <TableCell>-</TableCell>
                    </template>
                  </TableRow>
                  <template v-for="(detail, index) in organizationContactDetails.slice(1)" :key="detail.key">
                    <TableRow
                      v-if="detail.value !== '-'">
                      <TableCell :class="[detail.cellClass, {'border-b': index < organizationContactDetails.filter(cd => cd.value !== '-').length - 2 && detail.value !== '-'}]">
                        <div :class="detail.key === 'address' ? 'flex items-start' : 'flex items-center'">
                          <span class="font-medium text-sm mr-2">{{ detail.prefix }}</span>
                          <template v-if="detail.type === 'email-link'">
                            <a :href="`mailto:${detail.value}`" class="text-blue-600 hover:underline" v-if="detail.value">{{ detail.value }}</a>
                            <span v-else>-</span>
                          </template>
                          <template v-else>
                            <span>{{ detail.value ?? '-' }}</span>
                          </template>
                        </div>
                      </TableCell>
                    </TableRow>
                  </template>
                </TableBody>
              </Table>
            </div>
            <div v-else class="text-center text-slate-500 py-10">
              <ActivityIcon class="h-12 w-12 text-slate-300 mx-auto mb-3"/>
              <p>{{ t('common.no_data_available') }}</p>
            </div>
          </TabsContent>

          <!-- 招标需求标签页 -->
          <TabsContent value="requirements" class="overflow-x-auto max-w-full">
            <div v-if="requirementsCustomSections.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <template v-for="section in requirementsCustomSections" :key="section.key">
                <!-- 采购类型卡片 -->
                <Card v-if="section.key === 'procurement_type' && procurementTypeCardDisplayFields.length > 0">
                  <CardHeader>
                    <h3 class="text-base font-semibold">{{ t(section.titleKey) }}</h3>
                  </CardHeader>
                  <CardContent>
                    <Table class="w-full table-fixed">
                      <TableBody>
                        <TableRow v-for="field in procurementTypeCardDisplayFields" :key="field.key">
                          <TableCell :class="[field.labelCellClass || 'font-medium w-1/4']">{{ field.label }}</TableCell>
                          <TableCell :class="field.cellClass">{{ field.value ?? '-' }}</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <!-- 分类信息卡片 -->
                <Card v-else-if="section.key === 'classification' && classificationLevelDisplayFields.length > 0">
                  <CardHeader>
                    <h3 class="text-base font-semibold">{{ t(section.titleKey) }}</h3>
                  </CardHeader>
                  <CardContent>
                    <Table class="w-full table-fixed">
                      <TableBody>
                        <TableRow v-for="level in classificationLevelDisplayFields" :key="level.key">
                          <TableCell class="font-medium">{{ level.label }}</TableCell>
                          <TableCell>
                            <div>{{ level.nameValue ?? '-' }}</div>
                            <div class="flex items-center gap-2 mt-1" v-if="level.confidenceValue">
                              <Badge variant="outline" class="text-xs">
                                {{ t('biding.field_confidence_prefix') }} {{ level.confidenceValue ?? '-' }}
                              </Badge>
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <!-- 招标需求卡片 -->
                <Card v-else-if="section.key === 'tender_requirements' && tenderRequirementsCardDisplayFields.length > 0" class="md:col-span-2">
                  <CardHeader>
                    <h3 class="text-base font-semibold">{{ t(section.titleKey) }}</h3>
                  </CardHeader>
                  <CardContent>
                    <Table class="w-full table-fixed">
                      <TableBody>
                        <TableRow v-for="field in tenderRequirementsCardDisplayFields" :key="field.key">
                          <TableCell :class="[field.labelCellClass || 'font-medium w-1/4']">{{ field.label }}</TableCell>
                          <TableCell :class="[field.cellClass, 'text-wrap-cell']">
                            <template v-if="field.type === 'list' && Array.isArray(field.value)">
                              <ul class="list-disc pl-5">
                                <li v-for="(item, i) in field.value" :key="i" class="break-words whitespace-normal">{{ item }}</li>
                              </ul>
                              <span v-if="!field.value.length">-</span>
                            </template>
                            <template v-else>{{ field.value ?? '-' }}</template>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </template>
            </div>
            <div v-else class="text-center text-slate-500 py-10">
              <ActivityIcon class="h-12 w-12 text-slate-300 mx-auto mb-3"/>
              <p>{{ t('common.no_data_available') }}</p>
            </div>
          </TabsContent>

          <!-- 评分详情标签页 -->
          <TabsContent value="scoring" class="overflow-x-auto max-w-full p-4 md:p-6">
            <div v-if="sComprehensiveScoring && sComprehensiveScoring.breakdown && Object.keys(sComprehensiveScoring.breakdown).length > 0"
                 class="w-full h-[300px] sm:h-[350px] md:h-[400px]">
              <EchartsComponent :params="radarChartOptions" :autoSize="true"/>
            </div>
            <div v-else class="text-center text-slate-500 py-10 w-full h-full">
              <ActivityIcon class="h-12 w-12 text-slate-300 mx-auto mb-3"/>
              <p>{{ t('biding.no_scoring_data_radar') }}</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, onMounted, onUnmounted, ref } from 'vue'
import { Decimal } from 'decimal.js'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ArrowLeftIcon, ActivityIcon, ChevronLeftIcon, ChevronDownIcon, ChevronRightIcon, ListIcon, MapPinIcon, AlertCircleIcon, BrainIcon } from 'lucide-vue-next'
import EchartsComponent from '@/components/EchartsComponent.vue'
import type { BidingAnalysisItem, BidingResultItem } from '../types' // Adjust path if necessary
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'

// Define a type for individual scoring dimensions
interface ScoringDimension {
  score?: number;
  name?: string;
  reasoning?: string;
}

// Define a type for the overall scoring object, allowing specific keys we expect
// and a general index signature for flexibility if other scores might exist.
interface ScoringData {
  client_authority?: ScoringDimension;
  vendor_bias?: ScoringDimension;
  project_amount?: ScoringDimension;
  information_completeness?: ScoringDimension;
  future_collaboration?: ScoringDimension;
  comprehensive?: ScoringDimension & { breakdown?: Record<string, number> };
  language?: string;
  [key: string]: any; // Allows other keys, but we primarily care about the typed ones
}

// --- Enhanced Types for Configuration-Driven Rendering ---
interface FieldConfig {
  key: string;
  labelKey: string;
  dataPath: string; // 数据路径，如 'extraction.basic_info.title'
  type?: 'text' | 'date' | 'currency' | 'badge-list' | 'list' | 'link' | 'email-link' | 'pre-wrap';
  currencySymbol?: string;
  cellClass?: string;
  labelCellClass?: string;
  required?: boolean; // 是否必须显示，即使数据为空
  visible?: boolean; // 是否可见
}

interface ContactFieldConfig {
  key: string;
  prefixKey: string;
  dataPath: string;
  type?: 'text' | 'email-link';
  cellClass?: string;
}

interface TabConfig {
  visible: boolean;
  fields?: FieldConfig[];
  contactFields?: ContactFieldConfig[];
  customSections?: any[]; // 自定义区域配置
}

interface TabsConfig {
  [tabName: string]: TabConfig;
}

// --- Helper Types for Dynamic Rendering (copied from BidingRespon) ---
interface DynamicField {
  key: string;
  label: string;
  value: any;
  type?: 'text' | 'date' | 'currency' | 'badge-list' | 'list' | 'link' | 'email-link' | 'pre-wrap';
  currencySymbol?: string;
  cellClass?: string;
  labelCellClass?: string;
}

interface ContactDetailField {
  key: string;
  prefix: string;
  value: any;
  type?: 'text' | 'email-link';
  cellClass?: string;
}

interface ClassificationLevelField {
  key: string;
  label: string;
  nameValue: string | undefined | null;
  confidenceValue: string | number | undefined | null;
}


const props = defineProps<{
  selectedItem: BidingAnalysisItem | null
  selectedItemBasicInfo?: BidingResultItem | null
  detailLoadingState?: 'idle' | 'loading' | 'success' | 'error'
  allItems?: BidingAnalysisItem[]
  currentIndex?: number
  getScoreClass:(score: number | undefined) => string
  formatDateString: (dateInput: any) => string
  t: (key: string, ...args: any[]) => string // i18n function
  hexToRgb: (hex: string) => { r: number; g: number; b: number } | null
}>()

const emit = defineEmits(['close-detail', 'navigate-to-item', 'navigate-previous', 'navigate-next'])

// 添加 router
const router = useRouter()

const handleCloseDetail = () => {
  emit('close-detail')
}

// 导航事件处理函数
const handleNavigatePrevious = () => {
  emit('navigate-previous')
}

const handleNavigateNext = () => {
  emit('navigate-next')
}

const handleNavigateToItem = (index: number) => {
  emit('navigate-to-item', index)
}

// 键盘快捷键处理
const isHandlingKeyEvent = ref(false) // 添加防抖标记

const handleKeydown = (event: KeyboardEvent) => {
  // 如果正在处理键盘事件，忽略后续事件
  if (isHandlingKeyEvent.value) {
    return
  }

  // 检查是否在输入框或可编辑元素中
  const target = event.target as HTMLElement
  if (target && (
    target.tagName === 'INPUT' ||
    target.tagName === 'TEXTAREA' ||
    target.contentEditable === 'true' ||
    target.classList.contains('el-input__inner') // Element Plus 输入框
  )) {
    return
  }

  // 确保只有在有多个项目时才处理导航快捷键
  if (!props.allItems || props.allItems.length <= 1) {
    // 即使只有一个项目，ESC键仍然可以返回
    if (event.key === 'Escape') {
      event.preventDefault()
      event.stopPropagation()
      handleCloseDetail()
    }
    return
  }

  // 设置防抖标记
  isHandlingKeyEvent.value = true

  let handled = false

  switch (event.key) {
  case 'ArrowLeft':
    if (props.currentIndex !== undefined && props.currentIndex > 0) {
      event.preventDefault()
      event.stopPropagation()
      handleNavigatePrevious()
      handled = true
    }
    break
    
  case 'ArrowRight':
    if (props.currentIndex !== undefined && props.currentIndex < props.allItems.length - 1) {
      event.preventDefault()
      event.stopPropagation()
      handleNavigateNext()
      handled = true
    }
    break
    
  case 'Escape':
    event.preventDefault()
    event.stopPropagation()
    handleCloseDetail()
    handled = true
    break
  }

  // 如果事件被处理了，添加短暂延迟防止重复触发
  if (handled) {
    setTimeout(() => {
      isHandlingKeyEvent.value = false
    }, 100) // 100ms 防抖延迟
  } else {
    isHandlingKeyEvent.value = false
  }
}

// --- Computed properties for selectedItem fields ---
const recommendItem = computed(() => props.selectedItem?.recommendation)
const sScoringInfo = computed(() => props.selectedItem?.scoring as ScoringData | undefined) // Cast for type safety
const sComprehensiveScoring = computed(() => sScoringInfo.value?.comprehensive)
const sNewTitle = computed(() => props.selectedItem?.new_title || props.t('biding.detail_title_fallback'))

// 配置对象：定义各个tab页的字段配置
const tabsConfig = computed<Record<string, TabConfig>>(() => ({
  basic: {
    visible: true,
    fields: [
      { key: 'title', labelKey: 'biding.field_title', dataPath: 'extraction.basic_info.title', type: 'pre-wrap', cellClass: 'text-wrap-cell whitespace-pre-wrap', labelCellClass: 'font-medium w-1/4' },
      { key: 'country', labelKey: 'biding.field_country', dataPath: 'extraction.basic_info.country', type: 'text', labelCellClass: 'font-medium' },
      { key: 'publication_date', labelKey: 'biding.field_publication_date', dataPath: 'extraction.basic_info.publication_date', type: 'date', labelCellClass: 'font-medium' },
      { key: 'submission_deadline', labelKey: 'biding.field_submission_deadline', dataPath: 'extraction.basic_info.submission_deadline', type: 'date', labelCellClass: 'font-medium' },
      { key: 'estimated_value', labelKey: 'biding.field_estimated_value', dataPath: 'extraction.basic_info.estimated_value', type: 'currency', labelCellClass: 'font-medium' },
      { key: 'detailed_description', labelKey: 'biding.field_detailed_description', dataPath: 'extraction.basic_info.detailed_description', type: 'pre-wrap', cellClass: 'text-wrap-cell whitespace-pre-wrap', labelCellClass: 'font-medium' },
      { key: 'keywords', labelKey: 'biding.field_keywords', dataPath: 'keywords', type: 'badge-list', labelCellClass: 'font-medium' },
    ]
  },
  organization: {
    visible: true,
    fields: [
      { key: 'org_name', labelKey: 'biding.field_org_name', dataPath: 'extraction.issuing_organization.name', type: 'text', cellClass: 'text-wrap-cell', labelCellClass: 'font-medium w-1/4' },
      { key: 'org_website', labelKey: 'biding.field_org_website', dataPath: 'extraction.issuing_organization.website', type: 'link', cellClass: 'text-wrap-cell break-all', labelCellClass: 'font-medium' },
    ],
    contactFields: [
      { key: 'name', prefixKey: 'biding.field_contact_person_prefix', dataPath: 'extraction.issuing_organization.contact_info.name', type: 'text' },
      { key: 'phone', prefixKey: 'biding.field_contact_phone_prefix', dataPath: 'extraction.issuing_organization.contact_info.phone', type: 'text' },
      { key: 'email', prefixKey: 'biding.field_contact_email_prefix', dataPath: 'extraction.issuing_organization.contact_info.email', type: 'email-link' },
      { key: 'address', prefixKey: 'biding.field_contact_address_prefix', dataPath: 'extraction.issuing_organization.contact_info.address', type: 'text' },
    ]
  },
  requirements: {
    visible: true,
    customSections: [
      {
        key: 'procurement_type',
        titleKey: 'biding.card_title_procurement_type',
        fields: [
          { key: 'type', labelKey: 'biding.field_procurement_type_type', dataPath: 'procurement_type.type', type: 'text', labelCellClass: 'font-medium' },
          { key: 'confidence', labelKey: 'biding.field_procurement_type_confidence', dataPath: 'procurement_type.confidence', type: 'text', labelCellClass: 'font-medium' },
        ]
      },
      {
        key: 'classification',
        titleKey: 'biding.card_title_classification_info',
        isClassification: true
      },
      {
        key: 'tender_requirements',
        titleKey: 'biding.card_title_tender_requirements',
        fields: [
          { key: 'category', labelKey: 'biding.field_tender_req_category', dataPath: 'extraction.tender_requirements.category', type: 'text', labelCellClass: 'font-medium' },
          { key: 'industry_sector', labelKey: 'biding.field_tender_req_industry', dataPath: 'extraction.tender_requirements.industry_sector', type: 'text', labelCellClass: 'font-medium' },
          { key: 'items', labelKey: 'biding.field_tender_req_items', dataPath: 'extraction.tender_requirements.items', type: 'list', labelCellClass: 'font-medium' },
          { key: 'supplier_requirements', labelKey: 'biding.field_supplier_reqs', dataPath: 'extraction.supplier_requirements.supplier_requirement', type: 'list', labelCellClass: 'font-medium' },
        ]
      }
    ]
  }
}))

// 通用函数：根据数据路径获取值
const getValueByPath = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// 通用函数：生成动态字段
const generateDynamicFields = (config: FieldConfig[]): DynamicField[] => {
  if (!props.selectedItem) {
    return []
  }

  return config.filter(fieldConfig => {
    const value = getValueByPath(props.selectedItem, fieldConfig.dataPath)
    // 如果字段不是必需的且值为空，则过滤掉
    if (!fieldConfig.required && (!value || (Array.isArray(value) && value.length === 0))) {
      return false
    }
    return fieldConfig.visible !== false
  }).map(fieldConfig => {
    let value = getValueByPath(props.selectedItem, fieldConfig.dataPath)

    // 特殊处理货币字段的符号
    if (fieldConfig.type === 'currency' && fieldConfig.key === 'estimated_value') {
      const currencySymbol = getValueByPath(props.selectedItem, 'extraction.basic_info.currency') || ''
      return {
        key: fieldConfig.key,
        label: props.t(fieldConfig.labelKey),
        value,
        type: fieldConfig.type,
        currencySymbol,
        cellClass: fieldConfig.cellClass,
        labelCellClass: fieldConfig.labelCellClass
      }
    }

    return {
      key: fieldConfig.key,
      label: props.t(fieldConfig.labelKey),
      value,
      type: fieldConfig.type,
      cellClass: fieldConfig.cellClass,
      labelCellClass: fieldConfig.labelCellClass
    }
  })
}

const basicInfoDisplayFields = computed<DynamicField[]>(() => {
  const config = tabsConfig.value.basic
  return config.visible ? generateDynamicFields(config.fields || []) : []
})

const organizationMainDisplayFields = computed<DynamicField[]>(() => {
  const config = tabsConfig.value.organization
  return config.visible ? generateDynamicFields(config.fields || []) : []
})

const organizationContactDetails = computed<ContactDetailField[]>(() => {
  const config = tabsConfig.value.organization
  if (!config.visible || !props.selectedItem || !config.contactFields) {
    return []
  }

  return config.contactFields.filter(fieldConfig => {
    const value = getValueByPath(props.selectedItem, fieldConfig.dataPath)
    return value && value !== '-'
  }).map(fieldConfig => ({
    key: fieldConfig.key,
    prefix: props.t(fieldConfig.prefixKey),
    value: getValueByPath(props.selectedItem, fieldConfig.dataPath) ?? '-',
    type: fieldConfig.type,
    cellClass: fieldConfig.cellClass
  }))
})

// 获取需求页面的自定义区域配置
const requirementsCustomSections = computed(() => {
  const config = tabsConfig.value.requirements
  if (!config.visible || !props.selectedItem || !config.customSections) {
    return []
  }

  return config.customSections.filter(section => {
    if (section.isClassification) {
      // 分类信息特殊处理
      const classification = getValueByPath(props.selectedItem, 'classification')
      return classification && (classification.first_level || classification.second_level || classification.third_level)
    }

    if (section.fields) {
      // 检查是否有任何字段有值
      return section.fields.some((field: FieldConfig) => {
        const value = getValueByPath(props.selectedItem, field.dataPath)
        return value && (!Array.isArray(value) || value.length > 0)
      })
    }

    return true
  })
})

const procurementTypeCardDisplayFields = computed<DynamicField[]>(() => {
  const section = requirementsCustomSections.value.find(s => s.key === 'procurement_type')
  return section ? generateDynamicFields(section.fields || []) : []
})

const classificationLevelDisplayFields = computed<ClassificationLevelField[]>(() => {
  if (!props.selectedItem) { return [] }
  const classification = getValueByPath(props.selectedItem, 'classification')
  if (!classification) { return [] }

  const levelsRaw = [
    { key: 'first_level', label: props.t('biding.field_classification_first'), data: classification.first_level },
    { key: 'second_level', label: props.t('biding.field_classification_second'), data: classification.second_level },
    { key: 'third_level', label: props.t('biding.field_classification_third'), data: classification.third_level, defaultName: props.t('common.none') },
  ]
  return levelsRaw.filter(l => l.data).map(l => ({
    key: l.key,
    label: l.label,
    nameValue: l.data?.name ?? (l.key === 'third_level' && !l.data?.name ? l.defaultName : (l.data?.name ?? '-')),
    confidenceValue: l.data?.confidence ?? (l.data ? '-' : null),
  }))
})

const tenderRequirementsCardDisplayFields = computed<DynamicField[]>(() => {
  const section = requirementsCustomSections.value.find(s => s.key === 'tender_requirements')
  return section ? generateDynamicFields(section.fields || []) : []
})

const radarChartOptions = computed(() => {
  const dimensionsOrder: Array<keyof ScoringData & string> = [
    'client_authority', 'vendor_bias', 'project_amount',
    'information_completeness', 'future_collaboration'
  ]

  if (!sScoringInfo.value) { return {} }

  const scoringData = sScoringInfo.value
  let hasValidData = false

  const indicators = dimensionsOrder.map(key => {
    const dimensionScoreData = scoringData[key] as ScoringDimension | undefined
    if (dimensionScoreData && typeof dimensionScoreData.score === 'number') {
      hasValidData = true
    }
    const displayNameKey = `biding.score.${key}`
    return { name: props.t(displayNameKey), max: 10 }
  })

  const seriesDataValues = dimensionsOrder.map(key => {
    const dimensionScoreData = scoringData[key] as ScoringDimension | undefined
    return (dimensionScoreData && typeof dimensionScoreData.score === 'number')
      ? new Decimal(dimensionScoreData.score).toNumber() : 0
  })

  if (!hasValidData) { return {} }

  let primaryColorRgb = { r: 59, g: 130, b: 246 }
  if (typeof window !== 'undefined') {
    try {
      const primaryColorHex = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim()
      const rgb = props.hexToRgb(primaryColorHex) // Use hexToRgb from props
      if (rgb) { primaryColorRgb = rgb }
    } catch (e) {
      console.warn('Could not get --primary-color CSS variable:', e)
    }
  }
  const { r, g, b } = primaryColorRgb

  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        let tooltipContent = `${params.name}<br/>`
        if (params.data && params.data.value && params.data.value.length === indicators.length) {
          indicators.forEach((indicator, index) => {
            tooltipContent += `${indicator.name}: ${new Decimal(params.data.value[index]).toFixed(1)}<br/>`
          })
        }
        return tooltipContent
      }
    },
    radar: {
      indicator: indicators,
      shape: 'circle', splitNumber: 5,
      axisName: { formatter: '{value}', color: '#42627B', fontSize: 10, overflow: 'truncate', width: 80, ellipsis: '...' },
      splitArea: { areaStyle: { color: ['rgba(227, 235, 245, 0.4)', 'rgba(240, 243, 248, 0.3)'], shadowBlur: 0 } },
      axisLine: { lineStyle: { color: 'rgba(156, 163, 175, 0.3)' } },
      splitLine: { lineStyle: { color: 'rgba(203, 213, 225, 0.2)' } }
    },
    series: [{
      name: props.t('biding.original_score_radar_label'),
      type: 'radar',
      data: [{
        value: seriesDataValues,
        areaStyle: { color: `rgba(${r}, ${g}, ${b}, 0.3)` },
        lineStyle: { color: `rgba(${r}, ${g}, ${b}, 0.7)` },
        itemStyle: { color: `rgb(${r}, ${g}, ${b})` },
        label: { show: true, formatter: (params: any) => { return typeof params.value === 'number' ? new Decimal(params.value).toFixed(1) : '-' }, color: '#333' }
      }]
    }]
  }
})

// AI 流程处理方法
const handleAiProcessing = async () => {
  if (!props.selectedItem) {
    return
  }

  try {
    await ElMessageBox.confirm(
      `
        <div style="margin-bottom: 16px; color: #374151;">
          即将在 AI Dashboard 中为「${sNewTitle.value}」进行深度分析处理
        </div>
        <div style="color: #3b82f6; font-size: 14px; padding: 12px; background-color: #eff6ff; border-radius: 6px; border-left: 4px solid #3b82f6;">
          <strong>AI 分析将包括：</strong><br/>
          • 项目可行性评估<br/>
          • 竞争力分析<br/>
          • 投标策略建议<br/>
          • 风险评估
        </div>
      `,
      'AI 深度分析确认',
      {
        confirmButtonText: '开始 AI 分析',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        confirmButtonClass: 'el-button--primary',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        showCancelButton: true,
        showConfirmButton: true,
        icon: ''
      }
    )
    
    // 构建带参数的URL，传递当前项目信息
    const params = new URLSearchParams({
      projectId: props.selectedItem._id,
      title: sNewTitle.value,
      source: 'detail-view'
    })
    
    // 跳转到 AI Dashboard
    router.push(`/next/dashboard?${params.toString()}`)
    
  } catch {
    // 用户取消操作，不执行任何操作
    return
  }
}

// 添加和移除键盘事件监听器
const keyEventListenerAdded = ref(false)
const showAiConfirm = ref(false)

onMounted(() => {
  // 确保只添加一次事件监听器
  if (!keyEventListenerAdded.value) {
    document.addEventListener('keydown', handleKeydown, { passive: false })
    keyEventListenerAdded.value = true
    console.log('BidingDetailView: 键盘事件监听器已添加')
  }
})

onUnmounted(() => {
  // 组件卸载时移除事件监听器，避免内存泄漏
  if (keyEventListenerAdded.value) {
    document.removeEventListener('keydown', handleKeydown)
    keyEventListenerAdded.value = false
    isHandlingKeyEvent.value = false // 重置防抖标记
    console.log('BidingDetailView: 键盘事件监听器已移除')
  }
})

const handleConfirmAiProcessing = () => {
  if (!props.selectedItem) {
    return
  }
  
  // 关闭确认框
  showAiConfirm.value = false
  
  // 构建带参数的URL，传递当前项目信息
  const params = new URLSearchParams({
    projectId: props.selectedItem._id,
    title: sNewTitle.value,
    source: 'detail-view'
  })
  
  // 跳转到 AI Dashboard
  router.push(`/next/dashboard?${params.toString()}`)
}

const handleCancelAiProcessing = () => {
  showAiConfirm.value = false
}

</script>

<style scoped>
.text-wrap-cell {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}
.break-all {
  word-break: break-all;
}
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
}
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 推荐理由卡片样式 - 使用系统主题色 */
.recommendation-reason-card {
  background: var(--brand-color-1);
  border: 1px solid var(--brand-color-2);
  border-radius: var(--radius-default);
  padding: var(--m-default);
}

.recommendation-reason-indicator {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  margin-top: 2px;
}

.recommendation-reason-title {
  font-size: var(--size-font-8);
  font-weight: var(--weight-font-Semiblod);
  color: var(--primary-color);
  margin-bottom: var(--m-mini);
}

.recommendation-reason-content {
  font-size: var(--size-font-9);
  color: var(--primary-color);
  line-height: 1.5;
}
</style> 