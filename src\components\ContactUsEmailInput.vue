<template>
  <div :class="$style.root">
    <div :class="$style['input-container']">
      <el-input
        v-model="emailValue"
        :placeholder="placeholder"
        ref="inputRef"
        :status="emailError ? 'error' : ''"
        @input="validateEmail"
        @keydown.enter="handleSubmit"
        @keydown.esc="handleCancel"
        :class="{ 'hover:cursor-not-allowed': loading }">
        <template #suffix>
          <Icon
            v-if="!loading && emailValue && !emailError"
            icon="mdi:send"
            class="cursor-pointer text-[24px] text-[#4466BC]"
            :class="[
              'cursor-pointer',
              { 'opacity-50 cursor-not-allowed': !emailValue || !!emailError },
            ]"
            @click="handleSubmit"/>
          <Icon
            v-else-if="loading"
            icon="mdi:loading"
            class="cursor-pointer text-[24px] text-[#4466BC]"
            :class="[
              'cursor-pointer',
              { 'opacity-50 cursor-not-allowed': !emailValue || !!emailError },
            ]"/>
          <Icon
            v-else
            icon="mdi:close"
            class="cursor-pointer text-[24px] text-[#4466BC]"
            @click="handleCancel"/>
        </template>
      </el-input>

      <!-- Error Message Container -->
      <div
        :class="[$style['error-container'], { [$style.visible]: emailError }]">
        {{ emailError || " " }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, reactive } from 'vue'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import { useVModel } from '@vueuse/core'

const { t } = useI18n()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '',
  },
  email: {
    type: String,
    default: '',
  },
})

const emit = defineEmits<{
  'update:email': [value: string];
  submit: [email: string];
  cancel: [];
}>()

// Use VueUse's useVModel instead of manual ref + watch
const emailValue = useVModel(props, 'email', emit)
const emailError = ref('')
const inputRef = ref()

// Auto focus on mount
onMounted(() => {
  nextTick(() => {
    inputRef.value?.input?.focus()
  })
})

// Email validation
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailValue.value) {
    emailError.value = t('validation.required')
  } else if (!emailRegex.test(emailValue.value)) {
    emailError.value = t('validation.emailFormat')
  } else {
    emailError.value = ''
  }
}

// Handle submit
const handleSubmit = () => {
  if (props.loading || !emailValue.value || emailError.value) {
    return
  }
  emit('submit', emailValue.value)
}

// Handle cancel
const handleCancel = () => {
  emailValue.value = ''
  emailError.value = ''
  emit('cancel')
}
const iconButtons = reactive({
  send: {
    icon: 'mdi:send',
    text: 'Send',
    show: !props.loading && emailValue.value && emailError.value,
    action: handleSubmit,
  },
  loading: {
    icon: 'mdi:loading',
    text: 'Loading',
    show: props.loading,
    action: () => {},
  },
  close: {
    icon: 'mdi:close',
    text: 'Close',
    show: !props.loading,
    action: handleCancel,
  },
})
// Expose methods to parent
defineExpose({
  focus: () => inputRef.value?.input?.focus(),
  clear: () => {
    emailValue.value = ''
    emailError.value = ''
  },
})
</script>

<style lang="scss" module>
.root {
  position: relative;

  :global {
    .el-input {
      .el-input__wrapper {
        border-radius: 4px;
        background-color: #ffffff;
        box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
        background: none;
        padding-right: 16px;

        &.is-focus {
          box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0 !important;
        }
      }

      .el-input__inner {
        height: 48px;
        font-size: 16px;

        &::placeholder {
          color: #ccc;
          font-size: 16px;
        }
      }

      .el-input__suffix {
        display: flex;
        align-items: center;
        right: 16px;
      }
    }
  }
}

.input-container {
  display: flex;
  flex-direction: column;
  min-height: 80px; // Reserve space for error message
}

.error-container {
  color: #ef4444;
  font-size: 0.75rem;
  line-height: 1rem;
  margin-top: 0.5rem;
  text-align: left;
  opacity: 0;
  transform: translateY(-4px);
  transition: all 0.2s ease-in-out;
  min-height: 16px; // Minimum height for error message
  visibility: hidden;

  &.visible {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
}
</style>
