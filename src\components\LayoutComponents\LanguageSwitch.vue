<template>
  <el-dropdown 
    trigger="click" 
    @command="handleLanguageSwitch"
    class="language-switcher">
    <span class="flex items-center cursor-pointer">
      <Icon 
        :icon="getCurrentLanguageIcon" 
        class="mr-1 text-[16px]"/>
      {{ getCurrentLanguageLabel }}
      <Icon icon="mdi:chevron-down" class="ml-1 text-[16px]"/>
    </span>
      
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="lang in languageOptions"
          :key="lang.value"
          :command="lang.value"
          :class="{ 'is-active': currentLanguage === lang.value }">
          <div class="flex items-center">
            <Icon 
              :icon="lang.icon" 
              class="mr-2 text-[16px]"/>
            {{ lang.label }}
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
  
<script lang="ts" setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import { useUserStore } from '@/stores/user'
import { useLanguageSwitch } from '@/composables/useLanguageSwitch'
import type { Language } from '@/types/language'
  
const { locale } = useI18n()
const userStore = useUserStore()
const { switchLanguage } = useLanguageSwitch()
  
const languageOptions = [
  {
    value: 'chinese' as Language,
    label: '简体中文',
    icon: 'emojione:flag-for-china'
  },
  {
    value: 'english' as Language,
    label: 'English',
    icon: 'emojione:flag-for-united-states'
  },
  {
    value: 'japanese' as Language,
    label: '日本語',
    icon: 'emojione:flag-for-japan'
  }
] as const
  
const currentLanguage = computed(() => locale.value)
  
const getCurrentLanguageIcon = computed(() => {
  return languageOptions.find(lang => lang.value === currentLanguage.value)?.icon
})
  
const getCurrentLanguageLabel = computed(() => {
  return languageOptions.find(lang => lang.value === currentLanguage.value)?.label
})
  
const handleLanguageSwitch = async (newLanguage: Language) => {
  if (newLanguage === currentLanguage.value) {return}
    
  try {
    await switchLanguage(newLanguage, async (lang) => {
      await userStore.changeUserLanguage(lang)
    })
  } catch (error) {
    console.error('Language switch failed:', error)
  }
}
</script>
  
  <style lang="scss" scoped>
  .language-switcher {
    :deep(.el-dropdown-menu__item) {
      &.is-active {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
      
      &:hover {
        background-color: var(--el-dropdown-menuItem-hover-fill);
      }
    }
  }
  </style>