import { useAsyncState, type UseAsyncStateOptions } from '@vueuse/core'

export const useSinglePromise = <Data, <PERSON><PERSON> extends any[] = [], Shallow extends boolean = true>(promise: Promise<Data> | ((...args: Params) => Promise<Data>), initialState: Data, options?: UseAsyncStateOptions<Shallow, Data>) => {
  let activePromise: Promise<Data> | null = null
  const result = useAsyncState<Data, Params, Shallow>(promise, initialState, {
    ...(options || {}),
    onError: (e) => {
      activePromise = null
      options?.onError?.(e)
    },
    onSuccess: (value) => {
      activePromise = null
      options?.onSuccess?.(value)
    }
  })


  const execute = (...args: Params) => {
    if (result.isLoading.value) {
      if (!activePromise) {
        activePromise = new Promise((resolve, reject) => {
          result.then((value) => {
            if (value.error.value) {
              reject(value.error.value)
              return
            }
            resolve(value.state.value as Data)
          }, reject)
        })
      }
      return activePromise
    }

    return result.execute(0, ...args)
  }

  return {
    ...result,
    execute,
  }
}
