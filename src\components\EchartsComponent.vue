<script setup lang="ts">
import { useCurrentElement, useResizeObserver } from '@vueuse/core'
import type { EChartsType } from 'echarts'
import * as echarts from 'echarts'
import { nextTick, type PropType } from 'vue'
import { ref, onUnmounted, watch } from 'vue'

// Word cloud types
interface WordCloudData {
  name: string
  value: number
  textStyle?: Record<string, any>
}

interface WordCloudSeriesOption {
  type: 'wordCloud'
  shape?: 'circle' | 'cardioid' | 'diamond' | 'triangle-forward' | 'triangle' | 'pentagon' | 'star'
  keepAspect?: boolean
  maskImage?: HTMLImageElement
  sizeRange?: [number, number]
  rotationRange?: [number, number]
  rotationStep?: number
  gridSize?: number
  drawOutOfBound?: boolean
  shrinkToFit?: boolean
  layoutAnimation?: boolean
  data: WordCloudData[]
  textStyle?: Record<string, any>
  emphasis?: {
    focus?: 'self' | 'series'
    textStyle?: Record<string, any>
  }
}

type EChartsOptions = Parameters<EChartsType['setOption']>[0]

interface ChartParams {
  series?: (WordCloudSeriesOption | Record<string, any>)[]
  [key: string]: any
}

const props = defineProps({
  params: {
    type: Object as PropType<ChartParams>,
    default: () => ({})
  },
  getOption: {
    type: Function as PropType<(params: ChartParams) => Promise<EChartsOptions> | EChartsOptions>,
    default: null
  },
  autoSize: {
    type: Boolean,
    default: false
  },
  notMerge: {
    type: Boolean,
    default: false
  },
  noRebuilt: {
    type: Boolean,
    default: false
  },
  renderTimeout: {
    type: Number,
    default: 5000
  }
})

const emit = defineEmits<{
  'created': [value: EChartsType]
  'rendered': [value: EChartsType]
  'error': [error: Error]
  'dispose': []
}>()

const loading = ref(false)
const renderComplete = ref(false)
let rafId = 0
let charts: EChartsType | null = null
const el = useCurrentElement<HTMLElement>()

if (props.autoSize) {
  useResizeObserver(el, () => {
    if (rafId) {
      cancelAnimationFrame(rafId)
    }
    rafId = requestAnimationFrame(() => {
      charts?.resize()
    })
  })
}

const disposeChart = () => {
  charts?.dispose()
  charts = null
  emit('dispose')
}

const isWordCloudSeries = (series: any): series is WordCloudSeriesOption => {
  return series?.type === 'wordCloud'
}

const processWordCloudOptions = (options: ChartParams) => {
  if (!options.series) {return options}

  const processedSeries = options.series.map(series => {
    if (isWordCloudSeries(series)) {
      return {
        ...series,
        left: 'center',
        top: 'center',
        width: '70%',
        height: '80%',
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          ...series.textStyle,
          color: series.textStyle?.color || (() => {
            return 'rgb(' + [
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160)
            ].join(',') + ')'
          })
        },
        emphasis: {
          focus: 'self',
          textStyle: {
            textShadowBlur: 10,
            textShadowColor: '#333',
            ...series.emphasis?.textStyle
          },
          ...series.emphasis
        }
      }
    }
    return series
  })

  return {
    ...options,
    series: processedSeries
  }
}

const getOption = () => {
  const options = props.getOption?.(props.params) || props.params
  return processWordCloudOptions(options)
}

const setOption = (option: EChartsOptions) => {
  if (charts) {
    charts.setOption(option, { notMerge: props.notMerge })
  }
}

const waitForRender = async (chart: EChartsType): Promise<void> => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Chart render timeout'))
    }, props.renderTimeout)

    const checkRender = () => {
      if (chart.isDisposed()) {
        clearTimeout(timeout)
        reject(new Error('Chart was disposed'))
        return
      }

      const tryResolve = () => {
        chart.off('finished', tryResolve)
        clearTimeout(timeout)
        resolve()
      }

      chart.on('finished', tryResolve)
      
      chart.getZr().refresh()
    }

    setTimeout(checkRender, 50)
  })
}

const render = async () => {
  try {
    disposeChart()
    loading.value = true
    renderComplete.value = false
    
    const options = await getOption()
    
    await nextTick()
    charts = echarts.init(el.value)
    
    setOption(options)
    emit('created', charts)

    await waitForRender(charts)
    
    renderComplete.value = true
    emit('rendered', charts)
  } catch (error) {
    emit('error', error as Error)
  } finally {
    loading.value = false
  }
}

onUnmounted(() => {
  disposeChart()
})

watch(() => props.params, async() => {
  if (props.noRebuilt && charts) {
    const options = await getOption()
    setOption(options)
    return
  }
  nextTick(render)
}, { deep: true, immediate: true })

defineExpose({
  getEChartsInstance: () => charts,
  isRenderComplete: () => renderComplete.value
})
</script>

<template>
  <div class="w-[100%] h-[100%]" v-loading="loading"/>
</template>
