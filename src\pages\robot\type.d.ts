export interface Message {
    id: string;
    content: string;
    chatType: 'bot' | 'userInput';
    avatar?: string // 新增可选的avatar字段
  }
  
export interface RobotHelper {
  name: string;
  robot_profile: string;
  career: string;
  tags: string[];
  robot_id: string;
  avatar: string // 新增avatar字段
}

export interface CustomerService {
  id: string
  name: string
  avatar: string
  status: 'online' | 'offline'
}

export type CustomerServiceMessage = {
  id?: string;
  content: string;
  timestamp: number;
  sender: 'user' | 'service' | 'system';
}