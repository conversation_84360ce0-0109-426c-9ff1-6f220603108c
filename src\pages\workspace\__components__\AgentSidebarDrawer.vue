<template>
  <Drawer direction="right" :open="props.open" @update:open="emit('update:open', $event)">
    <DrawerContent class="p-0 flex flex-col h-full w-80"> 
      <AgentListSidebar 
        class="flex-1 min-h-0" 
        :agents="props.sidebarProps.agents"
        :selected-agent="props.sidebarProps.selectedAgent"
        :active-tab="props.sidebarProps.activeTab"
        :agent-search="props.agentSearch" 
        @update:agent-search="emit('update:agentSearch', $event)"
        @select-dynamic-filter="handleEvent('select-dynamic-filter', $event)"
        @switch-to-special-tab="handleEvent('switch-to-special-tab', $event)"/>
    </DrawerContent>
  </Drawer>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useI18n } from 'vue-i18n'
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerClose } from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { XIcon } from 'lucide-vue-next'
import AgentListSidebar from './AgentListSidebar.vue' // Import the actual sidebar
import type { Agent } from './AgentListSidebar.vue' // Import Agent type if needed elsewhere or define locally

// Type for the sidebarProps object
interface SidebarProps {
  agents: Agent[];
  selectedAgent: string | null | '__MONITOR__';
  activeTab: string;
}

// --- Props --- 
const props = defineProps<{
  open: boolean;
  agentSearch: string;
  sidebarProps: SidebarProps;
}>()

// --- Emits --- 
const emit = defineEmits<{
  (e: 'update:open', value: boolean): void;
  (e: 'update:agentSearch', value: string): void;
  (e: 'select-dynamic-filter', filterValue: string | null | '__MONITOR__'): void;
  (e: 'switch-to-special-tab', reportType: string): void;
}>()

const { t } = useI18n()

// Generic handler to forward events, ensuring type safety with emit definition
function handleEvent(eventName: 'select-dynamic-filter' | 'switch-to-special-tab', payload: any) {
  if (eventName === 'select-dynamic-filter') {
    emit(eventName, payload as string | null | '__MONITOR__')
  } else if (eventName === 'switch-to-special-tab') {
    emit(eventName, payload as string)
  }
  // Close drawer after selection/action
  emit('update:open', false)
}
</script> 