<template>
  <div :class="$style['container']">
    <!-- Type Tabs -->
    <div
      class="mb-6 p-4 rounded-xl border border-[rgba(68,102,188,0.1)] shadow-sm">
      <div class="flex flex-wrap gap-2 w-full">
        <Button
          v-for="mode in filteredAgentFilterOptions"
          :key="mode.value"
          :variant="currentType === mode.value ? 'default' : 'ghost'"
          size="sm"
          class="py-2 px-4 rounded-lg flex items-center gap-1.5 h-auto"
          :class="
            currentType === mode.value
              ? 'bg-[rgba(68,102,188,0.12)] text-[--main-color] font-semibold border-[rgba(68,102,188,0.3)] shadow-sm'
              : 'bg-[rgba(255,255,255,0.7)] text-[--font-color-2] border-[rgba(68,102,188,0.1)]'
          "
          @click="handleTypeChange(mode.value)">
          <Avatar class="h-9 w-9 ring-2 ring-white/70">
            <AvatarFallback
              :class="currentType === mode.value ? mode.iconClass : 'bg-white'">
              <component :is="mode.icon" class="h-4 w-4"/>
            </AvatarFallback>
          </Avatar>
          {{ $t(mode.label) }}
        </Button>
      </div>
    </div>

    <!-- Agents Grid -->
    <TransitionGroup
      name="agent-list"
      tag="div"
      :class="[$style['grid'], $style['transition-group']]">
      <AgentCard
        v-for="agent in displayAgents"
        :key="agent.id"
        :id="agent.id"
        :description="agent.description"
        :disabled="agent.disabled"
        :categoryTag="getAgentTag(agent)"
        @click="handleAgentClick(agent)"/>
      <div
        v-if="displayAgents.length === 0 && !isLoadingAgents && hasAnyPermission"
        :class="$style['empty-state']">
        <Icon icon="mdi:robot-off-outline" :class="$style['empty-icon']"/>
        <p :class="$style['empty-text']">
          {{ t("common.no_agent") }}
        </p>
      </div>
      <!-- 无权限提示 -->
      <div
        v-if="!hasAnyPermission"
        :class="$style['empty-state']">
        <Icon icon="mdi:shield-lock-outline" :class="$style['empty-icon']"/>
        <p :class="$style['empty-text']">
          {{ t("common.no_permission") }}
        </p>
      </div>
    </TransitionGroup>
  </div>

  <UserQueryDialog
    v-if="activeAgent"
    :title="activeAgent.name"
    :visible="dialogVisible"
    :userKeywords="activeAgent.query"
    :is-excel-mode="isSpecialAgent"
    :reportId="activeAgent.id"
    :show-binding-fields="showBindingFields"
    :industries="industries"
    :nations="activeAgent?.id === 'STRATEGIC' ? businessNations : nations"
    :is-loading-industries="isLoadingIndustries"
    :is-loading-nations="
      activeAgent?.id === 'STRATEGIC'
        ? isLoadingBusinessAnalysis
        : isLoadingNations
    "
    :is-strategic-type="isStrategicType"
    :news-types="newsTypeState || []"
    :is-loading-news-types="isLoadingNewsType"
    :is-loading-confirm="isDelayLoading"
    :initialSelectedNations="
      activeAgentIsStandardBidingType
        ? biddingTenderFilterParams.nations
        : undefined
    "
    :initialSelectedSecondLevelIndustries="
      activeAgentIsStandardBidingType
        ? biddingTenderFilterParams.industry_second
        : undefined
    "
    :initialSelectedKeywords="
      activeAgentIsStandardBidingType
        ? biddingTenderFilterParams.keywords
        : undefined
    "
    @confirm="handleConfirm"
    @update:visible="dialogVisible = $event"/>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import { definePage, useRouter, useRoute } from 'vue-router/auto'
import { useGet, usePost } from '@/hooks/useHttp'
import { useI18n } from 'vue-i18n'
import UserQueryDialog from '@/pages/format-report/__components__/UserQueryDialog.vue'
import { ElMessage } from 'element-plus'
import {
  AVATAR_MAP,
  SPECIAL_AGENT_TYPES,
} from '@/composables/useReportProgress'
import emitter from '@/utils/events'
import { useAgentStatusUpdates } from './composables/useAgentStatusUpdates'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import AgentCard from './__components__/AgentCard.vue'
import type { AgentMarket } from './type'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { BellRingIcon, UsersIcon, LayersIcon, Grid2x2 } from 'lucide-vue-next'
import type { Component } from 'vue'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { usePermissions } from '@/composables/usePermissions'

const BINDING_WORK_AGENT = ['STRATEGIC', 'AWARDED', 'TENDER']
const { execute: delayReport, isLoading: isDelaying } = usePost(
  '/formatted_report/delay_report_agent',
  {},
  {},
  {
    immediate: false,
  }
)

// 战略分析专用API
const {
  execute: delayBusinessAnalysis,
  isLoading: isDelayingBusinessAnalysis,
} = usePost(
  '/formatted_report/delay_business_analysis',
  {},
  {},
  {
    immediate: false,
  }
)

const {
  execute: getFilterParams,
  isLoading: isLoadingFilterParams,
  state: filterParamsState,
} = useGet<{ result: Record<string, string[]> }>(
  '/bid_agent/filter_params',
  {},
  {
    immediate: false,
  }
)

const biddingTenderFilterParams = computed(() => {
  return filterParamsState.value?.result || {}
})

const AGENT_FILTER_OPTIONS: {
  label: string;
  value: string;
  icon: Component;
  iconClass: string;
}[] = [
  {
    label: 'agent.filter.all',
    value: 'all',
    icon: Grid2x2,
    iconClass: 'bg-gradient-to-br from-violet-500 to-cyan-500 text-white',
  },
  {
    label: 'agent.filter.notify',
    value: 'notify',
    icon: BellRingIcon,
    iconClass: 'bg-gradient-to-br from-amber-400 to-orange-400 text-white',
  },
  {
    label: 'agent.filter.normal',
    value: 'normal',
    icon: UsersIcon,
    iconClass: 'bg-gradient-to-br from-violet-500 to-cyan-500 text-white',
  },
  {
    label: 'agent.filter.binding',
    value: 'binding',
    icon: LayersIcon,
    iconClass:
      'bg-gradient-to-br from-blue-400 to-indigo-400 text-white group-hover:scale-105 transition-transform',
  },
]
interface Agent {
  name: string;
  query: Record<string, string>;
  id: string;
  steps: {
    number: number;
    title: string;
  }[];
  description: string;
  agent_name?: string;
  report_type?: string;
}

interface AgentStatusUpdate {
  id: string;
  status: boolean;
}

interface SSEMessage {
  type: string;
  data: AgentStatusUpdate[];
}

interface Industry {
  label: string;
  value: string; // Or number, depending on API response
}

interface Nation {
  label: string;
  value: string; // Or number
}

interface Props {
  isExcelMode: boolean;
  modelValue?: File | null;
  showBindingFields?: boolean;
  industries?: Industry[];
  nations?: Nation[];
  isLoadingIndustries?: boolean;
  isLoadingNations?: boolean;
}

const { t } = useI18n()
const userStore = useUserStore()
const { canAccessNotifyFeature, canAccessNormalFeature, canAccessBindingFeature, canAccessBusinessOpportunityFeature, canAccessStrategicFeature, canAccessSpecialAnalysisFeature } = userStore
const { hasPermission, withPermission, canAccess } = usePermissions()

definePage({
  meta: {
    layout: 'default',
    requiresAuth: true,
  },
})

const dialogVisible = ref(false)
const activeAgent = ref<Agent>()
const {
  execute: getAgents,
  isLoading: isLoadingAgents,
  state: agentsState,
} = useGet<AgentMarket[]>(
  '/formatted_report/agent_market',
  {},
  {
    immediate: false,
  }
)

const isDelayLoading = computed(
  () =>
    isUploadingExcel.value ||
    isDelaying.value ||
    isLoadingBusinessAnalysis.value
)
const {
  execute: uploadExcel,
  isLoading: isUploadingExcel,
  state: uploadExcelState,
} = usePost(
  '/formatted_params/upload',
  {},
  {},
  {
    immediate: false,
  }
)
const {
  execute: getIndustry,
  isLoading: isLoadingIndustries,
  state: industryState,
} = useGet<Record<string, string[]>>(
  '/bid_agent/industry',
  {},
  {
    immediate: false,
  }
)
const {
  execute: getNations,
  isLoading: isLoadingNations,
  state: nationsState,
} = useGet<string[]>(
  '/bid_agent/nations',
  {},
  {
    immediate: false,
  }
)
const {
  execute: getBusinessAnalysis,
  isLoading: isLoadingBusinessAnalysis,
  state: businessAnalysisState,
} = useGet<string[]>(
  '/business_agent/nations',
  {},
  {
    immediate: false,
  }
)
const {
  execute: getNewsType,
  isLoading: isLoadingNewsType,
  state: newsTypeState,
} = useGet<string[]>(
  '/bid_agent/news_type',
  {},
  {
    immediate: false,
  }
)
// 使用新的composable
const { openConnection, closeConnection } = useAgentStatusUpdates(agentsState)

const route = useRoute()
const router = useRouter()

const isDialogConfirmLoading = ref(false)

// 通用重试函数
async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`尝试第 ${attempt} 次请求...`)
      const result = await requestFn()
      return result
    } catch (error: any) {
      lastError = error
      console.error(`第 ${attempt} 次请求失败:`, error)

      if (attempt < maxRetries) {
        // 显示重试提示
        ElMessage.warning(`${t('global.retrying')} (${attempt}/${maxRetries})`)
        // 等待一段时间后重试，每次重试间隔递增
        await new Promise((resolve) => setTimeout(resolve, delay * attempt))
      }
    }
  }

  // 所有重试都失败了，抛出最后的错误
  throw lastError
}

onMounted(async () => {
  console.log('Component mounted, initializing...')
  await getAgents()
  console.log('Opening SSE connection...')
  openConnection()

  // Pre-fetch common data
  console.log('Pre-fetching industry, nations, and news type data...')
  try {
    await Promise.all([
      getIndustry(),
      getNations(),
      getNewsType(),
      getFilterParams(),
    ])
    console.log('Common data pre-fetched successfully.')
  } catch (error) {
    console.error('Error pre-fetching common data:', error)
    // Optionally notify the user or handle specific errors
  }

  // 新增：检查是否需要获取业务分析国家数据
  if (route.query.agentId === 'STRATEGIC' && !businessAnalysisState.value) {
    try {
      await getBusinessAnalysis()
    } catch (error) {
      console.error('Error pre-fetching business analysis nations:', error)
    }
  }

  // --- Check for configuration request ---
  const agentIdToConfigure = route.query.configureAgentId as string | undefined // Ensure type
  const shouldAutoOpen = route.query.autoOpenDialog === 'true'

  if (agentIdToConfigure && shouldAutoOpen) {
    console.log(`Received request to configure agent: ${agentIdToConfigure}`)

    await nextTick() // Add a tick to allow computed properties to update after getAgents()

    // Find the *mapped* agent object using the ID
    const mappedAgentToConfigure = agents.value?.find(
      (agent) => agent.id === agentIdToConfigure
    )

    if (mappedAgentToConfigure) {
      console.log(
        'Found *mapped* agent, triggering dialog open:',
        mappedAgentToConfigure
      )
      // Pass the correctly mapped agent object to handleAgentClick
      await handleAgentClick(mappedAgentToConfigure)
    } else {
      console.warn(`Mapped agent with ID ${agentIdToConfigure} not found.`)
      // ElMessage.error(t('agent.config_not_found', { id: agentIdToConfigure }))
    }

    // --- IMPORTANT: Clear query parameters after processing ---
    router.replace({ query: {} })
  }
  // --------------------------------------
})

onUnmounted(() => {
  console.log('Closing SSE connection...')
  closeConnection()
})

const agents = computed(() => {
  return agentsState.value?.map((item) => {
    const steps =
      item?.steps?.map((step: string, index: number) => ({
        number: index + 1,
        title: t(`tools.${step}`),
      })) || []

    return {
      id: item.id,
      name:
        item.agent_name || t('agent.no_agent_name', { name: item.report_name }),
      description: `${item.report_name}: ${item.report_description}`,
      avatar: AVATAR_MAP[item.id as keyof typeof AVATAR_MAP],
      steps: steps,
      query: item.user_keywords,
      disabled: item.status || false,
      report_type: item.id,
    }
  })
})

const searchQuery = ref('')

// Add currentType ref
const currentType = ref('all')

// 根据功能权限过滤AGENT_FILTER_OPTIONS
const filteredAgentFilterOptions = computed(() => {
  return AGENT_FILTER_OPTIONS.filter(option => {
    switch (option.value) {
    case 'notify':
      return canAccess.notifyFeature.value
    case 'normal':
      return canAccess.normalFeature.value
    case 'binding':
      return canAccess.specialAnalysisFeature.value
    case 'all':
      // 'all'选项只有在至少有一个功能可用时才显示
      return canAccess.notifyFeature.value || canAccess.normalFeature.value || canAccess.specialAnalysisFeature.value
    default:
      return true
    }
  })
})

// 检查用户是否有任何权限
const hasAnyPermission = computed(() => {
  return canAccess.notifyFeature.value || canAccess.normalFeature.value || canAccess.specialAnalysisFeature.value
})

// Update filtering logic
const displayAgents = computed(() => {
  const filtered = filteredAgents.value || []

  // 通用的legal权限过滤函数
  const filterByLegalPermission = (agents: any[]) => {
    return agents.filter((agent) => {
      const agentId = agent.id || agent.report_type || ''
      // 如果没有legal权限，隐藏LegalAgent和CompositeAgent
      if (!canAccess.legalFeature.value && (agentId === 'LegalAgent' || agentId === 'CompositeAgent')) {
        return false
      }
      return true
    })
  }

  // 按权限和agent类型过滤的函数
  const filterByPermissionAndType = (agents: any[]) => {
    return agents.filter((agent) => {
      const reportType = agent.report_type || ''
      
      // Special agent types需要notify权限
      if (SPECIAL_AGENT_TYPES.includes(reportType)) {
        return canAccess.notifyFeature.value
      }
      
      // Strategic需要战略分析权限
      if (reportType === 'STRATEGIC') {
        return canAccess.strategicFeature.value
      }
      
      // TENDER和AWARDED需要商业机会权限
      if (['TENDER', 'AWARDED'].includes(reportType)) {
        return canAccess.businessOpportunityFeature.value
      }
      
      // 其他类型需要标准功能权限
      return canAccess.normalFeature.value
    })
  }

  switch (currentType.value) {
  case 'notify':
    return canAccess.notifyFeature.value ? filterByLegalPermission(
      filtered.filter((agent) =>
        SPECIAL_AGENT_TYPES.includes(agent.report_type || '')
      )
    ) : []
  case 'normal':
    return canAccess.normalFeature.value ? filterByLegalPermission(
      filtered.filter(
        (agent) => {
          const reportType = agent.report_type || ''
          return !SPECIAL_AGENT_TYPES.includes(reportType) &&
                 !BINDING_WORK_AGENT.includes(reportType)
        }
      )
    ) : []
  case 'binding':
    return filterByLegalPermission(
      filterByPermissionAndType(
        filtered.filter((agent) =>
          BINDING_WORK_AGENT.includes(agent.report_type || '')
        )
      )
    )
  case 'all':
    // 'all'选项需要按权限过滤代理
    return filterByLegalPermission(
      filterByPermissionAndType(filtered)
    )
  default:
    // 兜底情况，按权限过滤
    return filterByLegalPermission(
      filterByPermissionAndType(filtered)
    )
  }
})

const getAgentTag = (agent: Agent) => {
  const reportType = agent.report_type || ''
  
  if (SPECIAL_AGENT_TYPES.includes(reportType)) {
    return t(
      AGENT_FILTER_OPTIONS.find((option) => option.value === 'notify')
        ?.label as string
    )
  }
  if (BINDING_WORK_AGENT.includes(reportType)) {
    return t(
      AGENT_FILTER_OPTIONS.find((option) => option.value === 'binding')
        ?.label as string
    )
  }
  return t(
    AGENT_FILTER_OPTIONS.find((option) => option.value === 'normal')
      ?.label as string
  )
}

const filteredAgents = computed(() => {
  const query = searchQuery.value.toLowerCase()
  return agents.value?.filter(
    (agent) =>
      agent.name.toLowerCase().includes(query) ||
      agent.description.toLowerCase().includes(query)
  )
})

const isStrategicType = ref(false)

const handleAgentClick = async (agent: Agent) => {
  // 首先检查legal权限
  const agentId = agent.id || agent.report_type || ''
  if (!canAccess.legalFeature.value && (agentId === 'LegalAgent' || agentId === 'CompositeAgent')) {
    console.warn(`用户没有legal权限访问代理: ${agentId}`)
    ElMessage.warning(t('permission.no_legal_access'))
    return
  }

  // 然后检查其他权限 - 使用统一的权限检查逻辑
  const reportType = agent.report_type || ''
  let permission = 'feature.standard_feed'

  if (SPECIAL_AGENT_TYPES.includes(reportType)) {
    permission = 'feature.notify_insights'
  } else if (reportType === 'STRATEGIC') {
    permission = 'feature.strategic'
  } else if (['TENDER', 'AWARDED'].includes(reportType)) {
    permission = 'feature.business_opportunity'
  }

  if (!hasPermission(permission)) {
    console.warn(`用户没有权限访问代理类型: ${reportType}, 需要权限: ${permission}`)
    return
  }

  // 重置状态
  isStrategicType.value = false

  // 判断是否为战略分析类型
  if (agent.id === 'STRATEGIC') {
    isStrategicType.value = true
    // 加载业务分析国家数据
    if (
      !businessAnalysisState.value ||
      businessAnalysisState.value.length === 0
    ) {
      await getBusinessAnalysis()
    }
    // 加载新闻类型数据
    if (!newsTypeState.value || newsTypeState.value.length === 0) {
      await getNewsType()
    }
  }

  activeAgent.value = agent
  dialogVisible.value = true

  // Check if it's a binding work agent and fetch data if needed
  if (BINDING_WORK_AGENT.includes(agent.id)) {
    if (!industryState.value || Object.keys(industryState.value).length === 0) {
      await getIndustry()
    }
    if (!nationsState.value || nationsState.value.length === 0) {
      await getNations()
    }
  }
}

// 处理Excel文件上传
const handleExcelUpload = async (formData: { excelFile: File | null }) => {
  if (!formData.excelFile) {
    ElMessage.error(t('required.excel'))
    return false
  }

  // 验证文件扩展名
  const fileExt = formData.excelFile.name.split('.').pop()?.toLowerCase()
  if (!fileExt || !['xls', 'xlsx'].includes(fileExt)) {
    ElMessage.error(t('upload.excel.type.error'))
    return false
  }

  const formDataObj = new FormData()
  formDataObj.append('file', formData.excelFile)
  formDataObj.append('id', activeAgent.value?.id || '')
  formDataObj.append('agent_name', activeAgent.value?.name || '')

  try {
    await uploadExcel(0, formDataObj)
    return true
  } catch (error: any) {
    console.error('Upload Error:', error)
    ElMessage.error(error?.message || t('upload.excel.error'))
    return false
  }
}

// 处理API请求
const handleApiRequest = async (
  agentId: string,
  userData: Record<string, any>
) => {
  const requestData = {
    report_type: agentId,
    agent_name: activeAgent.value?.agent_name,
    ...userData,
  }

  try {
    await delayReport(0, requestData)
    return true
  } catch (error: any) {
    console.error('API Error:', error)
    ElMessage.error(error?.message || t('common.error'))
    return false
  }
}

// 主确认处理函数
const handleConfirm = async (formData: {
  userInfo: Record<string, string>;
  excelFile: File | null;
  selectedIndustries?: string[];
  selectedNations?: string[];
  keywords?: string[];
  newsTypes?: string[];
}) => {
  const agentId = activeAgent.value?.id || ''
  const reportType = activeAgent.value?.report_type || agentId

  // 首先检查legal权限
  if (!canAccess.legalFeature.value && (agentId === 'LegalAgent' || agentId === 'CompositeAgent')) {
    console.warn(`用户没有legal权限执行代理操作: ${agentId}`)
    ElMessage.warning(t('permission.no_legal_access'))
    return
  }

  // 根据代理类型检查其他权限 - 与handleAgentClick保持一致
  let permission = 'feature.standard_feed'
  if (SPECIAL_AGENT_TYPES.includes(reportType)) {
    permission = 'feature.notify_insights'
  } else if (reportType === 'STRATEGIC') {
    permission = 'feature.strategic'
  } else if (['TENDER', 'AWARDED'].includes(reportType)) {
    permission = 'feature.business_opportunity'
  }

  // 检查权限
  if (!hasPermission(permission)) {
    console.warn(`用户没有权限执行代理操作: ${reportType}, 需要权限: ${permission}`)
    return
  }
  
  isDialogConfirmLoading.value = true
  let success = false

  try {
    if (isSpecialAgent.value) {
      // 使用重试逻辑调用 handleExcelUpload
      success = await retryRequest(
        () => handleExcelUpload({ excelFile: formData.excelFile }),
        3,
        1000
      )
    } else if (agentId === 'STRATEGIC') {
      // 使用重试逻辑调用 delayBusinessAnalysis
      const strategicParams = {
        nation: formData.selectedNations,
        industry: formData.selectedIndustries,
        news_type: formData.newsTypes,
      }
      await retryRequest(
        () => delayBusinessAnalysis(0, strategicParams),
        3,
        1000
      )
      success = true
    } else {
      // 使用重试逻辑调用 handleApiRequest
      const userDataForApi: Record<string, any> = { ...formData.userInfo }
      if (BINDING_WORK_AGENT.includes(agentId) && agentId !== 'STRATEGIC') {
        if (formData.selectedIndustries) {
          userDataForApi.industry = formData.selectedIndustries
        }
        if (formData.selectedNations) {
          userDataForApi.nation = formData.selectedNations
        }
        if (formData.keywords) {
          userDataForApi.keywords = formData.keywords
        }
      }
      success = await retryRequest(
        () => handleApiRequest(agentId, userDataForApi),
        3,
        1000
      )
    }
  } catch (error: any) {
    console.error('General error in handleConfirm after retries:', error)
    ElMessage.error(error?.message || t('common.error'))
    success = false
  } finally {
    isDialogConfirmLoading.value = false
  }

  if (success) {
    ElMessage.success(t('common.success'))
    dialogVisible.value = false
    setTimeout(() => {
      emitter.emit('async:update')
    }, 1000)

    if (isSpecialAgent.value) {
      await getAgents()
    }
  }
}

const isSpecialAgent = computed(() => {
  return SPECIAL_AGENT_TYPES.includes(activeAgent.value?.id || '')
})

// Add type change handler
const handleTypeChange = (type: string) => {
  currentType.value = type
}

// --- 新增 Computed Property ---
const showBindingFields = computed(() => {
  return BINDING_WORK_AGENT.includes(activeAgent.value?.id || '')
})
// --------------------------

// --- 新增状态 ---
const industries = computed(() => {
  const data = industryState.value
  if (!data) {
    return []
  }
  return Object.entries(data).map(([groupLabel, optionsArray]) => ({
    label: groupLabel,
    options: optionsArray.map((optionLabel) => ({
      label: optionLabel,
      value: optionLabel, // Use label as value, or adjust if API provides specific IDs
    })),
  }))
})

const nations = computed(() => {
  const data = nationsState.value
  if (!data) {
    return []
  }
  return data.map((nationName) => ({
    label: nationName,
    value: nationName,
  }))
})

// 新增：创建业务分析国家数据的computed属性
const businessNations = computed(() => {
  const data = businessAnalysisState.value
  if (!data) {
    return []
  }
  return data.map((nationName) => ({
    label: nationName,
    value: nationName,
  }))
})
// ---------------

// New computed property to identify standard biding agents (excluding STRATEGIC for these specific params)
const activeAgentIsStandardBidingType = computed(() => {
  if (!activeAgent.value) {
    return false
  }
  // These are the types that should use the global biddingTenderFilterParams for pre-fill
  const standardBidingTypes = ['TENDER']
  return standardBidingTypes.includes(activeAgent.value.id)
})
</script>

<style lang="scss" module>
.container {
  padding: 32px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  perspective: 1000px; // Enable 3D space
}

.title {
  shrink: 0;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
}

.grid {
  margin-top: 24px;
  padding: 16px;
  flex: 1;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr); // 每行固定4列
  gap: 24px;
  overflow-y: auto;
  overflow-x: hidden; // 防止横向滚动
  will-change: transform, opacity; // Optimize performance
  min-height: 400px;

  @media (max-width: 1400px) {
    grid-template-columns: repeat(3, 1fr); // 中等屏幕3列
  }

  @media (max-width: 1100px) {
    grid-template-columns: repeat(2, 1fr); // 小屏幕2列
  }

  @media (max-width: 700px) {
    grid-template-columns: repeat(1, 1fr); // 移动设备1列
  }
}

.transition-group {
  width: 100%;
  transform-style: preserve-3d; // Enable 3D transforms for children
}

.card {
  max-height: 500px;
  transition: transform 0.2s, box-shadow 0.3s;
  backface-visibility: hidden; // Prevent flickering
  transform: translate3d(0, 0, 0); // Force GPU acceleration
  width: 100%; // 确保卡片宽度不溢出
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  text-align: center;
  height: 100%;
  width: 100%;
  grid-column: 1 / -1;

  .empty-icon {
    font-size: 64px;
    color: rgba(68, 102, 188, 0.2);
    margin-bottom: 20px;
  }

  .empty-text {
    font-size: 16px;
    color: var(--font-color-3);
    max-width: 400px;
    line-height: 1.6;
  }
}
</style>

<style lang="scss">
// Global transition styles
.agent-list-enter-active {
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.agent-list-leave-active {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute; // Prevent layout shifts
}

.agent-list-enter-from {
  opacity: 0;
  transform: translate3d(0, 30px, 0);
}

.agent-list-leave-to {
  opacity: 0;
  transform: translate3d(0, -20px, 0);
}

.agent-list-move {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
