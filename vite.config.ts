import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import VueDevTools from 'vite-plugin-vue-devtools'
import viteCompression from 'vite-plugin-compression'
import VueRouter from 'unplugin-vue-router/vite'
import Layouts from 'vite-plugin-vue-layouts'
import PreprocessorDirectives from 'unplugin-preprocessor-directives/vite'
import { vitePluginCDN2 } from './src/vue-plugins/vite-plugin-cdn2'
import { cdnFallbackPlugin } from './src/vue-plugins/cdnFallback'
import { visualizer } from 'rollup-plugin-visualizer'
import { chunkSplitPlugin } from 'vite-plugin-chunk-split'
import EnvTypes from './vite-plugins/env.types'
import { regionEnvVars, env } from './build.region.env'
import { viteMockServe } from 'vite-plugin-mock'
import { spaLoading } from 'vite-plugin-spa-loading'

console.log(env.VITE_API_BASE_URL, env.VITE_SERVER_API_URL, env.VITE_WS_SERVER_URL, process.env.PRIVATE)
const excludeRouter = ['**/__components__/**', '**/composables/**']
if (process.env.NODE_ENV === 'production') {
  excludeRouter.push('**/demo/**')
}

// 生产环境的CDN配置
const prodCDNConfig = {
  modules: [
    {
      name: 'vue',
      var: 'Vue',
      path: 'https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.prod.js',
    },
    {
      name: 'vue-router',
      var: 'VueRouter',
      path: 'https://cdn.jsdelivr.net/npm/vue-router@4.2.4/dist/vue-router.global.prod.js',
    },
    {
      name: 'vue-i18n',
      var: 'VueI18n',
      path: 'https://cdn.jsdelivr.net/npm/vue-i18n@9.4.1/dist/vue-i18n.global.prod.js',
    },
    {
      name: 'vue-demi',
      var: 'VueDemi',
      path: 'https://cdn.jsdelivr.net/npm/vue-demi@0.14.6/lib/index.iife.min.js',
    },
    {
      name: 'pinia',
      var: 'Pinia',
      path: 'https://cdn.jsdelivr.net/npm/pinia@2.1.6/dist/pinia.iife.prod.js',
    },
    {
      name: 'axios',
      var: 'axios',
      path: 'https://cdn.jsdelivr.net/npm/axios@1.5.0/dist/axios.min.js',
    },
    {
      name: 'vue-request',
      var: 'VueRequest',
      path: 'https://cdn.jsdelivr.net/npm/vue-request@2.0.3/dist/vue-request.min.js',
    },
    {
      name: 'element-plus',
      var: 'ElementPlus',
      path: 'https://cdn.jsdelivr.net/npm/element-plus@2.9.0/dist/index.full.min.js',
      css: 'https://cdn.jsdelivr.net/npm/element-plus@2.9.0/dist/index.min.css',
    },
    {
      name: 'echarts',
      var: 'echarts',
      path: 'https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js',
    },
    {
      name: '@element-plus/icons-vue',
      var: 'ElementPlusIconsVue',
      path: 'https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.3.1/dist/index.min.js',
    },
    {
      name: 'pdfmake',
      var: 'pdfMake',
      path: 'https://cdn.jsdelivr.net/npm/pdfmake@0.2.14/build/pdfmake.min.js',
    }
  ],
}

// 需要预加载的包
const preloadPackages = [
  {
    name: 'vue',
    version: '3.3.4',
    path: 'dist/vue.global.prod.js'
  },
  {
    name: 'element-plus',
    version: '2.9.0',
    path: 'dist/index.full.min.js'
  },
  {
    name: 'pdfmake',
    version: '0.2.14',
    path: 'build/pdfmake.min.js'
  }
]

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // 添加CDN降级插件
    cdnFallbackPlugin({
      enableDebugLog: true,
      preloadPackages
    }),
    spaLoading('text', {
      tipText:'Please wait a moment...',
      devEnable: true
    }),
    VueRouter({
      exclude: excludeRouter,
    }),
    vue(),
    vueJsx(),
    VueDevTools(),
    Layouts(),
    PreprocessorDirectives(),
    chunkSplitPlugin({
      customSplitting: {
        lodash: [/lodash-es/],
        'components-news': [/src\/pages\/__components__\/NewsComponents/],
        'components-intelligence-dashboard': [/src\/pages\/__components__\/IntelligenceComponents\/DashboardComponents/],
        'components-intelligence-panel': [/src\/pages\/__components__\/IntelligenceComponents\/RightPanelComponents/],
        'components-intelligence-collapsible': [/src\/pages\/__components__\/IntelligenceComponents\/CollapsiblePanel/],
        'components-intelligence-base': [/src\/pages\/__components__\/IntelligenceComponents\/BaseComponents/],
        'components-robot': [/src\/pages\/__components__\/RobotComponents/],
        'components-login': [/src\/pages\/__components__\/LoginComponents/],
        'markdown-output': [/src\/pages\/__components__\/IntelligenceComponents\/MarkdownOutputDialog.vue/],
        directives: [/src\/directives/],
      }
    }),
    viteCompression(),
    EnvTypes(),
    visualizer({
      gzipSize: true,
      brotliSize: true,
      emitFile: false,
      filename: 'debugger/stats.html',
    }),
    process.env.USE_MOCK ? viteMockServe({
      ignore: /^\_/,
      mockPath: 'mock',
    }) : null,
    // 使用封装的CDN插件
    vitePluginCDN2({
      modules: prodCDNConfig.modules,
    }),
  ],
  define: {
    ...regionEnvVars,
    'import.meta.env.VITE_BUILD_VERSION': JSON.stringify(Date.now()),
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0',
    port: 10001,

    proxy: {
      [env.VITE_API_BASE_URL as string]: {
        target: env.VITE_SERVER_API_URL,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path.replace(new RegExp(`^${env.VITE_API_BASE_URL}`), '')
        }
      },
      // '/socket.io': {
      //   target: env.VITE_WS_SERVER_URL,
      //   changeOrigin: true,
      //   ws: true,
      //   secure: false,
      //   // rewrite: (path) => {
      //   //   return path.replace(/^\/ws/, '')
      //   // }
      // }
    }
  },
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        // 保留CDN降级相关的console日志用于调试
        drop_console: false,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]',
        manualChunks: {
          mermaid: ['mermaid']
        }
      }
    },
    manifest: true,
    emptyOutDir: true,
  },
  // 添加对.node文件的处理
  assetsInclude: ['**/*.node'],
  optimizeDeps: {
    include: ['mermaid'],
    exclude: ['rs-module-lexer', 'pdfmake']
  }
})
