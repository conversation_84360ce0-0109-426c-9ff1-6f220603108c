import { ref, computed, toValue, type MaybeRefOrGetter } from 'vue'
import { useI18n } from 'vue-i18n'
// Remove unused Report types
// import type { Report, RunningAgent } from '@/pages/workspace/types'
// import type { CompletedAgent } from '@/pages/dashboard/type'
// import type { ReportList as StandardReport } from '@/pages/format-report/type'
import { SPECIAL_AGENT_TYPES, AVATAR_MAP } from '@/composables/useReportProgress'
// Import the WorkspaceAgent type
import type { WorkspaceAgent } from './useWorkspaceAgents' // Assuming useWorkspaceAgents exports this type

// Define the structure expected by AgentListSidebar
interface AgentDisplayInfo {
  id: string;
  name: string;
  role: string;
  status: string;
  avatar: string;
  currentTask: string | null;
  isMonitor: boolean;
  report_type: string; // Add report_type
}

// Type Guards are no longer needed as input is unified
// function isMonitorAgent(...) { ... }
// function isRunningAgent(...) { ... }
// function isStandardReport(...) { ... }
// function isPending(...) { ... }

// Update function signature to accept WorkspaceAgent array
export function useAgents(workspaceAgents: MaybeRefOrGetter<WorkspaceAgent[]>) {
  const { t } = useI18n()

  // --- Local State for Filtering ---
  const agentSearch = ref('')

  // --- Helper Functions (Internal) ---
  // Removed getAgentNameForFilter and getFrequencyLabel

  // --- Computed Properties ---

  // Generate the display list by directly mapping the unified input
  const agents = computed<AgentDisplayInfo[]>(() => {
    const sourceAgents = toValue(workspaceAgents) // Get the array value
    if (!sourceAgents) {return []}

    return sourceAgents.map(agent => ({
      name: agent.name,
      role: agent.role,
      id: agent.id,
      // Use the status provided by WorkspaceAgent directly
      status: agent.status?.toLowerCase() || 'idle',
      avatar: agent.avatar || '', // Ensure fallback
      currentTask: agent.currentTask,
      isMonitor: agent.isMonitor,
      report_type: agent.report_type,
    }))
  })

  // Filtered agents based ONLY on search term now
  const filteredAgents = computed(() => {
    let result = agents.value

    if (agentSearch.value) {
      const search = agentSearch.value.toLowerCase()
      result = result.filter(agent => {
        return (
          agent.name.toLowerCase().includes(search) ||
          (agent.role && agent.role.toLowerCase().includes(search))
        )
      })
    }

    return result
  })

  // --- Methods ---

  // --- Exposed API ---
  return {
    filteredAgents, // List filtered only by search term
    agentSearch,
  }
} 