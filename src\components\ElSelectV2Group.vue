<script setup lang="ts">
import { type ISelectV2Props, ElSelectV2 } from 'element-plus'
import { computed, nextTick, ref, watch, type PropType } from 'vue'
import { groupBy, isEqual, isNil, keyBy, omit } from 'lodash-es'
import { useCamelCaseAttrs } from '@/hooks/useCamelCaseAttrs'
import { useSelectFilter } from '@/hooks/useSelectFilter'

type SelectModelValue = ISelectV2Props['modelValue']
type SelectOption = ISelectV2Props['options'][number]

defineOptions({
  inheritAttrs: false,
})
const props = defineProps({
  modelValue: {
    type: [String, Array, Object, Number, Boolean] as PropType<SelectModelValue>,
    default: () => [],
  },
  hiddenCheckedIcon: {
    type: Boolean,
    default: false,
  },
  disabledGroupHover: {
    type: Boolean,
    default: false,
  },
  groupDivider: {
    type: Boolean,
    default: false,
  },
  groupClass: {
    type: String,
    default: '',
  },
  isHideClearable: {
    type: Boolean,
    default: false,
  },
  isHideSelectAll: {
    type: Boolean,
    default: false,
  },
  isGroupExclusive: {
    type: Boolean,
    default: false,
  },
  minCount: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits<{
  'update:modelValue': [value: SelectModelValue]
  'click-group': [item: SelectOption]
  'change': [value: SelectModelValue]
}>()

const { attrs, camelCaseAttrs } = useCamelCaseAttrs()
const select = ref<InstanceType<typeof ElSelectV2>>()

const isMultiple = computed(() => attrs.multiple === '' || !!attrs.multiple ? true : false)

const optionGroupValueCache: Record<string | number, string | number> = {}
const updateModelValue = (val: SelectModelValue) => {
  emit('update:modelValue', val)
}
let previousOptions: any[] = []
const updateCachedOptions = () => {
  if (select.value) {
    const { getValueKey, states } = select.value
    const optionsMap = keyBy(previousOptions, getValueKey)
    if (val.value.length > 0) {
      const cachedOptions = states.cachedOptions
      states.cachedOptions = val.value.map((v) => 
        optionsMap[v] || cachedOptions.find((item) => getValueKey(item) === v)
      ).filter(Boolean) as { [x: string]: any; created?: boolean | undefined; }[]
    }
  }
}
const value = computed({
  get() {
    return props.modelValue
  },
  set(value: SelectModelValue) {
    if (isEqual(value, props.modelValue)) {
      return
    }
    if (value && props.minCount > 0 && (Array.isArray(value) ? value.length : 1) < props.minCount) {
      // 当用户选择的选项小于minCount时，更新缓存选项，防止用户再次选择时，选项消失
      updateCachedOptions()
      return
    }
    if (props.isGroupExclusive) {
      updateModelValue(filterGroupExclusive(value))
      return
    }
    updateModelValue(value)
  }
})

const filterGroupExclusive = (setValue: SelectModelValue) => {
  // 找出用户最新添加的选项值(直接点击option选择会直接修改value)
  const newValue = isMultiple.value ? Array.isArray(setValue) ? setValue.filter(v => {
    if (Array.isArray(value.value)) {
      return !value.value.includes(v)
    }
    return value.value !== v
  }) : [setValue] : setValue
  if (!newValue || newValue.length === 0) {
    return setValue
  }
  // 找出用户最新添加的选项分组值
  const groupValue = groupBy(Array.isArray(newValue) ? newValue : [newValue], (item: any) => optionGroupValueCache[item])
  const newGroupValue = Object.keys(groupValue)[0]
  // 取第一个分组的值，防止直接设置了多个分组的值，如全选
  const newValues = groupValue[newGroupValue]

  const oldFirstValue = Array.isArray(value.value) ? value.value[0] : value.value
  const oldGroupValue = optionGroupValueCache[oldFirstValue]
  if (`${oldGroupValue}` !== `${newGroupValue}`) {
    // 不是同一个分组设置当前value
    return isMultiple.value ? newValues : newValues[0]
  }

  if (isMultiple.value) {
    return Array.isArray(value.value) ? [...value.value, ...newValues] : [value.value, ...newValues]
  }
  return newValues[0]
}
const changeValue = (v: SelectModelValue) => {
  value.value = v
  // 多选且可搜索时，更新缓存的options（搜索的时候修改value会导致cachedOptions的选项找不到）
  if (isMultiple.value && select.value && select.value.filterable) {
    nextTick(() => {
      updateCachedOptions()
    })
  }
}
const updateValue = (val: SelectModelValue) => {
  val = Array.isArray(val) ? [ ...new Set(val) ] : val

  if (isMultiple.value && Array.isArray(val) && camelCaseAttrs.multipleLimit) {
    val = val.slice(0, camelCaseAttrs.multipleLimit as number)
  }
  if (!Array.isArray(val) && isMultiple.value) {
    changeValue([val])
    return
  }
  changeValue(val)
}

const toggleGroup = (group: SelectOption, checked?: boolean) => {
  checked = checked ?? !group.checked
  if (!isMultiple.value) {
    if (group.options[0]) {
      updateValue(checked ? group.options[0].value : null)
    }
    return
  }
  const subValues = (group.options as SelectOption[] || []).map(subOption => subOption.value)
  if (checked) {
    updateValue([...value.value, ...subValues])
    return
  }
  updateValue(value.value.filter((val: any) =>!subValues.includes(val)))
}

const realAllSubOptions = computed(() => (attrs.options as SelectOption[] || []).map((option) => option.options || []).flat())
const val = computed(() => isNil(value.value) ? [] : Array.isArray(value.value) ? value.value : [value.value])
const options = ref<SelectOption[]>([])
const { enabledFilter, filterMethod, filterOptions } = useSelectFilter(realAllSubOptions, {
  filterMethod: (query: string) => {
    initialOptions(query)
  }
})

const initialOptions = (filter?: string) => {
  const _options = (attrs.options || []) as SelectOption[]
  options.value = _options?.map((option, index) => {
    let subOptions = filterOptions(option.options || [], filter)
    if (subOptions.length === 0) {
      return []
    }
    const groupValue: string = option.value || `__group__${index}__`
    // 这里必须重新创建新的option，否则会导致option为引用地址，导致数据更新不生效
    subOptions = subOptions.map(subOption => {
      optionGroupValueCache[subOption.value] = groupValue
      return {
        ...subOption,
        checked: computed({
          get: () => val.value.includes(subOption.value),
          set: (v) => {
            if (option.disabled) {
              return
            }
            updateValue(v ? [...val.value, subOption.value] : val.value.filter(vv => vv !== subOption.value))
          }
        })
      }
    })
    const checkedSubOptions = computed(() => subOptions.filter(subOption => val.value.includes(subOption.value)))
    const checked = computed({
      get() {
        return checkedSubOptions.value.length === subOptions.length
      },
      set(val) {
        if (option.disabled) {
          return
        }
        toggleGroup(option, val)
      }
    })

    return [{
      ...omit(option, ['options']),
      value: groupValue,
      isGroup: !!subOptions,
      checked,
      disabled: computed(() => {
        if (option.disabled) {
          return true
        }
        if (camelCaseAttrs.multipleLimit) {
          // 父级为全勾选时，允许取消勾选
          if (checked.value) {
            return false
          }
          return val.value.length >= camelCaseAttrs.multipleLimit
        }
        return false
      }),
      indeterminate: computed(() => checkedSubOptions.value.length > 0 && checkedSubOptions.value.length < subOptions.length),
    }, ...subOptions]
  }).flat() || []
}
const allOptionValues = computed(() => options.value.filter(option => !option.isGroup).map(option => option.value))
const isAllChecked = computed(() => val.value.length === allOptionValues.value.length)

watch(() => attrs.options, () => {
  initialOptions()
}, { immediate: true })

const onClickGroup = (item: SelectOption) => {
  item.checked = !item.checked
  emit('click-group', item)
}
const clear = () => {
  updateValue([])
}
const selectAll = () => {
  updateValue(allOptionValues.value)
}
const handleChange = (val: SelectModelValue) => {
  if (props.isGroupExclusive) {
    emit('change', filterGroupExclusive(val))
    return
  }
  emit('change', val)
}
const onVisibleChange = (visible: boolean) => {
  if (visible) {
    previousOptions = [...options.value]
    return
  }
  previousOptions = []
}
</script>

<template>
  <el-select-v2
    ref="select"
    v-model="value"
    v-bind="omit(attrs, ['options'])"
    :class="[
      props.minCount > 0 ? $style.hideTagClose : '',
    ]"
    :filterable="enabledFilter"
    :filter-method="filterMethod"
    :options="options"
    :popper-class="[
      camelCaseAttrs.popperClass || '',
      $style.popperClass,
      props.hiddenCheckedIcon ? $style.hideCheckedIcon : '',
      props.disabledGroupHover ? $style.disabledGroupHover : '',
      props.groupDivider ? $style.groupDivider : '',
      isMultiple ? 'is-multiple' : '',
    ].join(' ')"
    @change="handleChange"
    @visible-change="onVisibleChange">
    <template #prefix v-if="$slots.title">
      <slot name="title"></slot>
    </template>
    <template #header>
      <div v-if="isMultiple" :class="$style.header">
        <div class="flex items-center gap-2 text-[color:var(--font-color-1)]">
          {{ $t('component.selectedCount', [val.length]) }}
          <template v-if="!isHideClearable">
            <el-link v-show="val.length > 0 && !isHideClearable"
                     type="primary"
                     :underline="false"
                     class="text-xs"
                     @click.stop="clear">
              {{ $t('button.clear') }}
            </el-link>
          </template>
        </div>
        <template v-if="!isHideSelectAll">
          <el-link type="primary"
                   :underline="false"
                   class="text-xs"
                   :disabled="isAllChecked"
                   @click.stop="selectAll">
            {{ $t('button.selectAll') }}
          </el-link>
        </template>
      </div>
      <slot name="insertTop"></slot>
    </template>
    <template #default="{ item }">
      <div v-if="item.isGroup"
           :class="[$style.group, groupClass]"
           is-group
           @click.stop>
        <slot name="group"
              :item="item"
              :toggleGroup="toggleGroup"
              :clickGroup="() => onClickGroup(item)">
          <span>{{ item.label }}</span>
        </slot>
      </div>
      <div v-else>
        <slot name="item" :item="item">
          <span>{{ item.label }}</span>
        </slot>
      </div>
    </template>
  </el-select-v2>
</template>

<style lang="scss" module>
.popperClass {
  :global(.el-select-dropdown__item.is-hovering) {
    .group {
      background: none;
    }
  }
  :global(.el-select-dropdown__item) {
    .group {
      margin: 0;
      padding: 0 18px;
    }
  }
  :global(.el-checkbox) {
    overflow: hidden;
  }
  :global(.el-checkbox__label) {
    overflow: hidden;
  }
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.group {
  :global(.el-checkbox__label) {
    color: var(--font-color-1)
  }
  :global(.el-checkbox__input.is-checked+.el-checkbox__label) {
    color: var(--font-color-1)
  }
  cursor: default;
}
.groupDivider {
  // 如果开启分组分割线，则需要缩小分组高度（虚拟滚动目前每行34px - 分割线1px - 分割线间距3px）
  .group {
    height: 30px;
  }
  :global(.el-select-dropdown__item:has(> div[is-group]):not(:first-of-type)::before) {
    content: "";
    display: block;
    height: 1px;
    margin-top: 3px;
    background-color: var(--color-border-2);
    margin-left: -20px; // 左侧padding
    margin-right: -32px; // 右侧padding
  }
}

.hideCheckedIcon {
  :global(.el-select-dropdown.is-multiple .el-select-dropdown__item.is-selected:after) {
    display: none;
  }
}
.disabledGroupHover {
  :global(.el-select-dropdown__item.is-hovering:has(> div[is-group])) {
    background-color: initial;
  }
}
.hideTagClose {
  :global(
      .el-select__selection
        > .el-select__selected-item
        .el-tag
        .el-tag__close
    ) {
    display: none;
  }
  :global(
      .el-select__selection
        > .el-select__selected-item
        .el-tag.is-closable
    ) {
      padding-right: 9px;
  }
}
</style>
