import { isOverflowOfClone } from '@/utils/dom'
import { unrefElement, useEventListener, useMutationObserver, useResizeObserver, type MaybeElementRef, type MaybeRefOrGetter } from '@vueuse/core'
import { ref, shallowReadonly } from 'vue'

type OverflowOptions = {
  onChangeWithEnter?: (isOverflow: boolean) => void
  onEnter?: (e: MouseEvent) => void
}
export const useOverflow = (target: MaybeRefOrGetter<HTMLElement | null | undefined>, opts?: OverflowOptions) => {
  let rafId: number | undefined
  let elWidth: number | undefined
  // 是否需要重新计算，在鼠标移入时计算
  let calcFlag = false
  const isOverflowing = ref(false)

  const checkOverflow = (trigger?: boolean) => {
    const el = unrefElement(target as unknown as MaybeElementRef)
    if (!el || !(el instanceof HTMLElement)) {
      return
    }
    rafId && cancelAnimationFrame(rafId)
    rafId = requestAnimationFrame(() => {
      calcFlag = true
      isOverflowing.value = isOverflowOfClone(el)
      trigger && opts?.onChangeWithEnter?.(isOverflowing.value)
    })
  }

  const stopEnter = useEventListener(target, 'mouseenter', (e: MouseEvent) => {
    if (!calcFlag) {
      checkOverflow(true)
    }
    opts?.onEnter?.(e)
  })
  const { stop: stopMutationObserver } = useMutationObserver(target, () => calcFlag = false, { subtree: true, childList: true, characterData: true })
  const { stop: stopResizeObserver } = useResizeObserver(target, (entries) => {
    const entry = entries[0]
    const { width, height } = entry.contentRect
    // 如果宽度没有变化，说明元素尺寸没有变化，不需要重新计算
    if (width === elWidth || (width === 0 && height === 0)) {
      return
    }
    elWidth = width
    calcFlag = false
  })
  const stop = () => {
    stopMutationObserver()
    stopResizeObserver()
    stopEnter()
    rafId && cancelAnimationFrame(rafId)
  }

  return { isOverflowing: shallowReadonly(isOverflowing), stop }
}
