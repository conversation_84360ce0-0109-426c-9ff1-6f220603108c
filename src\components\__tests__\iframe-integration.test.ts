import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import IframeContainer from '../IframeContainer.vue'
import IframeWrapper from '../IframeWrapper.vue'

// Mock window.postMessage
const mockPostMessage = vi.fn()
Object.defineProperty(window, 'postMessage', {
  value: mockPostMessage,
  writable: true
})

// Mock iframe contentWindow
const mockContentWindow = {
  postMessage: vi.fn()
}

// Mock HTMLIFrameElement
Object.defineProperty(HTMLIFrameElement.prototype, 'contentWindow', {
  get() {
    return mockContentWindow
  }
})

describe('Iframe Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset DOM
    document.body.innerHTML = ''
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('IframeContainer Component', () => {
    it('should render iframe with correct src and token parameter', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-jwt-token',
          title: 'Test Dashboard'
        }
      })

      await nextTick()
      
      const iframe = wrapper.find('iframe')
      expect(iframe.exists()).toBe(true)
      expect(iframe.attributes('src')).toContain('token=test-jwt-token')
      expect(iframe.attributes('title')).toBe('Test Dashboard')
    })

    it('should handle PAGE_LOADED message correctly', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'PAGE_LOADED',
          data: {
            timestamp: Date.now(),
            url: 'https://example.com/embed/dashboard',
            fromOg: false,
            authMode: 'nextjs-standard'
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      // Simulate message event
      window.dispatchEvent(messageEvent)
      await nextTick()

      expect(wrapper.emitted('message')).toBeTruthy()
      const emittedMessage = wrapper.emitted('message')?.[0]?.[0]
      expect(emittedMessage.type).toBe('PAGE_LOADED')
    })

    it('should handle IFRAME_READY message and emit loaded event', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'IFRAME_READY',
          data: {
            timestamp: '2025-01-27T10:30:00.000Z',
            url: 'https://example.com/embed/dashboard',
            fromOg: false,
            authMode: 'nextjs-standard',
            message: 'Next.js标准iframe就绪，用户认证完成'
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      expect(wrapper.emitted('loaded')).toBeTruthy()
      expect(wrapper.emitted('message')).toBeTruthy()
    })

    it('should handle API_CALL message', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'API_CALL',
          data: {
            method: 'GET',
            url: '/api/user/get_current',
            timestamp: '2025-01-27T10:30:00.000Z',
            token: '已设置'
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      const emittedMessage = wrapper.emitted('message')?.[0]?.[0]
      expect(emittedMessage.type).toBe('API_CALL')
      expect(emittedMessage.data.method).toBe('GET')
      expect(emittedMessage.data.url).toBe('/api/user/get_current')
    })

    it('should handle API_SUCCESS message', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'API_SUCCESS',
          data: {
            url: '/api/user/get_current',
            message: '用户信息获取成功',
            user: 'testuser'
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      const emittedMessage = wrapper.emitted('message')?.[0]?.[0]
      expect(emittedMessage.type).toBe('API_SUCCESS')
      expect(emittedMessage.data.user).toBe('testuser')
    })

    it('should handle API_ERROR message and emit error event', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'API_ERROR',
          data: {
            url: '/api/user/get_current',
            message: '获取用户信息失败',
            tokenStatus: '已设置'
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      expect(wrapper.emitted('error')).toBeTruthy()
      expect(wrapper.emitted('message')).toBeTruthy()
    })

    it('should handle AUTH_ERROR message', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'AUTH_ERROR',
          data: {
            message: '未提供有效的认证token',
            tokenStatus: '未设置',
            fromOg: false
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      expect(wrapper.emitted('error')).toBeTruthy()
      const emittedError = wrapper.emitted('error')?.[0]?.[0]
      expect(emittedError.type).toBe('AUTH_ERROR')
    })

    it('should handle RESIZE message and update height', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          autoHeight: true,
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'RESIZE',
          data: {
            height: 800
          }
        },
        origin: 'https://example.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      expect(wrapper.emitted('heightChanged')).toBeTruthy()
      const heightChanged = wrapper.emitted('heightChanged')?.[0]?.[0]
      expect(heightChanged).toBe(800)
    })

    it('should reject messages from unauthorized origins', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true,
          allowedOrigins: ['https://example.com']
        }
      })

      const messageEvent = new MessageEvent('message', {
        data: {
          type: 'PAGE_LOADED',
          data: {}
        },
        origin: 'https://malicious-site.com',
        source: mockContentWindow
      })

      window.dispatchEvent(messageEvent)
      await nextTick()

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('拒绝来自未授权域名的消息')
      )
      expect(wrapper.emitted('message')).toBeFalsy()
      
      consoleSpy.mockRestore()
    })

    it('should send parent-ready message after iframe loads', async () => {
      const wrapper = mount(IframeContainer, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          enableCommunication: true
        }
      })

      // Simulate iframe load event
      const iframe = wrapper.find('iframe')
      await iframe.trigger('load')
      await nextTick()

      expect(mockContentWindow.postMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'parent-ready',
          data: expect.objectContaining({
            autoHeight: true,
            debug: false
          })
        }),
        '*'
      )
    })
  })

  describe('IframeWrapper Component', () => {
    it('should render IframeContainer with correct props', async () => {
      const wrapper = mount(IframeWrapper, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          title: 'Test Dashboard',
          height: 600,
          enableCommunication: true
        }
      })

      await nextTick()
      
      const iframeContainer = wrapper.findComponent(IframeContainer)
      expect(iframeContainer.exists()).toBe(true)
      expect(iframeContainer.props('src')).toBe('https://example.com/embed/dashboard')
      expect(iframeContainer.props('token')).toBe('test-token')
      expect(iframeContainer.props('title')).toBe('Test Dashboard')
    })

    it('should show loading state initially', async () => {
      const wrapper = mount(IframeWrapper, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token'
        }
      })

      // Should show loading initially
      expect(wrapper.text()).toContain('正在加载')
    })

    it('should handle iframe error and show error state', async () => {
      const wrapper = mount(IframeWrapper, {
        props: {
          src: 'https://example.com/embed/dashboard',
          token: 'test-token',
          showRetryButton: true
        }
      })

      // Simulate error
      const iframeContainer = wrapper.findComponent(IframeContainer)
      await iframeContainer.vm.$emit('error', {
        type: 'NETWORK_ERROR',
        message: '网络连接失败',
        code: 'NETWORK_ERROR',
        timestamp: Date.now(),
        retryCount: 0
      })

      await nextTick()

      expect(wrapper.text()).toContain('iframe加载失败')
      expect(wrapper.find('button').text()).toContain('重试加载')
    })
  })
})
