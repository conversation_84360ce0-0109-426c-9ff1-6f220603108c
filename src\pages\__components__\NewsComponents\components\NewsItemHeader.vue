<template>
  <div :class="$style.title">
    <svg 
      v-for="svg in svgs"
      :key="svg"
      class="inline-block align-top w-[18px] h-[18px] ml-[6px]" 
      viewBox="0 0 32 32">
      <image :href="svg"
             width="32"
             height="32"
             @error="handleImageError"/>
    </svg>
    <span @click="($event) => toWebSite($event, props.news.url)" class="ml-[6px]">{{ props.news.title }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type PropType } from 'vue'
import JapanFlag from '@/assets/images/countryFlag/japan.png'
import VietnamFlag from '@/assets/images/countryFlag/vietnam.png'
import KoreaFlag from '@/assets/images/countryFlag/korea.png'
import emptyIco from '@/assets/images/websiteIco/worldwide.png'
import type { NewsDetail } from '@/types'
import { useTrack } from '@/hooks/useTrack'

const { trackEvent } = useTrack()
type Nation = keyof typeof FLAG_MAP

const props = defineProps({
  news: {
    type: Object as PropType<NewsDetail>,
    required: true,
  },
})

const FLAG_MAP = {
  'japan': JapanFlag,
  'vietnam': VietnamFlag,
  'korea': KoreaFlag,
} as const


const flag = computed(() => {
  const nationKey = (props.news.nation?.toLowerCase() ?? '') as Nation
  return FLAG_MAP[nationKey]
})

const toWebSite = (event: Event, url: string) => {
  trackEvent('click_for_source', {
    title: props.news.title,
    news_url: url
  })
  event.stopPropagation()
  window.open(url, '_blank')
}

const icon = computed(() => {
  if (!props.news.icon) {return emptyIco}

  // 检查是否为有效的 base64 格式
  // Check if it's a valid base64 format
  const isValidBase64 = /^data:image\/(png|jpg|jpeg|gif|ico);base64,/.test(props.news.icon)
  return isValidBase64 ? props.news.icon : emptyIco
})
const handleImageError = (event: Event) => {
  const imgElement = event.target as HTMLImageElement
  imgElement.setAttribute('href', emptyIco)
}
const svgs = computed(()=> [flag.value, icon.value])
</script>

<style lang="scss" module>
.title {
  display: flex;
  align-items: flex-start;
  >span {   
    width: 100%;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    cursor: pointer;
    line-height: 1.2;
    

  &:hover {
    color: #2563eb;
  }
}
}
</style>
