<template>
  <EchartsComponent :params="getChartOption" :auto-size="true" data-pdf-chart/>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import EchartsComponent from '@/components/EchartsComponent.vue'
import { isObject, merge } from 'lodash-es'
interface BarData {
  data: number[]
}

interface Props {
  data: {
    title: string
    xData: string[]
    yData: BarData[]
  }
  config?: Record<string, any>
}

const props = defineProps<Props>()

// 颜色配置
const COLORS = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B']

// 图表配置
const getChartOption = computed(() => {
  const baseOption = {
    title: {
      text: props.data?.title,
      left: 'left',
      top: 0,
      textStyle: {
        color: '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      axisPointer: {
        type: 'shadow-sm'
      }
    },
    legend: {
      show: true,
      top: '30px',
      left: 'center'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '70px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data?.xData,
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#374151',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#374151',
        fontSize: 12
      }
    },
    series: props.data?.yData.map((item, index) => ({
      type: 'bar',
      data: item.data,
      stack:'one',
      barMaxWidth: 30,
      itemStyle: {
        color: COLORS[index],
        borderRadius: [4, 4, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: COLORS[index] + 'CC' // 添加透明度
        }
      },
      animationDelay: (idx: number) => idx * 50
    })),
    animation: true,
    animationEasing: 'elasticOut',
    animationDelayUpdate: (idx: number) => idx * 5
  }
  console.log('baseOption', baseOption)
  const result = isObject(props.config) ? merge(baseOption, props.config) : baseOption
  console.log('result', result)
  return result
})
</script> 