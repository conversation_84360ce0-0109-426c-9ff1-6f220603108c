<template>
  <div :class="$style.uploader">
    <el-upload
      class="excel-uploader"
      drag
      action="#"
      :auto-upload="false"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :limit="1"
      :file-list="fileList"
      :before-upload="handleBeforeUpload"
      accept=".xlsx,.xls"
      :disabled="isLoading">
      <div :class="$style.uploadArea">
        <Icon 
          icon="material-symbols:cloud-upload" 
          :class="$style.uploadIcon"
          width="48"
          height="48"/>
        <div :class="$style.uploadText">
          <span>{{ $t("agent.upload_excel_file") }}</span>
          <div class="text-main-color flex items-center gap-1 text-lg cursor-pointer"><Icon icon="material-symbols:upload-file"/>{{ $t("agent.upload_excel_file_button") }}</div>
        </div>
        <div :class="$style.uploadTip">
          {{ $t("agent.upload_excel_file_tip") }}
        </div>
      </div>
      
      <template #file="{ file }">
        <div :class="$style.fileItem">
          <div :class="$style.fileInfo">
            <Icon icon="mdi:file-excel" width="24"/>
            <span :class="$style.fileName">{{ file.name }}</span>
          </div>
          <div :class="$style.fileStatus">
            <template v-if="isLoading">
              <Icon icon="mdi:loading" class="is-loading" width="20"/>
              <span>{{ $t("agent.processing") }}</span>
            </template>
            <template v-else>
              <el-icon 
                :class="$style.removeIcon"
                @click.stop="handleFileRemove()">
                <Icon icon="mdi:close" width="20"/>
              </el-icon>
            </template>
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import type { UploadFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

interface FileValidationResult {
  isValid: boolean
  file: File | null
  error?: string
}

interface Props {
  isLoading?: boolean
  modelValue?: FileValidationResult
  maxSize?: number // in MB
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  modelValue: () => ({
    isValid: true,
    file: null
  }),
  maxSize: 2
})

const emit = defineEmits<{
  'update:modelValue': [result: FileValidationResult]
}>()

const fileList = ref<UploadFile[]>([])
const { t } = useI18n()

const validateFile = (file: File): FileValidationResult => {
  const isExcel = /\.(xlsx|xls)$/i.test(file.name)
  if (!isExcel) {
    return {
      isValid: false,
      file: null,
      error: t('upload.excel.type.error')
    }
  }
  
  const isLt2M = file.size / 1024 / 1024 < props.maxSize
  if (!isLt2M) {
    return {
      isValid: false,
      file: null,
      error: t('upload.excel.size.error')
    }
  }
  
  return {
    isValid: true,
    file
  }
}

const handleBeforeUpload = (file: File) => {
  const result = validateFile(file)
  if (!result.isValid) {
    ElMessage.error(result.error)
    return false
  }
  return true
}

const handleFileChange = (uploadFile: UploadFile) => {
  if (uploadFile.raw) {
    const result = validateFile(uploadFile.raw)
    if (result.isValid) {
      fileList.value = [uploadFile]
      emit('update:modelValue', result)
    } else {
      ElMessage.error(result.error)
      fileList.value = []
      emit('update:modelValue', {
        isValid: false,
        file: null,
        error: result.error
      })
    }
  }
}

const handleFileRemove = () => {
  fileList.value = []
  emit('update:modelValue', {
    isValid: true,
    file: null
  })
}

// Watch external file changes
watch(() => props.modelValue, (newValue) => {
  if (!newValue?.file && fileList.value.length > 0) {
    fileList.value = []
  }
}, { deep: true })
</script>

<style lang="scss" module>
.uploader {
  width: 100%;
  
  :global(.excel-uploader) {
    width: 100%;
  }
  
  :global(.el-upload) {
    width: 100%;
  }
  
  :global(.el-upload-dragger) {
    width: 100%;
    height: auto;
    padding: 24px;
    background-color: var(--el-fill-color-blank);
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    transition: all 0.3s;
    
    &:hover {
      border-color: var(--el-color-primary);
      background-color: var(--el-fill-color-light);
    }
  }
  
  :global(.is-loading) {
    animation: rotating 2s linear infinite;
  }
}

.uploadArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.uploadIcon {
  color: var(--el-color-primary);
}

.uploadText {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.uploadButton {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  font-weight: 500;
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.uploadTip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-top: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 6px;
  transition: all 0.3s;
  
  &:hover {
    background-color: var(--el-fill-color);
  }
}

.fileInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  
  :global(.iconify) {
    color: #217346; // Excel green color
  }
}

.fileName {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.fileStatus {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
}

.removeIcon {
  cursor: pointer;
  color: var(--el-text-color-secondary);
  transition: color 0.3s;
  
  &:hover {
    color: var(--el-color-danger);
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 