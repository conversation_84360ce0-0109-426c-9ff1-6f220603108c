import { h } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGet } from './useHttp'
import { ElMessageBox, ElSkeleton, ElSkeletonItem, type ElMessageBoxOptions } from 'element-plus'

type CaptchaResult = {
  captchaId: string,
  image: string,
}
type CaptchaOptions = {
  partition?: 'import-member-data',
  title?: string,
  validate?: (code: string, captchaId: string) => boolean | Promise<boolean>,
} & Pick<ElMessageBoxOptions, 'confirmButtonText' | 'cancelButtonText' | 'inputPlaceholder'>

export const useValidateCode = () => {
  const { t } = useI18n()
  const defaultCaptchaOptions: Partial<CaptchaOptions> = {
    partition: 'import-member-data',
    title: t('sso.forgot.placeholder.verifyCode'),
    confirmButtonText: t('button.confirm'),
    cancelButtonText: t('button.cancel'),
    inputPlaceholder: t('sso.forgot.placeholder.verifyCode'),
    validate: async (code, captchaId) => {
      return code.length > 0 && !!captchaId
    },
  }
  const getCaptchaOption = <K extends keyof CaptchaOptions>(key: K, options: CaptchaOptions): CaptchaOptions[K] => {
    if (Reflect.has(options, key)) {
      return options[key]
    }
    return defaultCaptchaOptions[key]
  }

  const showCaptchaMsgBox = async (options: CaptchaOptions = {}) => {
    const { state, execute, isLoading } = useGet<CaptchaResult>(`/captcha/${getCaptchaOption('partition', options)}/image`, {
      validateCode(code, data) {
        const res = data as unknown as CaptchaResult
        if (data) {
          data.data = data
        }
        return !!(res.captchaId && res.image)
      },
    })

    const result = await ElMessageBox({
      title: getCaptchaOption('title', options),
      message: () => {
        return h('div', [
          h(ElSkeleton, {
            animated: true,
            loading: isLoading.value,
          }, {
            template: () => h(ElSkeletonItem, { variant: 'image', style: 'height: 40px; width: 160px;' }),
            default: () => h('img', {
              src: `data:image/png;base64,${state.value?.image}`,
              alt: t('sso.accountSettings.verifyCode'),
              style: 'cursor: pointer; display: block;',
              onClick: () => {
                execute()
              },
            }),
          })
        ])
      },
      showCancelButton: true,
      confirmButtonText: getCaptchaOption('confirmButtonText', options),
      cancelButtonText: getCaptchaOption('cancelButtonText', options),
      inputPlaceholder: getCaptchaOption('inputPlaceholder', options),
      showInput: true,
      autofocus: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          const code = instance.inputValue?.trim() || ''
          instance.confirmButtonLoading = true
          try {
            const isValid = await getCaptchaOption('validate', options)?.(code, state.value?.captchaId || '')
            if (isValid === false) {
              return
            }
          } finally {
            instance.confirmButtonLoading = false
          }
        }
        done()
      },
    })

    return {
      code: result.value,
      captchaId: state.value?.captchaId,
    }
  }

  return {
    showCaptchaMsgBox,
  }
}
