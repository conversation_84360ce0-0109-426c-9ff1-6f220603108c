<template>
  <div
    :class="[$style.content, 'markdown-body', 'prose']"
    :data-markdown-content="sectionType"
    v-html="htmlContent"/>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, onUpdated } from 'vue'
import { markdownToHtml, removeMarkdown } from '@/utils/markdown'
import mermaid from 'mermaid'

const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  sectionType: {
    type: String,
    default: 'not-pdf'
  }
})


const htmlContent = computed(() => (markdownToHtml(props.content) as string).replace(/\*\*/g, ''))

// 初始化 mermaid 图表
const initMermaid = async () => {
  try {
    // 重新渲染所有 mermaid 图表
    await mermaid.run({
      querySelector: '.mermaid'
    })
  } catch (error) {
    console.error('Mermaid initialization error:', error)
  }
}

onMounted(() => {
  initMermaid()
})

onUpdated(() => {
  initMermaid()
})
</script>

<style lang="scss" module>
.content {
  padding: 12px 12px 20px;
  width: 100% !important;
  max-width: none !important;
  display: flex;
  flex-direction: column;

  :global {
    // Target all headings and strong tags regardless of nesting
    h1, h2, h3, h4, h5, h6, strong,
    * h1, * h2, * h3, * h4, * h5, * h6, * strong,
    *:not(style) h1, *:not(style) h2, *:not(style) h3, 
    *:not(style) h4, *:not(style) h5, *:not(style) h6, 
    *:not(style) strong {
      color: var(--primary-font-color) !important;
      font-size: 18px !important;
    }

    // Rest of your global styles...
    details {
      margin: 1rem 0;
      padding: 0.5rem;
      background-color: var(--el-fill-color-light);
      border-radius: 8px;
      border: 1px solid var(--el-border-color-lighter);
      transition: all 0.3s ease;
      color: #7f7a7a;
      &[open] {
        padding-bottom: 1rem;
        background-color: var(--el-fill-color);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      }

      // summary 样式
      summary {
        padding: 0.5rem;
        margin: -0.5rem;
        border-radius: 6px;
        cursor: pointer;
        user-select: none;
        list-style: none;
        position: relative;
        font-weight: 500;
        color: #7f7a7a;
        transition: all 0.3s ease;

        &::-webkit-details-marker {
          display: none;
        }

        &::before {
          content: '>';
          position: absolute;
          left: 0;
          transform: translateX(-150%);
          transition: transform 0.3s ease;
          color: var(--el-text-color-secondary);
          font-size: 0.8em;
        }

        &:hover {
          background-color: var(--el-fill-color-darker);
        }
      }

      &[open] summary::before {
        transform: translateX(-150%) rotate(90deg);
      }

      // 内容区域样式
      > *:not(summary) {
        margin-top: 1rem;
        padding: 0 1rem;
        color: #7f7a7a;
        line-height: 1.6;
      }

      // 代码块样式
      pre {
        background-color: var(--el-bg-color);
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
      }

      // 列表样式
      ul, ol {
        padding-left: 1.5rem;
        
        li {
          margin: 0.5rem 0;
        }
      }

    }
    .markdown-hr {
      margin: 2rem 0;
      border: 0;
      border-top: 1px solid var(--el-border-color-lighter);
    }

    // Add styles for mermaid diagrams
    .mermaid {
      margin: 1rem 0;
      background-color: var(--el-fill-color-light);
      padding: 1rem;
      border-radius: 8px;
    }

    // 表格样式
    table {
      width: 100%;
      margin: 1rem 0;
      border-collapse: collapse;
      border: 1px solid var(--el-border-color);
      
      th, td {
        padding: 0.75rem;
        border: 1px solid var(--el-border-color);
        text-align: left;
      }
      
      th {
        background-color: var(--el-fill-color-light);
        font-weight: 500;
        color: var(--primary-font-color);
      }
      
      tr {
        &:nth-child(even) {
          background-color: var(--el-fill-color-lighter);
        }
        
        &:hover {
          background-color: var(--el-fill-color);
        }
      }
      
      // 响应式表格
      @media screen and (max-width: 768px) {
        display: block;
        overflow-x: auto;
        
        th, td {
          white-space: nowrap;
        }
      }
    }
  }
}

.mermaid-wrapper {
  margin: 1rem 0;
  background: var(--el-fill-color-light);
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  
  :global(.mermaid) {
    display: flex;
    justify-content: center;
    
    // 确保 SVG 图表居中显示
    svg {
      max-width: 100%;
      height: auto;
    }
  }
}
</style>
