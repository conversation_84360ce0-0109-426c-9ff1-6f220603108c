import { onUnmounted, ref, computed } from 'vue'

export function usePolling(
  callback: () => null | Promise<void>, 
  interval: number = 10 * 60 * 1000,
  immediate: boolean = false
) {
  const timer = ref<NodeJS.Timeout | null>(null)
  const countdownTimer = ref<NodeJS.Timeout | null>(null)
  const isPolling = ref(false)
  const remainingTime = ref(0)

  const formattedRemainingTime = computed(() => {
    const seconds = Math.ceil(remainingTime.value / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (minutes >= 1) {
      return `${minutes}min`
    } 
    return `${seconds}s`
  })

  const updateRemainingTime = () => {
    if (remainingTime.value > 0) {
      remainingTime.value -= 1000
    }
  }

  const start = () => {
    if (isPolling.value) {return}
    
    isPolling.value = true
    
    // 只在 immediate 为 true 时立即执行
    if (immediate) {
      callback()
    }
    
    // 重置倒计时
    remainingTime.value = interval
    // 设置倒计时定时器
    countdownTimer.value = setInterval(updateRemainingTime, 1000)
    // 设置轮询定时器
    timer.value = setInterval(() => {
      callback()
      remainingTime.value = interval // 重置倒计时
    }, interval)
  }

  const stop = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
      countdownTimer.value = null
    }
    remainingTime.value = 0
    isPolling.value = false
  }

  onUnmounted(() => {
    stop()
  })

  return {
    isPolling,
    start,
    stop,
    remainingTime,
    formattedRemainingTime
  }
} 