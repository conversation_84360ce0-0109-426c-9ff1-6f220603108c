import { useComputed } from '@/hooks/useComputed'
import { defineStore, type StoreDefinition } from 'pinia'
import { computed, ref, type ComputedRef, type Ref } from 'vue'

type FeatureValue = boolean | string | object;
type DefaultValue = boolean;
export type Feature<T extends FeatureValue = DefaultValue> = T | undefined;
export type Features<T extends string = string> = Record<T, Feature<FeatureValue>>;
export type SwitchStoreDefinition<T extends string> = StoreDefinition<string, {}, {
  features: ComputedRef<Features<T>>;
}, {
  setFeatures(feature: Features<T>): void;
  setFeature<V extends FeatureValue = FeatureValue>(feature: T, value: Feature<V>): void;
  isSwitch(names?: T[], every?: boolean): boolean;
  isSwitchComputed(names?: T[], every?: boolean): ComputedRef<boolean>;
  getSwitchValue<V extends FeatureValue = DefaultValue>(feature: T): Feature<V>;
  getSwitchValueComputed<V extends FeatureValue = DefaultValue>(feature: T): ComputedRef<Feature<V>>;
  refreshFeatures<Args extends unknown[]>(...args: Args): Promise<void>;
}>
type SwitchStore<T extends string> = ReturnType<SwitchStoreDefinition<T>>;
export type SwitchStoreOptions<T extends string> = {
  load?: (...args: any[]) => Promise<Features<T>> | Features<T>;
  dependencies?: SwitchStore<T>[];
}

export const isSwitchOn = <T>(getSwitchValue: (feature: T) => Feature<any>, names?: T[], every?: boolean) => {
  if (!names?.length) {
    return true
  }

  const values = names.map(name => getSwitchValue(name))
  if (every) {
    return values.every(value => !!value)
  }

  return values.some(value => !!value)
}

export const createSwitchStore = <T extends string = string>(name: string, options?: SwitchStoreOptions<T>): SwitchStoreDefinition<T> => {
  const { load, dependencies = [] } = options || {}
  return defineStore(name, () => {
    const selfFeatures = ref<Features<T>>({} as Features<T>) as Ref<Features<T>>

    const features = computed<Features<T>>(() => ({
      ...selfFeatures.value,
      ...dependencies.reduce((acc, dep) => ({ ...acc, ...dep.features }), {} as Features<T>)
    }))

    const setFeatures = (newFeatures: Features<T>) => {
      selfFeatures.value = newFeatures
    }
    const setFeature = <V extends FeatureValue = FeatureValue>(feature: T, value: Feature<V>) => {
      selfFeatures.value[feature] = value
    }
    const getSwitchValue = <V extends FeatureValue = DefaultValue>(feature: T): Feature<V> => features.value[feature] as Feature<V>
    const isSwitch = (names?: T[], every?: boolean) => {
      return isSwitchOn(getSwitchValue, names, every)
    }

    const isSwitchComputed = useComputed(isSwitch)
    const getSwitchValueComputed = useComputed(getSwitchValue)

    const refreshFeatures = async <Args extends unknown[]>(...args: Args) => {
      const newFeatures = await (load || (() => Promise.resolve({})))(...args) as Features<T>
      setFeatures(newFeatures)
    }

    return {
      features,
      setFeatures,
      setFeature,
      isSwitch,
      isSwitchComputed,
      getSwitchValue,
      getSwitchValueComputed,
      refreshFeatures
    }
  })
}
