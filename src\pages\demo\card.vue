<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'

interface Props {
  text?: string
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: 'hover',
  className: ''
})

const mousePosition = reactive({ x: 0, y: 0 })
const randomString = ref('')
const cardRef = ref<HTMLElement | null>(null)

// Generate random string of characters
const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
const generateRandomString = (length: number) => {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}

// Handle mouse movement
const onMouseMove = (event: MouseEvent) => {
  if (!cardRef.value) {return}
  
  const { left, top } = cardRef.value.getBoundingClientRect()
  mousePosition.x = event.clientX - left
  mousePosition.y = event.clientY - top
  
  // Generate new random string on mouse move
  randomString.value = generateRandomString(1500)
}

// Compute mask image based on mouse position
const maskImage = computed(() => {
  return `radial-gradient(250px at ${mousePosition.x}px ${mousePosition.y}px, white, transparent)`
})

// Generate initial random string on mount
onMounted(() => {
  randomString.value = generateRandomString(1500)
})
</script>

<template>
  <div
    :class="`p-0.5 bg-transparent aspect-square flex items-center justify-center w-full h-full relative ${props.className}`">
    <div
      ref="cardRef"
      @mousemove="onMouseMove"
      class="group/card rounded-3xl w-full relative overflow-hidden bg-transparent flex items-center justify-center h-full">
      <!-- Card Pattern -->
      <div class="pointer-events-none">
        <div class="absolute inset-0 rounded-2xl [mask-image:linear-gradient(white,transparent)] group-hover/card:opacity-50"></div>
        
        <!-- Gradient background with mask -->
        <div
          class="absolute inset-0 rounded-2xl bg-gradient-to-r from-green-500 to-blue-700 opacity-0 group-hover/card:opacity-100 backdrop-blur-xl transition duration-500"
          :style="{
            maskImage: maskImage,
            WebkitMaskImage: maskImage
          }"></div>
        
        <!-- Text overlay with mask -->
        <div
          class="absolute inset-0 rounded-2xl opacity-0 mix-blend-overlay group-hover/card:opacity-100"
          :style="{
            maskImage: maskImage,
            WebkitMaskImage: maskImage
          }">
          <p class="absolute inset-x-0 text-xs h-full break-words whitespace-pre-wrap text-white font-mono font-bold transition duration-500">
            {{ randomString }}
          </p>
        </div>
      </div>
      
      <!-- Center glow with text -->
      <div class="relative z-10 flex items-center justify-center">
        <div class="relative h-44 w-44 rounded-full flex items-center justify-center text-white font-bold text-4xl">
          <div class="absolute w-full h-full bg-white/[0.8] dark:bg-black/[0.8] blur-sm rounded-full"></div>
          <span class="dark:text-white text-black z-20">{{ text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>