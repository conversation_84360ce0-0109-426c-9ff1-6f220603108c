<template>
  <div class="flex w-full h-full">
    <!-- Main Content -->
    <main class="flex-1 p-8 h-full overflow-y-auto">
      <!-- 顶部操作区域 -->
      <div class="mb-6 p-4 rounded-xl border border-[rgba(68,102,188,0.1)]">
        <div class="flex items-center gap-2 mb-4">
          <Button 
            :variant="filterMode === 'agent-type' ? 'default' : 'outline'"
            size="sm"
            class="min-w-[120px]"
            @click="handleModeChange('agent-type')">
            <Icon icon="mdi:account-group" class="mr-1"/>
            {{ t('common.filter_by_type') }}
          </Button>
          <Button 
            :variant="filterMode === 'date' ? 'default' : 'outline'"
            size="sm"
            class="min-w-[120px]"
            @click="handleModeChange('date')">
            <Icon icon="mdi:calendar" class="mr-1"/>
            {{ t('common.filter_by_date') }}
          </Button>
          <div class="ml-auto">
            <Badge
              v-if="filteredReports.length > 0"
              variant="outline"
              class="cursor-pointer hover:bg-muted transition-colors flex items-center gap-1 px-3 py-1.5"
              @click="handleEditClick">
              <Icon 
                icon="mdi:pencil" 
                class="text-main-color w-4 h-4 transition-colors duration-300"/>
              <span class="text-main-color transition-colors duration-300">
                {{ t('agent.edit_completed_agent') }}
              </span>
            </Badge>
          </div>
        </div>
        
        <!-- 修改Tabs样式 -->
        <div class="w-full">
          <div class="flex flex-wrap gap-2">
            <Button 
              v-for="tab in currentTabs" 
              :key="tab.value"
              :variant="currentType === tab.value ? 'default' : 'ghost'"
              size="sm"
              class="py-2 px-4 rounded-lg flex items-center gap-2 h-auto min-h-[44px]"
              :class="currentType === tab.value ? 'bg-[rgba(68,102,188,0.12)] text-[--main-color] font-semibold' : 'bg-[rgba(255,255,255,0.7)] text-[--font-color-2]'"
              @click="handleTypeChange(tab.value)">
              <div class="rounded-full bg-white/80 p-[2px] shadow-sm h-6 w-6 flex items-center justify-center shrink-0">
                <Icon 
                  :icon="tab.icon" 
                  class="text-main-color"/>
              </div>
              <span>{{ tab.label }}</span>
            </Button>
          </div>
        </div>
      </div>

      <!-- 报告卡片网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-7 mt-6 min-h-[400px]">
        <template v-if="isUserReportsLoading">
          <SkeletonCard v-for="i in 10" :key="i"/>
        </template>
        <template v-else-if="filteredReports.length === 0">
          <div class="col-span-full flex flex-col items-center justify-center p-15 text-center">
            <Icon icon="mdi:file-document-outline" class="text-[64px] text-[rgba(68,102,188,0.2)] mb-5"/>
            <p class="text-[--font-color-3] max-w-[400px] leading-relaxed text-base">
              {{ t('common.no_report') }}
            </p>
          </div>
        </template>
        <template v-else>
          <FormatReportTemplateItem
            v-for="item in filteredReports"
            :key="item.id"
            :item="mapReportToTemplateItem(item)"
            @click="viewReport(item)"
            class="transition-all duration-300 h-full hover:translate-y-[-4px] hover:shadow-lg"/>
        </template>
      </div>
    </main>

    <!-- 添加批量操作抽屉组件 -->

    <AgentTableDrawer
      :visible="showBatchOperations"
      :agents="reportsAsAgents"
      :loading="isUserReportsLoading"
      @view="handleAgentView"
      @delete="handleSingleDelete"
      @batch-view="handleBatchView"
      @batch-delete="handleBatchDelete"
      @refresh="refreshUserReports"/>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, definePage } from 'vue-router/auto'
import FormatReportTemplateItem from '@/pages/format-report/__components__/FormatReportTemplateItem.vue'
import { useGet, usePost } from '@/hooks/useHttp'
import type { ReportList } from './type'
import SkeletonCard from '@/pages/format-report/__components__/TemplateItemSkeleton.vue'
import { useI18n } from 'vue-i18n'
import { AVATAR_MAP, SPECIAL_AGENT_TYPES, BIDING_AGENT_TYPES } from '@/composables/useReportProgress'
import { Icon } from '@iconify/vue'
import AgentTableDrawer from '@/pages/__components__/DashboardComponents/AgentTableDrawer.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { Agent } from '@/pages/dashboard/type'
import { formatCalendarDateTime } from '@/utils/filter'
import dayjs from 'dayjs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Avatar, 
  AvatarImage 
} from '@/components/ui/avatar'
import {
  Sheet,
  SheetContent
} from '@/components/ui/sheet'

// 定义三种agent类型
const AGENT_TYPES = {
  NOTIFY: 'notify',
  NORMAL: 'normal',
  BINDING: 'binding'
}

// 定义BINDING_WORK_AGENT类型（参考agent-market/index.vue）
const BINDING_WORK_AGENT = ['STRATEGIC', 'AWARDED', 'TENDER']

definePage({
  meta: {
    layout: 'default',
    requiresAuth: true
  }
})

const { state: userReports, execute: refreshUserReports, isLoading: isUserReportsLoading } = useGet<ReportList[]>('/formatted_report/standard_report_list', {}, {
  immediate: true
})

const router = useRouter()

const { t } = useI18n()

const viewReport = (report: ReportList) => {
  const to = router.resolve(`/format-report/view/${report.id}`)
  router.push(to)
}

onMounted(() => {
  // refreshUserReports已在immediate: true时执行
})

// 获取报告类型对应的agent类型
const getAgentTypeByReportType = (reportType: string | undefined): string => {
  if (!reportType) {return AGENT_TYPES.NORMAL} // 默认为normal类型
  
  if (SPECIAL_AGENT_TYPES.includes(reportType)) {
    return AGENT_TYPES.NOTIFY
  }
  
  if (BINDING_WORK_AGENT.includes(reportType)) {
    return AGENT_TYPES.BINDING
  }
  
  return AGENT_TYPES.NORMAL
}

// 获取agent类型对应的标签名称
const getAgentTypeLabel = (agentType: string): string => {
  switch (agentType) {
  case AGENT_TYPES.NOTIFY:
    return t('agent.filter.notify', '预警类型')
  case AGENT_TYPES.BINDING:
    return t('agent.filter.binding', '绑定类型')
  case AGENT_TYPES.NORMAL:
    return t('agent.filter.normal', '标准类型')
  default:
    return t('agent.filter.normal', '标准类型')
  }
}

// 获取agent类型对应的图标
const getAgentTypeIcon = (agentType: string): string => {
  switch (agentType) {
  case AGENT_TYPES.NOTIFY:
    return 'mdi:bell-ring'
  case AGENT_TYPES.BINDING:
    return 'mdi:layers'
  case AGENT_TYPES.NORMAL:
    return 'mdi:account-group'
  default:
    return 'mdi:account-group'
  }
}

const currentType = ref('all')

// 日期分组
const dateTabs = computed(() => {
  if (!userReports.value?.length) {
    return [{ value: 'all', label: t('common.all'), icon: 'mdi:calendar-month' }]
  }

  const today = dayjs().startOf('day')
  const yesterday = dayjs().subtract(1, 'day').startOf('day')
  const startOfWeek = dayjs().startOf('week')
  
  // 预定义的日期组
  const dateGroups = [
    { value: 'all', label: t('common.all'), icon: 'mdi:calendar-month' },
    { value: 'today', label: t('common.today'), icon: 'mdi:calendar-today' },
    { value: 'yesterday', label: t('common.yesterday'), icon: 'mdi:calendar-arrow-left' },
    { value: 'this_week', label: t('common.this_week'), icon: 'mdi:calendar-week' }
  ]
  
  // 添加具体日期（按月份分组）
  const monthGroups = new Set<string>()
  
  userReports.value.forEach(report => {
    if (report.updated_at) {
      const reportDate = dayjs(report.updated_at)
      // 如果不是今天、昨天或本周，则按月份分组
      if (
        !reportDate.isSame(today, 'day') && 
        !reportDate.isSame(yesterday, 'day') && 
        reportDate.isBefore(startOfWeek)
      ) {
        const monthYear = reportDate.format('YYYY-MM')
        monthGroups.add(monthYear)
      }
    }
  })

  monthGroups.forEach(month => {
    dateGroups.push({
      value: month,
      label: month,
      icon: 'mdi:calendar-month'
    })
  })

  return dateGroups
})

// agent类型分组
const agentTypeTabs = computed(() => {
  if (!userReports.value?.length) {
    return [{ value: 'all', label: t('common.all'), icon: 'mdi:format-list-bulleted-type' }]
  }

  const uniqueReportTypesMap = new Map<string, { label: string; icon: string; count: number; originalReportType: string }>()

  userReports.value.forEach(report => {
    const type = report.report_type // This is the raw report_type, e.g., "RecentNewsAndSocialOpinionTrackingReport"
    const displayName = report.agent_name || type // Use agent_name as label, fallback to report_type
    
    if (type) {
      if (uniqueReportTypesMap.has(type)) {
        uniqueReportTypesMap.get(type)!.count++
      } else {
        // TODO: Implement a function getIconForDetailedReportType(type) if specific icons are needed
        uniqueReportTypesMap.set(type, { 
          label: displayName, 
          icon: 'mdi:file-document-outline', // Generic icon for now
          count: 1,
          originalReportType: type
        })
      }
    }
  })

  const tabs = [{ value: 'all', label: t('common.all'), icon: 'mdi:format-list-bulleted-type' }]
  
  uniqueReportTypesMap.forEach((data, _typeKey) => { // _typeKey is the same as data.originalReportType
    tabs.push({
      value: data.originalReportType, // Use the original report_type as the value for filtering
      label: `${data.label} (${data.count})`, 
      icon: data.icon
    })
  })
  
  // Optional: Sort tabs by label (excluding 'all')
  // tabs.sort((a, b) => {
  //   if (a.value === 'all') return -1;
  //   if (b.value === 'all') return 1;
  //   return a.label.localeCompare(b.label);
  // });

  return tabs
})

// 更新当前标签计算属性
const currentTabs = computed(() => {
  if (filterMode.value === 'agent-type') {
    return agentTypeTabs.value
  }
  return dateTabs.value
})

// 更新筛选报告的计算属性
const filteredReports = computed(() => {
  if (!userReports.value?.length) {
    return []
  }

  if (currentType.value === 'all') {
    return userReports.value
  }

  if (filterMode.value === 'agent-type') {
    const selectedReportType = currentType.value 
    return userReports.value.filter(item => item.report_type === selectedReportType)
  }
  
  // 日期筛选逻辑 (保持不变)
  const today = dayjs().startOf('day')
  const yesterday = dayjs().subtract(1, 'day').startOf('day')
  const startOfWeek = dayjs().startOf('week')
  
  return userReports.value.filter(item => {
    if (!item.updated_at) { return false }
    
    const itemDate = dayjs(item.updated_at)
    
    switch (currentType.value) {
    case 'today':
      return itemDate.isSame(today, 'day')
    case 'yesterday':
      return itemDate.isSame(yesterday, 'day')
    case 'this_week':
      return itemDate.isAfter(startOfWeek) || itemDate.isSame(startOfWeek, 'day')
    default:
      if (currentType.value.match(/^\d{4}-\d{2}$/)) {
        const [year, month] = currentType.value.split('-').map(Number)
        return itemDate.year() === year && itemDate.month() + 1 === month
      }
      return false
    }
  })
})

interface TemplateItemData {
  id: string;
  report_name: string;
  report_type: string[];
  avatar?: string;
  agent_name: string;
  summary: string;
  updated_at?: string;
  agent_type_tag: string; // 新增字段：用于显示agent类型标签
}

const mapReportToTemplateItem = (report: ReportList): TemplateItemData => {
  return {
    id: report.id,
    report_name: report.name,
    report_type: [report.report_type || ''], 
    avatar: undefined, 
    agent_name: getAgentTypeLabel(getAgentTypeByReportType(report.report_type)),
    summary: report.summary || '',
    updated_at: report.updated_at ? formatCalendarDateTime(dayjs(report.updated_at).valueOf()) : undefined,
    agent_type_tag: getAgentTypeLabel(getAgentTypeByReportType(report.report_type))
  }
}

const handleTypeChange = (type: string) => {
  currentType.value = type
}

// 批量操作相关
const showBatchOperations = ref(false)

// 将报告数据转换为Agent格式以兼容AgentTableDrawer组件
const reportsAsAgents = computed<Agent[]>(() => {
  if (!userReports.value?.length) {
    return [] as Agent[]
  }
  
  return userReports.value.map(report => {
    return {
      id: report.id,
      name: report.agent_name || report.report_type || t('common.unknown_type'), // Use agent_name or report_type
      duty: report.name, // This is the report's own name (title)
      report_type: report.report_type,
      summary: report.summary || '',
      status: 'normal', // Assuming 'normal' status for these
      updated_at: report.updated_at,
    }
  }) as Agent[]
})

// 批量操作相关函数
const handleEditClick = () => {
  showBatchOperations.value = true
}

const handleAgentView = (agent: Agent) => {
  const to = router.resolve(`/format-report/view/${agent.id}`)
  window.open(to.href, '_blank')
}

const handleSingleDelete = async (agent: Agent) => {
  try {
    await ElMessageBox.confirm(
      t('agent.delete_confirm'),
      t('common.warning'),
      { type: 'warning' }
    )
    const res = await batchDeleteReports(0, {
      ids: [agent.id],
      source: 'report'
    })
    if (res?.success) {
      ElMessage.success(t('common.delete_success'))
      showBatchOperations.value = false
      refreshUserReports()
    } else {
      ElMessage.error(t('common.operation_failed'))
    }
  } catch (err) {
    // 用户取消操作不显示错误
    if (err !== 'cancel') {
      ElMessage.error(t('common.operation_failed'))
    }
  }
}

const handleBatchView = (ids: string[]) => {
  ids.forEach(id => {
    const to = router.resolve(`/format-report/view/${id}`)
    window.open(to.href, '_blank')
  })
}

const handleBatchDelete = async (ids: string[]) => {
  try {
    await ElMessageBox.confirm(
      t('agent.batch_delete_confirm', { count: ids.length }),
      t('common.warning'),
      { 
        type: 'warning',
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel')
      }
    )
    
    const res = await batchDeleteReports(0, {
      ids,
      source: 'report'
    })

    if (res?.success) {
      showBatchOperations.value = false
      ElMessage.success(
        t('common.batch_delete_success', { 
          count: res?.deleted_count 
        })
      )
      
      if (res?.not_found?.length) {
        ElMessage.warning(
          t('common.items_not_found', { 
            count: res?.not_found.length 
          })
        )
      }

      refreshUserReports()
    } else {
      ElMessage.error(t('common.operation_failed'))
    }
  } catch (err) {
    // 用户取消操作不显示错误
    if (err !== 'cancel') {
      ElMessage.error(t('common.operation_failed'))
      console.error('Batch delete failed:', err)
    }
  }
}

// 添加批量删除API
const { execute: batchDeleteReports } = usePost<{
  success: boolean;
  deleted_count: number;
  not_found: string[];
}>(
  '/formatted_report/batch_delete_report',
  {},
  {},
  {
    immediate: false,
  }
)

const filterMode = ref('agent-type')

const handleModeChange = (mode: string) => {
  filterMode.value = mode
  // 切换模式时重置为全部
  currentType.value = 'all'
}
</script>

