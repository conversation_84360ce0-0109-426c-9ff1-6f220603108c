<template>
  <div class="h-full overflow-hidden bg-gradient-to-br from-slate-50 via-white to-slate-50/90 p-2 md:p-3">
    <div class="grid grid-cols-1 lg:grid-cols-[1fr_320px] gap-1 md:gap-2 h-full overflow-hidden">
      <div class="flex flex-col min-h-0">
        <Card class="h-full flex flex-col bg-white/70 backdrop-blur-md border-slate-100 shadow-sm gap-1 md:gap-2 pb-2">
          <CardHeader class="border-b flex-shrink-0 p-3 min-h-[70px]">
            <div class="flex items-center justify-between">
              <!-- 左侧：标签页切换 -->
              <div class="flex items-center min-h-[32px]">
                <!-- 加载骨架屏 -->
                <div v-if="pageLoading || !permissionsLoaded" class="animate-pulse">
                  <div class="h-8 bg-gray-200 rounded w-32"></div>
                </div>
                
                <!-- 单个标签页时显示为标题 -->
                <h2 v-else-if="availableTabsCount === 1" class="text-lg font-semibold tracking-tight">
                  {{ getSingleTabTitle() }}
                </h2>

                <!-- 多个标签页时显示为切换按钮 -->
                <CustomTabs
                  v-else-if="availableTabsCount > 1"
                  v-model="activeTab"
                  :tabs="availableTabsConfig"
                  tabs-list-class="bg-slate-100/80"
                  @tab-change="handleMainTabChange"/>
                  
                <!-- 权限加载完成但无可用标签页 -->
                <div v-else-if="availableTabsCount === 0" class="h-8 flex items-center">
                  <span class="text-sm text-muted-foreground">{{ t('common.permission.no_access') }}</span>
                </div>
              </div>

              <!-- 右侧：移动端侧边栏按钮 -->
              <Button variant="outline"
                      size="icon"
                      @click="isAgentSidebarDrawerOpen = true"
                      class="lg:hidden">
                <PanelRightIcon class="h-4 w-4"/>
              </Button>
            </div>
          </CardHeader>

          <CardContent class="flex-1 min-h-0 p-0">
            <!-- 使用shadcn-vue的Tab组件 -->
            <Tabs v-model="activeTab" class="h-full flex flex-col">

              <!-- 标签页内容 -->
              <div class="flex-1 h-full">
                <!-- 加载状态骨架屏 -->
                <div v-if="isWorkspaceAgentsLoading || pageLoading || !permissionsLoaded" class="h-full flex flex-col items-center justify-center p-8">
                  <div class="animate-pulse space-y-4 w-full max-w-md">
                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                    <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
                
                <!-- 标准信息流 -->
                <TabsContent v-if="canAccessNormalFeature" value="standard_feed" class="h-full m-0 p-4 overflow-hidden">
                  <ReportFeed
                    :grouped-reports="standardFeedReportsGrouped"
                    :officer-type="'normal'"
                    :officer-name="t('agent.filter.normal')"
                    :agent-descriptions="rawAgentDescription"
                    @view-report="viewReport"
                    @tagClick="handleTagClick"
                    @request-change-time="handleChangeReportTime"
                    @delete-agent="handleDeleteAgent"
                    @refresh-agent="handleRefreshAgent"
                    @show-risk-companies-drawer="() => showRiskCompaniesDrawer = true"
                    class="h-full"/>
                </TabsContent>

                <!-- 通知洞察 -->
                <TabsContent v-if="canAccessNotifyFeature" value="notify_insights" class="h-full m-0 p-1 md:p-2 overflow-hidden">
                  <ReportFeed
                    :grouped-reports="notifyFeedReportsGrouped"
                    :officer-type="'notify'"
                    :officer-name="t('agent.filter.notify')"
                    :agent-descriptions="rawAgentDescription"
                    @view-report="viewReport"
                    @tagClick="handleTagClick"
                    @request-change-time="handleChangeReportTime"
                    @delete-agent="handleDeleteAgent"
                    @refresh-agent="handleRefreshAgent"
                    @show-risk-companies-drawer="() => showRiskCompaniesDrawer = true"
                    class="h-full"/>
                </TabsContent>

                <!-- 专项分析 -->
                <TabsContent v-if="canAccessSpecialAnalysisFeature" value="special_analysis" class="h-full m-0 p-1 md:p-2 overflow-hidden">
                  <div class="h-full flex flex-col">
                    <!-- 专项分析子标签页 - 只有在有多个子标签页时才显示 -->
                    <div v-if="specialAnalysisSubTabs.length > 1" class="flex-shrink-0 mb-4 min-h-[40px]">
                      <CustomTabs
                        v-model="specialAnalysisActiveTab"
                        :tabs="specialAnalysisSubTabs"
                        tabs-list-class="bg-muted/50 h-8"
                        @tab-change="handleSpecialTabChange"/>
                    </div>

                    <!-- 子标签页内容 -->
                    <div class="flex-1 min-h-0 overflow-hidden">
                      <!-- 招投标分析 -->
                      <div v-if="specialAnalysisActiveTab === 'biding_analysis' && canAccessBusinessOpportunityFeature" class="h-full">
                        <Suspense>
                          <template #default>
                            <BidingRespon
                              v-if="bidingReportForDisplay"
                              :report="bidingReportForDisplay"
                              :api-loading="isAPIBidingDataLoading"
                              :is-agent-working="hasRunningBidingTask"
                              :view-report="viewReport"
                              :agent-descriptions="rawAgentDescription"
                              :officer-name="t('agent.filter.binding')"
                              :officer-type="'binding'"
                              :load-biding-detail="loadBidingDetail"
                              :current-biding-detail="currentBidingDetail"
                              :is-detail-loading="isBidingDetailLoading"
                              :clear-detail-cache="clearBidingDetailCache"
                              @apply-filters="handleBidingFilterApply"
                              class="h-full"/>
                          </template>
                          <template #fallback>
                            <div class="h-full flex items-center justify-center">
                              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            </div>
                          </template>
                        </Suspense>
                      </div>

                      <!-- 战略分析 -->
                      <div v-else-if="specialAnalysisActiveTab === 'strategic_analysis' && canAccessStrategicFeature" class="h-full">
                        <Suspense>
                          <template #default>
                            <StrategicRespon
                              v-if="strategicReportForDisplay"
                              :strategic-report="strategicReportForDisplay"
                              :api-loading="isAPIStrategicDataLoading || isStrategicLoading"
                              :is-agent-working="hasRunningStrategicTask"
                              @page-change="handleStrategicPageChange"
                              class="h-full"/>
                          </template>
                          <template #fallback>
                            <div class="h-full flex items-center justify-center">
                              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            </div>
                          </template>
                        </Suspense>
                      </div>

                      <!-- 无权限或无对应子标签页 -->
                      <div v-else class="flex flex-col items-center justify-center text-center text-muted-foreground py-10 h-full">
                        <InfoIcon class="w-12 h-12 text-muted-foreground/50 mb-4"/>
                        <p>{{ t('common.permission.no_access') }}</p>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <!-- 权限不足提示 -->
                <div v-else class="flex flex-col items-center justify-center text-center text-muted-foreground py-10 h-full">
                  <InfoIcon class="w-12 h-12 text-muted-foreground/50 mb-4"/>
                  <p>{{ t('common.permission.no_standard_access') }}</p>
                </div>
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      <div class="hidden lg:flex lg:flex-col lg:min-h-0">
        <AgentListSidebar 
          class="flex-1 min-h-0"
          v-bind="agentSidebarProps" 
          v-model:agentSearch="agentSearch"
          @select-dynamic-filter="handleDynamicFilterSelection"
          @switch-to-special-tab="handleSwitchTab"/>
      </div>
    </div>
  </div>
  
  <TimeIntervalDialog
    v-model:visible="showTimeIntervalDialog"
    :agent="selectedAgentForTimeChange ? { 
      id: selectedAgentForTimeChange.id, 
      name: selectedAgentForTimeChange.agent_name || selectedAgentForTimeChange.report_name || '',
      frequency: selectedAgentForTimeChange.frequency,
    } : undefined" 
    :current-frequency="currentFrequency"
    @confirm="handleTimeIntervalSubmit"/>
    
  <RiskCompanyDrawer v-model:visible="showRiskCompaniesDrawer"/>

  <AgentSidebarDrawer 
    v-model:open="isAgentSidebarDrawerOpen"
    v-model:agentSearch="agentSearch"
    :sidebarProps="agentSidebarProps"
    @select-dynamic-filter="handleDynamicFilterSelection"
    @switch-to-special-tab="handleSwitchTab"/>
</template>
        
<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { useGet, usePost } from '@/hooks/useHttp'
import { SPECIAL_AGENT_TYPES, BIDING_AGENT_TYPES } from '@/composables/useReportProgress'
import type { CompletedAgent } from '@/pages/dashboard/type'
import { useI18n } from 'vue-i18n'
import { useRouter, definePage } from 'vue-router/auto'
import ReportFeed from './__components__/ReportFeed.vue'
import AgentListSidebar from './__components__/AgentListSidebar.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { 
  Report, BidingAnalysisResult, 
  BidingApiResponse, StrategicApiResponse, StrategicNewsApiResponse,
  StrategicReport, StrategicOpportunity, StrategicNewsItem,
  StrategicResult, BidingDetailApiResponse,
  BidingAnalysisItem, BidingListItemCompatible
} from './types'
import { isMonitorAgent, isRunningAgent, isStandardReport, isBidingAnalysisResult, isStrategicReport } from './types'
import { useReports } from '@/composables/useReports'
import { useAgents } from '@/composables/useAgents'
import { useWorkspaceAgents } from '@/composables/useWorkspaceAgents'
import { useUserStore } from '@/stores/user'
import { usePermissions } from '@/composables/usePermissions'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Tabs, TabsContent } from '@/components/ui/tabs'
import { InfoIcon, PanelRightIcon, XIcon } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import CustomTabs from '@/components/ui/custom-tabs.vue'

const RiskCompanyDrawer = defineAsyncComponent(() => 
  import('@/pages/__components__/DashboardComponents/RiskCompanyDrawer.vue')
)
const TimeIntervalDialog = defineAsyncComponent(() => 
  import('@/pages/__components__/DashboardComponents/TimeIntervalDialog.vue')
)
const BidingRespon = defineAsyncComponent(() => 
  import('./__components__/BidingRespon.vue')
)
const StrategicRespon = defineAsyncComponent(() => 
  import('./__components__/StrategicRespon.vue')
)
const AgentSidebarDrawer = defineAsyncComponent(() =>
  import('./__components__/AgentSidebarDrawer.vue')
)

const { t } = useI18n()
const router = useRouter()
definePage({
  meta: {
    layout: 'default',
    requiresAuth: true,
  },
})
const userStore = useUserStore()
const { canAccessNotifyFeature, canAccessNormalFeature, canAccessBindingFeature, canAccessBusinessOpportunityFeature, canAccessStrategicFeature, canAccessSpecialAnalysisFeature } = userStore
const { withPermission } = usePermissions()

const { groupedReports, refreshReports, monitorAgents, dashboardData } = useReports({})
const { combinedAgentsList, rawAgentDescription, isLoading: isWorkspaceAgentsLoading } = useWorkspaceAgents(monitorAgents, dashboardData)
const { filteredAgents, agentSearch } = useAgents(combinedAgentsList)

// --- Agent Type Constants for Tab Grouping ---
// NOTIFY_AGENT_TYPES_FOR_TAB will use SPECIAL_AGENT_TYPES from useReportProgress
// SPECIAL_AGENT_TYPES are ['SocialAgent', 'LegalAgent', 'CompositeAgent']
const SPECIAL_BINDING_AGENT_TYPES_FOR_TAB = ['STRATEGIC', ...BIDING_AGENT_TYPES]


const selectedReportTypeFilter = ref<string | null | '__MONITOR__'>(null)
const showTimeIntervalDialog = ref(false)
const selectedAgentForTimeChange = ref<CompletedAgent | null>(null)
const currentFrequency = ref(1)
const showRiskCompaniesDrawer = ref(false)
const bidingResults = ref<BidingAnalysisResult[]>([])
const isBidingLoading = ref(false)
const strategicResults = ref<StrategicReport[]>([])
const isStrategicLoading = ref(false)
// 根据功能权限设置默认activeTab，优先显示专项分析
const getDefaultActiveTab = () => {
  // 优先显示专项分析，因为这是用户最关心的功能
  if (canAccessSpecialAnalysisFeature) {
    return 'special_analysis'
  }
  if (canAccessNormalFeature) {
    return 'standard_feed'
  }
  if (canAccessNotifyFeature) {
    return 'notify_insights'
  }
  return 'standard_feed' // 兜底
}

const activeTab = ref(getDefaultActiveTab())
const isAgentSidebarDrawerOpen = ref(false)
const bidingDataPollInterval = ref<ReturnType<typeof setInterval> | null>(null)
// 根据权限设置默认的专项分析子标签页
const getDefaultSpecialAnalysisTab = () => {
  if (canAccessBusinessOpportunityFeature) {
    return 'biding_analysis'
  }
  if (canAccessStrategicFeature) {
    return 'strategic_analysis'
  }
  return 'biding_analysis' // 兜底
}
// 添加权限加载状态跟踪
const permissionsLoaded = computed(() => {
  // 检查用户store是否已经完全初始化
  return userStore.userId && userStore.userInfo?.company?.id
})
const specialAnalysisActiveTab = ref(getDefaultSpecialAnalysisTab())

// 监听权限加载完成，重新评估默认标签页
watch(permissionsLoaded, (newPermissionsLoaded) => {
  if (newPermissionsLoaded) {
    const newDefaultTab = getDefaultActiveTab()

    // 如果当前标签页与新的默认标签页不同，且用户有权限访问新的默认标签页，则切换
    if (activeTab.value !== newDefaultTab) {
      activeTab.value = newDefaultTab
    }
  }
}, { immediate: true })


const pageLoading = computed(()=> isRefreshAgentLoading.value || isChangeTimeLoading.value || isWorkspaceAgentsLoading.value)
const { execute: fetchBidingData, isLoading: isAPIBidingDataLoading } = usePost<BidingApiResponse>(
  '/formatted_report/v3/tender_awards/overview', {
  }, {
    timeout: 5 * 60 * 1000,
  }, { immediate: false }
)
// RESTful API 详情接口不使用固定的 hook，而是在函数中动态构造路径
const isBidingDetailLoading = ref(false)
const { execute: fetchStrategicData, isLoading: isAPIStrategicDataLoading } = usePost<StrategicNewsApiResponse>(
  '/business_agent/get_business_news_list', {}, {}, { immediate: false }
)
const { execute: executeRefreshAgent, isLoading: isRefreshAgentLoading } = usePost(
  '/formatted_params/refresh_agent', {}, {}, { immediate: false }
)
const { execute: changeTime, isLoading: isChangeTimeLoading } = usePost(
  '/agent/up_agent_task', {}, {}, { immediate: false }
)
const { execute: batchDeleteReports } = usePost<{
  success: boolean;
  deleted_count: number;
  not_found: string[];
}>(
  '/formatted_report/batch_delete_report', {}, {}, { immediate: false }
)

const hasRunningBidingTask = computed(() => {
  // 添加权限检查
  if (!canAccessBusinessOpportunityFeature) {
    return false
  }
  return (dashboardData.value || []).some(task => 
    task.report_type === 'TENDER' && 
    ['running', 'pending'].includes(task.status || '')
  )
})

const hasRunningStrategicTask = computed(() => {
  // 添加权限检查
  if (!canAccessStrategicFeature) {
    return false
  }
  return (dashboardData.value || []).some(task => 
    task.report_type === 'STRATEGIC' && 
    ['running', 'pending'].includes(task.status || '')
  )
})


function getReportDate(report: Report): number {
  try {
    if (isBidingAnalysisResult(report)) {
      return new Date(report.updated_at).getTime()
    }
    if (isStrategicReport(report)) {
      return new Date(report.updated_at).getTime()
    }
    if (isRunningAgent(report)) {
      const parsedDate = report.date ? new Date(report.date).getTime() : NaN
      return !isNaN(parsedDate) ? parsedDate : Date.now()
    } 
    if (isStandardReport(report)) {
      const dateStr = (report as any).updated_at || (report as any).created_at
      const parsedDate = dateStr ? new Date(dateStr).getTime() : NaN
      return !isNaN(parsedDate) ? parsedDate : 0
    } 
    if (isMonitorAgent(report)) {
      const dateStr = report.updated_at || report.created_at
      const parsedDate = dateStr ? new Date(dateStr).getTime() : NaN
      return !isNaN(parsedDate) ? parsedDate : 0
    }
  } catch (e) {
    console.warn('Failed to parse date for report:', report, e)
  }
  return 0
}

function getReportTypeFromReport(report: Report): string | undefined {
  if (isBidingAnalysisResult(report)) { 
    return report.report_type 
  }
  if (isStrategicReport(report)) {
    return report.report_type
  }
  if (isMonitorAgent(report)) { 
    return report.report_type 
  }
  if (isRunningAgent(report)) { 
    return report.report_type 
  }
  if (isStandardReport(report)) { 
    return report.report_type 
  }
  return (report as any)?.report_type
}

const agentsToListIndividually = computed(() => filteredAgents.value)

interface BidingDisplayData extends BidingAnalysisResult {
  avatar: string
}

interface StrategicDisplayData extends StrategicReport {
  avatar: string
}

const bidingReportForDisplay = computed((): BidingDisplayData | null => {
  // 添加权限检查
  if (!canAccessBusinessOpportunityFeature) {
    return null
  }

  const bidingAgent = filteredAgents.value.find(agent => agent.report_type === 'TENDER')
  const result = bidingResults.value.length > 0 ? bidingResults.value[0] : null
  const isRunning = hasRunningBidingTask.value || isBidingLoading.value || isAPIBidingDataLoading.value

  if (result && bidingAgent) {
    const reportData = { ...result }
    if (!isRunning) {
      reportData.status = 'completed'
      reportData.is_loading = false
    }
    return {
      ...reportData,
      report_name: bidingAgent.name,
      avatar: bidingAgent.avatar
    }
  }
  if (isRunning && bidingAgent) {
    return {
      id: 'TENDER_LOADING',
      report_type: 'TENDER',
      report_name: bidingAgent.name,
      status: 'running',
      is_loading: true,
      date: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      results: [],
      avatar: bidingAgent.avatar,
      total: 0,
      total_count: 0
    }
  }

  return null
})

// 添加分页状态
const strategicPagination = ref({
  current_page: 1,
  page_size: 10,
  total: 0
})

// 新增：详情数据状态管理
const bidingDetailCache = ref<Map<string, BidingAnalysisItem>>(new Map())
const currentBidingDetail = ref<BidingAnalysisItem | null>(null)

// 修改：适配新的列表数据结构，移除分页参数，后端一次性返回所有数据
const loadBidingData = withPermission('api.fetchBidingData', async (filters?: Record<string, any>) => {
  const currentUserId = userStore.userId
  const currentCompanyId = userStore.userInfo?.company?.id
  
  if (!currentUserId || !currentCompanyId) {
    console.warn('用户ID或公司ID不可用，跳过招标数据获取')
    return
  }
  
  isBidingLoading.value = true
  
  try {
    // 移除 page 和 page_size 参数，后端一次性返回所有数据
    const requestFilters = {
      ...(filters || {})
    }
    
    const apiResponse = await fetchBidingData(0, requestFilters)
    
    if (apiResponse && apiResponse.results) {
      // 适配新的简化数据结构，创建兼容的列表项
      const apiBidingResults: BidingListItemCompatible[] = apiResponse.results.map(item => ({
        _id: item._id,
        new_title: item.new_title,
        created_at: item.created_at,
        updated_at: item.updated_at,
        update_time: item.updated_at, // 使用 updated_at 作为 update_time
        recommend_score: item.recommend_score,
        submission_deadline: item.submission_deadline,
        country: item.country,
        recommend_reason: item.recommend_reason,
        // 添加向后兼容的嵌套结构
        recommendation: {
          recommend_score: item.recommend_score,
          recommend_reason: item.recommend_reason || ''
        },
        extraction: {
          basic_info: {
            country: item.country || '',
            submission_deadline: item.submission_deadline
          }
        }
      }))
      
      const agentType = apiResponse.agent_type
      const relevantDateStr = apiResponse.results[0]?.updated_at || new Date().toISOString()
      const totalCount = apiResponse.total || apiResponse.total_count || 0
      
      const bidingSummary: BidingAnalysisResult = {
        id: apiResponse.agent_type,
        report_type: agentType,
        report_name: apiResponse.agent_name,
        updated_at: relevantDateStr,
        status: 'completed',
        date: relevantDateStr,
        results: apiBidingResults,
        query: apiResponse.query,
        total: totalCount,
        total_count: totalCount
      }
      bidingResults.value = [bidingSummary]
    } else {
      bidingResults.value = []
    }
  } catch (error) {
    console.error('获取招标数据失败:', error)
    bidingResults.value = []
  } finally {
    isBidingLoading.value = false
  }
})

// 新增：获取详情数据的函数 - 适配 RESTful API
const loadBidingDetail = async (itemId: string): Promise<BidingAnalysisItem | null> => {
  // 检查缓存
  if (bidingDetailCache.value.has(itemId)) {
    const cachedDetail = bidingDetailCache.value.get(itemId)!
    currentBidingDetail.value = cachedDetail
    return cachedDetail
  }
  
  // 设置加载状态
  isBidingDetailLoading.value = true
  
  try {
    // 构造 RESTful API 路径
    const detailPath = `/formatted_report/v3/tender_awards/${itemId}/detail`
    
    // 创建动态 useGet hook 来调用 RESTful API
    const { execute: fetchDetailData } = useGet<BidingDetailApiResponse>(
      detailPath, {}, { immediate: false }
    )
    
    const response = await fetchDetailData(0, {})
    if (response) {
      // 缓存详情数据
      bidingDetailCache.value.set(itemId, response as any)
      currentBidingDetail.value = response as any
      
      return response as any
    }
  } catch (error) {
    console.error('获取招标详情失败:', error)
    ElMessage.error(t('biding.detail_load_error'))
  } finally {
    // 清除加载状态
    isBidingDetailLoading.value = false
  }
  
  return null
}

// 新增：清理详情缓存的函数
const clearBidingDetailCache = () => {
  bidingDetailCache.value.clear()
  currentBidingDetail.value = null
}

const loadStrategicData = withPermission('api.fetchStrategicData', async (page?: number) => {
  const currentUserId = userStore.userId
  const currentCompanyId = userStore.userInfo?.company?.id
  
  if (!currentUserId || !currentCompanyId) {
    console.warn('用户ID或公司ID不可用，跳过战略数据获取')
    return
  }
  
  if (hasRunningStrategicTask.value) {
    if (!isStrategicLoading.value) {
      isStrategicLoading.value = true
    }
    return
  }
  
  isStrategicLoading.value = true
    
  try {
    const response = await fetchStrategicData(0, {
      page: (page || strategicPagination.value.current_page).toString(),
      page_size: strategicPagination.value.page_size.toString()
    })
    
    if (response && response.results) {
      strategicPagination.value = {
        current_page: response.current_page || 1,
        page_size: response.page_size || 30,
        total: response.total || 0
      }

      // 使用新的数据结构映射
      const mappedResults: StrategicResult[] = response.results.map((item: StrategicNewsItem) => ({
        id: item.id,
        title: item.title,
        updated_at: item.updated_at,
        // 详情字段暂时为空，需要通过详情接口获取
        opportunity_details: '',
        entry_strategy: '',
        key_opportunities: [],
        timeline: '',
        url: '',
        update_at: item.updated_at,
        country: '',
        first_level_industry: '',
        second_level_industry: '',
        news_type: '',
        summary: item.title // 暂时使用 title 作为 summary
      }))

      const strategicData: StrategicReport = {
        id: 'STRATEGIC',
        report_type: 'STRATEGIC',
        agent_type: 'STRATEGIC',
        report_name: response.agent_name || t('agent.strategic_insight'),
        agent_name: response.agent_name || t('agent.strategic_insight'),
        updated_at: new Date().toISOString(),
        status: 'completed',
        date: new Date().toISOString(),
        is_loading: false,
        results: mappedResults,
        total: response.total || 0,
        current_page: response.current_page || 1,
        page_size: response.page_size || 30
      }
      strategicResults.value = [strategicData]
    } else {
      strategicResults.value = []
    }
  } catch (error) { 
    console.error('获取战略分析数据失败:', error)
    strategicResults.value = []
  } finally {
    isStrategicLoading.value = false
  }
})

const handleRefreshAgent = withPermission('api.executeRefreshAgent', async (report: Report) => {
  if (!isMonitorAgent(report)) {
    console.warn('Refresh requested for non-monitor agent:', report)
    return
  }

  if (!checkReportPermission(report)) {
    showPermissionError()
    return
  }

  try {
    await executeRefreshAgent(0, { id: report.report_type })
    ElMessage.success(t('common.success'))
  } catch (error) {
    console.error('Failed to trigger agent refresh:', error)
  }
})

const handleDeleteAgent = withPermission('api.batchDeleteReports', async (report: Report) => {
  if (!isMonitorAgent(report)) {
    console.error('只能删除预警类型的agent')
    return
  }
  
  if (!checkReportPermission(report)) {
    showPermissionError()
    return
  }
  
  try {
    await ElMessageBox.confirm(
      t('agent.delete_confirm'),
      t('common.warning'),
      { type: 'warning' }
    )
    
    const res = await batchDeleteReports(0, {
      ids: [report.id],
      source: 'agent'
    })
    
    if (res?.success) {
      ElMessage.success(t('common.delete_success'))
      refreshReports('agentDeleted')
    }
  } catch (err) {
    if (err !== 'cancel') {
      console.error('删除 Agent 失败:', err)
      ElMessage.error(t('common.delete_failed'))
    }
  }
})

function viewReport(report: Report) {
  const reportId = (report as any).id
  if (!reportId) {
    console.error('Report ID is missing, cannot navigate.')
    return
  }
  
  let routePath: string
  let reportTypeQuery: string | undefined = undefined
  const reportType = (report as any).report_type

  if (isMonitorAgent(report) || (isRunningAgent(report) && SPECIAL_AGENT_TYPES.includes(reportType || ''))) {
    routePath = `/dashboard/${reportId}`
    reportTypeQuery = reportType
  } else if (isStandardReport(report)) { 
    routePath = `/format-report/view/${reportId}`
  } 
  else {
    console.warn('Unknown report type for navigation:', report)
    return
  }
  
  const to = router.resolve({ 
    path: routePath, 
    query: reportTypeQuery ? { report_type: reportTypeQuery } : {} 
  })
  window.open(to.href, '_blank')
}

function handleTagClick(tagData: any) {
  const report = tagData.report
  const reportId = (report as any).id
  if (!report || !reportId) {
    console.error('Report is missing or invalid')
    return
  }
  
  let reportType = (report as any).report_type
  
  if (!reportType || !SPECIAL_AGENT_TYPES.includes(reportType)) {
    console.error('Report type is not a special agent type or missing for tag click')
    return
  }
  
  const to = router.resolve({
    path: `/dashboard/${reportId}`,
    query: {
      report_type: reportType,
      tag: tagData.tag
    }
  })
  window.open(to.href, '_blank')
}
  
function handleChangeReportTime(report: Report) {
  if (isMonitorAgent(report)) {
    // 添加权限检查
    const reportType = getReportTypeFromReport(report)
    if (reportType && SPECIAL_AGENT_TYPES.includes(reportType) && !canAccessNotifyFeature) {
      console.warn('用户没有通知功能权限，无法修改special agent时间间隔')
      ElMessage.warning(t('common.no_permission'))
      return
    }
    
    const agentForDialog: CompletedAgent = {
      id: report.id,
      agent_name: report.agent_name,
      report_name: report.report_name,
      frequency: report.frequency,
      report_type: report.report_type,
      summary: report.summary || '',
      risk_status: report.risk_status || 'unknown',
      updated_at: report.updated_at || new Date().toISOString(),
      status: report.status || 'completed',
      created_at: report.created_at || new Date().toISOString(),
      company: report.company || {} as any, 
      user: report.user || {} as any, 
      sync_type: report.sync_type || false, 
      language: report.language || 'unknown', 
      params: report.params || {} as any,
      date: report.date || new Date().toISOString(), 
      tools_results: report.tools_results || [], 
      progress: report.progress || 100,
    }
    handleChangeTime(agentForDialog)
  } else {
    console.warn('Time interval change requested for non-monitor agent:', report)
  }
}

function handleChangeTime(agent: CompletedAgent) {
  selectedAgentForTimeChange.value = agent
  currentFrequency.value = agent.frequency || 1
  showTimeIntervalDialog.value = true
}

async function handleTimeIntervalSubmit(data: { agent_id: string, frequency: number }) {
  try {
    await changeTime(0, {
      agent_id: data.agent_id,
      frequency: data.frequency
    })
    ElMessage.success(t('agent.update_time_success'))
    
    refreshReports('timeIntervalChanged')
  } catch (error) {
    console.error('Failed to update time interval:', error)
  } finally {
    showTimeIntervalDialog.value = false
  }
}

function handleDynamicFilterSelection(filterValue: string | null | '__MONITOR__') {
  console.log('Setting dynamic filter from sidebar:', filterValue)
  selectedReportTypeFilter.value = filterValue

  if (filterValue === null) { // "All Agents" selected in sidebar
    // 根据权限设置默认标签页
    if (canAccessNormalFeature) {
      activeTab.value = 'standard_feed'
    } else if (canAccessNotifyFeature) {
      activeTab.value = 'notify_insights'  
    } else if (canAccessSpecialAnalysisFeature) {
      activeTab.value = 'special_analysis'
    }
  } else if (filterValue === '__MONITOR__') {
    if (canAccessNotifyFeature) {
      activeTab.value = 'notify_insights'
    } else {
      console.warn('用户没有通知功能权限，无法切换到notify_insights标签页')
    }
  } else {
    // Check which category the filterValue (a report_type) belongs to
    if (SPECIAL_AGENT_TYPES.includes(filterValue)) {
      if (canAccessNotifyFeature) {
        activeTab.value = 'notify_insights'
      } else {
        console.warn('用户没有通知功能权限，无法切换到notify_insights标签页')
      }
    } else if (SPECIAL_BINDING_AGENT_TYPES_FOR_TAB.includes(filterValue)) {
      if (canAccessSpecialAnalysisFeature) {
        activeTab.value = 'special_analysis'
        // 根据具体的agent类型切换到对应的子标签页
        if (filterValue === 'STRATEGIC' && canAccessStrategicFeature) {
          specialAnalysisActiveTab.value = 'strategic_analysis'
        } else if (['TENDER', 'AWARDED'].includes(filterValue) && canAccessBusinessOpportunityFeature) {
          specialAnalysisActiveTab.value = 'biding_analysis'
        }
      } else {
        console.warn('用户没有专项分析功能权限，无法切换到special_analysis标签页')
      }
    } else {
      if (canAccessNormalFeature) {
        activeTab.value = 'standard_feed'
      } else {
        console.warn('用户没有标准功能权限，无法切换到standard_feed标签页')
      }
    }
  }
}

function handleSwitchTab(reportTypeGroup: string) { // reportTypeGroup could be 'SPECIAL_GROUP'
  console.log('Switching tab based on sidebar group click:', reportTypeGroup)
  // This function is called when the "Special Insights Group Button" in sidebar is clicked
  // It should always switch to the "专项分析" tab.
  if (canAccessSpecialAnalysisFeature) {
    activeTab.value = 'special_analysis'
    selectedReportTypeFilter.value = null // Clear specific filter when switching to a group tab
  } else {
    console.warn('用户没有专项分析功能权限，无法切换到special_analysis标签页')
    ElMessage.warning(t('common.no_permission'))
  }
}

// Helper function to sort and group reports by date
function groupAndSortReports(reportsToProcess: Report[]): Record<string, Report[]> {
  const sorted = [...reportsToProcess].sort((a, b) => {
    const aIsActive = isRunningAgent(a) || isMonitorAgent(a)
    const bIsActive = isRunningAgent(b) || isMonitorAgent(b)

    // Rule 1: Active agents always go after non-active ones.
    if (aIsActive && !bIsActive) {
      return 1 // a (active) goes later
    }
    if (!aIsActive && bIsActive) {
      return -1 // b (active) goes later, so a is earlier
    }

    // Rule 2: If both are active or both are non-active, sort by date ascending.
    const dateA = getReportDate(a)
    const dateB = getReportDate(b)

    // Handle invalid/zero dates to push them further down within their active/non-active group
    if (dateA === 0 && dateB !== 0) { return 1 }
    if (dateA !== 0 && dateB === 0) { return -1 }
    if (dateA === 0 && dateB === 0) { return 0 }

    return dateA - dateB // Sort: oldest first within their respective active/non-active group
  })

  const result: Record<string, Report[]> = {}
  const activeAgentsGroupKey = t('common.running_agents_group') // Special key for active agents

  for (const report of sorted) {
    let dateKey: string
    const isReportActive = isRunningAgent(report) || isMonitorAgent(report)

    if (isReportActive) {
      dateKey = activeAgentsGroupKey
    } else {
      const timestamp = getReportDate(report)
      if (timestamp === 0 || isNaN(new Date(timestamp).getTime())) {
        dateKey = t('common.unknown_date')
      } else {
        dateKey = new Date(timestamp).toLocaleDateString()
      }
    }

    if (!result[dateKey]) {
      result[dateKey] = []
    }
    result[dateKey].push(report)
  }
  
  const finalGroupedResult: Record<string, Report[]> = {}
  for (const key in result) {
    if (key !== activeAgentsGroupKey) {
      finalGroupedResult[key] = result[key]
    }
  }
  if (result[activeAgentsGroupKey]) {
    finalGroupedResult[activeAgentsGroupKey] = result[activeAgentsGroupKey]
  }

  return finalGroupedResult
}

const notifyFeedReportsGrouped = computed<Record<string, Report[]>>(() => {
  const reportsFromComposable = Object.values(groupedReports.value || {}).flat()
  let filteredReports: Report[] = reportsFromComposable.filter(report => {
    const type = getReportTypeFromReport(report)
    return type && SPECIAL_AGENT_TYPES.includes(type)
  })

  if (selectedReportTypeFilter.value !== null && activeTab.value === 'notify_insights') {
    if (selectedReportTypeFilter.value === '__MONITOR__') {
      // This case is already covered by SPECIAL_AGENT_TYPES if __MONITOR__ implies one of those.
      // If __MONITOR__ is a broader category, adjust filtering logic here.
      // For now, assume SPECIAL_AGENT_TYPES are the monitor types for this tab.
      filteredReports = filteredReports.filter(report => isMonitorAgent(report)) // Example refinement
    } else {
      filteredReports = filteredReports.filter(report => getReportTypeFromReport(report) === selectedReportTypeFilter.value)
    }
  }
  return groupAndSortReports(filteredReports)
})

const standardFeedReportsGrouped = computed(() => {
  const reportsFromComposable = Object.values(groupedReports.value || {}).flat()
  let filteredReports: Report[] = reportsFromComposable.filter(report => {
    const type = getReportTypeFromReport(report)
    if (!type) {return true} // Include if type is undefined, or decide to exclude
    return !SPECIAL_AGENT_TYPES.includes(type) && !SPECIAL_BINDING_AGENT_TYPES_FOR_TAB.includes(type)
  })

  if (selectedReportTypeFilter.value !== null && activeTab.value === 'standard_feed') {
    if (selectedReportTypeFilter.value === '__MONITOR__') {
      // __MONITOR__ filter shouldn't apply to standard feed unless logic changes
      // Or if a monitor agent can somehow be 'standard', which is unlikely with current categories
      filteredReports = [] // Or handle as an edge case
    } else {
      filteredReports = filteredReports.filter(report => getReportTypeFromReport(report) === selectedReportTypeFilter.value)
    }
  }
  return groupAndSortReports(filteredReports)
})

// 将分页信息传递给 StrategicRespon 组件
const strategicReportForDisplay = computed((): StrategicDisplayData | null => {
  // 添加权限检查
  if (!canAccessStrategicFeature) {
    return null
  }

  const strategicAgent = filteredAgents.value.find(agent => agent.report_type === 'STRATEGIC')
  const result = strategicResults.value.length > 0 ? strategicResults.value[0] : null
  const isRunning = hasRunningStrategicTask.value || isStrategicLoading.value || isAPIStrategicDataLoading.value

  if (result && strategicAgent) {
    const reportData = { ...result }
    if (!isRunning) {
      reportData.status = 'completed'
      reportData.is_loading = false
    }
    return {
      ...reportData,
      report_name: strategicAgent.name,
      avatar: strategicAgent.avatar,
      // 添加分页信息
      total: strategicPagination.value.total,
      current_page: strategicPagination.value.current_page,
      page_size: strategicPagination.value.page_size
    }
  }
  if (isRunning && strategicAgent) {
    return {
      id: 'STRATEGIC_LOADING',
      report_type: 'STRATEGIC',
      agent_type: 'STRATEGIC',
      report_name: strategicAgent.name,
      status: 'running',
      is_loading: true,
      date: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      results: [],
      avatar: strategicAgent.avatar
    }
  }

  return null
})

// 添加分页变化处理函数
async function handleStrategicPageChange(page: number) {
  // 添加权限检查
  if (!canAccessStrategicFeature) {
    console.log('用户没有战略分析功能权限，无法切换页面')
    return
  }
  
  try {
    await loadStrategicData(page)
  } catch (error) {
    console.error('切换战略分析页面失败:', error)
    ElMessage.error(t('workspace.messages.page_change_error'))
  }
}

// 添加数据加载状态管理和缓存策略
const dataLoadedFlags = ref({
  biding: false,
  strategic: false
})

// 添加数据缓存时间戳
const dataCacheTimestamps = ref({
  biding: 0,
  strategic: 0
})

// 数据新鲜度配置（毫秒）
const DATA_FRESHNESS_CONFIG = {
  // 数据缓存有效期：5分钟
  CACHE_DURATION: 5 * 60 * 1000,
  // 强制刷新间隔：30分钟
  FORCE_REFRESH_INTERVAL: 30 * 60 * 1000
}

// 检查数据是否需要刷新
const shouldRefreshData = (dataType: 'biding' | 'strategic', forceRefresh = false) => {
  const now = Date.now()
  const lastUpdate = dataCacheTimestamps.value[dataType]
  const timeSinceUpdate = now - lastUpdate

  // 强制刷新
  if (forceRefresh) {
    console.log(`${dataType} data: Force refresh requested`)
    return true
  }

  // 从未加载过
  if (!dataLoadedFlags.value[dataType] || lastUpdate === 0) {
    console.log(`${dataType} data: Never loaded, need to fetch`)
    return true
  }

  // 检查数据是否为空数组 - 防止接口报错后一直没有数据
  const currentResults = dataType === 'biding' ? bidingResults.value : strategicResults.value
  if (Array.isArray(currentResults) && currentResults.length === 0) {
    console.log(`${dataType} data: Current results are empty, need to refresh`)
    return true
  }

  // 检查缓存是否过期
  if (timeSinceUpdate > DATA_FRESHNESS_CONFIG.CACHE_DURATION) {
    console.log(`${dataType} data: Cache expired (${Math.round(timeSinceUpdate / 1000)}s ago), need refresh`)
    return true
  }

  console.log(`${dataType} data: Using cached data (updated ${Math.round(timeSinceUpdate / 1000)}s ago)`)
  return false
}

// 检查是否有新的任务完成需要刷新数据
const shouldRefreshBasedOnTaskCompletion = (newDashboardData: any[]) => {
  // 检查是否有刚完成的 TENDER 或 STRATEGIC 任务
  const completedTasks = (newDashboardData || []).filter(task => 
    (task.report_type === 'TENDER' || task.report_type === 'STRATEGIC') &&
    task.status === 'completed'
  )
  
  if (completedTasks.length > 0) {
    console.log('Detected newly completed tasks:', completedTasks.map(t => t.report_type))
    return true
  }
  
  return false
}

// 计算当前应该显示的子标签页
const shouldLoadBidingData = computed(() => {
  return canAccessBusinessOpportunityFeature && 
         activeTab.value === 'special_analysis' && 
         specialAnalysisActiveTab.value === 'biding_analysis'
})

const shouldLoadStrategicData = computed(() => {
  return canAccessStrategicFeature && 
         activeTab.value === 'special_analysis' && 
         specialAnalysisActiveTab.value === 'strategic_analysis'
})

// 优化的数据加载函数
const loadSpecialAnalysisData = async (options: {
  forceReload?: boolean
  taskCompletion?: boolean
  tabSwitch?: boolean
} = {}) => {
  if (!canAccessSpecialAnalysisFeature) {
    console.log('用户没有专项分析功能权限，跳过相关数据加载')
    return
  }

  const { forceReload = false, taskCompletion = false, tabSwitch = false } = options

  // 加载招投标数据
  if (shouldLoadBidingData.value) {
    const shouldRefresh = shouldRefreshData('biding', forceReload) || 
                         (taskCompletion && dashboardData.value && shouldRefreshBasedOnTaskCompletion(dashboardData.value))
    
    if (shouldRefresh) {
      console.log('Loading biding data...', { forceReload, taskCompletion, tabSwitch })
      try {
        await loadBidingData()
        dataLoadedFlags.value.biding = true
        dataCacheTimestamps.value.biding = Date.now()
      } catch (error) {
        console.error('Failed to load biding data:', error)
      }
    }
  }

  // 加载战略数据
  if (shouldLoadStrategicData.value) {
    const shouldRefresh = shouldRefreshData('strategic', forceReload) || 
                         (taskCompletion && dashboardData.value && shouldRefreshBasedOnTaskCompletion(dashboardData.value))
    
    if (shouldRefresh) {
      console.log('Loading strategic data...', { forceReload, taskCompletion, tabSwitch })
      try {
        await loadStrategicData()
        dataLoadedFlags.value.strategic = true
        dataCacheTimestamps.value.strategic = Date.now()
      } catch (error) {
        console.error('Failed to load strategic data:', error)
      }
    }
  }
}

// 智能的 dashboardData 监听器
watch(dashboardData, (newData, oldData) => {
  if (!canAccessSpecialAnalysisFeature) {
    return
  }
  
  // 检查是否有正在运行的任务，如果有则处理状态
  const strategicTasks = (newData || []).filter(task => task.report_type === 'STRATEGIC')
  const hasRunningStrategicTask = strategicTasks.some(task => 
    ['running', 'pending'].includes(task.status?.toLowerCase() || '')
  )
  
  if (hasRunningStrategicTask) {
    isStrategicLoading.value = true
    return
  }
  
  // 检查是否有任务刚完成，如果有则可能需要刷新
  const hasNewlyCompletedTask = shouldRefreshBasedOnTaskCompletion(newData)
  
  // 只在有新完成的任务或者首次加载时才加载数据
  if (hasNewlyCompletedTask || !oldData) {
    loadSpecialAnalysisData({ 
      taskCompletion: hasNewlyCompletedTask 
    })
  }
}, { deep: true })

watch(hasRunningBidingTask, (isRunning) => {
  // 添加权限检查
  if (!canAccessBusinessOpportunityFeature) {
    console.log('用户没有商业机会功能权限，跳过轮询')
    return
  }
  
  if (isRunning) {
    if (bidingDataPollInterval.value) {
      clearInterval(bidingDataPollInterval.value)
    }
    console.log('[Workspace/Index] Running biding task detected. Starting 1-minute polling for loadBidingData.')
    loadBidingData()
    bidingDataPollInterval.value = setInterval(() => {
      console.log('[Workspace/Index] Polling: calling loadBidingData for TENDER.')
      loadBidingData()
    }, 60000)
  } else {
    if (bidingDataPollInterval.value) {
      console.log('[Workspace/Index] No running biding task. Stopping polling for loadBidingData.')
      clearInterval(bidingDataPollInterval.value)
      bidingDataPollInterval.value = null
    }
  }
})

async function handleBidingFilterApply(appliedFilters: Record<string, any>) {
  // 添加权限检查
  if (!canAccessBusinessOpportunityFeature) {
    console.log('用户没有商业机会功能权限，无法应用过滤器')
    ElMessage.warning(t('common.no_permission'))
    return
  }
  
  console.log('Applying biding filters from index.vue:', appliedFilters)
  try {
    await loadBidingData(appliedFilters)
  } catch (error) {
    console.error('Failed to load biding data with filters:', error)
    ElMessage.error(t('workspace.messages.filter_error'))
  }
}

const agentSidebarProps = computed(() => ({
  agents: agentsToListIndividually.value,
  selectedAgent: selectedReportTypeFilter.value,
  activeTab: activeTab.value,
}))

// 初始化数据加载：确保用户刷新页面时能正确加载当前标签页的数据
onMounted(() => {
  console.log('Component mounted, initializing data for current tab:', activeTab.value)

  // 主动触发当前标签页的数据加载，特别是当只有一个标签页时
  switch (activeTab.value) {
  case 'standard_feed':
    // 标准信息流数据由 useReports composable 自动处理
    console.log('Standard feed tab active, data handled by useReports')
    break
  case 'notify_insights':
    // 通知洞察数据刷新
    console.log('Notify insights tab active, refreshing reports')
    refreshReports('mounted')
    break
  case 'special_analysis':
    // 专项分析数据加载
    console.log('Special analysis tab active, loading special analysis data')
    if (canAccessSpecialAnalysisFeature) {
      loadSpecialAnalysisData()
    }
    break
  default:
    console.log('Unknown tab active:', activeTab.value)
  }
})

onUnmounted(() => {
  if (bidingDataPollInterval.value) {
    clearInterval(bidingDataPollInterval.value)
    console.log('[Workspace/Index] Component unmounted. Cleared biding data polling interval.')
  }
})

// 权限检查工具函数
const checkReportPermission = (report: Report): boolean => {
  const reportType = getReportTypeFromReport(report)
  if (reportType && SPECIAL_AGENT_TYPES.includes(reportType)) {
    return canAccessNotifyFeature
  }
  if (reportType === 'STRATEGIC') {
    return canAccessStrategicFeature
  }
  if (reportType && ['TENDER', 'AWARDED'].includes(reportType)) {
    return canAccessBusinessOpportunityFeature
  }
  return true
}

const showPermissionError = () => {
  ElMessage.warning(t('common.no_permission'))
}

// 处理主标签页变化 - 智能缓存版本
const handleMainTabChange = (tab: any) => {
  try {
    const tabKey = tab.value
    console.log('Main tab changed to:', tabKey)

    switch (tabKey) {
    case 'standard_feed':
      break
    case 'notify_insights':
      refreshReports('tabChanged')
      break
    case 'special_analysis':
      // 标签页切换时优先使用缓存数据
      loadSpecialAnalysisData({ tabSwitch: true })
      break
    default:
      console.warn('Unknown main tab:', tabKey)
    }
  } catch (error) {
    console.error('Error handling main tab change:', error)
  }
}

// 处理专项分析子标签页变化 - 智能缓存版本
const handleSpecialTabChange = (subTab: any) => {
  try {
    const tabKey = subTab.value
    console.log('Special analysis tab changed to:', tabKey)

    // 检查当前切换到的标签页是否有数据，如果没有则强制刷新
    let needsForceRefresh = false

    if (tabKey === 'biding_analysis') {
      // 检查招投标数据是否为空
      if (!bidingResults.value || bidingResults.value.length === 0) {
        console.log('Biding results are empty, forcing refresh')
        needsForceRefresh = true
      }
    } else if (tabKey === 'strategic_analysis') {
      // 检查战略分析数据是否为空
      if (!strategicResults.value || strategicResults.value.length === 0) {
        console.log('Strategic results are empty, forcing refresh')
        needsForceRefresh = true
      }
    }

    // 根据数据状态决定是否强制刷新
    if (needsForceRefresh) {
      loadSpecialAnalysisData({ forceReload: true, tabSwitch: true })
    } else {
      // 子标签页切换时优先使用缓存，除非数据为空
      loadSpecialAnalysisData({ tabSwitch: true })
    }
  } catch (error) {
    console.error('Error handling special tab change:', error)
  }
}

// 获取单个标签页的标题
const getSingleTabTitle = () => {
  if (!permissionsLoaded.value) {
    return t('common.loading')
  }
  if (canAccessNormalFeature) {
    return t('workspace.report_feed')
  }
  if (canAccessNotifyFeature) {
    return t('workspace.special_reports')
  }
  if (canAccessBindingFeature) {
    return t('workspace.binding_analysis')
  }
  return ''
}

const availableTabsCount = computed(() => {
  // 权限未加载完成时返回-1，避免过早计算
  if (!permissionsLoaded.value) {
    return -1
  }
  
  let count = 0
  if (canAccessNormalFeature) {
    count++
  }
  if (canAccessNotifyFeature) {
    count++
  }
  if (canAccessSpecialAnalysisFeature) {
    count++
  }
  return count
})

// 可用标签页配置
const availableTabsConfig = computed(() => {
  // 权限未加载完成时返回空数组
  if (!permissionsLoaded.value) {
    return []
  }
  
  const tabs = []

  if (canAccessNormalFeature) {
    tabs.push({
      value: 'standard_feed',
      label: t('agent.filter.normal'),
      disabled: false
    })
  }

  if (canAccessNotifyFeature) {
    tabs.push({
      value: 'notify_insights',
      label: t('agent.filter.notify'),
      disabled: false
    })
  }

  if (canAccessSpecialAnalysisFeature) {
    tabs.push({
      value: 'special_analysis',
      label: t('agent.filter.binding'),
      disabled: false
    })
  }

  return tabs
})

// 专项分析子标签页配置
const specialAnalysisSubTabs = computed(() => {
  // 权限未加载完成时返回空数组
  if (!permissionsLoaded.value) {
    return []
  }
  
  const subTabs = []
  
  if (canAccessBusinessOpportunityFeature) {
    subTabs.push({
      value: 'biding_analysis',
      label: t('workspace.binding_analysis'),
      loading: hasRunningBidingTask.value || isAPIBidingDataLoading.value,
      disabled: false
    })
  }
  
  if (canAccessStrategicFeature) {
    subTabs.push({
      value: 'strategic_analysis',
      label: t('workspace.strategic_analysis'),
      loading: hasRunningStrategicTask.value || isAPIStrategicDataLoading.value || isStrategicLoading.value,
      disabled: false
    })
  }
  
  return subTabs
})

// 监听权限加载状态，确保在权限加载完成后设置正确的标签页
watch(permissionsLoaded, (loaded) => {
  if (loaded) {
    // 权限加载完成后，确保 activeTab 是有效的
    const hasValidTab = availableTabsConfig.value.some(tab => tab.value === activeTab.value)
    if (!hasValidTab && availableTabsConfig.value.length > 0) {
      activeTab.value = availableTabsConfig.value[0].value
      console.log('Reset activeTab to first available tab:', activeTab.value)
    }
  }
})

// 监听子标签页变化，自动调整当前选中的子标签页
watch(specialAnalysisSubTabs, (newSubTabs) => {
  // 权限未加载完成时不执行任何操作
  if (!permissionsLoaded.value) {
    return
  }
  
  if (newSubTabs.length === 0) {
    // 如果没有可用的子标签页，保持当前值不变
    return
  }
  
  // 检查当前选中的子标签页是否仍然有效
  const currentTabExists = newSubTabs.some(tab => tab.value === specialAnalysisActiveTab.value)
  
  if (!currentTabExists) {
    // 如果当前选中的子标签页不再有效，切换到第一个可用的子标签页
    specialAnalysisActiveTab.value = newSubTabs[0].value
    console.log('Auto-switched to first available special analysis sub tab:', newSubTabs[0].value)
  }
}, { immediate: true })

// 添加手动刷新方法（可以在UI中提供刷新按钮）
const forceRefreshCurrentData = async () => {
  console.log('Force refreshing current data...')
  await loadSpecialAnalysisData({ forceReload: true })
  ElMessage.success(t('workspace.messages.data_refreshed'))
}

</script>
        
<style scoped>
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-in {
  animation: fadeIn 0.3s ease-out forwards;
}

:deep(.scrollbar) {
  width: 6px !important;
}

:deep(.thumb) {
  background-color: rgba(156, 163, 175, 0.5) !important;
  border-radius: 3px !important;
}

div[class*="overflow-y-auto"] {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

div[class*="overflow-y-auto"]::-webkit-scrollbar {
  width: 4px;
}

div[class*="overflow-y-auto"]::-webkit-scrollbar-track {
  background: transparent;
}

div[class*="overflow-y-auto"]::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }
}

.break-words {
  word-break: break-word;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

:deep(.card) {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  overflow: hidden;
}

:deep(.card:hover) {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

:deep(.button) {
  transition: all 0.2s ease;
}

:deep(.button:hover) {
  transform: translateY(-1px);
}

:deep(.card[class*="bg-red-50"]) {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

:deep(.card[class*="bg-amber-50"]) {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

:deep(.card[class*="bg-green-50"]) {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-indicator-pulse {
  /* animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite; */
  /* Using animate-ping from Tailwind directly in the template is simpler */
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}
</style>