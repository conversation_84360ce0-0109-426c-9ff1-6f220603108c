import type { App } from 'vue'
import * as Sentry from '@sentry/vue'
import { useConfig } from '@/hooks/useConfig'
import { BusinessError } from '@/hooks/useHttp'
import { CommonError } from '@/utils/commonError'

export default {
  install(app: App) {
    const config = useConfig()
    if (['dev', 'sit'].some(env => config.VITE_APP_ENV.startsWith(env))) {
      return
    }
    // #if !PRIVATE
    // 定义多种采样率，用于在不同场景做性能监控时使用
    const normalTraceSampleRate = 0.2
    Sentry.init({
      app,
      dsn: config.VITE_APP_SENTRY_DSN,
      tunnel: '/bugs',
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.reportingObserverIntegration({ types: ['crash'] })
      ],
      maxBreadcrumbs: 50,
      // Performance Monitoring
      tracesSampleRate: 1.0, //  Capture 100% of the transactions
      tracesSampler: () => {
        // 自定义采样率，对于不同场景的可以在这里
        return normalTraceSampleRate
      },
      // Session Replay
      replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
      beforeSend(event, hint) {
        const originalException = hint.originalException as Error | string | undefined | boolean
        const isCancel = typeof originalException === 'string' && originalException === 'cancel'
        const isClose = typeof originalException === 'string' && originalException === 'close'
        const isFormValidate = originalException === false
        const isIgnoreError = originalException instanceof BusinessError || originalException instanceof CommonError
        const isResizeObserverLoop = originalException instanceof Error && /ResizeObserver loop limit exceeded/gi.test(originalException?.message || `${originalException}`)
        const isChromeExtension = originalException instanceof Error && /chrome-extension/gi.test(originalException?.message || `${originalException}`)
        // 忽略confirm取消、关闭；表单验证失败、业务忽略错误
        if (isCancel || isFormValidate || isClose || isIgnoreError || isResizeObserverLoop || isChromeExtension) {
          return null
        }
        // 网络错误，记录URL
        if (originalException instanceof Error && /Network Error/gi.test(originalException?.message || '')) {
          originalException.message += `: ${(originalException as any).config?.url || ''}`
          if (event.exception?.values?.[0]) {
            event.exception.values[0].value = originalException.message
          }
        }
        return event
      }
    })
    return
    // 私有环境不上报错误
    // #endif
  }
}
