import { ref, unref, nextTick } from 'vue'
import type { MaybeRef } from '@vueuse/core'

export interface SmoothScrollOptions {
  duration?: number
  offset?: number
  container?: MaybeRef<HTMLElement | null | undefined>
  highlight?: boolean
  highlightClass?: string
  highlightDuration?: number
  onComplete?: () => void
}

/**
 * Enhanced smooth scrolling hook with element highlighting
 */
export function useSmoothScroll(defaultOptions: SmoothScrollOptions = {}) {
  const isScrolling = ref(false)
  
  /**
   * 滚动到指定元素，并可选择高亮显示
   */
  const scrollToElement = async (
    target: MaybeRef<HTMLElement | string | null | undefined>,
    options: SmoothScrollOptions = {}
  ) => {
    const mergedOptions = { ...defaultOptions, ...options }
    const { 
      offset = 0,
      duration = 500,
      container,
      highlight = true,
      highlightClass = 'highlight-element',
      highlightDuration = 3000,
      onComplete
    } = mergedOptions
    
    await nextTick()
    
    // 解析目标元素
    let element: HTMLElement | null = null
    const targetValue = unref(target)
    
    if (typeof targetValue === 'string') {
      element = document.querySelector(targetValue)
    } else if (targetValue instanceof HTMLElement) {
      element = targetValue
    }
    
    if (!element) {return false}
    
    // 获取滚动容器
    const scrollContainer = unref(container) || document.documentElement
    
    // 计算滚动位置
    const elementRect = element.getBoundingClientRect()
    const containerRect = scrollContainer.getBoundingClientRect()
    
    // 计算目标位置，确保元素在容器中居中显示
    let targetPosition
    
    if (scrollContainer === document.documentElement || scrollContainer === document.body) {
      // 如果滚动容器是整个页面，计算使元素居中的位置
      const windowCenterY = window.innerHeight / 2
      const elementCenterY = elementRect.height / 2
      targetPosition = window.scrollY + elementRect.top - windowCenterY + elementCenterY + offset
    } else {
      // 如果是自定义容器，计算使元素在容器中居中的位置
      const containerCenterY = containerRect.height / 2
      const elementCenterY = elementRect.height / 2
      targetPosition = scrollContainer.scrollTop + (elementRect.top - containerRect.top) - containerCenterY + elementCenterY + offset
    }
    
    // 开始滚动
    isScrolling.value = true
    
    // 实现平滑滚动
    const startPosition = scrollContainer.scrollTop
    const distance = targetPosition - startPosition
    const startTime = performance.now()
    
    const animateScroll = (currentTime: number) => {
      const elapsedTime = currentTime - startTime
      const progress = Math.min(elapsedTime / duration, 1)
      
      // 使用 easeInOutQuad 缓动函数
      const easeProgress = progress < 0.5 
        ? 2 * progress * progress 
        : 1 - Math.pow(-2 * progress + 2, 2) / 2
      
      scrollContainer.scrollTop = startPosition + distance * easeProgress
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll)
      } else {
        // 滚动完成
        isScrolling.value = false
        
        // 添加高亮效果
        if (highlight && element) {
          element.classList.add(highlightClass)
          
          setTimeout(() => {
            element?.classList.remove(highlightClass)
          }, highlightDuration)
        }
        
        onComplete?.()
      }
    }
    
    requestAnimationFrame(animateScroll)
    return true
  }
  
  /**
   * 获取元素并滚动到由 data-id 属性标识的元素
   */
  const scrollToElementById = async (
    id: string, 
    options: SmoothScrollOptions = {}
  ) => {
    const element = document.querySelector(`[data-id="${id}"]`)
    if (element) {
      return await scrollToElement(element as HTMLElement, options)
    }
    return false
  }
  
  return {
    isScrolling,
    scrollToElement,
    scrollToElementById
  }
}

// 导出默认的高亮样式
export const addHighlightStyles = () => {
  const styleId = 'smooth-scroll-highlight-styles'
  
  // 避免重复添加样式
  if (document.getElementById(styleId)) {return}
  
  const style = document.createElement('style')
  style.id = styleId
  style.innerHTML = `
    .highlight-element {
      animation: pulse-highlight 3s ease-in-out;
    }
    
    @keyframes pulse-highlight {
      0%, 100% {
        background-color: transparent;
        transform: scale(1);
      }
      15% {
        background-color: rgba(59, 130, 246, 0.2);
        transform: scale(1.02);
      }
      30% {
        background-color: rgba(59, 130, 246, 0.15);
        transform: scale(1.01);
      }
      45% {
        background-color: rgba(59, 130, 246, 0.1);
        transform: scale(1);
      }
    }
  `
  
  document.head.appendChild(style)
} 