<template>
  <div
    :class="[
      'relative p-8 rounded-3xl transition-all duration-500 hover:scale-[1.02] flex flex-col',
      plan.type === currentPlan
        ? 'bg-linear-to-br from-blue-50 via-white to-blue-100 ring-2 ring-blue-600/20 shadow-2xl'
        : 'bg-white hover:shadow-2xl ring-1 ring-gray-200/60',
    ]">
    <!-- Current Plan Corner Label -->
    <div
      v-if="currentPlan && plan.type === currentPlan"
      :class="$style['corner-ribbon']">
      {{ $t("setting.plans.currentPlan") }}
    </div>

    <!-- Plan Content -->
    <div class="flex-1 flex flex-col">
      <div class="mb-8">
        <h3 class="text-2xl font-bold bg-linear-to-br from-blue-700 to-blue-500 bg-clip-text text-transparent">
          {{ $t(`setting.plans.${plan.type}`) }}
        </h3>

        <!-- Plan Descriptions -->
        <div class="mt-4 space-y-3">
          <p
            v-for="(desc, index) in plan.description"
            :key="index"
            class="text-sm leading-relaxed text-gray-600">
            {{ desc }}
          </p>
        </div>

        <!-- Price Display -->
        <div class="mt-8 flex items-baseline">
          <span class="text-6xl font-extrabold tracking-tight bg-linear-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
            ￥{{ getPlanPrice }}
          </span>
          <span class="ml-2 text-lg font-medium text-gray-500">
            /{{ billingPeriodText }}
          </span>
          <span 
            v-if="selectedBillingPeriod === 'annually' && plan.type !== 'free'"
            class="ml-2 inline-flex items-center rounded-full bg-green-50 px-2 py-0.5 text-xs font-medium text-green-600">
            {{ plan.type === 'pro' ? '-10%' : '-15%' }}
          </span>
        </div>
      </div>

      <!-- Features List -->
      <ul class="space-y-5 mb-8">
        <li
          v-for="(feature, idx) in plan.features"
          :key="idx"
          class="flex items-start group">
          <!-- <el-icon class="text-blue-600 h-5 w-5 shrink-0 mt-1 transition-all duration-300 group-hover:scale-110 group-hover:rotate-12"> -->
          <Icon icon="mdi:check" class="text-blue-600 h-5 w-5 shrink-0 mt-1 transition-all duration-300 group-hover:scale-110 group-hover:rotate-12"/>
          <!-- </el-icon> -->
          <span class="ml-3 text-sm text-gray-600 group-hover:text-gray-900 transition-colors duration-300">{{ feature }}</span>
        </li>
      </ul>

      <!-- CTA Button -->
      <div class="mt-auto" v-if="currentPlan">
        <div
          v-if="plan.type === currentPlan"
          class="w-full flex items-center justify-center text-white font-medium text-center h-[48px] rounded-2xl bg-linear-to-r from-blue-600 via-blue-500 to-blue-400 shadow-lg shadow-blue-500/20">
          {{ $t("setting.plans.currentPlan") }}
        </div>
        <button
          v-else
          class="w-full h-[48px] rounded-2xl font-medium text-white transition-all duration-500 hover:shadow-xl hover:shadow-blue-500/20 bg-linear-to-r from-blue-600 via-blue-500 to-blue-400 hover:from-blue-500 hover:via-blue-400 hover:to-blue-300 relative overflow-hidden group"
          @click="handlePlanSelect">
          <span class="relative z-10">{{ $t("setting.plans.upgrade") }}</span>
          <div class="absolute inset-0 bg-linear-to-r from-blue-500 via-blue-400 to-blue-300 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import type { PlanConfig, PlanType } from '@/pages/settings/plans/type'

const { t } = useI18n()

const props = defineProps<{
  plan: PlanConfig
  currentPlan?: string
  selectedBillingPeriod: 'monthly' | 'annually'
}>()

const emit = defineEmits<{
  'select': [plan: PlanConfig]
}>()

const getPlanPrice = computed(() => {
  return props.selectedBillingPeriod === 'monthly'
    ? props.plan.monthlyPrice
    : props.plan.yearlyPrice
})

const billingPeriodText = computed(() => {
  return props.selectedBillingPeriod === 'monthly'
    ? t('setting.plans.billingPeriod.monthly')
    : t('setting.plans.billingPeriod.annually')
})

const handlePlanSelect = () => {
  emit('select', props.plan)
}
</script>

<style lang="scss" module>
.corner-ribbon {
  position: absolute;
  color: white;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  background: linear-gradient(135deg, #4466BC, #60A5FA);
  padding: 8px 40px;
  right: -35px;
  top: 20px;
  transform: rotate(45deg);
  transform-origin: center;
  text-align: center;
  width: 140px;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  overflow: hidden;
  z-index: 1;
  
  &::before {
    display: none;
  }
}
</style> 