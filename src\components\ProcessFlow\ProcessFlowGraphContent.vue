<template>
  <div ref="container" class="w-full h-full"/>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import G6 from '@antv/g6'
import type { Graph, GraphData } from '@antv/g6'

interface Props {
  data: GraphData
}

const props = defineProps<Props>()
const container = ref<HTMLDivElement>()
const graph = ref<Graph>()

const initGraph = () => {
  if (!container.value) {return}

  graph.value = new G6.Graph({
    container: container.value,
    width: container.value.scrollWidth,
    height: container.value.scrollHeight,
    fitView: true,
    fitViewPadding: 30,
    animate: true,
    groupByTypes: false,
    modes: {
      default: [
        'drag-canvas',
        {
          type: 'zoom-canvas',
          sensitivity: 1.5,
          minZoom: 0.5,
          maxZoom: 2
        }
      ]
    },
    layout: {
      type: 'dagre',
      sortByCombo: true,
      ranksep: 30,
      nodesep: 50,
      controlPoints: true
    },
    defaultNode: {
      size: [150, 40],
      type: 'rect',
      style: {
        radius: 8,
        lineWidth: 1.5,
        fill: '#fff'
      },
      anchorPoints: [[0.5, 0], [0.5, 1]]
    },
    defaultEdge: {
      type: 'line',
      style: {
        stroke: '#93a8ff', // --brand-color-4
        lineWidth: 1.5,
        endArrow: {
          path: G6.Arrow.triangle(8, 10),
          fill: '#93a8ff', // --brand-color-4
          stroke: '#93a8ff' // --brand-color-4
        }
      }
    },
    defaultCombo: {
      type: 'rect',
      style: {
        fillOpacity: 0.1,
        radius: 8,
        padding: [30, 20],
        lineWidth: 1.5
      }
    }
  })

  // 设置节点样式
  props.data.nodes?.forEach((node) => {
    switch (node.comboId) {
    case 'search_combo':
      node.style = {
        fill: '#f0f2ff', // --brand-color-1
        stroke: '#93a8ff' // --brand-color-4
      }
      node.labelCfg = {
        style: {
          fill: '#4466BC', // --brand-color-7
          fontSize: 13,
          fontWeight: 600
        }
      }
      break
    case 'filter_combo':
      node.style = {
        fill: '#f2f1f5', // --purpleGray-2
        stroke: '#b7b6cd' // --purpleGray-6
      }
      node.labelCfg = {
        style: {
          fill: '#575872', // --purpleGray-8
          fontSize: 13,
          fontWeight: 600
        }
      }
      break
    case 'output_combo':
      node.style = {
        fill: '#e2f5e1', // --state-done-bg
        stroke: '#20a149' // --state-done-color-1
      }
      node.labelCfg = {
        style: {
          fill: '#1e873f', // --state-done-color-2
          fontSize: 13,
          fontWeight: 600
        }
      }
      break
    }
  })

  graph.value.data(props.data)
  graph.value.render()
}

const handleResize = () => {
  if (graph.value && container.value) {
    graph.value.changeSize(
      container.value.scrollWidth,
      container.value.scrollHeight
    )
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  graph.value?.destroy()
})

// 监听数据变化重新渲染
watch(() => props.data, () => {
  if (graph.value) {
    graph.value.data(props.data)
    graph.value.render()
  }
}, { deep: true })

defineExpose({
  initGraph
})
</script> 