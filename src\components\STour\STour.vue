<template>
  <el-tour
    v-model="isVisible"
    :z-index="2000"
    :current="currentStep"
    :mask="computedMask"
    :type="type"
    v-bind="$attrs"
    @close="handleClose"
    @finish="handleFinish"
    @change="handleStepChange">
    <el-tour-step
      v-for="step in steps"
      :key="step.id"
      :target="step.target"
      :title="t(step.title)"
      :description="t(step.description)"
      :placement="step.placement"
      :mask="step.mask"
      v-bind="step.theme || {}">
      <template v-if="step.customContent">
        <component :is="step.customContent"/>
      </template>
      <template v-else>
        {{ t(step.description) }}
      </template>
      
      <template #indicators v-if="$slots.indicators">
        <slot name="indicators" :current="currentStep" :total="steps.length"/>
      </template>
    </el-tour-step>
  </el-tour>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { ElTour, ElTourStep } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useTour } from './composables/useTour'
import type { TourInstance, ModuleTourConfig } from './types'
import { useVModel } from '@vueuse/core'

const { t } = useI18n()

const props = withDefaults(defineProps<{
  visible: boolean
  config?: ModuleTourConfig
}>(), {
  visible: false,
  config: undefined
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'finish': []
}>()

const visible = useVModel(props, 'visible', emit)

const {
  isVisible,
  currentStep,
  steps,
  computedMask,
  type,
  handleClose,
  handleFinish,
  handleStepChange,
  updateConfig
} = useTour({
  visible,
  onFinish: () => emit('finish')
})

// 监听 config 变化，更新配置
watch(
  () => props.config,
  (newConfig) => {
    if (newConfig) {
      updateConfig(newConfig)
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.el-tour {
  :deep(.el-tour__step) {
    --el-tour-bg-color: var(--primary-color);
    --el-tour-text-color: var(--font-color-5);
    border-radius: var(--radius-default);
    box-shadow: var(--shadow-dialog);
    
    // 添加最大宽度限制
    max-width: min(90vw, 400px);
    
    // 确保内容可滚动
    .el-tour__content {
      max-height: 60vh;
      overflow-y: auto;
    }

    .el-tour__title {
      font-size: var(--size-font-5);
      font-weight: var(--weight-font-Semiblod);
      
      // 小屏幕下调整字号
      @media screen and (max-width: 768px) {
        font-size: var(--size-font-6);
      }
    }

    .el-tour__description {
      font-size: var(--size-font-7);
      color: var(--font-color-2);
      
      // 小屏幕下调整字号和行高
      @media screen and (max-width: 768px) {
        font-size: var(--size-font-8);
        line-height: 1.4;
      }
    }

    .el-tour__buttons {
      margin-top: var(--m-default);
      
      // 小屏幕下调整按钮间距
      @media screen and (max-width: 768px) {
        margin-top: var(--m-sm);
      }
    }
  }
}
</style>
