<template>
  <ul :class="$style.root">
    <li
      v-for="(item, index) of levelRequiredValue"
      :key="index"
      :class="[
        $style['item'],
        { 'is-focus': focusable || !hasChange },
        { 'is-active': item.active },
      ]">
      <em :class="[$style['check-password-level-check'],'check-password-level-check']"/>
      {{ item.text }}
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref, watch, type PropType } from 'vue'
import { useVModel } from '@vueuse/core'
import { isEqual } from 'lodash-es'
interface LevelItem {
  text: string;
  active: boolean;
  check: (password: string) => boolean;
}

// 定义 props 类型
const props = defineProps({
  levelRequired: {
    type: Array as PropType<LevelItem[]>,
    required: true,
  },
  password: {
    type: String,
    default: null,
  },
  focusable: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emit = defineEmits(['check'])
const hasChange = ref<boolean>(false)

watch(()=>props.levelRequired, (newVal, oldVal)=>{
  if (!isEqual(newVal, oldVal)){
    hasChange.value = true
  }
})

// 初始 requiredCheck 状态
const requiredCheck = ref(false)
const levelRequiredValue = useVModel(props, 'levelRequired', emit)
// 监视密码变化并验证
watch(
  () => props.password,
  (newPassword) => {
    requiredCheck.value = props.levelRequired.every((item) =>
      item.check(newPassword || '')
    )
    emit('check', requiredCheck.value)
  },
  { immediate: true }
)

const check = (password: string) => {
  requiredCheck.value = props.levelRequired.every((item) =>
    item.check(password)
  )
  return requiredCheck.value
}

defineExpose({
  check,
})
</script>

<style lang="scss" module>
.root {
  color: #f5222d;
  font-size: var(--size-font-9);
  font-weight: var(--weight-font-Regular);
  list-style-type: none;
  padding: 0;
  display: flex;
  line-height: 20px;
  .item {
    display: flex;
    align-items: center;
    white-space: nowrap;
    &:first-child{
      margin-left: 0;
    }
    margin-left: var(--p-larger);
  }
  .check-password-level-check {
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--m-mini);
    background: #f5222d;
  }
}
</style>
<style lang="scss" scope>
.is-focus {
  color: var(--font-color-2);
  .check-password-level-check {
    background: var(--font-color-3);
  }
}
.is-active {
  color: var(--font-color-1);
  .check-password-level-check {
    background: #00ca15;
  }
}
</style>
