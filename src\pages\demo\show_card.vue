<script setup lang="ts">
import EvervaultCard from './card.vue'
// import Icon from './Icon.vue'
</script>

<template>
  <div class="border border-black/[0.2] dark:border-white/[0.2] flex flex-col items-start max-w-sm mx-auto p-4 relative h-[30rem]">
    <!-- Corner Icons -->
    <Icon className="absolute h-6 w-6 -top-3 -left-3 dark:text-white text-black"/>
    <Icon className="absolute h-6 w-6 -bottom-3 -left-3 dark:text-white text-black"/>
    <Icon className="absolute h-6 w-6 -top-3 -right-3 dark:text-white text-black"/>
    <Icon className="absolute h-6 w-6 -bottom-3 -right-3 dark:text-white text-black"/>

    <!-- Card Component -->
    <EvervaultCard text="hover"/>

    <!-- Instruction Text -->
    <h2 class="dark:text-white text-black mt-4 text-sm font-light">
      Hover over this card to reveal an awesome effect. Running out of copy here.
    </h2>
    
    <!-- Button -->
    <p class="text-sm border font-light dark:border-white/[0.2] border-black/[0.2] rounded-full mt-4 text-black dark:text-white px-2 py-0.5">
      Watch me hover
    </p>
  </div>
</template>