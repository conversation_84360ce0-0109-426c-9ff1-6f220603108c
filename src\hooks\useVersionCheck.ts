import { onMounted, onUnmounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { useConfig } from '@/hooks/useConfig'

export function useVersionCheck(interval = 5 * 60 * 1000) {
  const config = useConfig()
  const { locale } = useI18n()
  let timer: NodeJS.Timeout

  const checkVersion = async () => {
    try {
      const res = await fetch('/version.json?t=' + Date.now())
      const data = await res.json()
      
      if (data.version !== config.VITE_APP_VERSION) {
        ElMessageBox.confirm(
          locale.value === 'chinese' 
            ? '发现新版本,请刷新页面获取最新内容'
            : 'New version available, please refresh the page',
          locale.value === 'chinese' ? '提示' : 'Notice'
        ).then(() => {
          window.location.reload()
        })
      }
    } catch (err) {
      console.error('Version check failed:', err)
    }
  }

  onMounted(() => {
    timer = setInterval(checkVersion, interval)
  })

  onUnmounted(() => {
    clearInterval(timer)
  })
} 