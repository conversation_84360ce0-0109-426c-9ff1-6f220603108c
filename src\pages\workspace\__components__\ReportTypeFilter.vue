<template>
  <div class="relative" ref="filterContainer">
    <Button 
      variant="outline" 
      class="flex items-center gap-2 bg-white/80 backdrop-blur-sm w-full sm:w-auto shadow-sm border-slate-200"
      @click="showReportTypeMenu = !showReportTypeMenu">
      <span>{{ modelValue ? t(`report.${modelValue}`, modelValue) : t('common.all_report_types') }}</span>
      <ChevronDownIcon class="h-4 w-4 text-slate-500"/>
    </Button>
            
    <div 
      v-if="showReportTypeMenu" 
      class="absolute z-10 mt-1 w-full bg-white/95 backdrop-blur-md rounded-lg shadow-lg py-1 border border-slate-100">
      <div 
        class="px-4 py-2 hover:bg-slate-50 cursor-pointer transition-colors"
        @click="selectType(null)">
        {{ t('common.all_report_types') }}
      </div>
      <div 
        v-for="type in reportTypes" 
        :key="type"
        class="px-4 py-2 hover:bg-slate-50 cursor-pointer transition-colors"
        @click="selectType(type)">
        {{ t(`report.${type}`, type) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ChevronDownIcon } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { onClickOutside } from '@vueuse/core'

// --- Props ---
const props = defineProps<{
  reportTypes: string[];
  modelValue: string | null; // For v-model
}>()

// --- Emits ---
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | null): void;
}>()

// --- i18n ---
const { t } = useI18n()

// --- Local State ---
const showReportTypeMenu = ref(false)
const filterContainer = ref<HTMLElement | null>(null)

// 使用 onClickOutside hook
onClickOutside(filterContainer, () => {
  showReportTypeMenu.value = false
})

// --- Methods ---
function selectType(type: string | null) {
  emit('update:modelValue', type)
  showReportTypeMenu.value = false
}

// onMounted/onUnmounted 不是严格必需的，
// 因为 onClickOutside 会自动处理清理，但显式管理有时更清晰。
// onMounted(() => { /* 可以在这里添加额外的挂载逻辑 */ });
// onUnmounted(() => { /* 可以在这里添加额外的卸载逻辑 */ });
</script>

<style scoped>
/* Add specific styles if needed */
</style> 