/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/agent-market/': RouteRecordInfo<'/agent-market/', '/agent-market', Record<never, never>, Record<never, never>>,
    '/dashboard/': RouteRecordInfo<'/dashboard/', '/dashboard', Record<never, never>, Record<never, never>>,
    '/dashboard/[id]': RouteRecordInfo<'/dashboard/[id]', '/dashboard/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/demo/card': RouteRecordInfo<'/demo/card', '/demo/card', Record<never, never>, Record<never, never>>,
    '/demo/hero': RouteRecordInfo<'/demo/hero', '/demo/hero', Record<never, never>, Record<never, never>>,
    '/demo/icon': RouteRecordInfo<'/demo/icon', '/demo/icon', Record<never, never>, Record<never, never>>,
    '/demo/light_theme_dashboard_design': RouteRecordInfo<'/demo/light_theme_dashboard_design', '/demo/light_theme_dashboard_design', Record<never, never>, Record<never, never>>,
    '/demo/markdown': RouteRecordInfo<'/demo/markdown', '/demo/markdown', Record<never, never>, Record<never, never>>,
    '/demo/new_dashboard': RouteRecordInfo<'/demo/new_dashboard', '/demo/new_dashboard', Record<never, never>, Record<never, never>>,
    '/demo/report_chat_view': RouteRecordInfo<'/demo/report_chat_view', '/demo/report_chat_view', Record<never, never>, Record<never, never>>,
    '/demo/select-group': RouteRecordInfo<'/demo/select-group', '/demo/select-group', Record<never, never>, Record<never, never>>,
    '/demo/shadcn': RouteRecordInfo<'/demo/shadcn', '/demo/shadcn', Record<never, never>, Record<never, never>>,
    '/demo/show_card': RouteRecordInfo<'/demo/show_card', '/demo/show_card', Record<never, never>, Record<never, never>>,
    '/format-report/': RouteRecordInfo<'/format-report/', '/format-report', Record<never, never>, Record<never, never>>,
    '/format-report/view/': RouteRecordInfo<'/format-report/view/', '/format-report/view', Record<never, never>, Record<never, never>>,
    '/format-report/view/[id]': RouteRecordInfo<'/format-report/view/[id]', '/format-report/view/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/home': RouteRecordInfo<'/home', '/home', Record<never, never>, Record<never, never>>,
    '/landing': RouteRecordInfo<'/landing', '/landing', Record<never, never>, Record<never, never>>,
    '/next/[id]': RouteRecordInfo<'/next/[id]', '/next/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/poll/': RouteRecordInfo<'/poll/', '/poll', Record<never, never>, Record<never, never>>,
    '/procurement': RouteRecordInfo<'/procurement', '/procurement', Record<never, never>, Record<never, never>>,
    '/robot/': RouteRecordInfo<'/robot/', '/robot', Record<never, never>, Record<never, never>>,
    '/robot/chat': RouteRecordInfo<'/robot/chat', '/robot/chat', Record<never, never>, Record<never, never>>,
    '/settings/': RouteRecordInfo<'/settings/', '/settings', Record<never, never>, Record<never, never>>,
    '/settings/company-info/': RouteRecordInfo<'/settings/company-info/', '/settings/company-info', Record<never, never>, Record<never, never>>,
    '/settings/personal-info/': RouteRecordInfo<'/settings/personal-info/', '/settings/personal-info', Record<never, never>, Record<never, never>>,
    '/settings/plans/': RouteRecordInfo<'/settings/plans/', '/settings/plans', Record<never, never>, Record<never, never>>,
    '/settings/subscription-info/': RouteRecordInfo<'/settings/subscription-info/', '/settings/subscription-info', Record<never, never>, Record<never, never>>,
    '/workspace/': RouteRecordInfo<'/workspace/', '/workspace', Record<never, never>, Record<never, never>>,
  }
}
