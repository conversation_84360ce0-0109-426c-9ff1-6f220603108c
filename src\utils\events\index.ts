import mitt, { type Emitter } from 'mitt'
import { type ResponseData } from '@/hooks/useHttp'
import { tryOnUnmounted } from '@vueuse/core'
import type { Socket } from 'socket.io-client'
export type Events = {
  login: undefined;
  logout: undefined;
  'api:token-error': ResponseData;
  'rights:end': undefined;
  'token:change': { token: string; oldToken?: string };
  'socket:connected': void
  'socket:disconnected': Socket.DisconnectReason
  'socket:error': Error
  'socket:auth_error': Error
  'socket:reconnected': number
  'socket:reconnecting': number
  'socket:reconnect_error': Error
  'socket:reconnect_failed': void
  'socket:resubscribe': void
  'socket:heartbeat_failed': void
  'socket:high_latency': number
  'async:update':void
};

const emitter: Emitter<Events> = mitt<Events>()
emitter.once = (event, handler) => {
  const _handler = (...args: Parameters<typeof handler>) => {
    handler(...args)
    emitter.off(event, _handler)
  }
  emitter.on(event, _handler)
}

export default emitter

export const useGlobalEvent = <K extends keyof Events>(
  event: K,
  handler: (event: Events[K]) => void
) => {
  emitter.on(event, handler)
  tryOnUnmounted(() => {
    emitter.off(event, handler)
  })
}
