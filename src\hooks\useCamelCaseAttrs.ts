import { useAttrs } from 'vue'

// Helper function to convert kebab-case to camelCase
function kebabToCamelCase(str: string) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

export function useCamelCaseAttrs() {
  const attrs = useAttrs()
  const camelCaseAttrs = {} as Record<string, any>

  // Convert all attribute keys to camelCase
  Object.keys(attrs).forEach(key => {
    camelCaseAttrs[kebabToCamelCase(key) as string] = attrs[key]
  })

  return { camelCaseAttrs, attrs }
}
