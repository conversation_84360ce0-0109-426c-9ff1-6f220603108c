<template>
  <div class="el-timeline-item relative">
    <!-- Timeline Node -->
    <div 
      class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary"
      :class="$style['timeline-node']"></div>
    
    <!-- Timeline Tail Line -->
    <div class="el-timeline-item__tail"></div>
    
    <!-- Content Wrapper -->
    <div class="el-timeline-item__wrapper">
      <!-- Timestamp -->
      <div class="el-timeline-item__timestamp is-top mb-2">
        <el-skeleton :loading="true" animated>
          <template #template>
            <el-skeleton-item 
              variant="text" 
              style="width: 100px; height: 14px;"/>
          </template>
        </el-skeleton>
      </div>
      
      <!-- Content -->
      <div class="el-timeline-item__content">
        <NewsItemSkeleton 
          :show-index="false"
          :use-card="false"
          :skeleton-class="$style['news-skeleton']"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElSkeleton, ElSkeletonItem } from 'element-plus'
import NewsItemSkeleton from './NewsItemSkeleton.vue'
</script>

<style lang="scss" module>
.timeline-node {
  z-index: 1;
  position: relative;
  background-color: var(--el-color-primary) !important;
}

.news-skeleton {
  :global(.el-skeleton) {
    padding: 16px;
    background-color: var(--el-bg-color);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
    transition: box-shadow 0.3s ease;
    margin-left: 20px;
    
    &:hover {
      box-shadow: var(--el-box-shadow);
    }
  }
}
</style>
