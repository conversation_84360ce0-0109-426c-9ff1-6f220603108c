import dotenv from 'dotenv'
import fs from 'node:fs'
import path from 'node:path'
import { normalizePath, loadEnv } from 'vite'
import { pick } from 'lodash-es'
const region = (process.env.BUILD_REG || 'CN').toLowerCase()

function getEnvFilesForMode(mode: string, envDir: string = '') {
  return [
    /** region file */ `.env.${mode}`,
    /** region local file */ `.env.${mode}.local`,
  ].map((file) => normalizePath(path.join(envDir, file)))
}
export function tryStatSync(file: string): fs.Stats | undefined {
  try {
    // The "throwIfNoEntry" is a performance optimization for cases where the file does not exist
    return fs.statSync(file, { throwIfNoEntry: false })
  } catch {
    // Ignore errors
  }
}
const env = loadEnv(region, '')
const envFiles = getEnvFilesForMode(region, '')
const parsed = Object.fromEntries(envFiles.flatMap((filePath) => {
  if (!tryStatSync(filePath)?.isFile())
  {return []}
  return Object.entries(dotenv.parse(fs.readFileSync(filePath)))
}))
const regionEnv = pick(env, Object.keys(parsed))
const regionEnvVars = Object.entries(regionEnv).reduce((acc, [key, value]) => {
  acc[`import.meta.env.${key}`] = JSON.stringify(value)
  return acc
}, {} as Record<string, string>)

export { regionEnvVars, env }
