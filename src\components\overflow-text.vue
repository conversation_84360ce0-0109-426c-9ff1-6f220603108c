<template>
  <el-text ref="textRef" v-bind="_textProps" :class="$style.text">
    <slot>
      {{ props.text }}
    </slot>
  </el-text>
</template>

<script lang="ts" setup>
import { computed, ref, watch, type PropType } from 'vue'
import { ElText as _ElText, textProps, useTooltipProps } from 'element-plus'
import { omit } from 'lodash-es'
import { useOverflowTooltip } from '@/hooks/useOverflowTooltip'

const props = defineProps({
  ...omit(useTooltipProps, ['visible']),
  text: {
    type: String,
    default: '',
  },
  textConfig: {
    type: Object as PropType<Partial<typeof textProps>>,
    default: () => ({})
  }
})
const textRef = ref<InstanceType<typeof _ElText>>()
const textEl = computed<HTMLElement>(() => textRef.value?.$el)

const { updateOptions } = useOverflowTooltip(textEl, props)
watch(() => props, updateOptions, { deep: true })
const _textProps = computed<typeof textProps>(() => {
  return {
    truncated: true,
    ...props.textConfig
  } as typeof textProps
})
</script>

<style lang="scss" module>
.text {
  &:global(.el-text) {
    color: inherit;
  }
}
</style>
