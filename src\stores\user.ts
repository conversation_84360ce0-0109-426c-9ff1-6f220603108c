import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { getToken, removeToken, setToken } from '@/hooks/useToken'
import type { User, LoginResult, TourConfigItem } from '@/types/login'
import { useGet, BusinessError, usePost } from '@/hooks/useHttp'
import emitter from '@/utils/events'
import { useSinglePromise } from '@/hooks/useSinglePromise'
import { intoSystem } from '@/utils/router'
import { useRouter } from 'vue-router/auto'
import type { AccessParams } from 'types'
import {
  useUserCompanyConfigResourceStore,
  useUserPermissionsResourceStore,
  useUserRoleResourceStore,
} from './userResourceStores'
import { useStorage } from '@vueuse/core'
import { getI18nInstance, updateLocaleResources } from '@/lang/i18n'
import type { Language } from '@/types/language'
import { isString } from 'lodash-es'
import { USER_TOKEN_EXPIRED, ERROR_CODE_USER_NOT_LOGIN } from '@/const/errorCode'
import { PERMISSION_USER_TYPE_MAP, type PdfPermissionType } from '@/const/permission'

export type LoginType = 'password';

export interface LoginParams {
  user_name: string;
  password: string;
}

export interface UserRole {
  id: number;
  roleName: string;
  roleCode: string;
  enabled: 1 | 0;
}
export interface UserPermission {
  id: number;
  code: string;
}

export enum UserType {
  CompanyUser = 1,
  CompanyAdmin = 2,
  CompanyCreator = 3,
}

export interface MenuConfigItem {
  menu:{
    id: string
    status: boolean
  }[]
  feature:{
    id: string
    status: boolean
  }[]
}   


// 角色权限映射
export const USER_TYPE_MAP = {
  [UserType.CompanyUser]: {
    label: 'setting.companyMgr.user',
    level: 1,
    canManageUsers: false,
    canManageRoles: false,
    canManageBilling: false,
    canAccessPlans: false,
    canAccessCompanyInfo: false,
  },
  [UserType.CompanyAdmin]: {
    label: 'setting.companyMgr.admin',
    level: 2,
    canManageUsers: true,
    canManageRoles: true,
    canManageBilling: false,
    canAccessPlans: false,
    canAccessCompanyInfo: true,
  },
  [UserType.CompanyCreator]: {
    label: 'setting.companyMgr.creator',
    level: 3,
    canManageUsers: true,
    canManageRoles: true,
    canManageBilling: true,
    canAccessPlans: true,
    canAccessCompanyInfo: true,
  },
} as const

const languageRequest = usePost(
  '/user/completion',
  undefined,
  {},
  {
    immediate: false,
  }
)

export const useUserStore = defineStore('user', () => {
  const userRoleStore = useUserRoleResourceStore()
  const userPermissionStore = useUserPermissionsResourceStore()
  const userCompanyConfigStore = useUserCompanyConfigResourceStore()
  const router = useRouter()
  const userInfo = ref<User>()
  const companyConfig = computed(() => userInfo.value?.company?.config)
  const userRole = computed(() => {
    if (!userInfo.value?.user_type) {return null}
    return USER_TYPE_MAP[userInfo.value.user_type]
  })
  const menuConfig = ref<MenuConfigItem['menu']>([])
  const featureConfig = ref<MenuConfigItem['feature']>([])
  const tourConfig = ref<TourConfigItem[]>([])
  const avatarName = computed(() => userInfo.value?.user_name.slice(0, 2))
  const userId = computed(() => userInfo.value?.id)
  const isTokensValid = computed(() => {
    if (!companyConfig.value) {
      return false
    }
    if (isString(companyConfig.value?.tokens)) {
      return companyConfig.value?.tokens === 'unlimited'
    }
    return companyConfig.value?.tokens > 0
  })

  const setUserInfo = (info: User) => {
    userInfo.value = info
    tourConfig.value = info?.tour_config
  }

  const requests: Record<
    LoginType,
    (params: LoginParams) => Promise<LoginResult | null>
  > = {
    password: (params: LoginParams) =>
      usePost<LoginResult, LoginParams>('/user/login', params).promiseResult(),
  }

  const localStorageKey = computed(() => [
    `isHideChartSwiper-${userId.value}`,
    `isCollapsed-${userId.value}`,
    `has-clicked-aside-${userId.value}`,
  ])
  const removeLocalStorage = () => {
    localStorageKey.value.forEach((key) => {
      const _value = useStorage(key, undefined)
      _value.value = null
    })
  }
  const clear = () => {
    removeLocalStorage()
    removeToken()
    userInfo.value = undefined
  }

  const changeUserLanguage = async (language: Language) => {
    try {
      await languageRequest.execute(0, { language })
      return true
    } catch (error) {
      console.error('Failed to update user language:', error)
      throw error
    }
  }
  const login = async (type: LoginType, params: LoginParams) => {
    const res = await requests[type](params)
    if (!res) {
      return
    }
    // leaveState 空或者0表示用户状态正常，1移除组织，2退出组织，3组织解散，4未加入任何组织
    if (res) {
      setToken(res.token)
      await intoSystem(router)
      return
    }
  }
  const loadAccount = async () => {
    const info = await useGet<User>('/user/get_current', {
      showError: false,
      noAuthHandler: () => {
        clear()
        return false
      },
    }).promiseResult()
    if (info) {
      setUserInfo(info)
      // 更新语言资源
      await updateLocaleResources(info.language)
      // 设置 i18n locale
      const i18n = getI18nInstance()
      i18n.global.locale.value = info.language
    }
  }
  const loadMenuConfig = async () => {
    const res = await useGet<MenuConfigItem>('/common/menu', {
      showError: true,
    }).promiseResult()
    if (res) {
      menuConfig.value = res.menu
      featureConfig.value = res.feature
    }
  }
  const logout = async () => {
    await usePost('/user/logout', {}, { showError: false }).promiseResult()
    clear()
    emitter.emit('logout')
  }
  const _validateToken = async () => {
    const token = getToken()
    if (!token) {
      return false
    }
    if (userId.value) {
      return true
    }
    try {
      await loadAccount()
      await loadMenuConfig()
      userCompanyConfigStore.setResources(companyConfig.value?.permissions)
      // 这里只允许书写强制在进入系统之前必要的资源代码，其他的均由emitter分发自行处理
      emitter.emit('login')
    } catch (error) {
      console.error('validateToken error:', error)
      if (error && error instanceof BusinessError && USER_TOKEN_EXPIRED.includes(error.code)) {
        clear()
      }
      return false
    }
    return true
  }

  const { execute: validateToken } = useSinglePromise<boolean>(
    _validateToken,
    false,
    { immediate: false }
  )

  const checkAccess = (params?: AccessParams): boolean => {
    if (!params) {
      return true
    }
    const { permissions, roles, userTypes, fallback = [] } = params
    const checkFallbackConditions = () =>
      fallback.some((item) => checkAccess(item))

    // 检查用户类型
    if (userTypes?.length && userInfo.value?.user_type) {
      if (!userTypes.includes(userInfo.value.user_type)) {
        return checkFallbackConditions()
      }
    }

    // 判断后备条件

    // 判断权限资源
    if (!userPermissionStore.hasResources(permissions)) {
      return checkFallbackConditions()
    }
    // 判断角色
    if (!userRoleStore.hasResources(roles)) {
      return checkFallbackConditions()
    }

    return true
  }

  const hasPermission = (permission: keyof typeof USER_TYPE_MAP[UserType] | PdfPermissionType) => {
    if (!userInfo.value?.user_type) {return false}
    
    // 检查是否是 PDF 权限
    if (permission in PERMISSION_USER_TYPE_MAP) {
      return PERMISSION_USER_TYPE_MAP[permission as PdfPermissionType]
        .includes(userInfo.value.user_type)
    }
    
    // 检查是否是用户类型权限
    return USER_TYPE_MAP[userInfo.value.user_type][permission as keyof typeof USER_TYPE_MAP[UserType]]
  }

  const hasPermissionForRole = (targetUserType: UserType) => {
    if (!userInfo.value?.user_type) {return false}
    const currentUserLevel = USER_TYPE_MAP[userInfo.value.user_type].level
    const targetUserLevel = USER_TYPE_MAP[targetUserType].level
    return currentUserLevel > targetUserLevel
  }

  const silentRetryLoadAccount = async () => {
    let attempts = 0
    const maxAttempts = 10
    const maxTimeout = 60 * 1000
    const startTime = Date.now()

    const getRetryDelay = (attempt: number) => {
      // 指数退避算法: 4s, 3.5s, 3s, 2.5s, 2s, 2s...
      const delay = 4000 - (attempt * 500)
      return Math.max(delay, 2000) // 最小间隔2秒
    }

    while (attempts < maxAttempts) {
      try {

        await loadAccount()
        await loadMenuConfig()
        return true
      } catch (error) {
        if (error instanceof BusinessError && USER_TOKEN_EXPIRED.includes(error.code)) {
          clear()
          return false
        }

        if (error instanceof BusinessError && error.code === 502) {
          attempts++
          
          if (Date.now() - startTime > maxTimeout) {
            console.warn('Silent retry timeout after 60 seconds')
            return false
          }

          if (attempts >= maxAttempts) {
            console.warn('Silent retry max attempts (10) reached, server might be down')
            return false
          }

          await new Promise(resolve => setTimeout(resolve, getRetryDelay(attempts - 1)))
        } else {
          console.error('Unexpected error:', error)
          return false
        }
      }
    }

    return false
  }
  // 功能权限检查函数
  const hasFeatureAccess = (featureId: string): boolean => {
    const feature = featureConfig.value.find(f => f.id === featureId)
    return feature?.status ?? false
  }

  // 具体功能权限的computed属性
  const canAccessNotifyFeature = computed(() =>
    hasFeatureAccess('risk_monitoring_intelligence_officer')
  )

  const canAccessNormalFeature = computed(() =>
    hasFeatureAccess('overseas_market_intelligence_officer')
  )

  // 分离原来的 canAccessBindingFeature 为更细粒度的权限
  const canAccessBusinessOpportunityFeature = computed(() =>
    hasFeatureAccess('business_opportunity_intelligence_officer')
  )

  const canAccessStrategicFeature = computed(() =>
    hasFeatureAccess('strategic')
  )

  // 专项分析总权限：两者中至少有一个为true
  const canAccessSpecialAnalysisFeature = computed(() =>
    canAccessBusinessOpportunityFeature.value || canAccessStrategicFeature.value
  )

  // 保留原来的命名以兼容现有代码
  const canAccessBindingFeature = computed(() =>
    canAccessSpecialAnalysisFeature.value
  )

  return {
    userInfo,
    avatarName,
    userId,
    userRole,
    languageRequest,
    companyConfig,
    isTokensValid,
    menuConfig,
    featureConfig,
    tourConfig,

    silentRetryLoadAccount,
    loadAccount,
    validateToken,
    setUserInfo,
    logout,
    login,
    checkAccess,
    changeUserLanguage,
    hasPermission,
    hasPermissionForRole,
    hasFeatureAccess,
    canAccessNotifyFeature,
    canAccessNormalFeature,
    canAccessBindingFeature,
    canAccessBusinessOpportunityFeature,
    canAccessStrategicFeature,
    canAccessSpecialAnalysisFeature
  }
})
