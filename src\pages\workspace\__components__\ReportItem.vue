<template>
  <div class="flex gap-3 slide-in-from-bottom-5 duration-300">
    <!-- 情报官头像 (而非 agent 头像) -->
    <div class="relative flex-shrink-0">
      <Avatar class="ring-2 ring-white shadow-sm h-10 w-10">
        <!-- 根据不同的情报官类型显示不同的图标和背景 -->
        <AvatarFallback v-if="officerType === 'notify'" class="bg-gradient-to-br from-amber-400 to-orange-400 text-white">
          <BellRingIcon class="h-5 w-5"/>
        </AvatarFallback>
        <AvatarFallback v-else-if="officerType === 'binding'" class="bg-gradient-to-br from-blue-400 to-indigo-400 text-white">
          <LayersIcon class="h-5 w-5"/>
        </AvatarFallback>
        <AvatarFallback v-else class="bg-gradient-to-br from-violet-400 to-indigo-400 text-white">
          <UsersIcon class="h-5 w-5"/>
        </AvatarFallback>
      </Avatar>
    </div>
            
    <div class="flex-1 space-y-2 max-w-[calc(100%-60px)]">
      <div class="flex justify-between items-end">
        <div class="flex flex-col">
          <!-- 显示情报官名称和图标作为一个整体 -->
          <div class="flex items-center gap-1.5">
            <span class="font-medium text-sm text-slate-800">
              {{ officerName }}
            </span>
            <span v-if="isMonitorAgent(report)" class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
              <BellIcon class="h-3 w-3 mr-0.5"/>
              {{ t('agent.monitor_alert_tag') }}
            </span>
            <template v-if="isMonitorAgent(report) && isCompleted">
              <Icon
                icon="mdi:refresh"
                :class="[
                  'h-4 w-4 mr-1',
                  canRefreshAgent
                    ? 'text-slate-500 hover:text-blue-500 cursor-pointer'
                    : 'text-slate-300 cursor-not-allowed'
                ]"
                :title="canRefreshAgent ? t('agent.refresh_agent') : t('permission.no_legal_access')"
                @click.stop="canRefreshAgent && emit('refresh-agent', report)"/>
              <Icon
                icon="mdi:cog-outline"
                class="h-4 w-4 text-slate-500 hover:text-slate-700 cursor-pointer mr-1"
                @click.stop="emit('request-change-time', report)"/>
              <Icon
                icon="mdi:delete-outline"
                class="h-4 w-4 text-slate-500 hover:text-red-500 cursor-pointer"
                @click.stop="emit('delete-agent', report)"/>
            </template>
          </div>

          <!-- 显示报告层级结构 -->
          <div class="flex flex-col gap-1 mt-1">
            <!-- 如果存在 report.name，则在 displayReportName 上方显示 -->
            <span v-if="reportName" class="text-xs font-medium text-slate-700">
              {{ reportName }}
            </span>
            
            <!-- 显示 report_name 和 info 图标 (hover 显示描述) -->
            <div class="flex items-center gap-1">
              <span class="text-xs font-medium text-slate-600">
                {{ displayReportName }}
              </span>
              <el-tooltip
                v-if="displayDescription"
                :content="displayDescription"
                placement="top"
                effect="light"
                popper-class="report-description-tooltip">
                <Icon icon="mdi:information-outline" class="h-3.5 w-3.5 text-slate-400 hover:text-blue-500 cursor-help"/>
              </el-tooltip>
            </div>
          </div>
        </div>
        
        <div class="flex items-center gap-1">
          <span class="text-xs text-slate-500 ml-2">{{ report.date }}</span>
        </div>
      </div>
              
      <component 
        :is="reportComponent"
        :report="report"
        :t="t" 
        @tagClick="handleTagClickWrapper" 
        :viewReport="handleViewReport"/> 
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { BellRingIcon, BellIcon, UsersIcon, LayersIcon } from 'lucide-vue-next'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import MonitorRespon from './MonitorRespon.vue'
import NormalRespon from './NormalRespon.vue'
import type { Report, RunningAgent } from '../types'
import type { CompletedAgent } from '@/pages/dashboard/type'
import { useRouter } from 'vue-router/auto'
import { Icon } from '@iconify/vue'
import { isMonitorAgent, isRunningAgent, isStandardReport } from '../types'
import { ElTooltip } from 'element-plus'
import dayjs from 'dayjs'
import { formatCalendarDateTime } from '@/utils/filter'
import { usePermissions } from '@/composables/usePermissions'

// --- Props ---
const props = defineProps<{
  report: Report;
  // 将情报官类型和名称设置为可选的 props
  officerType?: 'notify' | 'normal' | 'binding'; // 对应三个标签页的类型
  officerName?: string; // 情报官的名称
  // 新增：agent 的 report_name 和 description
  agentReportName?: string;
  agentDescription?: string;
}>()

// 设置默认值
const officerType = computed(() => props.officerType || 'normal')
const officerName = computed(() => props.officerName || t('agent.filter.normal', '标准类型'))

// *** ADD CONSOLE LOG HERE ***
console.log('ReportItem received props.report:', props.report)

// --- Router ---
const router = useRouter()

const handleViewReport = (report: Report) => {
  emit('view-report', report)
}
// --- Emits ---
const emit = defineEmits<{
  (e: 'view-report', report: Report): void;
  (e: 'tagClick', tagData: any): void;
  (e: 'request-change-time', report: Report): void;
  (e: 'delete-agent', report: Report): void;
  (e: 'refresh-agent', report: Report): void;
}>()

// --- i18n ---
const { t } = useI18n()

// --- Permissions ---
const { canAccess } = usePermissions()

// --- Define Type Guards Locally (or ensure exported from types.ts) ---
// Guard for CompletedMonitorAgent (check unique properties like frequency)
function isCompletedMonitorAgent(report: Report | undefined | null): report is CompletedAgent {
  return isMonitorAgent(report) && 'frequency' in report && !isRunningAgent(report) // Ensure it's a monitor type AND has frequency AND isn't a running agent
}

const isCompleted = computed(() => {
  const report = props.report
  if (isStandardReport(report)) { return true } // Standard reports are always completed in this context
  if (isRunningAgent(report)) { return report.status === 'COMPLETED' }
  if (isCompletedMonitorAgent(report)) { // Use the specific guard
    const status = report.status?.toLowerCase() ?? ''
    return status !== 'running' && status !== 'pending' && status !== 'error'
  }
  return false
})

// 检查是否可以刷新 - 基于legal权限
const canRefreshAgent = computed(() => {
  const report = props.report
  if (!isMonitorAgent(report) || !isCompleted.value) {
    return false
  }

  // 检查是否为LegalAgent或CompositeAgent
  const reportType = (report as any)?.report_type || ''

  // 如果没有legal权限，禁用LegalAgent和CompositeAgent的刷新
  if (!canAccess.legalFeature.value && (reportType === 'LegalAgent' || reportType === 'CompositeAgent')) {
    return false
  }

  return true
})

// --- Computed Properties for Display ---
const reportDate = computed(() => {
  const currentReport = props.report
  if (isCompletedMonitorAgent(currentReport)) { return currentReport.updated_at || currentReport.created_at || new Date(0).toISOString() }
  if (isRunningAgent(currentReport)) { return currentReport.date || new Date().toISOString() }
  if (isStandardReport(currentReport)) { return currentReport.updated_at || new Date(0).toISOString() }
  return formatCalendarDateTime(dayjs(currentReport.updated_at || new Date(0)).valueOf())
})

const formattedTime = computed(() => {
  try {
    const date = new Date(reportDate.value)
    if (isNaN(date.getTime())) { return '--:--' } // Add check for invalid date
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false })
  } catch (e) { return '--:--' }
})

// 获取 report.name（如果存在）
const reportName = computed(() => {
  const report = props.report
  if (!report) { return '' }
  
  // 尝试获取 report.name
  return isRunningAgent(report) ? report.report_name : (report as any)?.name || ''
})

// 新增：处理显示的 report_name 和 description
const displayReportName = computed(() => {
  if (props.agentReportName) {
    return props.agentReportName
  }
  
  const currentReport = props.report
  if (isCompletedMonitorAgent(currentReport)) { 
    return currentReport.report_name || t('agent.monitor_agent_name', '监控预警')
  }
  if (isRunningAgent(currentReport)) { 
    return currentReport.report_name || t('agent.running_agent_name', '运行中任务')
  } 
  if (isStandardReport(currentReport)) { 
    return currentReport.name || t('agent.report_agent_name', '标准报告')
  }
  
  return (currentReport as any)?.report_name || (currentReport as any)?.name || t('agent.unknown_agent_name', '未知类型')
})

const displayDescription = computed(() => {
  return props.agentDescription || ''
})

const reportComponent = computed(() => {
  // 处理监控类报告
  if (isMonitorAgent(props.report)) {
    return MonitorRespon
  }
  
  // 处理标准完成的报告
  return NormalRespon
})

// *** Define the original handleTagClick ***
const handleTagClick = (tagData: { report: Report, tag: string }) => {
  const report = tagData.report
  if (isCompletedMonitorAgent(report)) { 
    const url = router.resolve({ 
      path: `/dashboard/${report.id}`,
      query: { report_type: report.report_type, tag: tagData.tag }
    })
    window.open(url.href, '_blank')
  }
}

// *** Define the wrapper function ***
function handleTagClickWrapper(payload: unknown) {
  // Perform type assertion inside the wrapper
  const tagData = payload as { report: Report, tag: string }
  // Optional: Add runtime checks for safety
  if (typeof tagData === 'object' && tagData !== null && 'report' in tagData && 'tag' in tagData) {
    handleTagClick(tagData) // Call the original typed handler
  } else {
    console.error('Invalid payload received for tagClick:', payload)
  }
}

// --- Helper Functions ---
function getInitials(name: string | undefined): string {
  if (!name || name === t('agent.unknown_agent_name')) { return '?' }
  if (typeof name === 'string' && name.length > 0) {
    const nameParts = name.trim().split(/[\s_-]+/)
    if (nameParts.length > 1) {
      if (nameParts[0].length > 1 && nameParts[1].toLowerCase() === 'agent') { return nameParts[0][0].toUpperCase() }
      return nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase()
    }
    return name[0].toUpperCase()
  }
  return '?'
}
</script>

<style scoped>
/* Styles specific to ReportItem can go here */
.animate-in {
  /* Consider inheriting or defining animation here if needed */
}

.report-description-tooltip {
  max-width: 300px;
  line-height: 1.4;
  word-break: break-word;
}
</style> 