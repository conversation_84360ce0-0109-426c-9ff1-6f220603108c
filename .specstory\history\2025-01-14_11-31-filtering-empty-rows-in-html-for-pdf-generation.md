<!-- Generated by SpecStory -->

# Filtering Empty Rows in HTML for PDF Generation (2025-01-14 19:31:12)

_**User**_

 @index.ts 生成pdf的过程中报错,我分析了html结构，发现存在这种html结构的内容：
```
<table>
<thead>
<tr>
<th style="text-align:center">序号</th>
<th style="text-align:left">标题</th>
<th style="text-align:center">发布日期</th>
<th style="text-align:left">案件类型</th>
<th style="text-align:left">原告</th>
<th style="text-align:left">被告</th>
<th style="text-align:left">结果</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center">1</td>
<td style="text-align:left"><a href="https://www.tianyancha.com/lawsuit/e1bb411928384b619b0b5065ec662a29?icode=715a72f4ba18d4ee560ec883a748a45c" class="external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link" target="_blank">张年胜、龙南县鑫塔建设有限公司建设工程施工合同纠纷二审民事判决书</a></td>
<td style="text-align:center">2020-07-31</td>
<td style="text-align:left">建设工程施工合同纠纷</td>
<td style="text-align:left">龙南县鑫塔建设有限公司</td>
<td style="text-align:left">江西志浩电子科技有限公司</td>
<td style="text-align:left">-</td>
</tr>
<tr>
<td style="text-align:center">2</td>
<td style="text-align:left"><a href="https://www.tianyancha.com/lawsuit/4468b828d2474cc3bebb3fd1a6721f6b?icode=2d47127fc56f2ddcf7b9057b16350468" class="external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link external-link" target="_blank">江西志浩电子科技有限公司与龙南县鑫塔建设有限公司建设工程合同纠纷一审民事判决书</a></td>
<td style="text-align:center">2019-12-27</td>
<td style="text-align:left">建设工程合同纠纷</td>
<td style="text-align:left">江西志浩电子科技有限公司</td>
<td style="text-align:left">龙南县鑫塔建设有限公司</td>
<td style="text-align:left">部分支持</td>
</tr>
<tr>
<td style="text-align:center">…</td>
</tr>
</tbody>
</table>
```

我发现有一行空的tr，所以导致生成pdf报错了，能不能在处理html结构的时候就规避这个问题？将有问题的结构过滤掉

---

_**Assistant**_

```typescript:src/hooks/usePdf/index.ts
// 在 processHTMLContent 函数中的 processTable 处理器中添加对空行的过滤
if (processTable) {
  processors.push(el => {
    el.querySelectorAll('table').forEach(table => {
      // 移除空的表格行
      table.querySelectorAll('tr').forEach(row => {
        // 检查行是否为空或只包含空白字符/省略号
        const isEmpty = Array.from(row.cells).every(cell => {
          const text = cell.textContent?.trim() || '';
          return !text || text === '...' || text === '…';
        });
        
        if (isEmpty) {
          row.remove();
        } else {
          // 处理非空单元格
          row.querySelectorAll('td, th').forEach(cell => {
            if (!cell.textContent?.trim()) {
              cell.textContent = '-';
            }
            
            // 处理重复的 external-link 类
            const links = cell.querySelectorAll('a');
            links.forEach(link => {
              const classes = [...new Set(link.className.split(/\s+/))];
              link.className = classes.join(' ');
            });
          });
        }
      });
    });
  });
}
```

这个更新主要做了以下改进：

1. 检查并移除空的表格行(tr)，包括:
   - 完全空的行
   - 只包含省略号(... 或 …)的行

2. 对于非空行:
   - 将空单元格填充为 '-' 
   - 处理链接中重复的 class 名称

这样可以在 HTML 处理阶段就清理掉可能导致 PDF 生成错误的问题结构。

主要改动是在表格处理器中添加了对空行的检测和移除逻辑，同时保留了原有的空单元格处理功能。这样可以避免空行导致的 PDF 生成错误，同时保持表格数据的完整性。

---

