/**
 * useProjectFilter Hook
 * 
 * 项目数据的高性能过滤器，基于通用的 useFilter hook 实现。
 * 
 * 功能特点：
 * 1. 数据结构转换
 *    - 支持 Projects 类型的层级结构
 *    - 自动扁平化和重构数据
 *    - 保持原有的数据组织形式
 * 
 * 2. 搜索字段
 *    - 国家名称 (nation_cn)
 *    - 行业名称 (industry, industry_cn)
 *    - 项目名称 (name, name_cn)
 *    - 支持多语言匹配
 * 
 * 3. 继承功能
 *    - 模糊匹配：支持部分词匹配
 *    - 大小写敏感：可配置是否区分大小写
 *    - 完整匹配：可配置是否需要完整匹配
 *    - 搜索缓存：缓存搜索结果提升性能
 * 
 * 使用示例：
 * ```ts
 * const { 
 *   filteredProjects,  // 过滤后的项目数据
 *   handleSearch,      // 搜索处理函数
 *   clearCache,        // 清除缓存
 *   trimCache,         // 清理过期缓存
 *   config            // 当前配置
 * } = useProjectFilter(projects, {
 *   debounceTime: 300,      // 防抖时间
 *   maxCacheSize: 100,      // 最大缓存数
 *   caseSensitive: false,   // 是否区分大小写
 *   exactMatch: false       // 是否完整匹配
 * })
 * ```
 * 
 * 搜索语法：
 * - 空格分隔：'日本 制造业'
 * - 支持中英文混合搜索
 * - 支持模糊匹配：'制造' 可匹配 '制造业'
 * - 支持任意顺序：'制造业日本' 可匹配 '日本制造业'
 * 
 * @param projects - 项目数据的响应式引用
 * @param options - 过滤器配置选项
 * @returns 过滤器实例和过滤后的项目数据
 */

import { computed, type Ref } from 'vue'
import type { Projects, Project } from '@/types/intelligence'
import { useFilter, type FilterOptions } from './useFilter'

export function useProjectFilter(
  projects: Ref<Projects | null | undefined>,
  options: FilterOptions = {}
) {
  // 将 Projects 对象转换为扁平数组
  const flattenProjects = (projects: Projects | null | undefined) => {
    console.log('projects', projects)
    if (!projects) {return []}
    return Object.entries(projects).flatMap(([nation, projectList]) => 
      projectList.map(project => ({
        ...project,
        _nation: nation
      }))
    )
  }

  // 将扁平数组重新组织为 Projects 结构
  const reconstructProjects = (items: Project[]): Projects => {
    const result: Projects = {}
    items.forEach(item => {
      const nation = item._nation
      if (!nation) {return}
      if (!result[nation]) {
        result[nation] = []
      }
      result[nation].push(item)
    })
    return result
  }

  // 使用通用过滤器
  const {
    filteredItems,
    handleSearch,
    clearCache,
    trimCache,
    config
  } = useFilter(
    computed(() => flattenProjects(projects.value)),
    {
      getSearchFields: (project: Project) => [
        project.nation_cn,
        project.industry,
        project.industry_cn,
        project.name,
        project.name_cn
      ],
      calculateScore: (project: Project, searchTerms: string[]) => {
        if (searchTerms.length > 1) {
          let score = 0
          let hasNationMatch = false
          let hasIndustryMatch = false

          for (const term of searchTerms) {
            if (project.nation_cn === term) {
              hasNationMatch = true
              score += 2
            }

            if (project.industry?.includes(term) || 
                project.industry_cn?.includes(term)) {
              hasIndustryMatch = true
              score += 2
            }
          }

          // 确保国家和行业都匹配
          return (hasNationMatch && hasIndustryMatch) ? score : 0
        }

        // 单词搜索逻辑保持不变
        const term = searchTerms[0]
        if (project.nation_cn === term) {
          return 4
        }

        if (project.industry?.includes(term) || 
            project.industry_cn?.includes(term)) {
          return 3
        }

        if (project.name?.includes(term) || 
            project.name_cn?.includes(term)) {
          return 2
        }

        return 0
      }
    },
    options
  )

  return {
    filteredProjects: computed(() => reconstructProjects(filteredItems.value || [])),
    handleSearch,
    clearCache,
    trimCache,
    config
  }
}
