<template>
  <div :class="$style['plans-root']" v-loading="isLoadingPlanConfig">
    <PricingPlansForm
      v-model:plan="currentPlan"
      :plans="planConfig!"
      :current-plan="currentPlan"
      @update:plan="handlePlanChange"/>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { definePage } from 'vue-router/auto'
import PricingPlansForm from '@/components/PricingPlansForm.vue'
import { ElMessage } from 'element-plus'
import type { PlanType } from './type'
import { useGet, usePost } from '@/hooks/useHttp'
import type { PlanConfig } from './type'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import type { User } from '@/types/login'
const { execute: refreshPlanConfig, isLoading: isLoadingPlanConfig, state: planConfig } = useGet<PlanConfig[]>('/common/company_config', {}, {
  immediate: false
})

// 定义路由元信息 Define route meta
definePage({
  meta: {
    requiresAuth: true,
  },
})

const userStore = useUserStore()
const { companyConfig } = storeToRefs(userStore)
const { execute: updatePlan, isLoading: isUpdatingPlan } = usePost<User['company']>('/company/set_config', {}, {}, {
  immediate: false
})


// 状态管理 State management
const currentPlan = computed(() => companyConfig.value?.plan as PlanType)

// 处理计划变更 Handle plan changes
const handlePlanChange = async (newPlan: { type: PlanType; billingPeriod: 'monthly' | 'yearly' }) => {
  try {
    // 这里应该调用API更新计划 Here should call API to update plan
    await updatePlan(0, {
      type: newPlan.type,
      period: newPlan.billingPeriod === 'monthly' ? 'month' : 'year'
    })
    await userStore.loadAccount()
    ElMessage.success(`成功升级到 ${newPlan.type} 计划`)
  } catch (error) {
    ElMessage.error('计划更新失败，请稍后重试')
    console.error('Plan update failed:', error)
  }
}

onMounted(async () => {
  await refreshPlanConfig()
})
</script>

<style lang="scss" module>
.plans-root {
  width: 100%;
  height: 100%;
  background-color: #f9fafb;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(239, 246, 255, 0.5) 0%, rgba(219, 234, 254, 0.5) 100%);
}
</style>
