import type { TooltipOptions } from './overflowTooltip'
import OverflowTooltip from './overflowTooltip'

type TooltipBinding = {
  value: TooltipOptions
}
const optionMap = new WeakMap<HTMLElement, OverflowTooltip>()

export default {
  name: 'overflow-tooltip',
  mounted(el: HTMLElement, binding: TooltipBinding): void {
    optionMap.set(el, new OverflowTooltip(el, binding.value))
  },
  updated(el: HTMLElement, binding: TooltipBinding): void {
    optionMap.get(el)?.update(binding.value)
  },
  unmounted(el: HTMLElement) {
    const cache = optionMap.get(el)
    if (cache) {
      cache.destroy()
      optionMap.delete(el)
    }
  }
}
