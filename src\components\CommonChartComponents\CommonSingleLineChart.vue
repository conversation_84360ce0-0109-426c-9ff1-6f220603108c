<template>
  <EchartsComponent :params="getChartOption" :auto-size="true" data-pdf-chart/>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'
import EchartsComponent from '@/components/EchartsComponent.vue'
import { useI18n } from 'vue-i18n'
import { defaults, isObject, merge } from 'lodash-es'

interface DataPoint {
  x: string;
  y: number;
}

const props = defineProps<{
  data: DataPoint[];
  name?: string;
  config?: Record<string, any>;
}>()

const { locale } = useI18n()

// 主题色
const PRIMARY_COLOR = '#4466BC'

// 转换数据格式
const chartData = computed(() => {
  if (!props.data?.length) {
    return { xData: [], yData: [] }
  }
  
  // 对数据点进行日期排序
  const sortedData = [...props.data].sort((a, b) => 
    dayjs(a.x).valueOf() - dayjs(b.x).valueOf()
  )
  
  return {
    xData: sortedData.map(item => dayjs(item.x).format('YYYY-MM-DD')),
    yData: sortedData.map(item => item.y)
  }
})

// 图表配置
const getChartOption = computed(() => {
  const baseOption = {
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10px',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.xData,
      axisLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#000000'
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#000000',
      }
    },
    series: [{
      name: props.name || '',
      type: 'line',
      data: chartData.value.yData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: PRIMARY_COLOR,
      },
      lineStyle: {
        width: 2,
        color: PRIMARY_COLOR,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: `${PRIMARY_COLOR}33`,
            },
            {
              offset: 1,
              color: `${PRIMARY_COLOR}00`,
            },
          ],
        },
      },
    }],
  }
  return isObject(props.config) ? merge(baseOption, defaults(props.config, {})) : baseOption
})
</script>
