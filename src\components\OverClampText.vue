<template>
  <el-tooltip
    class="item"
    effect="dark"
    :disabled="!shouldShowTooltip"
    :placement="placement"
    :append-to-body="true"
    :show-after="showAfter"
    :hide-after="hideAfter"
    :auto-close="autoClose"
    popper-class="text-tooltip">
    <p
      :class="[$style['text-container'], className]"
      :style="containerStyle"
      @mouseenter="checkOverflow">
      <span v-html="htmlContent" ref="textRef" :class="$style.text"></span>
    </p>
    <template #content>{{ formattedContent }} </template>
  </el-tooltip>
  <!-- 用于测量完整文本高度的隐藏元素 -->
  <div ref="measureRef" :class="$style['measure-container']">
    <span>{{ content }}</span>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue'
import type { CSSProperties } from 'vue'
import { useThrottleFn } from '@vueuse/core'
import { useI18n } from 'vue-i18n'
import MarkdownContainer from '@/components/MarkdownComponents/MarkdownContainer.vue'
import { markdownToHtml } from '@/utils/markdown'

const htmlContent = computed(() => markdownToHtml(props.content))

const { locale } = useI18n()
interface Props {
  content?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  lineClamp?: number;
  lineHeight?: string;
  className?: string;
  showAfter?: number;
  hideAfter?: number;
  autoClose?: number;
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  placement: 'top',
  lineClamp: 2,
  lineHeight: '160%',
  className: '',
  showAfter: 500,
  hideAfter: 500,
  autoClose: 3000,
})

const textRef = ref<HTMLElement | null>(null)
const measureRef = ref<HTMLElement | null>(null)
const shouldShowTooltip = ref(false)

// 计算容器样式
const containerStyle = computed<CSSProperties>(() => ({
  '-webkit-line-clamp': props.lineClamp,
  'line-height': props.lineHeight,
  // 'text-indent': locale.value === 'chinese' ? '2em' : '0'
}))

// 格式化tooltip内容
const formattedContent = computed(() => {
  if (!props.content) {
    return ''
  }
  return props.content.match(/.{1,300}/g)?.join('<br/>')
})

// 检查是否需要显示tooltip的函数
const checkOverflow = useThrottleFn(() => {
  if (!textRef.value || !measureRef.value) {
    return
  }

  const displayElement = textRef.value.parentElement // 获取p元素
  const measureElement = measureRef.value

  // 确保测量元素和显示元素宽度一致
  measureElement.style.width = `${displayElement?.clientWidth}px`

  // 比较实际内容高度和显示区域高度
  const isOverflowing =
    measureElement.scrollHeight > (displayElement?.clientHeight || 0)
  shouldShowTooltip.value = isOverflowing && Boolean(props.content)
}, 200)

// 监听content变化时重新检查
watch(
  () => props.content,
  () => {
    nextTick(() => {
      checkOverflow()
    })
  }
)

// 监听lineClamp变化时重新检查
watch(
  () => props.lineClamp,
  () => {
    nextTick(() => {
      checkOverflow()
    })
  }
)

// 组件挂载后进行初始检查
onMounted(() => {
  nextTick(() => {
    checkOverflow()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', checkOverflow)

  // 在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', checkOverflow)
  })
})
</script>

<style lang="scss" module>
.text-container {
  margin: 0;
  word-break: break-word;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 14px;

  // 确保在所有浏览器中表现一致
  -moz-box-orient: vertical;
  -ms-box-orient: vertical;
  .text {
    > p {
      strong {
        color: var(--primary-font-color);
        font-size: 18px;
      }
    }
  }
}

.measure-container {
  position: fixed;
  top: -9999px;
  left: -9999px;
  visibility: hidden;
  font-size: 14px;
  line-height: inherit;
  word-break: break-word;
  overflow-wrap: break-word;

  // 继承容器的字体样式
  font-family: inherit;
  font-weight: inherit;
  letter-spacing: inherit;

  // 设置宽度样式
  white-space: pre-wrap;

  span {
    display: inline-block;
    width: 100%;
  }
}
</style>

<style>
.text-tooltip {
  max-width: 300px;
  white-space: pre-line;
  word-break: break-word;
}
</style>
