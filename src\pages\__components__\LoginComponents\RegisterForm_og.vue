<template>
  <form @submit.prevent="handleSubmit" class="w-full">
    <CustomInput
      v-if="!invite_code"
      v-model="form.company_name"
      :label="$t('login.companyName')"
      :placeholder="$t('login.companyNamePlaceholder')"
      type="text"
      required
      :validator="validateName"
      @validation-error="handleValidationError('company_name')"
      @validation-success="handleValidationSuccess('company_name')"/>
    <CustomInput
      v-model="form.user_name"
      :label="$t('login.username')"
      :placeholder="$t('login.usernamePlaceholder')"
      type="text"
      required
      :validator="validateName"
      @validation-error="handleValidationError('name')"
      @validation-success="handleValidationSuccess('name')"/>
    <CustomInput
      v-model="form.email"
      :label="$t('login.email')"
      :placeholder="$t('login.emailPlaceholder')"
      required
      :validator="validateEmail"
      @validation-error="handleValidationError('email')"
      @validation-success="handleValidationSuccess('email')"/>
    <CustomInput
      v-model="form.password"
      :label="$t('login.password')"
      :placeholder="$t('login.passwordPlaceholder')"
      type="password"
      required
      :validator="validatePassword"
      @validation-error="handleValidationError('password')"
      @validation-success="handleValidationSuccess('password')"/>
    <div class="mt-8">
      <el-button
        type="primary"
        :disabled="!isFormValid"
        class="w-full !h-[48px] !text-[16px] !font-medium"
        native-type="submit">
        {{ $t('login.register') }}
      </el-button>
    </div>
    <div class="w-full flex justify-center items-center gap-2 text-center mt-4">
      <span class="text-[14px] text-[#666]">{{ $t('login.noAccount') }}</span>
      <el-link
        type="primary"
        class="!text-[14px] ml-1"
        @click="$emit('switchMode', 'login')">
        {{ $t('login.login') }}
      </el-link>
    </div>
  </form>
</template>
  
<script lang="ts" setup>
import { ref, computed, toRaw } from 'vue'
import CustomInput from './CustomInput.vue'
import { usePost } from '@/hooks/useHttp'
import type { Register, LoginMode } from '@/types/login'
import { RegisterStep } from '../../poll/enum'
import { setToken } from '@/hooks/useToken'
import dayjs from 'dayjs'
import { v4 } from 'uuid'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
  
//get Route query invite_code
const route = useRoute()
const invite_code = computed(() => route.query.invite_code as string ?? '')
const create_company_code = computed(() => route.query.create_company_code as string ?? '')
const { t } = useI18n()
const emit = defineEmits<{
    switchMode: [value: LoginMode];
    toStepPage: [void];
    toHome: [void];
  }>()
  
const form = ref({
  user_name: '',
  email: '',
  password: '',
  company_name:'',
  invite_code:invite_code.value,
  create_company_code:create_company_code.value
})
  
const formErrors = ref(new Set<string>())
  
// 表单验证状态
const isFormValid = computed(() => {
  return (
    form.value.user_name &&
      form.value.email &&
      form.value.password &&
      formErrors.value.size === 0
  )
})
  
// 姓名验证规则
const validateName = (value: string) => {
  if (value.trim().length < 2) {
    return t('login.nameRequire')
  }
  // if (value.length > 20) {
  //   return '姓名不能超过20个字符'
  // }
  return true
}
  
// 邮箱验证规则
const validateEmail = (value: string) => {
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
  if (!emailRegex.test(value)) {
    return t('login.emailRequire')
  }
  return true
}
  
// 密码验证规则
const validatePassword = (value: string) => {
  if (value.length < 3) {
    return t('login.passwordRequire')
  }
  return true
}
  
// 处理验证错误
const handleValidationError = (field: string) => {
  formErrors.value.add(field)
}
  
// 处理验证成功
const handleValidationSuccess = (field: string) => {
  formErrors.value.delete(field)
}
  
const checkCreateCompanyCode = async () => {
  const res = await usePost<{status:boolean}>('/user/check_create_company_code', {
    create_company_code:create_company_code.value
  }, {
    showError: true,
  }).promiseResult()
  return res?.status ?? false
}
  
const handleSubmit = async () => {
  if (!isFormValid.value) {return}
  if (create_company_code.value && !await checkCreateCompanyCode()) {
    ElMessage.error(t('login.createCompanyCodeError'))
    return
  }
  try {
    const res = await usePost<Register>('/user/register', toRaw(form.value)).promiseResult()
    if (res) {
      const isToStep = res?.user.step !== 4
  
      setToken(res.token);
      (isToStep && !invite_code.value) ? emit('toStepPage') : emit('toHome')
    }
  } catch (error) {
    console.error('error:', error)
  }
}
</script>
  
  <style lang="scss" module>
  .custom-button {
    height: 56px;
    font-size: 18px;
    font-weight: 700;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
  
    &:hover {
      opacity: 0.9;
    }
  
    &:active {
      transform: scale(0.98);
    }
  
    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
  
  .register-btn {
    box-shadow: 0px 4px 3px 0px rgba(0, 0, 0, 0.25);
    background: rgb(0, 69, 129);
  }
  </style>
  