<template>
  <div class="pdf-exclude w-full max-w-[1150px] mx-auto h-full flex flex-col gap-4 my-2">
    <span class="text-2xl font-bold text-main-color">{{ title }}</span>
    <TransitionGroup
      :class="$style.root"
      name="chart"
      tag="div"
      appear>
      <div 
        v-for="chart in charts" 
        :key="chart.id" 
        :class="[$style.chartWrapper, $style[`span-${chart.gridSpan || 1}`]]"
        :data-pdf-chart="true"
        :data-chart-id="chart.id">
        <component
          :is="getChartComponent(chart.type)"
          :data="chart.data"
          :indicators="chart.type === 'radar' ? chart.data.indicators : undefined"
          :config="chart.config"
          @created="($event: any) => onChartCreated(chart.id, $event)"
          @rendered="onChartRendered(chart.id)"
          @error="(error: any) => onChartError(chart.id, error)"/>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, defineExpose, watch, onBeforeUnmount, nextTick, type Ref, computed, defineComponent } from 'vue'
import type { EChartsType } from 'echarts'
import CommonLineChart from '@/components/CommonChartComponents/CommonLineChart.vue'
import CommonRadarChart from '@/components/CommonChartComponents/CommonRadarChart.vue'
import CommonBarChart from '@/components/CommonChartComponents/CommonBarChart.vue'
import type { SSEMessage } from '@/hooks/useEnhancedEventSource'
import { useTimeoutFn, useDebounceFn } from '@vueuse/core'
import CommonWordCloud from '@/components/CommonChartComponents/CommonWordCloud.vue'
import CommonSingleLineChart from '@/components/CommonChartComponents/CommonSingleLineChart.vue'

export interface ChartDisplayInstance {
  getChartImages: () => Promise<Record<string, string>>
  isLoading: boolean
  renderErrors: Ref<Record<string, Error>>
}


export interface ChartData {
  id: string;
  type: SSEMessage['chart_type'];
  gridSpan?: 1 | 2 | 3;
  data: any;
  config?: Record<string, any>;
}

interface RenderStatus {
  complete: boolean
  error: Error | null
  retries: number
}

const props = defineProps<{
  charts: ChartData[];
  title?: string;
  globalConfig?: Record<string, any>;
}>()

const MAX_RETRIES = 3
const RETRY_DELAY = 1000
const RENDER_TIMEOUT = 5000

const chartInstances = ref<Record<string, EChartsType>>({})
const renderStatus = ref<Record<string, RenderStatus>>({})
const isLoading = ref(false)

const emit = defineEmits<{
  'charts-rendered': [images: Record<string, string>]
  'render-error': [errors: Record<string, Error>]
}>()

const defaultChartConfig: Record<string, any> = {
  colors: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B'],
  tooltip: {
    trigger: 'axis',
    appendToBody: true
  }
}

const mergeConfig = (defaultConfig: any, customConfig?: any) => {
  if (!customConfig) {return defaultConfig}
  return {
    ...defaultConfig,
    ...customConfig,
    tooltip: {
      ...defaultConfig.tooltip,
      ...customConfig.tooltip
    }
  }
}

const initRenderStatus = (chartId: string) => {
  renderStatus.value[chartId] = {
    complete: false,
    error: null,
    retries: 0
  }
}

const getChartComponent = (type: SSEMessage['chart_type']) => {
  if (!type) {return null}
  const components = {
    line: CommonLineChart,
    radar: CommonRadarChart,
    bar: CommonBarChart,
    word_cloud: CommonWordCloud,
    single_line: CommonSingleLineChart
  }
  return components[type]
}

const onChartCreated = (chartId: string, chartInstance: EChartsType) => {
  chartInstances.value[chartId] = chartInstance
  initRenderStatus(chartId)
}

const onChartRendered = (chartId: string) => {
  if (renderStatus.value[chartId]) {
    renderStatus.value[chartId].complete = true
  }
}

const onChartError = (chartId: string, error: Error) => {
  if (renderStatus.value[chartId]) {
    renderStatus.value[chartId].error = error
  }
}

const waitForChartRender = async (chartId: string): Promise<void> => {
  const status = renderStatus.value[chartId]
  const instance = chartInstances.value[chartId]
  
  if (!instance || status.complete) {return}
  
  try {
    await new Promise((resolve, reject) => {
      const { start: startTimeout, stop: clearTimeout } = useTimeoutFn(() => {
        reject(new Error(`Chart ${chartId} render timeout`))
      }, RENDER_TIMEOUT)

      const checkRender = () => {
        if (!instance || instance.isDisposed()) {
          clearTimeout()
          reject(new Error('Chart was disposed'))
          return
        }

        instance.getZr().on('rendered', () => {
          clearTimeout()
          resolve(true)
        })
      }

      startTimeout()
      checkRender()
    })

    renderStatus.value[chartId].complete = true
  } catch (error) {
    renderStatus.value[chartId].error = error as Error
    
    if (status.retries < MAX_RETRIES) {
      status.retries++
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY))
      return waitForChartRender(chartId)
    }
    
    throw error
  }
}

const getChartImage = async (chartId: string): Promise<[string, string | null]> => {
  const instance = chartInstances.value[chartId]
  if (!instance) {return [chartId, null]}
  try {
    await waitForChartRender(chartId)
    
    return [
      chartId,
      instance.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    ]
  } catch (error) {
    console.error(`Failed to get image for chart ${chartId}:`, error)
    return [chartId, null]
  }
}

const processChartImages = async () => {
  if (isLoading.value) {return}
  try {
    isLoading.value = true
    
    const imagePromises = Object.keys(chartInstances.value)
      .map(getChartImage)
    
    const results = await Promise.all(imagePromises)
    const images = Object.fromEntries(
      results.filter(([, url]) => url !== null)
    )

    const errors = Object.entries(renderStatus.value)
      .reduce((acc, [id, status]) => {
        if (status.error) {acc[id] = status.error}
        return acc
      }, {} as Record<string, Error>)

    if (Object.keys(errors).length) {
      emit('render-error', errors)
    }

    if (Object.keys(images).length) {
      console.log('images', images)
      emit('charts-rendered', images as Record<string, string>)
    }

    return images
  } finally {
    isLoading.value = false
  }
}

const allChartsRendered = computed(() => {
  const statusEntries = Object.entries(renderStatus.value)
  return statusEntries.length > 0 && 
         statusEntries.every(([, status]) => status.complete && !status.error)
})

const debouncedProcessChartImages = useDebounceFn(async () => {
  if (!allChartsRendered.value) {return}
  await processChartImages()
}, 300)

watch([renderStatus, allChartsRendered], ([, isAllRendered]) => {
  if (isAllRendered) {
    debouncedProcessChartImages()
  }
}, { deep: true })


onBeforeUnmount(() => {
  Object.values(chartInstances.value).forEach(instance => {
    try {
      instance.dispose()
    } catch (error) {
      console.error('Failed to dispose chart:', error)
    }
  })
  chartInstances.value = {}
  renderStatus.value = {}
})

defineExpose({
  getChartImages: processChartImages,
  isLoading:isLoading.value,
  renderErrors: renderStatus
})
</script>

<style lang="scss" module>
.root {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  position: relative;
}

.chartWrapper {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 350px;
  transform-origin: center;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
}

.span-1 {
  grid-column: span 1;
}

.span-2 {
  grid-column: span 2;
}

.span-3 {
  grid-column: span 3;
}

:global {
  .chart-move {
    transition: all 0.5s ease;
  }

  .chart-enter-active {
    transition: all 0.5s ease;
    transition-delay: calc(var(--el-transition-duration) * 0.5);
  }

  .chart-leave-active {
    transition: all 0.5s ease;
    position: absolute;
    width: calc(33.33% - 7px); // Adjust based on grid gap
  }

  .chart-enter-from {
    opacity: 0;
    transform: scale(0.6);
  }

  .chart-leave-to {
    opacity: 0;
    transform: scale(0.6);
  }
}
</style>