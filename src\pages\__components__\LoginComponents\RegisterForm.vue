<template>
  <form @submit.prevent="handleSubmit" class="w-full relative">
    <CustomInput
      v-if="!invite_code"
      v-model="form.company_name"
      :label="$t('login.companyName')"
      :placeholder="$t('login.companyNamePlaceholder')"
      type="text"
      required
      :validator="validateName"
      @validation-error="handleValidationError('company_name')"
      @validation-success="handleValidationSuccess('company_name')"/>
    <CustomInput
      v-model="form.user_name"
      :label="$t('login.username')"
      :placeholder="$t('login.usernamePlaceholder')"
      type="text"
      required
      :validator="validateName"
      @validation-error="handleValidationError('name')"
      @validation-success="handleValidationSuccess('name')"/>
    <CustomInput
      v-model="form.email"
      :label="$t('login.email')"
      :placeholder="$t('login.emailPlaceholder')"
      required
      :validator="validateEmail"
      @validation-error="handleValidationError('email')"
      @validation-success="handleValidationSuccess('email')"/>
    <CustomInput
      v-model="form.password"
      :label="$t('login.password')"
      :placeholder="$t('login.passwordPlaceholder')"
      type="password"
      required
      :validator="validatePassword"
      @validation-error="handleValidationError('password')"
      @validation-success="handleValidationSuccess('password')"/>

    <!-- 邮件验证码部分 - 使用过渡效果 -->
    <transition name="slide-fade">
      <div
        v-if="isEmailCodeSectionVisible"
        class="captcha-section absolute top-0 bottom-0 left-[114%] w-[380px] h-full z-10 bg-[#E7F3FF] p-6 border border-[#E7F3FF] rounded-md shadow-md flex flex-col justify-start">
        
        <!-- 邮件验证码标题和说明 -->
        <div class="text-center text-[#333] text-base mb-4">{{ $t('login.emailVerification') || '邮箱验证' }}</div>
        <div class="text-center text-[#666] text-sm mb-6">
          {{ $t('login.emailCodeSent') || '验证码已发送至您的邮箱' }}:
          <div class="font-medium">{{ form.email }}</div>
        </div>
        
        <!-- 邮件验证码输入框 - 优化为分开的数字输入框 -->
        <div class="w-full mb-5">
          <div class="flex justify-between items-center mb-2">
            <span class="text-[#333] text-sm">{{ $t('login.emailCodeInputTitle') || '验证码' }}</span>
            <button 
              @click="sendEmailCode" 
              :disabled="isSendEmailCodeLoading || countdown > 0"
              class="text-sm text-blue-500 hover:text-blue-700 focus:outline-hidden disabled:text-gray-400 transition-colors duration-300"
              type="button">
              <span v-if="countdown > 0">{{ countdown }}s</span>
              <span v-else>{{ $t('login.resendCode') || '重新发送' }}</span>
            </button>
          </div>
          
          <!-- 数字输入框组 -->
          <div class="flex justify-between mb-4">
            <input
              v-for="(_, index) in 6"
              :key="index"
              v-model="codeDigits[index]"
              type="text"
              maxlength="1"
              class="w-[44px] h-[52px] text-center text-xl border border-[#DCDFE6] rounded-md focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-300"
              :class="{'border-red-500': formErrors.has('email_code'), 'border-green-500': isEmailCodeVerified && codeDigits[index]}"
              @input="handleCodeDigitInput(index)"
              @keydown="handleCodeKeydown($event, index)"
              @paste="handleCodePaste"
              @focus="($event.target as HTMLInputElement).select()"
              ref="codeInputRefs"/>
          </div>
          
          <!-- 验证状态指示器 -->
          <div class="flex items-center justify-center h-6 mb-2">
            <transition name="fade">
              <div v-if="isEmailCodeVerifyLoading" class="flex items-center text-blue-500">
                <div class="w-4 h-4 mr-2 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span class="text-sm">{{ $t('login.verifying') || '正在验证...' }}</span>
              </div>
              <div v-else-if="isEmailCodeVerified" class="flex items-center text-green-500">
                <svg class="w-5 h-5 mr-1"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24"
                     xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">{{ $t('login.verified') || '验证成功' }}</span>
              </div>
            </transition>
          </div>
        </div>
        
        <!-- 验证码验证状态和错误信息 -->
        <transition name="fade">
          <div v-if="formErrors.has('email_code')" class="text-xs text-red-500 mb-2 p-2 bg-red-50 rounded-md">
            {{ validateEmailCodeInput(emailCodeInput) }}
          </div>
        </transition>
        <transition name="fade">
          <div v-if="emailCodeErrorMessage" class="text-xs text-red-500 mb-2 p-2 bg-red-50 rounded-md">
            {{ emailCodeErrorMessage }}
          </div>
        </transition>
        <div v-if="!isEmailCodeVerified" class="text-xs text-gray-500 mt-auto">
          {{ $t('login.emailCodeNote') }}
        </div>
      </div>
    </transition>

    <div class="mt-8">
      <el-button
        type="primary"
        :disabled="!isSubmitReady"
        :loading="isEmailCodeVerifyLoading || isRegisterLoading"
        class="w-full h-[48px]! text-[16px]! font-medium! transition-all duration-300"
        native-type="submit">
        {{ $t('login.register') }}
      </el-button>
    </div>
    <div class="w-full flex justify-center items-center gap-2 text-center mt-4">
      <span class="text-[14px] text-[#666]">{{ $t('login.haveAccount') }}</span>
      <el-link
        type="primary"
        class="text-[14px]! ml-1"
        @click="$emit('switchMode', 'login')">
        {{ $t('login.login') }}
      </el-link>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, toRaw, watch, onMounted, nextTick } from 'vue'
import CustomInput from './CustomInput.vue'
import { usePost } from '@/hooks/useHttp'
import type { Register, LoginMode } from '@/types/login'
import { setToken } from '@/hooks/useToken'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

//get Route query invite_code
const route = useRoute()
const invite_code = computed(() => route.query.invite_code as string ?? '')
const create_company_code = computed(() => route.query.create_company_code as string ?? '')
const { t } = useI18n()
const emit = defineEmits<{
  switchMode: [value: LoginMode];
  toStepPage: [void];
  toHome: [void];
}>()

// 发送邮件验证码API
const { execute: executeSendEmailCode, isLoading: isSendEmailCodeLoading, error: sendEmailCodeError } = usePost<{success:boolean}>('/verify_code/send_email_code', {}, {}, {
  immediate: false,
  throwError: false,
})

// 验证邮件验证码API
const { execute: executeVerifyEmailCode, isLoading: isEmailCodeVerifyLoading, state: emailCodeVerifyStatus } = usePost<{success:boolean}>('/verify_code/verify_email_code', {}, {}, {
  immediate: false,
})

const form = ref({
  user_name: '',
  email: '',
  password: '',
  company_name:'',
  invite_code:invite_code.value,
  create_company_code:create_company_code.value,
})

const formErrors = ref(new Set<string>())
const isRegisterLoading = ref(false)

// --- 邮件验证码状态 ---
const emailCodeInput = ref('')
const isEmailCodeVerified = ref(false)
const isEmailCodeSectionVisible = ref(false)
const emailCodeErrorMessage = ref('')
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)

// 分离数字验证码输入
const codeDigits = ref(['', '', '', '', '', ''])
const codeInputRefs = ref<HTMLInputElement[]>([])
// --- 邮件验证码状态结束 ---

// 基础表单验证状态(不包括邮件验证码)
const isBaseFormValid = computed(() => {
  const requiredFields = [
    form.value.user_name,
    form.value.email,
    form.value.password,
  ]
  if (!invite_code.value) {
    requiredFields.push(form.value.company_name)
  }
  // 检查所有必填字段是否已填写且无错误
  const baseFieldsSet = new Set(['user_name', 'email', 'password', ...(invite_code.value ? [] : ['company_name'])])
  const hasBaseErrors = Array.from(formErrors.value).some(field => baseFieldsSet.has(field))

  return requiredFields.every(field => !!field) && !hasBaseErrors
})

// 提交按钮是否可用
const isSubmitReady = computed(() => {
  // 如果验证码区域可见，则需要验证码验证通过才能提交
  if (isEmailCodeSectionVisible.value) {
    return isBaseFormValid.value && isEmailCodeVerified.value
  }
  // 否则只需要基础表单验证通过
  return isBaseFormValid.value
})

// --- 邮件验证码函数 ---
// 发送邮件验证码
const sendEmailCode = async () => {
  if (isSendEmailCodeLoading.value || countdown.value > 0) {return}
  
  // 重置验证码输入
  emailCodeInput.value = ''
  codeDigits.value = ['', '', '', '', '', '']
  isEmailCodeVerified.value = false
  emailCodeErrorMessage.value = ''
  formErrors.value.delete('email_code')
  
  try {
    const res = await executeSendEmailCode(0, { email: form.value.email })
    if (res?.success) {
      ElMessage.success(t('login.emailCodeSendSuccess'))
      startCountdown()
      isEmailCodeSectionVisible.value = true
      
      // 自动聚焦第一个输入框
      nextTick(() => {
        if (codeInputRefs.value[0]) {
          codeInputRefs.value[0].focus()
        }
      })
    } else {
      ElMessage.error(t('login.emailCodeSendError'))
    }
  } catch (error) {
    console.error('发送邮件验证码失败:', error)
    const errorMsg = (sendEmailCodeError.value as Error)?.message ?? (t('login.emailCodeSendError'))
    emailCodeErrorMessage.value = errorMsg
  }
}

// 处理验证码单个数字输入
const handleCodeDigitInput = (index: number) => {
  // 确保只有一个字符
  if (codeDigits.value[index].length > 1) {
    codeDigits.value[index] = codeDigits.value[index].slice(0, 1)
  }
  
  // 自动跳到下一个输入框
  if (codeDigits.value[index] && index < 5) {
    nextTick(() => {
      if (codeInputRefs.value[index + 1]) {
        codeInputRefs.value[index + 1].focus()
      }
    })
  }
  
  // 更新完整的验证码
  emailCodeInput.value = codeDigits.value.join('')
  
  // 输入满6位后自动验证
  if (emailCodeInput.value.length === 6) {
    verifyEmailCode()
  } else {
    // 未满6位时重置验证状态
    isEmailCodeVerified.value = false
    emailCodeErrorMessage.value = ''
  }
}

// 处理验证码输入框键盘事件
const handleCodeKeydown = (event: KeyboardEvent, index: number) => {
  // 删除键处理
  if (event.key === 'Backspace') {
    if (!codeDigits.value[index] && index > 0) {
      // 如果当前框为空且不是第一个，聚焦前一个输入框
      codeDigits.value[index - 1] = ''
      nextTick(() => {
        if (codeInputRefs.value[index - 1]) {
          codeInputRefs.value[index - 1].focus()
        }
      })
    }
  }
  // 左右箭头键导航
  else if (event.key === 'ArrowLeft' && index > 0) {
    nextTick(() => {
      if (codeInputRefs.value[index - 1]) {
        codeInputRefs.value[index - 1].focus()
      }
    })
  } 
  else if (event.key === 'ArrowRight' && index < 5) {
    nextTick(() => {
      if (codeInputRefs.value[index + 1]) {
        codeInputRefs.value[index + 1].focus()
      }
    })
  }
}

// 处理验证码粘贴事件
const handleCodePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text') || ''
  const digits = pastedData.replace(/\D/g, '').slice(0, 6)
  
  if (digits) {
    // 填充验证码数组
    for (let i = 0; i < digits.length; i++) {
      if (i < 6) {
        codeDigits.value[i] = digits[i]
      }
    }
    // 更新完整验证码
    emailCodeInput.value = codeDigits.value.join('')
    
    // 自动验证
    if (emailCodeInput.value.length === 6) {
      verifyEmailCode()
    }
  }
}

// 验证邮件验证码
const verifyEmailCode = async () => {
  if (emailCodeInput.value.length !== 6 || isEmailCodeVerifyLoading.value) {
    return
  }
  
  emailCodeErrorMessage.value = ''
  formErrors.value.delete('email_code')
  
  try {
    await executeVerifyEmailCode(0, { 
      email: form.value.email, 
      email_code: emailCodeInput.value 
    })

    if (emailCodeVerifyStatus.value?.success === true) {
      isEmailCodeVerified.value = true
      ElMessage.success(t('login.verifySuccess'))
      
      // 验证码验证成功后，短暂延迟后自动提交表单
      setTimeout(async () => {
        if (isBaseFormValid.value) {
          await registerUser()
        }
      }, 500)
    } else {
      isEmailCodeVerified.value = false
      handleValidationError('email_code')
      emailCodeErrorMessage.value = t('login.emailCodeVerifyFailed')
      
      // 自动聚焦第一个输入框，便于重新输入
      codeDigits.value = ['', '', '', '', '', '']
      emailCodeInput.value = ''
      nextTick(() => {
        if (codeInputRefs.value[0]) {
          codeInputRefs.value[0].focus()
        }
      })
    }
  } catch (error) {
    console.error('验证邮件验证码失败:', error)
    isEmailCodeVerified.value = false
    const catchErrorMsg = (error as Error)?.message ?? (t('login.emailCodeVerifyError'))
    emailCodeErrorMessage.value = catchErrorMsg
    handleValidationError('email_code')
  }
}

// 倒计时函数
const startCountdown = () => {
  countdown.value = 60
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
  countdownTimer.value = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
        countdownTimer.value = null
      }
    }
  }, 1000)
}

// 监听邮件验证码输入，触发验证
watch(emailCodeInput, (newValue) => {
  // 仅在长度为6时尝试验证
  if (newValue.length === 6) {
    verifyEmailCode()
  }
})

// 组件卸载时清理定时器
onMounted(() => {
  return () => {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }
  }
})

// 姓名验证规则
const validateName = (value: string) => {
  if (value.trim().length < 2) {
    return t('login.nameRequire')
  }
  return true
}

// 邮箱验证规则
const validateEmail = (value: string) => {
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
  if (!emailRegex.test(value)) {
    return t('login.emailRequire')
  }
  return true
}

// 密码验证规则
const validatePassword = (value: string) => {
  if (value.length < 3) {
    return t('login.passwordRequire')
  }
  return true
}

// 处理验证错误
const handleValidationError = (field: string) => {
  formErrors.value.add(field)
}

// 处理验证成功
const handleValidationSuccess = (field: string) => {
  formErrors.value.delete(field)
}

// 邮件验证码输入验证
const validateEmailCodeInput = (value: string) => {
  if (value.length > 0 && value.length < 6) {
    return t('login.emailCodeLengthError') || '验证码长度不足'
  }
  return true
}

const checkCreateCompanyCode = async () => {
  const res = await usePost<{status:boolean}>('/user/check_create_company_code', {
    create_company_code:create_company_code.value
  }, {
    showError: true,
  }).promiseResult()
  return res?.status ?? false
}

// 注册用户函数
const registerUser = async () => {
  isRegisterLoading.value = true
  try {
    const res = await usePost<Register>('/user/register', toRaw(form.value)).promiseResult()
    if (res) {
      const isToStep = res?.user.step !== 4

      setToken(res.token);
      (isToStep && !invite_code.value) ? emit('toStepPage') : emit('toHome')
    }
  } catch (error) {
    console.error('error:', error)
  } finally {
    isRegisterLoading.value = false
  }
}

// 提交表单处理
const handleSubmit = async () => {
  if (!isBaseFormValid.value) { return }
  
  // 检查创建公司码
  if (create_company_code.value && !await checkCreateCompanyCode()) {
    ElMessage.error(t('login.createCompanyCodeError'))
    return
  }
  
  // 如果邮件验证码区域未显示，说明还未发送验证码
  if (!isEmailCodeSectionVisible.value) {
    await sendEmailCode()
    return
  }
  
  // 如果邮件验证码已验证，直接注册
  if (isEmailCodeVerified.value) {
    await registerUser()
  } else {
    // 如果已经显示验证码区域但未验证成功，尝试验证
    if (emailCodeInput.value.length === 6) {
      await verifyEmailCode()
    } else {
      ElMessage.warning(t('login.pleaseInputEmailCode'))
      // 聚焦第一个空的验证码输入框
      const emptyIndex = codeDigits.value.findIndex(digit => !digit)
      if (emptyIndex >= 0 && codeInputRefs.value[emptyIndex]) {
        codeInputRefs.value[emptyIndex].focus()
      } else if (codeInputRefs.value[0]) {
        codeInputRefs.value[0].focus()
      }
    }
  }
}
</script>

<style lang="scss" module>
.custom-button {
  height: 56px;
  font-size: 18px;
  font-weight: 700;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.register-btn {
  box-shadow: 0px 4px 3px 0px rgba(0, 0, 0, 0.25);
  background: rgb(0, 69, 129);
}

/* 添加过渡动画 */
:global(.slide-fade-enter-active),
:global(.slide-fade-leave-active) {
  transition: all 0.3s ease;
}

:global(.slide-fade-enter-from),
:global(.slide-fade-leave-to) {
  transform: translateX(20px);
  opacity: 0;
}

:global(.fade-enter-active),
:global(.fade-leave-active) {
  transition: opacity 0.3s ease;
}

:global(.fade-enter-from),
:global(.fade-leave-to) {
  opacity: 0;
}
</style>