<template>
  <div
    class="flex flex-col justify-center items-center h-full w-full p-10 bg-[#EEF3F6]">
    <List :messages="messages" :is-loading="isWaiting"/>
    <div class="w-full flex justify-center items-center gap-[50px] mb-[50px]">
      <BackArrowBtn :to="'/robot'"/>
      <ChatInput class="w-[60%]"
                 @sendMessage="sendMessage"
                 :loading="robotLoading || isWaiting"
                 @pauseMessage="pauseMessage"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { definePage } from 'vue-router/auto'
import { useHttpPostStream } from '../../hooks/useHttpPostStream'
import { onMounted, onUnmounted, ref } from 'vue'
import type { Message } from './type'
import List from '../__components__/RobotComponents/ChatComponents/List.vue'
import ChatInput from '../__components__/RobotComponents/ChatComponents/ChatInput.vue'
import BackArrowBtn from '@/components/BackArrowBtn.vue'
import { v4 as uuidv4 } from 'uuid'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

// 导入所有机器人头像
import FormulaAvatar from '@/assets/images/robot/formula.png'
import Emoji from '@/assets/images/robot/emoji.png'
import Emoji2 from '@/assets/images/robot/emoji3.png'


// 机器人ID和头像的映射关系
const robotAvatarMap = {
  'FORMULA_Consulting': FormulaAvatar,
  '5df41b3a-7b9c-3c9e-8b4a-1b0f0c0d0e0f': Emoji,
  'a1b2c3d4-e5f6-4a1b-8c9d-0e0f1a2b3c4d': Emoji2,
  // ... 添加其他机器人的映射
} as const

// 获取机器人头像的函数
const getRobotAvatar = (robotId: string) => {
  return robotAvatarMap[robotId as keyof typeof robotAvatarMap] || FormulaAvatar // 默认返回 Formula 头像
}

const { locale } = useI18n()
const route = useRoute()
definePage({
  meta: {
    layout: 'default',
    requiresAuth: true,
  },
})

let currentBotMessageId = ''

const onAILoading = (chunk: string) => {
  const existingMessage = messages.value.find(
    (message) => message.chatType === 'bot' && message.id === currentBotMessageId
  )
  if (existingMessage) {
    existingMessage.content += chunk
  } else {
    const botMessage: Message = {
      id: uuidv4(),
      content: chunk,
      chatType: 'bot',
      avatar: getRobotAvatar(route.query.id as string)
    }
    messages.value.push(botMessage)
    currentBotMessageId = botMessage.id
  }
}

const onAILogDone = () => {
  currentBotMessageId = ''
}

const messages = ref<Message[]>([])

const { startStreaming, onError, abort, isLoading:robotLoading, isWaiting } = useHttpPostStream(
  {
    url: '/api/common/stream',
    language: locale.value as 'chinese' | 'english'
  },
  onAILoading,
  onAILogDone
)

const FORMULA_INTRO = 'FORMULA Consulting, Ltd.，（フォーミュラコンサルティング），是一家提供全球服务的国际战略咨询公司。主要从事亚洲企业市场研究、市场咨询、企业收并购等业务。作为亚洲头部的咨询公司，针对中、日、韩等亚洲地区头部企业、政府等客户提供咨询服务。主要方向为：高端装备制造业、智能制造、芯片半导体、消费品及新零售业、生物医药、跨境投融资服务等。'

onMounted(()=>{
  if (route.query.id === 'FORMULA_Consulting') {
    messages.value.push({
      id: uuidv4(),
      content: FORMULA_INTRO,
      chatType: 'bot',
      avatar: getRobotAvatar(route.query.id as string)
    })
    return
  }

  const TextMap = {
    chinese: '你好，你是谁，能帮我做什么？',
    english: 'hello, who are you? what can you help me with?',
    japanese: 'こんにちは、あなたは誰ですか、何を助けてくれますか？'
  }
  startStreaming({
    message: TextMap[locale.value as 'chinese' | 'english' | 'japanese'],
    robot_id: route.query.id
  })
})

const sendMessage = (message: string) => {
  if (robotLoading.value || isWaiting.value) {return}
  
  const userMessage: Message = {
    id: uuidv4(),
    content: message,
    chatType: 'userInput',
  }
  
  messages.value.push(userMessage)
  startStreaming({
    message: message,
    robot_id: route.query.id
  })
}

const pauseMessage = () => {
  abort()
  currentBotMessageId = ''
}

onError((err) => {
  console.log('err', err)
})

onUnmounted(() => {
  abort()
})
</script>
