<template>
  <el-timeline>
    <TransitionGroup name="news-item" tag="div">
      <el-timeline-item
        v-for="(news, index) in news"
        :timestamp="formatCalendarDateTime(news.publish_time)"
        :key="news.id"
        placement="top"
        :hollow="true"
        type="primary">
        <NormalNewsItem
          :news="news"
          :index="index + 1"
          :is-flashing="flashingIds.has(news.id)"
          @click="emit('click-news', index)"
          class="cursor-pointer"/>
      </el-timeline-item>
    </TransitionGroup>
  </el-timeline>
</template>
  
<script setup lang="ts">
import { ElTimeline, ElTimelineItem } from 'element-plus'
import NormalNewsItem from './NormalNewsItem.vue'
import { formatCalendarDateTime } from '@/utils/filter'
import type { NewsDetail } from '@/types'
  
const emit = defineEmits<{
    'click-news': [index: number]
  }>()
  
defineProps<{
    news: NewsDetail[],
    flashingIds: Set<string>
  }>()
</script>