import { defineStore } from 'pinia'
import { useI18n } from 'vue-i18n'
import { ElMessageBox } from 'element-plus'
import type { User } from '@/types/login'
export const useI18nStore = defineStore('i18n', () => {

  function localeTrigger(language: User['language'], promiseFn: () => Promise<void>) {
    const ENGLISH_MESSAGE =
      'Are you sure you want to switch the language? This will <strong>refresh</strong> the page.'
    const CHINESE_MESSAGE =
      '您确定要切换语言吗？这将<strong>刷新</strong>页面。'

    ElMessageBox.confirm(
      language === 'english' ? ENGLISH_MESSAGE : CHINESE_MESSAGE,
      language === 'english' ? 'Warning' : '警告',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: 'Refresh',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
      .then(async () => {
        await promiseFn()
        location.reload()
      })
      .catch(() => {
        // Do nothing on cancel
      })
  }


  return {
    localeTrigger,
  }
})
