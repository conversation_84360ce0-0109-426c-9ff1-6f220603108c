<template>
  <div class="flex flex-col items-center justify-center h-full p-8 text-center">
    <!-- Icon/Illustration -->
    <div class="mb-6 text-gray-300">
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        class="w-24 h-24"
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor">
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="1" 
          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
      </svg>
    </div>

    <!-- Text Content -->
    <h3 class="mb-2 text-xl font-semibold text-gray-700">
      {{ $t('agent.no_agents_yet') }}
    </h3>
    <p class="mb-8 text-gray-500">
      {{ $t('agent.start_by_creating_agent') }}
    </p>

    <!-- Action Button -->
    <el-button type="primary" @click="handleExploreMarket">
      {{ $t('agent.explore_market') }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router/auto'

const router = useRouter()

const handleExploreMarket = () => {
  router.push('/agent-market')
}
</script>

<style lang="scss" module>
// Additional styles can be added if needed
</style>
