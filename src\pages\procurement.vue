<template>
  <div :class="$style.wrapper">
    <!-- 修改标题部分，添加过滤器和导出按钮 -->
    <div :class="$style.header">
      <h1 class="text-2xl font-bold text-main-color">
        {{ $t('procurement.title') }}
      </h1>
      
      <div :class="$style.headerActions">
        <el-space>
          <!-- <el-select
            v-model="filters.country"
            :placeholder="$t('procurement.filters.countryPlaceholder')"
            clearable>
            <el-option
              v-for="item in countryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
          
          <el-select
            v-model="filters.category"
            :placeholder="$t('procurement.filters.categoryPlaceholder')"
            clearable>
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
          
          <el-select
            v-model="filters.industry"
            :placeholder="$t('procurement.filters.industryPlaceholder')"
            clearable>
            <el-option
              v-for="item in industryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
          
          <el-select
            v-model="filters.service"
            :placeholder="$t('procurement.filters.servicePlaceholder')"
            clearable>
            <el-option
              v-for="item in serviceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select> -->
          
          <el-popover
            placement="bottom-end"
            :width="300"
            trigger="click"
            popper-class="column-settings-popover">
            <template #reference>
              <div :class="$style.settingIcon">
                <Icon icon="mdi:cog" width="24"/>
              </div>
            </template>
            
            <div :class="$style.columnSettings">
              <div :class="$style.settingsHeader">
                <span>{{ $t('procurement.settings.columnTitle') }}</span>
                <el-button
                  link
                  type="primary"
                  @click="resetColumns">
                  {{ $t('procurement.settings.reset') }}
                </el-button>
              </div>
              
              <el-scrollbar max-height="400px">
                <el-checkbox-group v-model="selectedColumns">
                  <div
                    v-for="col in availableColumns"
                    :key="col.prop"
                    :class="$style.columnOption">
                    <el-checkbox :label="col.prop">
                      {{ col.label }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </el-scrollbar>
            </div>
          </el-popover>
        </el-space>
      </div>
    </div>
    
    <!-- Loading Skeleton -->
    <el-skeleton :rows="5" animated v-if="loading"/>
    
    <!-- Data Table -->
    <div :class="$style.content" v-else>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        :class="$style.table"
        @row-click="handleRowClick">
        <el-table-column 
          v-for="col in visibleColumns" 
          :key="col.prop"
          v-bind="col">
          <template #default="scope">
            <template v-if="col.prop === 'ai_score'">
              <div :class="$style.scores">
                <el-tooltip
                  :content="$t('procurement.aiScore.credibility')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.credibility]">
                    <Icon icon="mdi:shield-check"/>
                    {{ scope.row.ai_score.credibility.toFixed(1) }}
                  </div>
                </el-tooltip>
                <el-tooltip
                  :content="$t('procurement.aiScore.importance')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.importance]">
                    <Icon icon="mdi:star"/>
                    {{ scope.row.ai_score.importance.toFixed(1) }}
                  </div>
                </el-tooltip>
                <el-tooltip
                  :content="$t('procurement.aiScore.authority')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.authority]">
                    <Icon icon="mdi:gavel"/>
                    {{ scope.row.ai_score.authority.toFixed(1) }}
                  </div>
                </el-tooltip>
              </div>
            </template>
            <span v-else
                  :class="[
                    col.prop === 'procurement_content' ? $style.primaryContent : '',
                  ]">
              {{ col.formatter ? col.formatter(scope.row[col.prop]) : scope.row[col.prop] }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <!-- Pagination -->
      <div :class="$style.paginationBox">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          background
          :layout="$t('procurement.pagination.layout')"
          :class="$style.pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </div>
    </div>

    <!-- Detail Dialog -->
    <el-dialog
      v-model="showDetail"
      :title="$t('procurement.detail.title')"
      width="80%"
      fullscreen
      destroy-on-close
      :modal-append-to-body="true"
      :append-to-body="true"
      :lock-scroll="true"
      :class="$style.dialog">
      <div v-if="selectedRow" :class="$style.dialogContent">
        <div 
          v-for="group in detailFieldGroups" 
          :key="group.title"
          :class="$style.detailGroup">
          <div :class="$style.groupTitle">{{ group.title }}</div>
          <el-card :class="$style.groupContent" shadow-sm="never">
            <div 
              v-for="field in group.fields" 
              :key="field"
              :class="$style.detailRow">
              <span :class="$style.fieldLabel">
                {{ $t(`procurement.fields.${field}`) }}
              </span>
              <span :class="[
                $style.fieldValue,
                field === 'procurement_content' ? $style.primaryContent : ''
              ]">
                {{ formatFieldValue(field, selectedRow[field]) }}
              </span>
            </div>
          </el-card>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGet } from '@/hooks/useHttp'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { definePage } from 'vue-router/auto'
import { Icon } from '@iconify/vue'
const { t } = useI18n()
// Page Configuration
definePage({
  meta: {
    requiresAuth: true
  },
})

// Types
interface ProcurementBidding {
  id: string
  created_at: string
  updated_at: string
  name: string
  country: string
  category: string
  detail: string
  agency: string
  contact_person: string
  contact_phone: string
  contact_email: string
  address: string
  release_date: string
  final_submission_date: string
  procurement_type: string
  procurement_content: string
  procurement_industry: string
  supplier_type: string
  supplier_qualifications: string
  other_supplier_requirements: string
  others: Record<string, any>
  ai_score?: {
    credibility: number // 可信度
    importance: number // 重要性
    authority: number // 权威性
  }
  [key: string]: any
}

interface ApiResponse {
  items: ProcurementBidding[]
  meta: {
    page: number
    page_size: number
    max_page: number
    total: number
  }
}

// Add this type
type ProcurementField = { key: keyof ProcurementBidding }

// Table Columns Configuration
const availableColumns = computed(() => [
  {
    prop: 'name',
    label: t('procurement.fields.name'),
    minWidth: 200,
    showTooltip: true,
    fixed: true,
    formatter: (val: any) => val
  },
  {
    prop: 'country',
    label: t('procurement.fields.country'),
    width: 100,
    sortable: true
  },
  {
    prop: 'category',
    label: t('procurement.fields.category'),
    width: 120,
  },
  {
    prop: 'agency',
    label: t('procurement.fields.agency'),
    minWidth: 180,
  },
  {
    prop: 'contact_person',
    label: t('procurement.fields.contact_person'),
    width: 120
  },
  {
    prop: 'contact_phone',
    label: t('procurement.fields.contact_phone'),
    width: 130
  },
  {
    prop: 'contact_email',
    label: t('procurement.fields.contact_email'),
    width: 180
  },
  {
    prop: 'address',
    label: t('procurement.fields.address'),
    minWidth: 200
  },
  {
    prop: 'release_date',
    label: t('procurement.fields.release_date'),
    width: 130,
    sortable: true
  },
  {
    prop: 'final_submission_date',
    label: t('procurement.fields.final_submission_date'),
    width: 130
  },
  {
    prop: 'procurement_type',
    label: t('procurement.fields.procurement_type'),
    width: 120
  },
  {
    prop: 'procurement_content',
    label: t('procurement.fields.procurement_content'),
    minWidth: 400,
    showTooltip: true
  },
  {
    prop: 'procurement_industry',
    label: t('procurement.fields.procurement_industry'),
    width: 120
  },
  {
    prop: 'ai_score',
    label: t('procurement.fields.aiScore'),
    width: 200,
    sortable: true,
    fixed: 'right',
    formatter: (scores: ProcurementBidding['ai_score']) => {
      return '' // 返回空字符串，使用自定义模板
    }
  }
])

// 默认显示的列
const defaultColumns = ['name', 'country', 'category', 'agency', 'procurement_content', 'ai_score']

// 选中的列
const selectedColumns = ref<string[]>(defaultColumns)

// 实际显示的列
const visibleColumns = computed(() => {
  return availableColumns.value.filter(col => selectedColumns.value.includes(col.prop))
})

// 重置列设置
const resetColumns = () => {
  selectedColumns.value = defaultColumns
}

// Update detailFields
const detailFieldGroups = computed(() => [
  {
    title: t('procurement.detail.basicInfo'),
    fields: ['name', 'country', 'category', 'agency']
  },
  {
    title: t('procurement.detail.contactInfo'),
    fields: ['contact_person', 'contact_phone', 'contact_email', 'address']
  },
  {
    title: t('procurement.detail.timeInfo'),
    fields: ['release_date', 'final_submission_date']
  },
  {
    title: t('procurement.detail.procurementInfo'),
    fields: ['procurement_type', 'procurement_content', 'procurement_industry']
  },
  {
    title: t('procurement.detail.supplierInfo'),
    fields: ['supplier_type', 'supplier_qualifications', 'other_supplier_requirements']
  }
])

// State
const tableData = ref<ProcurementBidding[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)
const showDetail = ref(false)
const selectedRow = ref<ProcurementBidding | null>(null)

// Data Fetching
const { execute: fetchData } = useGet<ApiResponse>(
  '/procurement_bidding/procurement_data',
  {},
  {
    immediate: false,
  }
)

// Methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await fetchData(0, {
      params: {
        page: currentPage.value,
        page_size: pageSize.value
      }
    })
    if (response) {
      // 为每条数据添加随机分数
      tableData.value = response.items.map(item => ({
        ...item,
        ai_score: {
          credibility: Math.floor(Math.random() * 5 + 6),
          importance: Math.floor(Math.random() * 5 + 6),
          authority: Math.floor(Math.random() * 5 + 6)
        }
      }))
      total.value = response.meta.total
    }
  } catch (error) {
    console.error('Failed to load procurement data:', error)
    ElMessage.error(t('procurement.messages.loadError'))
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatPhone = (phone: string) => {
  return phone.replace(/['"]/g, '')
}

const formatContent = (content: string) => {
  return content.split('|').join('、')
}

const handleRowClick = (row: ProcurementBidding) => {
  selectedRow.value = row
  showDetail.value = true
}

const formatFieldValue = (key: any, value: any) => {
  if (key.includes('date')) {
    return formatDate(value)
  }
  if (key === 'contact_phone') {
    return formatPhone(value)
  }
  if (key === 'procurement_content') {
    return formatContent(value)
  }
  return value
}

// Lifecycle
onMounted(() => {
  loadData()
})

// 添加新的状态和方法
const filters = ref({
  country: 'japan',
  category: 'public',
  industry: 'energy',
  service: 'consulting'
})

// 模拟的选项数据
const countryOptions = [
  { value: 'japan', label: '日本' },
  { value: 'china', label: '中国' },
  { value: 'usa', label: '美国' }
]

const categoryOptions = [
  { value: 'public', label: '公开招标' },
  { value: 'invite', label: '邀请招标' },
  { value: 'negotiate', label: '竞争性谈判' }
]

const industryOptions = [
  { value: 'it', label: t('procurement.options.industry.it') },
  { value: 'construction', label: t('procurement.options.industry.construction') },
  { value: 'medical', label: t('procurement.options.industry.medical') },
  { value: 'education', label: t('procurement.options.industry.education') },
  { value: 'energy', label: t('procurement.options.industry.energy') }
]

const serviceOptions = [
  { value: 'consulting', label: t('procurement.options.service.consulting') },
  { value: 'engineering', label: t('procurement.options.service.engineering') },
  { value: 'maintenance', label: t('procurement.options.service.maintenance') },
  { value: 'training', label: t('procurement.options.service.training') }
]

// 导出按钮点击处理
const handleExport = () => {
  ElMessage.success(t('procurement.messages.exportSuccess'))
}
</script>

<style lang="scss" module>
.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--p-larger);
}

.content {
  width: 100%;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--main-bg-color);
  border-radius: var(--radius-default);
  box-shadow: var(--shadow-normal);
}

.table {
  width: 100%;
  flex: 1;
  overflow: hidden;

  :global {
    .el-table__inner-wrapper {
      height: 100%;
    }

    .el-table__header {
      th {
        background-color: var(--table-header-bg-color);
        color: var(--font-color-2);
        font-weight: var(--weight-font-Semiblod);
        padding: var(--p-default);
        height: 40px;
        border-bottom: 1px solid var(--color-border-2);
      }
    }
    
    .el-table__body-wrapper {
      height: calc(100% - 40px);
      overflow-y: auto;
    }

    .el-table__row {
      cursor: pointer;
      transition: background-color 0.3s;
      
      td {
        padding: var(--p-default);
        color: var(--font-color-1);
      }
      
      &:hover {
        background-color: var(--brand-transparent-hover) !important;
        td {
          background-color: var(--brand-transparent-hover) !important;
        }
      }
    }

    .el-table--striped {
      .el-table__body {
        tr.el-table__row--striped {
          td {
            background-color: var(--gray-1);
          }
        }
      }
    }
  }
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.paginationBox {
  shrink: 0;
  background-color: var(--main-bg-color);
  padding: var(--p-default);
  border-top: 1px solid var(--color-border-2);
}

.pagination {
  display: flex;
  justify-content: flex-end;
  
  :global {
    .el-pagination.is-background {
      .btn-next,
      .btn-prev,
      .el-pager li {
        background-color: transparent;
        color: var(--font-color-2);
        
        &:not(.disabled):hover {
          color: var(--primary-color);
        }
        
        &.active {
          background-color: var(--primary-color);
          color: white;
        }
      }
    }
  }
}

.dialog {
  :global {
    .el-dialog {
      margin: 8vh auto !important;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .el-dialog__header {
      margin: 0;
      padding: var(--p-larger);
      border-bottom: var(--divider-height) solid var(--color-border-2);
      shrink: 0;
      
      .el-dialog__title {
        font-size: var(--size-font-5);
        font-weight: var(--weight-font-Semiblod);
        color: var(--font-color-1);
      }
    }
    
    .el-dialog__body {
      padding: 0;
      margin: 0;
      flex: 1;
      overflow: hidden;
    }
  }
}

.dialogContent {
  height: 100%;
  max-height: calc(85vh - 120px);
  overflow-y: auto;
  padding: var(--p-larger);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-background-color);
    border-radius: 3px;

    &:hover {
      background-color: var(--scrollbar-hover-background-color);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track-background);
  }
}

.detailGroup {
    margin-bottom: 15px;
}

.groupTitle {
  font-size: var(--size-font-4);
  font-weight: var(--weight-font-Semiblod);
  color: var(--font-color-1);
  margin-bottom: var(--m-default);
  padding-left: var(--p-larger);
  border-left: 3px solid var(--primary-color);
}

.groupContent {
  background-color: var(--gray-1);
  border-radius: var(--radius-default);
  
  :global(.el-card__body) {
    padding: var(--p-larger);
  }
}

.detailRow {
  display: flex;
  align-items: flex-start;
  padding: var(--p-default) 0;
  
  &:not(:last-child) {
    border-bottom: 1px dashed var(--color-border-2);
  }
}

.fieldLabel {
  flex: 0 0 140px;
  color: var(--font-color-2);
  font-weight: var(--weight-font-Medium);
  padding-right: var(--p-larger);
}

.fieldValue {
  flex: 1;
  color: var(--font-color-1);
  line-height: 1.6;
  word-break: break-word;
  
  &.primaryContent {
    color: var(--primary-color);
    font-weight: var(--weight-font-Semiblod);
  }
}

// 添加新的样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--m-large);
}

.headerActions {
  :global {
    .el-select {
      width: 180px;
    }
  }
}

.exportIcon {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  color: var(--primary-color);
  border-radius: var(--radius-mini);
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--brand-transparent-hover);
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.primaryContent {
  color: var(--primary-color);
  font-weight: var(--weight-font-Semiblod);
  
  &:hover {
    color: var(--link-primary-color-hover);
  }
}

.settingIcon {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  color: var(--font-color-2);
  border-radius: var(--radius-mini);
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--brand-transparent-hover);
    color: var(--primary-color);
  }
}

.columnSettings {
  .settingsHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: var(--p-default);
    margin-bottom: var(--p-default);
    border-bottom: 1px solid var(--color-border-2);
    font-weight: var(--weight-font-Semiblod);
  }
}

.columnOption {
  padding: 8px 0;
  
  &:hover {
    background-color: var(--brand-transparent-hover);
  }
}

.scores {
  display: flex;
  gap: 4px;
  align-items: center;
}

.scoreChip {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 12px;
  
  :global(svg) {
    width: 12px;
    height: 12px;
  }
}

.credibility {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.importance {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.authority {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
}
</style>
