<template>
  <el-scrollbar :class="$style.scrollbar" always>
    <div :class="$style.resultsContainer">
      <div
        v-for="(result, index) in results"
        :key="index"
        :class="$style.item">
        <div :class="$style.itemContent">
          <el-image
            v-if="result && (result.favicon || result.image)"
            :src="result.favicon || result.image"
            :class="$style.favicon"
            @error="handleImageError"/>
          <a 
            v-if="result"
            :href="result.url" 
            target="_blank" 
            :class="$style.link">
            <span>
              <overflowText>{{ result.title || result.text?.slice(0, 50) }}</overflowText>
            </span>
          </a>
          <div :class="$style.scores">
            <el-tooltip
              v-if="result && result.credibility_score !== undefined"
              :content="$t('global.credibilityScore')"
              placement="top">
              <div :class="[$style.scoreChip, $style.credibility]">
                <Icon icon="mdi-shield-check"/>
                {{ formatScore(result.credibility_score) }}
              </div>
            </el-tooltip>
            <el-tooltip
              v-if="result && result.importance_score !== undefined"
              :content="$t('global.importanceScore')"
              placement="top">
              <div :class="[$style.scoreChip, $style.importance]">
                <Icon icon="mdi-star"/>
                {{ formatScore(result.importance_score) }}
              </div>
            </el-tooltip>
            <el-tooltip
              v-if="result && result.average_score !== undefined"
              :content="$t('global.averageScore')"
              placement="top">
              <div :class="[$style.scoreChip, $style.average]">
                <Icon icon="mdi-chart-line"/>
                {{ formatScore(result.average_score) }}
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import type { url } from '@/pages/format-report/type'
import { Icon } from '@iconify/vue'
import overflowText from './overflow-text.vue'

defineProps<{
  results: url[]
}>()

const formatScore = (score: number) => {
  return score.toFixed(1)
}

const handleImageError = (event: Event) => {
  const imgElement = event.target as HTMLImageElement
}
</script>

<style lang="scss" module>
.scrollbar {
  height: 100%;
  
  :global(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }
}

.resultsContainer {
  padding: 8px 0;
}

.item {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.itemContent {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.favicon {
  width: 16px;
  height: 16px;
  shrink: 0;
}

.link {
  flex: 1;
  min-width: 0;
  color: var(--primary-color);
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.scores {
  display: flex;
  gap: 8px;
  margin-left: auto;
  shrink: 0;
}

.scoreChip {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  
  svg {
    width: 14px;
    height: 14px;
  }
}

.credibility {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.importance {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.average {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style> 