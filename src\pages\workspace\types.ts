import type { CompletedAgent } from '@/pages/dashboard/type'
import type { ReportList as StandardReport } from '@/pages/format-report/type'
import type { ToolResult } from '@/composables/useReportProgress'
import { BIDING_AGENT_TYPES, SPECIAL_AGENT_TYPES } from '@/composables/useReportProgress'

export interface AgentStep {
  title: string;
  description: string;
  status: string; // 'PENDING', 'PROCESSING', 'COMPLETED', 'ERROR'
}

// Interface for processed running agent data used in template
export interface RunningAgent {
  id: string;
  name: string; // Agent name (priority: agent_name > report_name)
  report_name: string;
  duty: string; // Task description
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'ERROR'; // Overall task status
  avatar: string;
  steps: AgentStep[]; // Mapped steps
  progress: number;
  agent_name?: string;
  report_type?: string;
  date?: string; // Added date for sorting
  description?: string; // Current step description
}

export interface ExtractionData {
  extraction: {
    basic_info: {
      title: string
      country: string
      publication_date: string
      submission_deadline: string
      award_date: string | null
      detailed_description: string
      estimated_value: string | null
      currency: string | null
    }
    issuing_organization: {
      name: string
      description: string | null
      contact_info: {
        name: string | null
        phone: string
        email: string
        address: string
        other_info: string | null
      }
      website: string
    }
    tender_requirements: {
      category: string
      items: string[]
      industry_sector: string
    }
    supplier_requirements: {
      supplier_requirement: string[]
      business_registration_requirement: string | null
      eligibility_requirement: string | null
    }
  }
  classification: {
    first_level: {
      name: string
      confidence: number
      reasoning: string
    }
    second_level: {
      name: string
      confidence: number
      reasoning: string
    }
    third_level?: any
  }
  scoring: {
    project_amount: {
      name: string
      score: number
      reasoning: string
    }
    vendor_bias: {
      name: string
      score: number
      reasoning: string
    }
    client_authority: {
      name: string
      score: number
      reasoning: string
    }
    future_collaboration: {
      name: string
      score: number
      reasoning: string
    }
    information_completeness: {
      name: string
      score: number
      reasoning: string
    }
    comprehensive: {
      score: number
      breakdown: Record<string, number>
      reasoning: string
    }
    language: string
  }
  procurement_type: {
    type: string
    confidence: number
    classification_reason: string
  }
  new_title: string
  keywords: string[]
  processing_timestamp: string
}

// 新增：招标项目基础接口（包含列表和详情都需要的基本字段）
export interface BidingItemBase {
  _id: string;
  new_title: string;
  created_at: string | { $date: string }; // 兼容两种格式
  updated_at: string | { $date: string }; // 兼容两种格式
  update_time?: string; // 兼容字段
}

// 新增：列表项简化数据结构（用于新的列表接口）
export interface BidingListItem extends Omit<BidingItemBase, 'created_at' | 'updated_at'> {
  created_at: string;
  updated_at: string;
  recommend_score: number;
  submission_deadline: string;
  country?: string; // 建议后端添加此字段，用于表格显示
  recommend_reason?: string; // 建议后端添加此字段，用于推荐理由 popover
}

// 扩展版：支持简化数据结构的列表项（用于向后兼容）
export interface BidingListItemCompatible extends BidingListItem {
  recommendation?: {
    recommend_score: number;
    recommend_reason?: string;
  };
  extraction?: {
    basic_info?: {
      country?: string;
      submission_deadline?: string;
    };
  };
}

// Type for individual items returned by the biding API data array (详情数据的完整结构)
export interface BidingAnalysisItem extends BidingItemBase {
  bidding_result_ref?: string; // Optional based on example
  classification: {
    first_level: { confidence: number; name: string; reasoning: string };
    second_level: { confidence: number; name: string; reasoning: string };
    third_level?: any; // Type unknown from example
  };
  company_id: string;
  created_at: { $date: string } | string; // 兼容两种格式
  extraction: {
    basic_info: {
      award_date?: string | null;
      country: string;
      currency?: string | null;
      detailed_description?: string | null;
      estimated_value?: number | null;
      publication_date?: string;
      submission_deadline?: string | null;
      title: string;
    };
    issuing_organization?: {
      contact_info?: {
        name?: string;
        phone?: string;
        email?: string;
        address?: string;
        other_info?: string;
      };
      description?: string | null;
      name?: string | null;
      website?: string | null;
    }; // Optional
    supplier_requirements?: {
      business_registration_requirement?: string | null;
      eligibility_requirement?: string | null;
      supplier_requirement?: string[];
    }; // Optional
    tender_requirements?: {
      category: string;
      industry_sector: string;
      items: string[];
    }; // Optional
  };
  keywords: string[];
  original_id?: string; // Optional
  processing_timestamp: { $date: string } | string; // 兼容两种格式
  procurement_type: {
    classification_reason: string;
    confidence: number;
    type: string;
  };
  recommendation?: {
    recommend_reason: string;
    recommend_score: number;
    update_time: string;
  }; // Added recommendation field
  scoring: {
    client_authority: { name: string; reasoning: string; score: number };
    comprehensive: { breakdown: Record<string, number>; reasoning: string; score: number };
    future_collaboration: { name: string; reasoning: string; score: number };
    information_completeness: { name: string; reasoning: string; score: number };
    language: string;
    project_amount: { name: string; reasoning: string; score: number };
    vendor_bias: { name: string; reasoning: string; score: number };
  };
  source_crawler?: string; // Optional
  source_url?: string; // Optional
  updated_at: { $date: string } | string; // 兼容两种格式
  user_id: string;
}

// 新增：招标结果项联合类型（可以是列表项或详情项）
export type BidingResultItem = BidingListItemCompatible | BidingAnalysisItem;

// BidingAnalysisResult represents the summarized object for the report feed
export interface BidingAnalysisResult {
  id: string; // Unique ID for the summary card
  report_name: string; // Display name (e.g., from extraction.basic_info.title)
  updated_at: string; // Use a relevant date string from API (e.g., processing_timestamp)
  status: 'completed' | 'error' | 'running' | 'pending';
  report_type: string;
  date: string; // Use the same date as updated_at for sorting/grouping
  is_loading?: boolean;
  avatar?: string; // Add optional avatar field
  agent_name?: string;
  query?: Record<string, string>;
  total?: number; // 总数量字段，用于分页
  total_count?: number; // 备用总数量字段

  // 使用联合类型支持不同的数据结构
  results: BidingResultItem[]; // 可以是简化的列表项或完整的详情项
}

// 新增：详情接口返回结构
export interface BidingDetailApiResponse {
  success: boolean;
  data: BidingAnalysisItem;
}

// 新增：列表接口返回结构
export interface BidingListApiResponse {
  agent_name: string;
  agent_type: string;
  report_type: string;
  query?: Record<string, string>;
  results: BidingListItem[]; // 简化的列表项结构
  total: number;
  total_count?: number; // 添加可能的另一个总数量字段
}

// 保持向后兼容的API响应类型（原有的接口）
export interface BidingApiResponse {
  agent_name: string;
  agent_type: string;
  report_type: string;
  query?: Record<string, string>;
  results: BidingListItem[]; // 更新为简化的列表项结构
  total: number;
  total_count?: number; // 添加可能的另一个总数量字段
  // Add any other top-level fields returned by the API if necessary
}

// 新增：类型守卫函数 - 检查是否为列表项
export function isBidingListItem(item: BidingResultItem): item is BidingListItemCompatible {
  return !('scoring' in item) && !('classification' in item) && !('procurement_type' in item)
}

// 新增：类型守卫函数 - 检查是否为详情项
export function isBidingAnalysisItem(item: BidingResultItem): item is BidingAnalysisItem {
  return 'scoring' in item && 'classification' in item && 'procurement_type' in item
}

// 新增：类型守卫函数 - 检查是否为兼容的列表项
export function isBidingListItemCompatible(item: any): item is BidingListItemCompatible {
  return item && 
         typeof item._id === 'string' && 
         typeof item.new_title === 'string' && 
         (typeof item.recommend_score === 'number' || 
          (item.recommendation && typeof item.recommendation.recommend_score === 'number'))
}

// Ensure Report includes StrategicReport and BidingAnalysisResult with report_name
export type Report = RunningAgent | CompletedAgent | StandardReport | BidingAnalysisResult | StrategicReport

// Review isBidingAnalysisResult guard based on the new summary structure
export function isBidingAnalysisResult(report: any): report is BidingAnalysisResult {
  // Check for report_name as well
  return !!report && 'report_type' in report && report.report_type === 'TENDER' && 'results' in report && 'report_name' in report
}

export function isMonitorAgent(report: any): report is CompletedAgent {
  return !!report && typeof report === 'object' && 'report_type' in report && report.report_type !== undefined && SPECIAL_AGENT_TYPES.includes(report.report_type as any) && 'frequency' in report
}

export function isRunningAgent(report: any): report is RunningAgent {
  return !!report && typeof report === 'object' && 'steps' in report && 'progress' in report && !('language' in report) && ('status' in report && (report.status === 'RUNNING' || report.status === 'PENDING'))
}

export function isStandardReport(report: any): report is StandardReport {
  return !!report && typeof report === 'object' && 'language' in report && 'summary' in report && !('steps' in report) && !('frequency' in report) && (!('report_type' in report) || !BIDING_AGENT_TYPES.includes(report.report_type as any))
}

export interface StrategicOpportunity {
  _id: string;
  agent_id: string;
  company_id: string;
  country: string;
  created_at: string;
  entry_strategy: string;
  first_level_industry: string;
  key_opportunities: string[];
  news_type: string;
  opportunity_details: string;
  second_level_industry: string;
  status: string;
  timeline: string;
  update_at: string;
  updated_at: string;
  url: string;
  user_id: string;
  summary: string;
}

export interface StrategicApiResponse {
  agent_type: string;
  agent_name: string;
  results: StrategicOpportunity[];
  total: number;
  current_page: number; 
  page_size: number;
}

// RENAME and UPDATE the type guard to check for StrategicReport
export function isStrategicReport(report: any): report is StrategicReport {
  // Check for properties specific to StrategicReport, including its 'results' structure
  return report && report.report_type === 'STRATEGIC' && Array.isArray(report.results) && 'agent_type' in report &&
         (report.results.length === 0 || ('country' in report.results[0] && 'news_type' in report.results[0])) // Check a couple of fields from StrategicResult
}

// --- Strategic Insight Types ---
// Moved from StrategicRespon.vue
export interface StrategicResult {
  id: string;
  opportunity_details: string;
  entry_strategy: string;
  key_opportunities: string[];
  timeline: string;
  url?: string;
  update_at: string; // Or updated_at? Check consistency
  updated_at: string;
  country: string;
  first_level_industry: string;
  second_level_industry: string;
  news_type: string;
  summary: string;
}

// Moved from StrategicRespon.vue
export interface StrategicReport {
  id: string;
  agent_name?: string;
  report_name?: string;
  agent_type: string;
  report_type: string;
  results: StrategicResult[];
  updated_at: string;
  status: string;
  date: string;
  is_loading?: boolean;
  avatar?: string;
  total?: number;
  current_page?: number;
  page_size?: number;
}

// Type guard for StrategicReport (if needed, can be added later)
// export function isStrategicReport(report: any): report is StrategicReport { ... } 

// Add these new type definitions
export interface IndustryOption {
  label: string;
  value: string;
}

export interface IndustryGroup {
  label: string;
  options: IndustryOption[];
}

// 新的商机新闻列表项接口（适配新接口）
export interface StrategicNewsItem {
  id: string;
  title: string;
  updated_at: string;
}

// 新的商机新闻API响应接口
export interface StrategicNewsApiResponse {
  agent_name: string;
  agent_type: string;
  report_type: string;
  total: number;
  results: StrategicNewsItem[];
  current_page: number;
  page_size: number;
}


// 商机新闻详情接口（复用现有的详情结构）
export interface StrategicNewsDetailResponse {
  code: number;
  success: boolean;
  message: string;
  data: StrategicOpportunity; // 详情接口返回完整的机会数据
} 