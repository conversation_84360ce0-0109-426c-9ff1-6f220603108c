<template>
  <Card @click="handleClick" class="h-[320px] flex flex-col hover:translate-y-[-4px] transition-all cursor-pointer overflow-hidden">
    <CardHeader class="flex flex-row gap-5 pb-5 border-b shrink-0 min-h-[120px]">
      <!-- <Avatar v-if="item.avatar" class="h-[70px] w-[70px] rounded-full p-4 bg-gradient-to-br from-[rgba(68,102,188,0.1)] to-[rgba(68,102,188,0.05)] shadow-md shrink-0">
        <AvatarImage :src="item.avatar" alt="Report Icon" class="h-[42px] w-[42px] object-contain"/>
        <AvatarFallback class="h-[42px] w-[42px] bg-[#4466BC]">
          <Icon icon="mdi:file-document-outline" class="text-white"/>
        </AvatarFallback>
      </Avatar>
      <div v-else class="h-[70px] w-[70px] rounded-full p-4 flex items-center justify-center bg-gradient-to-br from-[rgba(68,102,188,0.1)] to-[rgba(68,102,188,0.05)] shadow-md shrink-0">
        <div class="h-[42px] w-[42px] bg-[#4466BC] mask-file"></div>
      </div> -->
      
      <div class="text-left overflow-hidden flex flex-col">
        <div class="min-h-[56px] flex items-start">
          <CardTitle class="text-[20px] line-clamp-2 break-words">{{ item.report_name }}</CardTitle>
        </div>
        <div class="flex flex-wrap gap-2 items-center mt-auto h-[28px]">
          <Badge v-if="item.agent_name" variant="outline" class="bg-[rgba(68,102,188,0.05)] text-[--font-color-2] font-normal truncate max-w-full">
            <!-- <Icon icon="mdi:account" class="text-main-color mr-1 shrink-0"/> -->
            <Avatar v-if="item.avatar" class="h-[28px] w-[28px] rounded-full p-4 bg-gradient-to-br from-[rgba(68,102,188,0.1)] to-[rgba(68,102,188,0.05)] shadow-md shrink-0">
              <AvatarFallback class="h-[28px] w-[28px] bg-[#4466BC]">
                <!-- <Icon icon="mdi:account" class="text-white"/> -->
                <component :is="item.avatar" class="text-white"/>
              </AvatarFallback>
            </Avatar>
            <span class="truncate">{{ item.agent_name }}</span>
          </Badge>
          <div v-else class="h-[28px]"></div>
        </div>
      </div>
    </CardHeader>
    
    <CardContent class="flex-1 overflow-hidden py-4">
      <OverClampText
        v-if="item.summary"
        :content="item.summary"
        :line-clamp="4"
        class="text-[15px] text-[--font-color-2] leading-7 text-left break-words"/>
    </CardContent>
    
    <CardFooter v-if="item.updated_at" class="border-t border-dashed border-[rgba(68,102,188,0.15)] pt-3 text-[14px] font-medium text-[--primary-color] shrink-0">
      <Icon icon="mdi:clock-outline" class="text-main-color mr-1 shrink-0"/>
      <span class="truncate">{{ item.updated_at || '暂无更新时间' }}</span>
    </CardFooter>
  </Card>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import OverClampText from '@/components/OverClampText.vue'
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card'
import { 
  Avatar, 
  AvatarImage, 
  AvatarFallback 
} from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'

interface TemplateItem {
  id: string;
  report_name: string;
  agent_name: string;
  avatar?: string;
  summary: string;
  updated_at?: string;
}

const props = defineProps<{
  item: TemplateItem;
}>()

const emit = defineEmits<{
  click: [id: string];
}>()

const handleClick = () => {
  emit('click', props.item.id)
}
</script>

<style lang="scss">
.mask-file {
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>') no-repeat center / contain;
}
</style>

