<template>
  <div :class="$style.container">
    <div :class="$style.header">
      <h4 :class="$style.title">{{ $t("global.searchResults") }}</h4>
    </div>
    <div :class="$style.content">
      <el-tabs v-model="activeTab">
        <el-tab-pane 
          :label="$t('formatReport.citedReferences', { count: citedCount })" 
          name="cited">
          <SearchResultsSection :results="searchResults"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { url } from '@/pages/format-report/type'
import SearchResultsSection from './SearchResultsSection.vue'

const props = defineProps<{
  searchResults: url[]
}>()

const activeTab = ref('cited')

const citedCount = computed(() => {
  return props.searchResults.length
})

</script>

<style lang="scss" module>
.container {
  border: 1px solid #eee;
  border-radius: 8px;
  height: calc(100vh - 220px);
  display: flex;
  flex-direction: column;
}

.header {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: var(--el-fill-color-light);
}

.title {
  margin: 0;
  font-size: 14px;
  color: black;
}

.content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  :global(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :global(.el-tabs__header) {
    margin-bottom: 0;
  }

  :global(.el-tabs__nav-wrap) {
    padding: 0 16px;
  }

  :global(.el-tabs__item) {
    padding: 0 32px;
    height: 40px;
    line-height: 40px;
    
    &:first-child {
      padding-left: 0;
    }
  }

  :global(.el-tabs__content) {
    flex: 1;
    overflow: hidden;
  }

  :global(.el-tab-pane) {
    height: 100%;
  }
}
</style> 