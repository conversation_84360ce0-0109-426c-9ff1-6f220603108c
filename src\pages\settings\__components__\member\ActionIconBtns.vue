<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface Props {
  /**
   * 是否禁用
   */
  disabled?: boolean;
}

defineProps<Props>()

const emit = defineEmits<{
  delete: [void];
}>()
</script>

<template>
  <div :class="$style.iconWarper">
    <Icon icon="mdi:delete"
          class="text-[red] text-[16px]"
          :class="{ [$style.disabled]: disabled }"
          @click="!disabled && emit('delete')"/>
  </div>
</template>

<style lang="scss" module>
.iconWarper {
  display: flex;
  align-items: center;

  svg {
    cursor: pointer;
    outline: none;
    margin-right: 12px;

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}
</style>
