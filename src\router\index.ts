import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router/auto'
import { setupLayouts } from 'virtual:generated-layouts'
import { mergeRouteConfig } from './route.config'
import { fixRouteIndex, redirectTo } from '@/utils/router'
import emitter from '@/utils/events'

// 递归设置路由配置
const deepSetupRoutes = (routers: RouteRecordRaw[], setups: ((route: RouteRecordRaw) => RouteRecordRaw)[]): RouteRecordRaw[] => {
  return routers.map((route) => {
    const children = route.children ? deepSetupRoutes(route.children, setups) : undefined
    route.children = children
    for (const setup of setups) {
      route = setup(route)
    }
    return route
  })
}
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  extendRoutes: (_routers) => {
    const routers = setupLayouts(_routers)
    console.log('routers', routers)
    console.log('🚀 ~ deepSetupRoutes:', deepSetupRoutes(routers, [mergeRouteConfig]))

    return deepSetupRoutes(routers, [ mergeRouteConfig])
  }
})

emitter.on('logout', () => {
  const to = router.resolve('/landing')
  const from = router.currentRoute.value
  const route = redirectTo(from, to, {
    addSSORedirectQuery: true,
    redirectPath: to.path,
  })
  if (route) {
    router.push(route)
  }
})
export default router

const modules = import.meta.glob('./guards/*.ts')
// 提取文件名中的数字并进行排序
const sortedPaths = Object.keys(modules).sort((a, b) => {
  // 提取倒数第二个数字，如果不存在则默认为 0
  const extractNumber = (path: string) => {
    const parts = path.split('.')
    const numberPart = parts.length > 2 ? parts[parts.length - 2] : '0'
    const number = numberPart.match(/\d+/)
    return number ? parseInt(number[0], 10) : 0
  }

  return extractNumber(a) - extractNumber(b)
})

// 按排序后的顺序导入模块
export const loadGuards = async () => {
  for (const path of sortedPaths) {
    await modules[path]() // 等待当前模块加载完成
    console.log(`[Router Guard] ${path} loaded`)
  }
}
