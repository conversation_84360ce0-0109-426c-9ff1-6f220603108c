<!-- src/components/PdfComponents/PdfActionButtons.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import PdfSettingsDialog from '@/components/PdfComponents/PdfSettingsDialog.vue'
import { usePdfActions } from '@/hooks/usePdfActions'
import type { CreatedReport } from '@/pages/format-report/type'

const props = defineProps<{
  containerRef: HTMLElement
  title: string
  isReady?: boolean
  chartImages?: Record<string, string>
  searchSource?:CreatedReport['search_result']
  timelineData?:CreatedReport['time_line']
  legalTimelineData?:CreatedReport['legal_data']['time_line']
}>()

const { t } = useI18n()

const {
  showSettings,
  downloadPDF,
  previewPDF,
  updatePdfSettings
} = usePdfActions(computed(() => props.containerRef))

const handlePdfSettings = (settings: any) => {
  updatePdfSettings(settings)
}

</script>

<template>
  <div class="flex items-center gap-4">
    <template v-if="isReady">
      <el-tooltip :content="t('intelligence.markdown.settings')">
        <Icon
          icon="mdi:settings-outline"
          class="text-[#333] hover:text-main-color text-[24px] cursor-pointer"
          @click="props.containerRef && (showSettings = true)"/>
      </el-tooltip>
      
      <el-tooltip :content="t('intelligence.markdown.previewpdf')">
        <Icon
          icon="mdi:file-eye-outline"
          class="text-[#333] text-[24px] hover:text-main-color cursor-pointer"
          @click="previewPDF(title, chartImages, searchSource, timelineData, legalTimelineData)"/>
      </el-tooltip>
      
      <el-tooltip :content="t('intelligence.markdown.downloadpdf')">
        <Icon
          icon="mdi:file-pdf-outline" 
          class="text-[#333] text-[24px] hover:text-main-color cursor-pointer"
          @click="downloadPDF(title, chartImages, searchSource, timelineData, legalTimelineData)"/>
      </el-tooltip>
    </template>
    <template v-else>
      <div
        class="text-sm text-gray-500 flex items-center gap-2">
        <Icon icon="mdi:loading" class="animate-spin"/>
        {{ t("global.generating") }}
      </div>
    </template>

    <PdfSettingsDialog
      v-model="showSettings"
      :content="containerRef"
      :title="title"
      :images="chartImages"
      :searchSource="searchSource"
      :time_line-data="timelineData"
      @confirm="handlePdfSettings"/>
  </div>
</template>