<template>
  <div v-if="loading" class="flex items-center justify-start mx-auto max-w-[1150px] w-full">
    <Icon 
      icon="mdi:loading" 
      class="animate-spin text-main-color"/>
    <span class="text-sm text-main-color">{{ $t('formatReport.threadLoading') }}</span>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
defineProps<{
  loading: boolean,
  text: string
}>()
</script>

<style lang="scss" module>
 .container {
 }
</style>
