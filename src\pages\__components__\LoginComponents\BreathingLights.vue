<template>
  <div :class="$style.container" id="breathing-lights">
    <div
      v-for="particle in particles"
      :key="particle.id"
      :class="$style.particle"
      :style="{
        left: `${particle.x}px`,
        top: `${particle.y}px`,
        animationDuration: `${particle.duration}s`,
        width: `${particle.size}px`,
        height: `${particle.size}px`,
        '--hue': `${particle.hue}`,
      }"/>
  </div>
</template>

<script lang="ts" setup>
import { shallowRef, onMounted, onUnmounted } from 'vue'
import { useRafFn } from '@vueuse/core'

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  duration: number;
  hue: number;
}

// 配置项
const PARTICLE_COUNT = 30 // 粒子数量
const MIN_SIZE = 6 // 最小粒子尺寸
const MAX_SIZE = 25 // 最大粒子尺寸
const MIN_DURATION = 2 // 最小动画持续时间
const MAX_DURATION = 4 // 最大动画持续时间

const particles = shallowRef<Particle[]>([])
let containerEl: HTMLElement | null = null
let containerWidth = 0
let containerHeight = 0

const { pause, resume } = useRafFn((time) => {
  particles.value = particles.value.map(particle => 
    Math.random() < 0.01 ? createParticle() : particle
  )
}, {
  immediate: false,
  fpsLimit: 1000 / 30
})

// 使用 IntersectionObserver 优化可见性处理
const observeVisibility = () => {
  const observer = new IntersectionObserver((entries) => {
    entries[0].isIntersecting ? resume() : pause()
  }, { threshold: 0 })
  
  if (containerEl) {observer.observe(containerEl)}
  return observer
}

// 生成随机数的工具函数
const random = (min: number, max: number) => {
  return Math.random() * (max - min) + min
}

// 创建新粒子
const createParticle = (): Particle => {
  return {
    id: Math.random(),
    x: random(0, containerWidth),
    y: random(containerHeight * 0.5, containerHeight), // 只在下半部分生成
    size: random(MIN_SIZE, MAX_SIZE),
    duration: random(MIN_DURATION, MAX_DURATION),
    hue: random(180, 220),
  }
}

// 初始化粒子
const initParticles = () => {
  if (!containerEl) {return}
  containerWidth = containerEl.clientWidth
  containerHeight = containerEl.clientHeight
  particles.value = Array.from({ length: PARTICLE_COUNT }, createParticle)
}

onMounted(() => {
  containerEl = document.getElementById('breathing-lights')
  if (!containerEl) {return}

  const resizeObserver = new ResizeObserver(initParticles)
  resizeObserver.observe(containerEl)
  
  const visibilityObserver = observeVisibility()
  
  initParticles()
  resume()

  onUnmounted(() => {
    pause()
    resizeObserver.disconnect()
    visibilityObserver.disconnect()
    containerEl = null
  })
})
</script>

<style lang="scss" module>
.container {
  position: absolute;
  bottom: 0;
  left: 0;  
  width: 100%;
  /* height: 100%; */
  height: 50vh;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: radial-gradient(
    circle at center,
    hsl(var(--hue), 100%, 100%) 0%,
    hsla(var(--hue), 100%, 90%, 0.9) 40%,
    hsla(var(--hue), 100%, 85%, 0) 100%
  );
  box-shadow: 
    0 0 20px hsla(var(--hue), 100%, 95%, 0.95),
    0 0 35px hsla(var(--hue), 100%, 90%, 0.7),
    0 0 50px hsla(var(--hue), 100%, 85%, 0.4);
  border-radius: 50%;
  pointer-events: none;
  animation: breathe infinite ease-in-out;
}

@keyframes breathe {
  0% {
    transform: scale(0);
    opacity: 0;
    filter: brightness(0.8) blur(2px);
  }
  50% {
    transform: scale(1);
    opacity: 1;
    filter: brightness(1.5) blur(1px);
  }
  100% {
    transform: scale(0);
    opacity: 0;
    filter: brightness(0.8) blur(2px);
  }
}
</style>
