<template>
  <div class="strategic-table-container bg-gray-50/40 p-4 flex-1 min-h-0">
    <div class="h-full flex flex-col bg-white rounded-lg shadow-sm border border-gray-100">
      <!-- Header with title -->
      <div class="flex-shrink-0 p-6 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="w-2 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
            <h2 class="text-xl font-bold text-gray-800">{{ t('workspace.strategic_opportunities') }}</h2>
            <Badge variant="outline" class="ml-2" :class="totalCount === 0 ? 'bg-gray-50 text-gray-500' : ''">
              {{ t('workspace.business_news_count', { count: totalCount }) }}
            </Badge>
          </div>
          
          <!-- Search Control -->
          <div class="flex items-center gap-3">
            <!-- 搜索框 -->
            <div class="relative">
              <SearchIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"/>
              <Input
                :value="searchText"
                :placeholder="t('workspace.search_placeholder')"
                class="pl-10 w-64"
                @input="handleSearchInput"/>
            </div>
          </div>
        </div>
      </div>

      <!-- Content Area -->
      <div class="flex-1 min-h-0">
        <!-- Loading State -->
        <template v-if="isLoading || apiLoading">
          <div class="flex flex-col items-center justify-center h-full space-y-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="text-sm text-gray-600">{{ t('agent.loading') }}</p>
          </div>
        </template>

        <!-- Error State -->
        <template v-else-if="hasError">
          <div class="flex flex-col items-center justify-center h-full space-y-4">
            <AlertCircleIcon class="h-12 w-12 text-red-400"/>
            <h3 class="text-lg font-medium text-red-700">{{ t('workspace.load_error') }}</h3>
            <p class="text-sm text-red-500 text-center max-w-md">
              {{ t('workspace.load_error_description') }}
            </p>
            <Button variant="outline" size="sm" @click="handleRetry">
              {{ t('workspace.retry') }}
            </Button>
          </div>
        </template>

        <!-- Empty State -->
        <template v-else-if="filteredItems.length === 0">
          <div class="flex flex-col items-center justify-center h-full space-y-4">
            <FileTextIcon class="h-12 w-12 text-gray-400"/>
            <h3 class="text-lg font-medium text-gray-700">
              {{ searchText ? t('workspace.no_business_news_found') : t('workspace.no_business_news_data') }}
            </h3>
            <p class="text-sm text-gray-500 text-center max-w-md">
              {{ searchText 
                ? t('workspace.no_business_news_try_keywords', { searchText }) 
                : t('workspace.no_business_news_system_updating')
              }}
            </p>
            <Button v-if="searchText" 
                    variant="outline" 
                    size="sm" 
                    @click="clearFilters">
              {{ t('workspace.clear_search_filters') }}
            </Button>
          </div>
        </template>

        <!-- Strategic Items List -->
        <template v-else>
          <div class="h-full flex flex-col">
            <!-- Table Header -->
            <div class="flex-shrink-0 bg-slate-50/50 border-b border-slate-200 sticky top-0 z-10">
              <div class="flex px-3 py-2.5 text-xs font-medium text-slate-600 items-center">
                <!-- 标题列 -->
                <div class="flex-grow min-w-0 pr-3">{{ t('workspace.strategic_opportunities') }}</div>
                <!-- 更新时间 -->
                <div class="w-20 md:w-24 flex-shrink-0 text-center px-2">{{ t('workspace.update_time') }}</div>
                <!-- 状态列 -->
                <div class="w-16 md:w-20 flex-shrink-0 text-center px-2">{{ t('workspace.status') }}</div>
                <!-- 操作列 -->
                <div class="w-16 flex-shrink-0 text-center">{{ t('workspace.actions') }}</div>
              </div>
            </div>

            <!-- Virtual Scrolling List -->
            <DynamicScroller
              class="flex-1"
              :items="displayedItems"
              :min-item-size="60"
              key-field="id">
              <template v-slot="{ item, index, active }">
                <DynamicScrollerItem
                  :item="item"
                  :active="active"
                  :size-dependencies="[item.summary, item.opportunity_details]"
                  :data-index="index">
                  <div
                    class="flex items-center px-3 py-3 border-b border-slate-100 min-h-[60px] transition-all duration-200 ease-in-out hover:bg-slate-50/80 hover:shadow-sm cursor-pointer"
                    @click="() => handleViewDetail(item, index)">
                    <!-- 标题列 -->
                    <div class="flex-grow min-w-0 pr-3 flex items-center space-x-2">
                      <p class="text-sm text-slate-800 truncate" :title="getItemTitle(item)">{{ getItemTitle(item) }}</p>
                    </div>
                    <!-- 更新时间列 -->
                    <div class="w-20 md:w-24 flex-shrink-0 text-center px-2">
                      <p class="text-xs text-slate-600 leading-tight">{{ formatDateString(item.updated_at) }}</p>
                    </div>
                  </div>
                </DynamicScrollerItem>
              </template>
            </DynamicScroller>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  SearchIcon, 
  AlertCircleIcon, 
  FileTextIcon, 
  EyeIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from 'lucide-vue-next'

// 定义 Strategic 数据项类型
interface StrategicDataItem extends Record<string, any> {
  id: string | number
  country?: string
  first_level_industry?: string
  news_type?: string
  summary?: string
  opportunity_details?: string
  entry_strategy?: string
  key_opportunities?: string[]
  timeline?: string
  url?: string
  recommend_reason?: string
  updated_at?: string
  update_at?: string
  title?: string
}

const props = defineProps<{
  items: StrategicDataItem[]
  isLoading: boolean
  apiLoading: boolean
  hasError: boolean
  searchText: string
  totalCount: number
  t:(key: string, ...args: any[]) => string
  formatDateString: (dateInput: any) => string
}>()

const emit = defineEmits([
  'view-detail', 
  'search', 
  'retry'
])

// 获取项目标题的辅助函数
const getItemTitle = (item: StrategicDataItem): string => {
  return item.title || item.summary || props.t('strategic.unknown_title')
}

// 过滤后的项目列表
const filteredItems = computed(() => {
  return props.items.filter(item => {
    // 搜索文本过滤 - 只搜索标题
    if (props.searchText) {
      const searchLower = props.searchText.toLowerCase()
      const title = getItemTitle(item).toLowerCase()
      
      if (!title.includes(searchLower)) {
        return false
      }
    }
    
    return true
  })
})

// 显示的项目列表 (移除分页逻辑)
const displayedItems = computed(() => {
  return filteredItems.value
})

// 事件处理函数
const handleViewDetail = (item: StrategicDataItem, index: number) => {
  emit('view-detail', item, index)
}

const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('search', target.value)
}

const handleRetry = () => {
  emit('retry')
}

const clearFilters = () => {
  emit('search', '')
}
</script>

<style scoped>
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
}
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}
</style> 