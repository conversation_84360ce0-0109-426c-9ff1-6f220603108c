#!/bin/bash
set -e

# ð§© 传入环境参数（默认 dev）
BUILD_ENV=${1:-dev}

# 配置项：端口、标签、容器名
BASE_IMAGE_NAME="web-ui"
PRIVATE_REGISTRY="*********:5000"
TAG="${BUILD_ENV}"
CONTAINER_PORT=80

# 根据环境设置端口和容器名
if [ "$BUILD_ENV" = "pd" ]; then
  HOST_PORT=80
  CONTAINER_NAME="web-ui-pd-app"
else
  HOST_PORT=10086
  CONTAINER_NAME="web-ui-dev-app"
fi

REMOTE_IMAGE_FULL_NAME="${PRIVATE_REGISTRY}/${BASE_IMAGE_NAME}:${TAG}"

echo "ð 部署前端容器: ${CONTAINER_NAME} (环境: ${BUILD_ENV})"

# 拉取最新镜像
docker pull "${REMOTE_IMAGE_FULL_NAME}"

# 停止并删除旧容器
docker stop "${CONTAINER_NAME}" || true
docker rm "${CONTAINER_NAME}" || true

# 启动新容器
docker run -d \
  --name "${CONTAINER_NAME}" \
  --restart unless-stopped \
  -p "${HOST_PORT}:${CONTAINER_PORT}" \
  "${REMOTE_IMAGE_FULL_NAME}"

echo "✅ 容器已启动: ${CONTAINER_NAME} (http://<your-server-ip>:${HOST_PORT})"
