<template>
  <form @submit.prevent="handleSubmit" class="w-full">
    <CustomInput
      v-model="form.username"
      :label="t('login.username')"
      :placeholder="t('login.usernamePlaceholder')"
      required
      :validator="validateUsername"
      @validation-error="handleValidationError('username')"
      @validation-success="handleValidationSuccess('username')"/>
    <CustomInput
      v-model="form.password"
      :label="t('login.password')"
      :placeholder="t('login.passwordPlaceholder')"
      type="password"
      required
      :validator="validatePassword"
      @validation-error="handleValidationError('password')"
      @validation-success="handleValidationSuccess('password')"/>
    <!-- <div class="flex items-center mb-4">
      <el-checkbox v-model="rememberPassword">{{ t('login.rememberPassword') }}</el-checkbox>
    </div> -->
    <div class="flex items-center justify-between mt-8">
      <el-button
        type="primary"
        :loading="loading"
        :disabled="!isFormValid || loading"
        native-type="submit"
        class="w-[120px] h-[40px]!">
        {{ loading ? t('login.loggingIn') : t('login.login') }}
      </el-button>
    </div>
    <!-- <div class="w-full flex justify-center items-center gap-2 text-center mt-4">
      <span class="text-[14px] text-[#666]">{{ $t('login.haveAccount') }}</span>
      <el-link
        type="primary"
        class="text-[14px]! ml-1"
        @click="$emit('switchMode', 'register')">
        {{ t('login.register') }}
      </el-link>
    </div> -->
  </form>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import CustomInput from './CustomInput.vue'
import type { LoginResult, LoginMode } from '@/types/login'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'  
import { ElMessage } from 'element-plus'

const emit = defineEmits<{
  switchMode: [value: LoginMode];
  toStepPage: [void];
  toHome: [void];
}>()

const { t } = useI18n()
const rememberPassword = ref(false)
const form = ref({
  username: '',
  password: '',
})

const formErrors = ref(new Set<string>())

const isFormValid = computed(() => {
  return form.value.username && 
         form.value.password && 
         formErrors.value.size === 0
})

const validateUsername = (value: string) => {
  if (value.length < 3) {
    return t('login.usernameRequire')
  }
  return true
}

const validatePassword = (value: string) => {
  // if (value.length < 6) {
  //   return '密码至少需要6个字符'
  // }
  // if (!/[A-Z]/.test(value)) {
  //   return '密码需要包含至少一个大写字母'
  // }
  // if (!/[0-9]/.test(value)) {
  //   return '密码需要包含至少一个数字'
  // }
  if (value.trim().length === 0){
    return t('login.require')
  }
  return true
}

const handleValidationError = (field: string) => {
  formErrors.value.add(field)
}

const handleValidationSuccess = (field: string) => {
  formErrors.value.delete(field)
}

const userStore = useUserStore()
const loading = ref(false)
const submitted = ref(false)

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value || submitted.value) {
    return
  }
  
  loading.value = true
  try {
    // 登录请求
    await userStore.login('password', {
      password: form.value.password,
      user_name: form.value.username,
    })
    
    // 等待用户信息加载
    await userStore.silentRetryLoadAccount()
    
    submitted.value = true
    
    // 触发父组件的跳转事件
    emit('toHome')
  } catch (error: any) {
    console.error('Login failed:', error)
    ElMessage.error(error?.message || t('login.failed'))
    submitted.value = false
  } finally {
    loading.value = false
  }
}
</script>
<style lang="scss" module>

</style>
