export interface Member {
  items: Item[];
  meta: Meta;
}

interface Meta {
  page: number;
  page_size: number;
  total: number;
}

interface Item {
  id: string;
  created_at: string;
  updated_at: string;
  user_name: string;
  nickname: null | string;
  email: string;
  mobile: null;
  password: string;
  user_type: number;
  language: string;
  projects: string[];
  nation: string[];
  industry: string[];
  default_project: null | string;
  company: Company;
  is_delete: null;
  step: number;
  user_profile: Userprofile2;
  others: Others;
}

interface Others {
  password?: string;
  old_password?: string;
}

interface Userprofile2 {
  industry?: string[];
  industry_value?: string;
  area?: any[];
  area_value?: string;
  style?: string[];
  style_value?: string;
  overseas?: string[];
  overseas_value?: string;
  project_recommendation?: string[];
  industry_recommendation?: string[];
  target_market_recommendation?: (null | null | string | string)[];
  user_profile_summary?: string;
  language?: string;
}

interface Company {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  nations: any[];
  industries: any[];
  invite_code: string;
  language: string;
  exp: string;
  employees_limit: number;
  token_limit: number;
  is_delete: boolean;
  step: number;
  user_profile: Userprofile;
  others: Userprofile;
}

interface Userprofile {
}

interface Userprofile2 {
  industry: any[];
  industry_value: string;
  area: any[];
  area_value: string;
  style: any[];
  style_value: string;
  overseas: any[];
  overseas_value: string;
  project_recommendation: any[];
  industry_recommendation: string[];
  target_market_recommendation: null[];
}

interface Company {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  nations: any[];
  industries: any[];
  invite_code: string;
  language: string;
  exp: string;
  employees_limit: number;
  token_limit: number;
  is_delete: boolean;
  step: number;
  user_profile: Userprofile;
  others: Userprofile;
}

interface Userprofile {}
