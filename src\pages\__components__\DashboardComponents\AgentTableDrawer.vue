<template>
  <el-drawer
    v-model="drawerVisible"
    :title="$t('agent.batch_operations')"
    size="80%"
    :destroy-on-close="false"
    @update:modelValue="updateVisible">
    <template #header>
      <div class="flex items-center justify-between w-full pr-4">
        <span class="text-lg font-semibold">
          {{ $t("agent.batch_operations") }}
        </span>
        <div class="flex items-center gap-3">
          <el-input
            v-model="searchQuery"
            class="w-[240px]!"
            :placeholder="$t('common.search_placeholder')"
            clearable
            @clear="handleSearch"
            @input="handleSearch">
            <template #prefix>
              <Icon icon="mdi:search" class="text-gray-500"/>
            </template>
          </el-input>
          
          <el-select
            v-model="filterName"
            class="w-[180px]!"
            clearable
            :placeholder="$t('agent.filter_by_name')">
            <el-option
              v-for="name in uniqueNames"
              :key="name"
              :label="name"
              :value="name">
              <div class="flex items-center">
                <span>{{ name }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
      </div>
    </template>

    <div class="h-full flex flex-col">
      <div class="flex-1 overflow-auto">
        <el-table
          ref="multipleTable"
          v-loading="loading"
          :data="filteredAgents"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          :default-sort="{ prop: 'updated_at', order: 'descending' }"
          height="100%">
          <el-table-column type="selection" width="55" fixed="left"/>

          <el-table-column
            prop="name"
            :label="$t('agent.name')"
            width="200"
            fixed="left">
            <template #default="{ row }">
              <div class="flex items-center">
                <!-- <el-avatar :size="24" :src="row.avatar" class="mr-2"/> -->
                {{ row.name }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="duty"
            :label="$t('agent.duty')"
            width="200"
            show-overflow-tooltip/>

          <el-table-column 
            prop="summary" 
            :label="$t('agent.summary')" 
            min-width="400"
            show-overflow-tooltip>
            <template #default="{ row }">
              <div 
                class="text-main-color! text-sm line-clamp-2 leading-6 whitespace-pre-wrap cursor-pointer"
                @click="handleView(row)">
                {{ row.summary }}
              </div>
              <!-- <div class="flex items-center">
                <OverClampText
                  :content="row.summary"
                  :line-clamp="2"
                  class="text-main-color! text-sm line-clamp-2 leading-6 whitespace-pre-wrap cursor-pointer"
                  style="min-height: 48px"
                  @click="handleView(row)">
                </OverClampText>
              </div> -->
            </template>
          </el-table-column>

          <el-table-column
            prop="updated_at"
            :label="$t('agent.updated_at')"
            sortable
            width="160"
            :formatter="(row: Agent) => formatDate(row.updated_at)"/>

          <el-table-column 
            fixed="right" 
            :label="$t('common.actions')" 
            width="120"
            align="center">
            <template #default="{ row }">
              <el-button-group class="flex justify-center">
                <el-button
                  size="small"
                  @click="handleView(row)"
                  type="primary"
                  text>
                  {{ $t("common.view") }}
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  text
                  @click="handleDelete(row)">
                  {{ $t("common.delete") }}
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Batch Operations Footer -->
      <div
        v-show="selected.length"
        class="sticky bottom-0 left-0 right-0 bg-white border-t p-4 flex justify-between items-center">
        <span class="text-gray-600">
          {{ selected.length }} {{ $t("common.items_selected") }}
        </span>
        <div class="flex gap-2">
          <!-- <el-button
            type="primary"
            @click="handleBatchView"
            :disabled="!selected.length">
            {{ $t("common.batch_view") }}
          </el-button> -->
          <el-button
            type="danger"
            @click="handleBatchDelete"
            :disabled="!selected.length">
            {{ $t("common.batch_delete") }}
          </el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import dayjs from 'dayjs'
import type { Agent } from '@/pages/dashboard/type'
import { Icon } from '@iconify/vue'
const props = defineProps<{
  visible: boolean;
  agents: Agent[];
  loading?: boolean;
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'batch-view': [ids: string[]];
  'batch-delete': [ids: string[]];
  view: [agent: Agent];
  delete: [agent: Agent];
  refresh: [];
}>()

// Internal state
const drawerVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})
const selected = ref<Agent[]>([])
const searchQuery = ref('')
const filterName = ref('')

// Computed
const uniqueNames = computed(() => {
  return [...new Set(props.agents.map(agent => agent.name))]
})

const filteredAgents = computed(() => {
  let result = props.agents

  if (filterName.value) {
    result = result.filter(agent => agent.name === filterName.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(agent => 
      agent.name.toLowerCase().includes(query) ||
      agent.duty.toLowerCase().includes(query)
    )
  }

  return result.sort((a, b) => 
    new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
  )
})

// Methods
const updateVisible = (val: boolean) => {
  if (!val) {
    selected.value = []
    searchQuery.value = ''
    filterName.value = ''
  }
  emit('update:visible', val)
}

const handleSearch = useDebounceFn(() => {
  // Trigger search/filter
}, 300)

const handleSelectionChange = (val: Agent[]) => {
  selected.value = val
}

const handleView = (agent: Agent) => {
  emit('view', agent)
}

const handleDelete = (agent: Agent) => {
  emit('delete', agent)
}

const handleBatchDelete = () => {
  emit(
    'batch-delete',
    selected.value.map((agent) => agent.id)
  )
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

</script>

<style scoped>
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}
</style>
