<template>
  <div class="w-full h-full flex flex-col overflow-hidden">
    <transition name="fade" mode="out-in">
      <div
        v-if="isScrollbarNeeded"
        class="w-full flex justify-start items-center">
        <el-alert :title="$t('robot.helper.scrollbarTip')" type="info"/>
      </div>
    </transition>
    <el-scrollbar
      class="flex-1 w-full"
      :view-style="{
        display: 'flex',
        'justify-content': 'center',
        'align-items': 'flex-start',
        width: '100%',
        padding: '20px 0'
      }"
      ref="helperListRef">
      <div class="w-full flex justify-center items-start gap-6 pr-6 min-w-min">
        <div
          v-for="(helper, index) in list"
          :key="index"
          class="w-[300px] h-[520px] shrink-0 bg-white rounded-lg p-6 flex flex-col items-center border border-gray-200 shadow-xs hover:shadow-md transition-shadow">
          <!-- Avatar -->
          <div
            class="w-[115px] h-[115px] rounded-full overflow-hidden bg-white flex justify-center items-center border border-gray-200 shadow-xs">
            <el-image 
              :src="avatar[index]" 
              fit="contain"
              class="formula-logo"/>
          </div>

          <!-- Description -->
          <div class="text-gray-600 text-sm text-center h-[80px] w-full overflow-hidden mt-6">
            <OverClampText 
              :content="helper.robot_profile" 
              :line-clamp="3"
              class="leading-6"/>
          </div>

          <!-- Tags -->
          <div class="flex flex-wrap gap-2 justify-center h-[80px] w-full overflow-visible items-start">
            <el-tag
              v-for="(tag, tagIndex) in helper.tags"
              :size="'large'"
              :key="tagIndex"
              class="px-3 py-1 rounded-full bg-gray-100 text-gray-600 text-xs">
              {{ tag }}
            </el-tag>
          </div>

          <!-- Name and Career -->
          <div class="flex flex-col items-center h-[80px] justify-center">
            <span class="text-blue-500 font-medium">{{ helper.name }}</span>
            <span class="text-gray-900 font-bold mt-1">{{ helper.career }}</span>
          </div>

          <!-- Select Button -->
          <el-button
            type="primary"
            color="#3b82f6"
            round
            @click="handleSelect(helper)">
            {{ $t("robot.select") }}
          </el-button>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, type PropType } from 'vue'
import Emoji from '@/assets/images/robot/emoji.png'
import Emoji2 from '@/assets/images/robot/emoji3.png'
import formula from '@/assets/images/robot/formula.png'
import { ElScrollbar } from 'element-plus'
import type { RobotHelper as RobotHelperType } from '@/pages/robot/type'
import OverClampText from '@/components/OverClampText.vue'

const props = defineProps({
  list: {
    type: Array as PropType<RobotHelperType[]>,
    default: () => [],
    required: true,
  },
})

const avatar = [Emoji, Emoji2, formula]
const helperListRef = ref<typeof ElScrollbar>()
const isScrollbarNeeded = computed(() => {
  console.log('helperListRef', helperListRef)
  if (!helperListRef.value) {
    return false
  }
  const containerWidth = helperListRef.value.wrapRef.clientWidth
  console.log('containerWidth', containerWidth)
  const contentWidth = helperListRef.value.wrapRef.scrollWidth
  return contentWidth > containerWidth
})

const emit = defineEmits<{
  select: [helper: RobotHelperType];
}>()
onMounted(() => {
  console.log(
    'helperListRef',
    helperListRef.value,
    helperListRef.value?.wrapRef.clientWidth,
    helperListRef.value?.wrapRef.scrollWidth
  )
})

const handleSelect = (helper: RobotHelperType) => {
  const index = props.list.findIndex(item => item.robot_id === helper.robot_id)
  emit('select', {
    ...helper,
    avatar: avatar[index]
  })
}
</script>

<style lang="scss" module>
.formula-logo {
  :deep(img) {
    width: 80% !important;
    height: 80% !important;
    object-fit: contain !important;
    background-color: white;
  }
}
</style>
