import qs from 'qs'
import { useConfig } from '@/hooks/useConfig'

import { isNil, isBoolean, isDate, isObject, isFunction, isArray } from 'lodash-es'
const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
const webUrlRegex = /[a-zA-z]+:\/\/[^\s]*/
const $config = useConfig()
const mobileRegex = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/
const emailRegex = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{1,10}$/

export function isIPv4(hostname: string) {
  return ipRegex.test(hostname)
}
export function isEmail(email: string) {
  return emailRegex.test(email)
}
export function isMobile(mobile: string) {
  return mobileRegex.test(mobile)
}

export const getTopLevelDomain = (domain: string) => {
  if (domain === 'localhost' || isIPv4(domain)) {
    return domain
  }
  // 如果输入已经是顶级域名
  if (domain.startsWith('.')) {
    return domain
  }

  // 分割域名为各个部分
  const parts = domain.split('.')

  // 取最后两个部分��为顶级域名的一部分，假设顶级域名不会有二级后缀如.co.uk
  // （如果需要处理这种情况，那么就需要一个特殊的后缀列表来确定何时需要额外的部分）
  const topLevelDomainParts = parts.slice(-2)

  // 将顶级域名的部分连接起来，添加点作为前缀
  return `.${topLevelDomainParts.join('.')}`
}

export const isWebUrl = (url: string) => {
  return webUrlRegex.test(url)
}

/**
 * 新增url参数
 * @param url
 * @param params
 * @param options
 * @returns {string}
 */
export const addUrlParams = (url: string, params: Record<string, any>, options?: qs.IStringifyOptions) => {
  const queryIndex = url.lastIndexOf('?')
  const queryString = queryIndex > -1 ? url.substring(queryIndex) : ''
  const urlPrefix = queryIndex > -1 ? url.substring(0, queryIndex) : url
  const old = qs.parse(queryString, { ignoreQueryPrefix: true })
  const paramsString = qs.stringify({ ...old, ...params }, options)
  return `${urlPrefix}?${paramsString}`
}

let isOverseasVersion:boolean
// #if BUILD_REG == 'INTL'
/**
 * 是否为海外版
 * @type {boolean}
 */
isOverseasVersion = true
// #else
isOverseasVersion = false
// #endif
export { isOverseasVersion }

/**
 * 尝试转换为number类型
 * @param v 要转换的值
 * @returns {string|*|number}
 */
export const tryToNumber = (v:any )=> {
  if (isNil(v) || isBoolean(v) || isDate(v) || isObject(v) || isFunction(v) || isArray(v)) {
    return v
  }
  if (typeof v === 'string' && v.trim().length === 0) {
    return v
  }
  const number = Number(v)
  return isNaN(number) ? v : number
}
/**
 * 去掉url多余的 /
 * @param url
 * @returns {*}
 */
export const resolveUrl = (url:string) => url.replace(/[/]+[/]/g, '/').replace(':/', '://')

export const escapeRegexpString = (value = '') => String(value).replace(/[|\\{}()[\]^$+*?.]/g, '\\$&')

// 定义每种日期格式的可用语言
interface DateFormatLocales {
  chinese: string,
  english: string,
}

// 定义支持的日期格式结构
interface DateFormat {
  date: DateFormatLocales,
  dateYearMonth: DateFormatLocales,
  dateMonthDay: DateFormatLocales,
  dateTime: DateFormatLocales,
  dateTimeHasSeconds: DateFormatLocales,
  weekDateTime: DateFormatLocales
}
const dateFormat:DateFormat = {
  date: {
    chinese: 'yyyy-MM-dd',
    english: 'MMM d, yyyy'
  },
  dateYearMonth: {
    chinese: 'yyyy-MM',
    english: 'MMM, yyyy'
  },
  dateMonthDay: {
    chinese: 'MM-dd',
    english: 'MMM d'
  },
  dateTime: {
    chinese: 'yyyy-MM-dd HH:mm',
    english: 'MMM d, yyyy HH:mm'
  },
  dateTimeHasSeconds: {
    chinese: 'yyyy-MM-dd HH:mm:ss',
    english: 'MMM d, yyyy HH:mm:ss'
  },
  weekDateTime: {
    chinese: 'ddd, yyyy-MM-dd, HH:mm',
    english: 'ddd, MMM d, yyyy HH:mm'
  }
}
// 定义 getDateFormat 返回值的类型
export interface LocalizedDateFormat {
  [key: string]: string;
}
export const getDateFormat = (locale:'chinese' | 'english'): LocalizedDateFormat => {
  const result: LocalizedDateFormat = {}
  const keys = Object.keys(dateFormat) as Array<keyof DateFormat>
  keys.forEach(key => {
    result[key] = dateFormat[key][locale]
  })
  return result
}

type Locale = 'chinese' | 'english' | 'japanese'

interface FormatUnit {
  value: number
  symbol: string
}

/**
 * Currency format units configuration
 * 货币格式化单位配置
 */
const CURRENCY_UNITS: Record<Locale, FormatUnit[]> = {
  chinese: [
    { value: 1e8, symbol: '亿' },
    { value: 1e4, symbol: '万' },
    { value: 1, symbol: '' }
  ],
  english: [
    { value: 1e9, symbol: 'B' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'K' },
    { value: 1, symbol: '' }
  ],
  japanese: [
    { value: 1e8, symbol: '億' },
    { value: 1e4, symbol: '万' },
    { value: 1, symbol: '' }
  ]
}

/**
 * Format number with specific unit
 * 使用特定单位格式化数字
 */
const formatWithUnit = (num: number, unit: FormatUnit): string => {
  const value = (num / unit.value).toFixed(2)
  return value + unit.symbol
}

/**
 * Get appropriate unit for number
 * 获取适合数字的单位
 */
const getUnit = (num: number, locale: Locale): FormatUnit => {
  return CURRENCY_UNITS[locale].find(unit => num >= unit.value) || CURRENCY_UNITS[locale][0]
}

/**
 * Format currency based on locale
 * 根据语言环境格式化货币
 * 
 * @example
 * // CN format
 * formatCurrency(123456789, 'chinese') // -> '1.23亿'
 * formatCurrency(123456, 'chinese')    // -> '12.35万'
 * 
 * // US format
 * formatCurrency(123456789, 'english') // -> '$123.46M'
 * formatCurrency(1234567, 'english')   // -> '$1.23M'
 * formatCurrency(1234, 'english')      // -> '$1.23K'
 * 
 * // JP format
 * formatCurrency(123456789, 'japanese') // -> '￥1.23億'
 * formatCurrency(123456, 'japanese')    // -> '￥1.23万'
 * formatCurrency(1234, 'japanese')      // -> '￥1.23K'
 */
export const formatCurrency = (amount: number, locale: 'chinese' | 'english' | 'japanese'): string => {
  // 处理 0 的情况
  if (amount === 0) {
    return locale === 'chinese' ? '0' : '$0'
  }
  
  // 获取符号和绝对值
  const isNegative = amount < 0
  const absoluteValue = Math.abs(amount)
  
  // 获取适当的单位并格式化
  const unit = getUnit(absoluteValue, locale)
  const formattedValue = formatWithUnit(absoluteValue, unit)
  
  // 根据区域设置添加货币符号和负号
  if (locale === 'chinese') {
    return isNegative ? `-${formattedValue}` : formattedValue
  } if (locale === 'japanese') {
    return isNegative ? `-${formattedValue}` : formattedValue
  }
  return isNegative ? `-$${formattedValue}` : `$${formattedValue}`
  
}
