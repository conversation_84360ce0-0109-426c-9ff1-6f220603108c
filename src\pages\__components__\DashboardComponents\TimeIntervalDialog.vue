<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('agent.set_interval_time')"
    width="450px"
    :close-on-click-modal="false"
    :class="$style['time-interval-dialog']"
    @close="handleClose">
    
    <div :class="$style['dialog-content']">
      <!-- 图标和说明部分 -->
      <div :class="$style['header-section']">
        <div :class="$style['icon-container']">
          <Icon icon="mdi:timer-outline" :class="$style['timer-icon']"/>
        </div>
        <p :class="$style['description']">
          {{ $t('agent.interval_description_days') }}
        </p>
      </div>
      
      <!-- 定时天数选择部分 -->
      <div :class="$style['selection-container']">
        <p :class="$style['selection-label']">{{ $t('agent.select_days') }}</p>
        
        <!-- 快速选择按钮组 -->
        <div :class="$style['quick-select-buttons']">
          <el-button 
            v-for="day in quickSelectDays" 
            :key="day"
            :type="form.frequency === day ? 'primary' : 'default'"
            :class="[$style['day-button'], form.frequency === day ? $style['active'] : '']"
            @click="selectDays(day)">
            {{ day }} {{ $t('agent.days') }}
          </el-button>
        </div>
        
        <!-- 自定义输入 -->
        <div :class="$style['custom-input-container']">
          <p :class="$style['custom-input-label']">{{ $t('agent.custom_days') }}</p>
          <el-input-number
            v-model="form.frequency"
            :min="1"
            :max="365"
            :step="1"
            controls-position="right"
            :class="$style['days-input']"
            :placeholder="$t('agent.enter_days')"/>
        </div>
        
        <!-- 当前配置信息 -->
        <div :class="$style['current-config']" v-if="props.agent?.name">
          <div :class="$style['agent-name']">
            <Icon icon="mdi:robot-outline" :class="$style['agent-icon']"/>
            <span>{{ props.agent.name }}</span>
          </div>
          <div :class="$style['config-details']">
            {{ $t('agent.will_run_every', { days: form.frequency }) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div :class="$style['dialog-footer']">
        <el-button @click="handleClose" :class="$style['cancel-button']">
          {{ $t('common.cancel') }}
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="isSubmitting"
          :class="$style['confirm-button']">
          {{ $t('common.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { PropType } from 'vue'
import { Icon } from '@iconify/vue'

interface Agent {
  id: string;
  name: string;
  [key: string]: any;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agent: {
    type: Object as PropType<Agent>,
    default: () => ({})
  },
  currentFrequency: {
    type: Number,
    default: 1 // 默认为1天，原来是24小时
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 计算属性用于控制对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = ref({
  frequency: props.currentFrequency
})

// 快速选择天数选项
const quickSelectDays = [1, 3, 7, 14, 30]

// 提交状态
const isSubmitting = ref(false)

// 当agent或currentFrequency变化时，更新表单
watch(() => props.currentFrequency, (newValue) => {
  form.value.frequency = newValue
})

// 快速选择天数
const selectDays = (days: number) => {
  form.value.frequency = days
}

// 处理关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 处理提交
const handleSubmit = () => {
  if (!form.value.frequency || form.value.frequency < 1) {
    return
  }
  
  isSubmitting.value = true
  
  // 向父组件发送数据
  emit('confirm', {
    agent_id: props.agent.id,
    frequency: form.value.frequency
  })
  
  // 实际应用中，这应该在父组件中的回调中处理
  setTimeout(() => {
    isSubmitting.value = false
    handleClose()
  }, 300)
}
</script>

<style lang="scss" module>
.time-interval-dialog {
  :global(.el-dialog__header) {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    
    :global(.el-dialog__title) {
      font-weight: 600;
      font-size: 18px;
      color: #1e293b;
    }
  }
  
  :global(.el-dialog__body) {
    padding: 24px;
  }
  
  :global(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.icon-container {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  display: flex;
  justify-content: center;
  align-items: center;
}

.timer-icon {
  width: 32px;
  height: 32px;
  color: var(--el-color-primary);
}

.description {
  color: #475569;
  font-size: 14px;
  line-height: 1.5;
  max-width: 360px;
}

.selection-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.selection-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 15px;
  margin-bottom: 4px;
}

.quick-select-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.day-button {
  flex: 1;
  min-width: 80px;
  
  &.active {
    font-weight: 600;
  }
}

.custom-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.custom-input-label {
  font-size: 14px;
  color: #64748b;
}

.days-input {
  width: 100%;
}

.current-config {
  margin-top: 16px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px dashed rgba(0, 0, 0, 0.1);
}

.agent-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 8px;
}

.agent-icon {
  width: 18px;
  height: 18px;
  color: #64748b;
}

.config-details {
  font-size: 14px;
  color: #64748b;
}

.days-highlight {
  font-weight: 600;
  color: var(--el-color-primary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirm-button {
  min-width: 100px;
}

.cancel-button {
  min-width: 100px;
  border-color: #e2e8f0;
}
</style> 