<template>
  <div class="iframe-wrapper" :class="wrapperClass">
    <!-- 错误状态 -->
    <div 
      v-if="hasError" 
      class="flex items-center justify-center h-full bg-gradient-to-br from-red-50 to-red-100 p-8">
      <div class="max-w-md text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon icon="mdi:alert-circle" class="w-8 h-8 text-red-600"/>
        </div>
        <h2 class="text-xl font-bold text-red-900 mb-3">{{ errorTitle || 'iframe加载失败' }}</h2>
        <p class="text-red-700 mb-4">{{ errorMessage }}</p>
        <div v-if="showErrorDetails" class="text-xs text-red-600/80 mb-6 p-3 bg-red-50 rounded-lg border border-red-200">
          <div class="font-semibold mb-1">错误详情：</div>
          <div class="font-mono">{{ errorDetails }}</div>
        </div>
        <div class="space-y-3">
          <Button 
            v-if="showRetryButton"
            @click="handleRetry" 
            class="w-full bg-red-600 hover:bg-red-700 text-white">
            <Icon icon="mdi:refresh" class="w-4 h-4 mr-2"/>
            重试加载
          </Button>
          <Button 
            v-if="showGoBackButton"
            @click="handleGoBack" 
            variant="outline" 
            class="w-full border-red-300 text-red-700 hover:bg-red-50">
            <Icon icon="mdi:arrow-left" class="w-4 h-4 mr-2"/>
            返回
          </Button>
          <Button 
            v-if="showCloseButton"
            @click="handleClose" 
            variant="ghost" 
            size="sm"
            class="w-full text-red-600 hover:bg-red-50">
            <Icon icon="mdi:close" class="w-4 h-4 mr-2"/>
            关闭
          </Button>
        </div>
      </div>
    </div>

    <!-- iframe内容区域 -->
    <div v-else class="flex-1 h-full relative">
      <!-- iframe组件 -->
      <IframeContainer
        v-if="src && !hasError"
        ref="iframeRef"
        :key="iframeKey"
        :src="src"
        :title="title"
        :token="token"
        :from-og="fromOg"
        :locale="locale"
        :height="height"
        :min-height="minHeight"
        :max-height="maxHeight"
        :auto-height="autoHeight"
        :enable-communication="enableCommunication"
        :debug="debug"
        :sandbox="sandbox"
        :allowed-origins="allowedOrigins"
        :retry-attempts="retryAttempts"
        :load-timeout="loadTimeout"
        :container-class="containerClass"
        @loaded="handleIframeLoaded"
        @error="handleIframeError"
        @message="handleIframeMessage"
        @retry="handleIframeRetry"
        :class="{ 'opacity-0': isLoading, 'opacity-100': !isLoading }"
        class="h-full w-full transition-opacity duration-300"/>
      
      <!-- Loading遮罩层 -->
      <div 
        v-if="isLoading" 
        class="absolute inset-0 flex items-center justify-center bg-white/95 backdrop-blur-sm z-10">
        <div class="flex flex-col items-center gap-4">
          <div class="relative">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <div class="absolute inset-0 flex items-center justify-center">
              <Icon :icon="loadingIcon" class="w-5 h-5 text-blue-500 animate-pulse"/>
            </div>
          </div>
          <div class="text-center space-y-2">
            <h3 class="text-base font-semibold text-gray-900">{{ loadingTitle }}</h3>
            <p class="text-sm text-gray-600">{{ loadingMessage }}</p>
            <div v-if="showLoadingUrl" class="text-xs text-gray-400 font-mono">{{ displayUrl }}</div>
            <div v-if="loadingHint" class="text-xs text-blue-600 mt-2">
              {{ loadingHint }}
            </div>
          </div>
          <!-- 加载时的取消按钮 -->
          <Button 
            v-if="showCancelButton"
            @click="handleCancel" 
            variant="outline" 
            size="sm"
            class="mt-2 text-gray-600 hover:text-gray-800">
            <Icon icon="mdi:close" class="w-4 h-4 mr-2"/>
            取消
          </Button>
        </div>
      </div>
    </div>

    <!-- 调试信息（仅开发环境） -->
    <div 
      v-if="showDebugInfo && debug" 
      class="absolute bottom-4 right-4 bg-gray-900/90 text-white p-3 rounded-lg shadow-lg max-w-xs z-30">
      <div class="text-xs font-mono space-y-1">
        <div class="font-semibold text-green-400">Debug Info</div>
        <div>Status: {{ status }}</div>
        <div>Messages: {{ messageCount }}</div>
        <div>Key: {{ iframeKey }}</div>
      </div>
      <Button 
        @click="showDebugInfo = false" 
        size="sm" 
        variant="ghost" 
        class="mt-2 h-5 px-2 text-xs text-gray-300 hover:text-white">
        Hide
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import IframeContainer from '@/components/IframeContainer.vue'
import { useIframeMessages, type UseIframeMessagesConfig, type IframeMessage } from '@/composables/useIframeMessages'
import { adaptIframeError, getErrorMessage, isDetailedError, type IframeError } from '@/utils/iframeErrorAdapter'

// Props 定义
interface Props {
  // iframe基础配置
  src: string
  title?: string
  token?: string

  // iframe集成指南参数
  fromOg?: boolean // 标识来自Vue3旧项目的iframe
  locale?: string // 界面语言设置

  // iframe尺寸配置
  height?: number
  minHeight?: number
  maxHeight?: number
  autoHeight?: boolean

  // iframe功能配置
  enableCommunication?: boolean
  debug?: boolean
  sandbox?: string[]
  allowedOrigins?: string[]
  retryAttempts?: number
  loadTimeout?: number

  // 样式配置
  wrapperClass?: string
  containerClass?: string

  // UI配置
  loadingTitle?: string
  loadingIcon?: string
  loadingHint?: string
  showLoadingUrl?: boolean
  showCancelButton?: boolean

  // 错误状态配置
  errorTitle?: string
  showErrorDetails?: boolean
  errorDetails?: string
  showRetryButton?: boolean
  showGoBackButton?: boolean
  showCloseButton?: boolean

  // 消息处理配置
  messagesConfig?: UseIframeMessagesConfig
}

const props = withDefaults(defineProps<Props>(), {
  title: 'iframe',
  token: '',
  fromOg: false,
  locale: 'zh',
  height: 600,
  minHeight: 400,
  maxHeight: 800,
  autoHeight: false,
  enableCommunication: true,
  debug: false,
  sandbox: () => [
    'allow-scripts',
    'allow-same-origin',
    'allow-forms',
    'allow-popups'
  ],
  allowedOrigins: () => [],
  retryAttempts: 3,
  loadTimeout: 30000,
  wrapperClass: '',
  containerClass: 'h-full w-full',
  loadingTitle: '正在加载',
  loadingIcon: 'mdi:loading',
  loadingHint: '',
  showLoadingUrl: false,
  showCancelButton: true,
  errorTitle: '',
  showErrorDetails: false,
  errorDetails: '',
  showRetryButton: true,
  showGoBackButton: true,
  showCloseButton: true,
  messagesConfig: () => ({})
})

// Emits 定义
const emit = defineEmits<{
  (e: 'loaded'): void
  (e: 'error', error: IframeError | string): void
  (e: 'message', message: IframeMessage): void
  (e: 'retry', attempt: number): void
  (e: 'cancel'): void
  (e: 'close'): void
  (e: 'go-back'): void
}>()

// 组件引用
const iframeRef = ref<InstanceType<typeof IframeContainer> | null>(null)

// iframe唯一键值
const iframeKey = ref('')

// 调试信息显示状态
const showDebugInfo = ref(false)

// 使用iframe消息处理hook
const messageHandlerConfig = computed(() => ({
  autoManageLoading: true,
  initialLoadingMessage: '正在初始化...',
  ...props.messagesConfig,
  handlers: {
    onSuccess: async (message: IframeMessage) => {
      // 鉴权完成的特殊处理
      if (message.type === 'IFRAME_READY') {
        // 发送确认消息给iframe
        nextTick(() => {
          if (iframeRef.value) {
            iframeRef.value.sendMessage('auth-confirmed', {
              parentId: 'iframe-wrapper',
              timestamp: Date.now()
            })
          }
        })
      }
      
      // 调用原始处理器
      if (props.messagesConfig?.handlers?.onSuccess) {
        await props.messagesConfig.handlers.onSuccess(message)
      }
    },
    ...props.messagesConfig?.handlers
  }
}))

const {
  isLoading,
  hasError,
  errorMessage,
  status,
  loadingMessage,
  messageCount,
  handleMessage,
  reset,
  setError
} = useIframeMessages(messageHandlerConfig.value)

// 计算属性
const displayUrl = computed(() => {
  if (!props.src) {return ''}
  try {
    const url = new URL(props.src)
    return `${url.origin}${url.pathname}`
  } catch {
    return props.src
  }
})

// 事件处理方法
const handleIframeLoaded = () => {
  emit('loaded')
}

const handleIframeError = (error: IframeError | string) => {
  const errorMessage = getErrorMessage(error)
  const errorObject = adaptIframeError(error)
  
  // 如果是详细错误对象，显示详细信息
  if (isDetailedError(error) && props.showErrorDetails) {
    console.error('🚨 IframeWrapper: 详细错误信息', {
      type: error.type,
      message: error.message,
      code: error.code,
      url: error.url,
      retryCount: error.retryCount,
      timestamp: new Date(error.timestamp).toLocaleString(),
      details: error.details
    })
  }
  
  setError(errorMessage)
  emit('error', error)
}

const handleIframeMessage = (message: IframeMessage) => {
  handleMessage(message)
  emit('message', message)
}

const handleIframeRetry = (attempt: number) => {
  reset()
  emit('retry', attempt)
}

const handleRetry = () => {
  reset()
  // 生成新的iframe key强制重新加载
  iframeKey.value = `iframe-${Date.now()}`
  
  if (iframeRef.value) {
    iframeRef.value.retryLoad()
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleClose = () => {
  emit('close')
}

const handleGoBack = () => {
  emit('go-back')
}

// 生成初始iframe key
iframeKey.value = `iframe-${Date.now()}`

// 开发环境键盘快捷键
const handleKeydown = (e: KeyboardEvent) => {
  if (props.debug && e.key === 'F12') {
    e.preventDefault()
    showDebugInfo.value = !showDebugInfo.value
  }
}

// 监听键盘事件
if (props.debug) {
  document.addEventListener('keydown', handleKeydown)
  
  // 清理键盘事件监听
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
}
</script>

<style lang="scss" scoped>
.iframe-wrapper {
  @apply relative w-full h-full;
}

// 响应式调整
@media (max-width: 768px) {
  .debug-info {
    max-width: 200px;
    font-size: 0.625rem;
  }
}
</style> 