/* eslint-disable spellcheck/spell-checker */
/**
 * feature：新功能
 * update：更新某功能
 * fixbug：修补某功能的bug
 * refactor：重构某个功能
 * optimize: 优化构建工具或运行时性能
 * style：仅样式改动
 * docs：仅文档新增/改动
 * chore：构建过程或辅助工具的变动
 */

export default {
  rules: {
    'issueId': [2, 'always'],
    'type-empty': [1, 'never'],
    'type-enum': [
      2,
      'always',
      [
        'feat',
        'feature',
        'fix',
        'docs',
        'style',
        'refactor',
        'perf',
        'test',
        'build',
        'ci',
        'chore',
        'revert',
        'wip',
        'workflow',
        'types',
        'release'
      ]
    ]
  },
  plugins: [
    {
      rules: {
        'issueId': ({ raw }) => {
          return [
            /\[[a-zA-Z]+-\d+\]+.{2,}/.test(raw),
            '为了方便LigaAI产品协同与事后反查代码、减少团队阅读代码成本等需求和缺陷必须关联工作ID(建议使用IDE插件提交): \ntype: [工作ID] 内容'
          ];
        }
      }
    }
  ]
}
