import router from '@/router'
import { useUserStore } from '@/stores/user'
import { redirectToPath } from '@/utils/router'
import { menuItems, isRouteInMenu, getRouteToMenuMap } from '@/config/menu.config'

const routeToMenuMap = getRouteToMenuMap()

// Get first accessible route
const getFirstAccessibleRoute = (userStore: ReturnType<typeof useUserStore>) => {
  for (const item of menuItems) {
    if (userStore.menuConfig.find(menu => menu.id === item.menuId && menu.status)) {
      return item.route
    }
    if (item.children) {
      for (const child of item.children) {
        if (userStore.menuConfig.find(menu => menu.id === child.menuId && menu.status)) {
          return child.route
        }
      }
    }
  }
  return undefined
}

router.beforeEach(async (to, from, next) => {
  // Skip check for non-menu routes
  if (!isRouteInMenu(to)) {
    return next()
  }

  const userStore = useUserStore()
  
  // Skip check for auth/login related routes
  if (to.meta.requiresGuest) {
    return next()
  }

  // Check if any menu is enabled
  const hasEnabledMenu = userStore.menuConfig.some(menu => menu.status)
  if (!hasEnabledMenu) {
    console.warn('All menus are disabled. Redirecting to home...')
    redirectToPath('/landing', to, from, next, { replaceRedirectFromQuery: true })
    return
  }

  // Find matching menu ID for current route
  let menuId = routeToMenuMap[to.path]
  
  // For nested routes
  if (!menuId) {
    for (const [route, id] of Object.entries(routeToMenuMap)) {
      if (to.path.startsWith(`${route}/`)) {
        menuId = id
        break
      }
    }
  }

  // If menu is disabled or not found
  const menuItem = userStore.menuConfig.find(item => item.id === menuId)
  if (!menuItem?.status) {
    const firstAccessibleRoute = getFirstAccessibleRoute(userStore)
    if (firstAccessibleRoute) {
      console.warn(`Menu ${menuId} is disabled. Redirecting to first accessible route: ${firstAccessibleRoute}`)
      redirectToPath(firstAccessibleRoute, to, from, next, { replaceRedirectFromQuery: true })
    } else {
      console.warn('No accessible menu routes found. Redirecting to home...')
      redirectToPath('/landing', to, from, next, { replaceRedirectFromQuery: true })
    }
    return
  }

  next()
}) 