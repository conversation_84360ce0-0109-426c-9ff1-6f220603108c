<template>
  <div v-if="searchState.isSearching" :class="$style['steps-container']">
    <div class="flex justify-center items-start my-2 h-[50px]">
      <span class="text-base font-medium text-main-color">{{
        $t("global.currentSearchSection", {
          section: searchState.currentSection,
        })
      }}</span>
    </div>
    <el-steps :active="currentStepIndex" align-center>
      <el-step v-for="(outline, key) in reportOutline" :key="key" :title="key">
        <template #icon>
          <div
            :class="[
              $style['step-icon'],
              $style[getIconClass(key as string)]
            ]">
            <template v-if="isStepCompleted(key as string)">
              <Icon icon="mdi:check" :class="$style['icon']"/>
            </template>
            <template v-else-if="isStepCurrent(key as string)">
              <Icon
                icon="mdi:loading"
                :class="[$style['icon']]"
                class="animate-spin"/>
            </template>
            <template v-else>
              {{ getStepNumber(key as string) }}
            </template>
          </div>
        </template>
        <template #title>
          <span
            :class="[
              $style['step-title'],
              $style[getTitleClass(key as string)]
            ]">
            {{ key }}
          </span>
        </template>
        <template #description>
          <template
            v-if="isStepCompleted(key as string) && searchResults[key]?.length">
            <el-popover
              placement="bottom"
              :width="500"
              trigger="hover"
              :popper-class="$style['popover-class']"
              :show-arrow="true">
              <template #reference>
                <div
                  class="flex items-center justify-center gap-1 cursor-pointer text-main-color">
                  <span class="text-[12px]">{{
                    $t("formatReport.viewResults", {
                      count: searchResults[key]?.length,
                    })
                  }}</span>
                  <div class="p-1 bg-gray-100 rounded-sm transition-colors">
                    <Icon
                      icon="mdi:format-list-text"
                      class="w-4 h-4 text-main-color"/>
                  </div>
                </div>
              </template>
              <template #default>
                <SearchResultsList :results="searchResults[key] || []" :showSummary="false" :wrapClass="$style['warp-class']"/>
              </template>
            </el-popover>
          </template>
          <template v-else-if="isStepCurrent(key as string)">
            <span>{{ $t("formatReport.loadingStep") }}</span>
          </template>
          <template v-else>
            <span>{{ outline.description }}</span>
          </template>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import SearchResultsList from '@/components/SearchResultsList.vue'
import type { Reportoutline, url } from '../type'

interface SearchState {
  isSearching: boolean;
  currentSection: string;
  completedSections: string[];
}

const props = defineProps<{
  reportOutline: Reportoutline;
  searchState: SearchState;
  searchResults: Record<string, url[]>;
}>()


const currentStepIndex = computed(() => {
  if (!props.searchState.currentSection) {
    return 0
  }
  const keys = Object.keys(props.reportOutline)
  return keys.indexOf(props.searchState.currentSection) + 1
})

const isStepCompleted = (key: string) =>
  props.searchState.completedSections.includes(key)
const isStepCurrent = (key: string) => key === props.searchState.currentSection

const getStepNumber = (key: string) => {
  return Object.keys(props.reportOutline).indexOf(key) + 1
}

const getIconClass = (key: string) => {
  if (isStepCompleted(key)) {
    return 'icon-completed'
  }
  if (isStepCurrent(key)) {
    return 'icon-current'
  }
  return 'icon-waiting'
}

const getTitleClass = (key: string) => {
  if (isStepCompleted(key)) {
    return 'title-completed'
  }
  if (isStepCurrent(key)) {
    return 'title-current'
  }
  return 'title-waiting'
}
</script>

<style lang="scss" module>
.steps-container {
  width: 100%;
  max-width: 1150px;
  margin: 0 auto;
  padding: 20px;
  background: var(--main-bg-color);
  border-radius: var(--radius-default);
  box-shadow: var(--shadow-normal);

  :global {
    .el-step__line {
      background-color: var(--gray-3);

      &.is-finish {
        background-color: var(--primary-color);
      }
    }

    .el-step__head {
      &.is-finish {
        color: var(--primary-color);
        border-color: var(--primary-color);
      }

      &.is-process {
        color: var(--primary-color);
        border-color: var(--primary-color);
      }
    }

    .el-step__description {
      color: var(--font-color-2);
      font-size: var(--size-font-9);
    }
  }
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size-default);
  height: var(--icon-size-default);
  border-radius: 50%;
  font-weight: var(--weight-font-icon);
  transition: all 0.3s ease;
}

.icon {
  width: var(--size-font-7);
  height: var(--size-font-7);
}

.icon-spin {
  animation: spin 1s linear infinite;
}

.icon-waiting {
  background-color: var(--gray-3);
  color: var(--font-color-2);
}

.icon-completed {
  background-color: var(--primary-color);
  color: var(--font-color-5);
}

.icon-current {
  background-color: var(--primary-color);
  color: var(--font-color-5);
}

.step-title {
  font-size: var(--size-font-7);
  transition: color 0.3s ease;
}

.title-waiting {
  color: var(--font-color-2);
}

.title-completed {
  color: var(--primary-color);
  font-weight: var(--weight-font-Semiblod);
}

.title-current {
  color: var(--primary-color);
  font-weight: var(--weight-font-Semiblod);
}
.popover-class{
  // content固定高度400px
  display: flex;
  flex-direction: column;
  overflow: auto;
}
.warp-class{
  max-height: 230px !important;
}
</style>
