<template>
  <div :class="$style.previewContainer">
    <div :class="$style.previewHeader">
      <span>{{ $t('intelligence.markdown.preview') }}</span>
      <el-button type="primary" size="small" @click="refreshPreview">
        {{ $t('button.refresh') }}
      </el-button>
    </div>
    <div :class="$style.previewContent">
      <el-scrollbar>
        <div :class="$style.previewWrapper">
          <object
            ref="pdfPreviewFrame"
            :class="$style.previewFrame"
            :data="`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&view=FitH`"
            type="application/pdf"
            :style="{ height: FRAME_HEIGHT }">
            <div>{{ $t('intelligence.markdown.preview.fallback') }}</div>
          </object>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, ref, watch } from 'vue'
import { debounce } from 'lodash-es'
import { usePdf } from '@/hooks/usePdf'
import type { PdfOptions } from '@/hooks/usePdf/types'
import type { CreatedReport } from '@/pages/format-report/type'

interface Props {
  content: HTMLElement
  title: string
  options: PdfOptions
  images?: Record<string, string>
  searchSource?: CreatedReport['search_result']
}

const props = defineProps<Props>()
const pdfUrl = ref<string>('about:blank')
const pdfPreviewFrame = ref<HTMLObjectElement>()
const FRAME_HEIGHT = '100%'

const generatePreview = async () => {
  if (!pdfPreviewFrame.value || !props.content) {return}
  try {
    const { generatePdf } = usePdf()
    const pdfDoc = await generatePdf(props.title, props.content, props.options, props.images, props.searchSource)

    pdfDoc.getBuffer((buffer: ArrayBuffer) => {
      const blob = new Blob([buffer], { type: 'application/pdf' })

      // 清理旧的 URL
      if (pdfUrl.value && pdfUrl.value !== 'about:blank') {
        URL.revokeObjectURL(pdfUrl.value)
      }

      // 创建新的 URL
      pdfUrl.value = URL.createObjectURL(blob)

    })
  } catch (error) {
    console.error('Preview generation failed:', error)
  }
}

const refreshPreview = () => {
  generatePreview()
}

const debouncedPreview = debounce(generatePreview, 500)

watch(
  () => [props.options, props.content, props.title],
  () => {
    debouncedPreview()
  },
  { deep: true, immediate: true }
)

// 组件卸载时清理 URL
onBeforeUnmount(() => {
  if (pdfUrl.value && pdfUrl.value !== 'about:blank') {
    URL.revokeObjectURL(pdfUrl.value)
  }
})
</script>

<style lang="scss" module>
.previewContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 800px; // 添加最小高度
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.previewContent {
  flex: 1;
  background: #f5f5f5;
  overflow: hidden;
  position: relative; // 添加相对定位
}

.previewWrapper {
  position: absolute; // 绝对定位
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px;
}

.previewFrame {
  width: 100%;
  min-height: 800px; // 添加最小高度
  border: none;
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  // Hide PDF viewer UI elements
  :global {
    .toolbar {
      display: none !important;
    }

    #sidebarContainer {
      display: none !important;
    }

    #viewerContainer {
      top: 0 !important;
      padding: 0 !important;
    }

    #viewer {
      margin: 0 !important;
    }
  }
}
</style>
