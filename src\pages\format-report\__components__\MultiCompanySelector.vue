<template>
  <div :class="$style.multiCompanySelector" data-multi-selector="true">
    <el-autocomplete
      v-model="inputValue"
      :fetch-suggestions="props.fetchSuggestions"
      :placeholder="placeholder || t('agent.search_company_placeholder')"
      :trigger-on-focus="false"
      :loading="props.loading"
      clearable
      value-key="value"
      @select="handleSelect"
      @keydown.enter.prevent
      style="width: 100%; margin-bottom: 8px;"/>
    <TagsInput :model-value="localModelValue" @update:modelValue="handleTagsUpdate" :class="$style.tagsContainer">
      <TagsInputItem
        v-for="item in localModelValue"
        :key="item"
        :value="item">
        <TagsInputItemText/>
        <TagsInputItemDelete/>
      </TagsInputItem>
      <!-- No TagsInputInput needed here as input is handled by Autocomplete -->
    </TagsInput>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useVModel } from '@vueuse/core'
import { ElAutocomplete } from 'element-plus'
import { TagsInput, TagsInputItem, TagsInputItemText, TagsInputItemDelete } from '@/components/ui/tags-input'
import { useI18n } from 'vue-i18n'
import type { AcceptableInputValue } from 'reka-ui'

interface CompanySuggestion {
  value: string;
  id?: string | number; // Include id if available/needed
}

interface Props {
  modelValue: string[]; // Array of selected company names
  fetchSuggestions: (query: string, cb: (suggestions: CompanySuggestion[]) => void) => void | CompanySuggestion[] | Promise<CompanySuggestion[]>;
  loading?: boolean;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  placeholder: ''
})

const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()

const localModelValue = ref<string[]>([...(props.modelValue || [])])
const inputValue = ref('')

watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(localModelValue.value)) {
    localModelValue.value = [...newValue]
  }
}, { deep: true })

const handleSelect = (item: Record<string, any> | CompanySuggestion) => {
  const selectedValue = item?.value as string | undefined
  if (selectedValue && !localModelValue.value.includes(selectedValue)) {
    const newValue = [...localModelValue.value, selectedValue]
    localModelValue.value = newValue
    emit('update:modelValue', newValue)
  }
  inputValue.value = '' // Clear input after selection
}

const handleTagsUpdate = (newValue: AcceptableInputValue[]) => {
  const stringValues = newValue.map(v => String(v))
  localModelValue.value = stringValues
  emit('update:modelValue', stringValues)
}

</script>

<style lang="scss" module>
.multiCompanySelector {
  width: 100%;
}

.tagsContainer {
  // Add any specific styling for the tags container if needed
  min-height: 32px; // Ensure it has some height even when empty
  border: 1px solid var(--el-border-color); // Match input border
  border-radius: var(--el-border-radius-base);
  padding: 5px;
  box-sizing: border-box;
  background-color: var(--el-fill-color-blank); // Match input background
  display: flex;
  flex-wrap: wrap;
  gap: 6px;

  &:focus-within {
      border-color: var(--el-color-primary);
  }
}

/* Optional: Style TagsInput items if needed */
.tagsContainer :global(.inline-flex) { // Targeting generated class by TagsInputItem
 // Add styles here if needed, e.g., background, border-radius
}

</style> 