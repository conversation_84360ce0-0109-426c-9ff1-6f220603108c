<script setup lang="ts">
import emitter from '@/utils/events'
import { removeToken } from './hooks/useToken'
import { ref } from 'vue'
import { elementPlusLocale } from './lang/i18n'

emitter.on('api:token-error', () => {
  removeToken()
})

const elementLocale = ref(elementPlusLocale)
</script>

<template>
  <el-config-provider :locale="elementLocale">
    <RouterView/>
  </el-config-provider>
</template>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
  width: 100%;
}
</style>
