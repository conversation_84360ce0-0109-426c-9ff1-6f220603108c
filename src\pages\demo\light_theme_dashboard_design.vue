<template>
  <div class="dashboard-container min-h-screen bg-background text-foreground">
    <div class="dashboard-layout">
      <!-- Main Content Area -->
      <main class="main-content">
        <div class="content-header">
          <h1 class="text-2xl font-bold bg-gradient-to-r from-violet-600 to-cyan-500 bg-clip-text text-transparent">
            AI Agent Workspace
          </h1>
          
          <div class="filter-controls">
            <div class="filter-dropdown">
              <button class="filter-button">
                <span>{{ selectedReportType || 'All Report Types' }}</span>
                <ChevronDownIcon class="h-4 w-4"/>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Agent Conversation Feed -->
        <div class="card-container">
          <div class="card">
            <div class="card-header">
              <div class="flex items-center gap-2">
                <ActivityIcon class="h-4 w-4"/>
                <h2 class="text-lg font-semibold">Agent Reporting</h2>
              </div>
              <Badge variant="outline" class="ml-auto">
                {{ filteredReports.length }} Updates
              </Badge>
            </div>
            
            <ScrollArea class="conversation-scroll-area">
              <div v-if="filteredReports.length > 0" class="conversation-feed">
                <!-- Date separators and messages -->
                <template v-for="(group, date) in groupedByDate" :key="date">
                  <div class="date-separator">
                    <span>{{ formatDateHeader(date) }}</span>
                  </div>
                  
                  <template v-for="report in group" :key="report.id">
                    <div class="message-wrapper" :class="{ 'animate-in': report.animateIn }">
                      <!-- Agent Avatar and Info -->
                      <div class="message-avatar">
                        <Avatar>
                          <AvatarImage :src="getAgentAvatar(report.agent_name)" :alt="report.agent_name"/>
                          <AvatarFallback>{{ getInitials(report.agent_name) }}</AvatarFallback>
                        </Avatar>
                        <span :class="`status-dot ${getStatusDotClass(report.status)}`"></span>
                      </div>
                      
                      <div class="message-content">
                        <div class="message-header">
                          <div>
                            <h3 class="agent-name">{{ report.agent_name }}</h3>
                            <span class="agent-role">{{ report.report_type }}</span>
                          </div>
                          <span class="message-time">{{ getFormattedTime(report) }}</span>
                        </div>
                        
                        <!-- Different message types based on status and type -->
                        <div :class="`message-bubble ${getStatusClass(report.status)}`" @click="report.status === 'completed' && openReportDetails(report)">
                          <!-- Report Header with Status Badge -->
                          <div class="message-status">
                            <Badge :variant="getStatusVariant(report.status)">
                              {{ getStatusLabel(report.status) }}
                            </Badge>
                            <span class="report-id">{{ report.report_name || report.name }}</span>
                          </div>
                          
                          <!-- Pending Status -->
                          <template v-if="report.status === 'pending'">
                            <p class="message-text">{{ report.report_description }}</p>
                            <div class="loading-dots">
                              <span></span>
                              <span></span>
                              <span></span>
                            </div>
                          </template>
                          
                          <!-- Running Status -->
                          <template v-else-if="report.status === 'running'">
                            <p class="message-text">{{ getProgressMessage(report) }}</p>
                            <Progress :value="report.progress" class="my-2"/>
                            <p class="progress-percentage">{{ report.progress }}% Complete</p>
                            
                            <!-- Tool Results -->
                            <div v-if="report.tools_results && report.tools_results.length > 0" class="tool-results">
                              <div v-for="(tool, index) in report.tools_results" :key="index" class="tool-result">
                                <div class="tool-header">
                                  <span class="tool-name">{{ tool.name }}</span>
                                  <Badge :variant="getToolStatusVariant(tool.status)">{{ tool.status }}</Badge>
                                </div>
                                <p v-if="tool.summary" class="tool-summary">{{ getToolSummary(tool.summary) }}</p>
                              </div>
                            </div>
                          </template>
                          
                          <!-- Completed Status (Historical Report) -->
                          <template v-else-if="report.status === 'completed'">
                            <div class="completed-report">
                              <p class="message-text">{{ report.summary }}</p>
                              
                              <!-- Report Preview Section -->
                              <div class="report-preview">
                                <!-- Key Findings Section -->
                                <div v-if="report.findings" class="findings-section">
                                  <h4 class="preview-section-title">Key Findings</h4>
                                  <ul class="findings-list">
                                    <li v-for="(finding, idx) in report.findings" :key="idx">
                                      {{ finding }}
                                    </li>
                                  </ul>
                                </div>
                                
                                <!-- Chart Preview if available -->
                                <div v-if="report.hasChart" class="chart-preview">
                                  <h4 class="preview-section-title">Data Visualization</h4>
                                  <div class="chart-placeholder">
                                    <BarChartIcon class="h-8 w-8 text-muted-foreground"/>
                                    <span class="text-sm text-muted-foreground">Chart preview available in full report</span>
                                  </div>
                                </div>
                              </div>
                              
                              <Button variant="outline"
                                      size="sm"
                                      class="view-report-button"
                                      @click.stop="openReportDetails(report)">
                                <ExternalLinkIcon class="h-4 w-4 mr-2"/>
                                View Full Report
                              </Button>
                            </div>
                          </template>
                          
                          <!-- Error Status -->
                          <template v-else-if="report.status === 'error'">
                            <p class="message-text text-destructive">An error occurred while processing this report.</p>
                            <Button variant="outline" size="sm" class="retry-button">
                              <RefreshCwIcon class="h-4 w-4 mr-2"/>
                              Retry
                            </Button>
                          </template>
                        </div>
                      </div>
                    </div>
                  </template>
                </template>
              </div>
              
              <!-- Empty State -->
              <div v-else class="empty-state">
                <div class="empty-state-icon">
                  <MessageSquarePlusIcon class="h-12 w-12 text-muted-foreground"/>
                </div>
                <h2 class="empty-state-title">No Active Work</h2>
                <p class="empty-state-description">{{ selectedAgent ? `${selectedAgent} has no active tasks.` : 'Assign tasks to your AI agents to see their work progress here.' }}</p>
                <Button>
                  <PlusIcon class="h-4 w-4 mr-2"/>
                  Assign New Task
                </Button>
              </div>
            </ScrollArea>
          </div>
        </div>
      </main>
      
      <!-- Right Sidebar - Agent List -->
      <aside class="right-sidebar">
        <div class="sidebar-header">
          <h2 class="text-lg font-semibold">Agent Employees</h2>
          <Badge>{{ activeAgentsCount }} Active</Badge>
        </div>
        
        <div class="search-container">
          <div class="search-input">
            <SearchIcon class="h-4 w-4 text-muted-foreground"/>
            <input type="text" placeholder="Search agents..." v-model="agentSearch"/>
          </div>
        </div>
        
        <ScrollArea class="agent-list-scroll">
          <div class="agent-list">
            <!-- All Agents Option -->
            <div class="agent-item" :class="{ active: !selectedAgent }" @click="filterByAgent(null)">
              <div class="agent-avatar">
                <Avatar>
                  <AvatarFallback>
                    <UsersIcon class="h-5 w-5"/>
                  </AvatarFallback>
                </Avatar>
              </div>
              <div class="agent-info">
                <h3 class="agent-name">All Agents</h3>
                <p class="agent-role">View all reports</p>
              </div>
            </div>
            
            <!-- Agent List -->
            <div v-for="agent in filteredAgents"
                 :key="agent.name"
                 class="agent-item"
                 :class="{ active: selectedAgent === agent.name }"
                 @click="filterByAgent(agent.name)">
              <div class="agent-avatar">
                <Avatar>
                  <AvatarImage :src="agent.avatar" :alt="agent.name"/>
                  <AvatarFallback>{{ getInitials(agent.name) }}</AvatarFallback>
                </Avatar>
                <span :class="`status-indicator ${getAgentStatusClass(agent.status)}`"></span>
              </div>
              <div class="agent-info">
                <h3 class="agent-name">{{ agent.name }}</h3>
                <p class="agent-role">{{ agent.role }}</p>
                <div class="agent-status">
                  <Badge :variant="getAgentStatusVariant(agent.status)">{{ agent.status }}</Badge>
                  <span class="status-text">{{ agent.currentTask || 'No active task' }}</span>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>
        
        <div class="sidebar-footer">
          <Button class="new-task-button">
            <PlusIcon class="h-4 w-4 mr-2"/>
            Assign New Task
          </Button>
        </div>
      </aside>
    </div>
    
    <!-- Report Details Modal (hidden by default) -->
    <Dialog :open="showReportModal" @update:open="closeReportDetails">
      <DialogContent class="report-modal-content">
        <DialogHeader>
          <DialogTitle>{{ activeReport.title }}</DialogTitle>
          <DialogDescription>
            Completed {{ activeReport.completedAt }} by {{ activeReport.agentName }}
          </DialogDescription>
        </DialogHeader>
        
        <div class="report-modal-body">
          <div class="report-header-info">
            <div class="agent-info-large">
              <Avatar class="h-12 w-12">
                <AvatarImage :src="activeReport.agentAvatar" :alt="activeReport.agentName"/>
                <AvatarFallback>{{ getInitials(activeReport.agentName) }}</AvatarFallback>
              </Avatar>
              <div>
                <h3 class="agent-name-large">{{ activeReport.agentName }}</h3>
                <p class="agent-role-large">{{ activeReport.reportType }}</p>
              </div>
            </div>
            <div class="report-meta">
              <div class="report-meta-item">
                <span class="meta-label">Report ID</span>
                <span class="meta-value">{{ activeReport.id }}</span>
              </div>
              <div class="report-meta-item">
                <span class="meta-label">Completed</span>
                <span class="meta-value">{{ activeReport.completedAt }}</span>
              </div>
              <div class="report-meta-item">
                <span class="meta-label">Type</span>
                <span class="meta-value">{{ activeReport.reportType }}</span>
              </div>
            </div>
          </div>
          
          <Tabs default-value="summary" class="report-tabs">
            <TabsList>
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="process">Process</TabsTrigger>
            </TabsList>
            <TabsContent value="summary">
              <div class="report-section">
                <h4 class="section-title">Executive Summary</h4>
                <p>{{ activeReport.summary }}</p>
                
                <div v-if="activeReport.findings && activeReport.findings.length > 0">
                  <h4 class="section-title mt-4">Key Findings</h4>
                  <ul class="findings-list">
                    <li v-for="(finding, idx) in activeReport.findings" :key="idx">
                      {{ finding }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="activeReport.recommendations && activeReport.recommendations.length > 0">
                  <h4 class="section-title mt-4">Recommendations</h4>
                  <ul class="recommendations-list">
                    <li v-for="(rec, idx) in activeReport.recommendations" :key="idx">
                      {{ rec }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="activeReport.hasChart" class="mt-6">
                  <h4 class="section-title">Data Visualization</h4>
                  <div class="chart-container">
                    <div class="chart-placeholder">
                      <BarChartIcon class="h-12 w-12 text-muted-foreground"/>
                      <span class="text-sm text-muted-foreground">Interactive chart would appear here</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="details">
              <div class="report-section">
                <h4 class="section-title">Detailed Analysis</h4>
                <div v-html="activeReport.content"></div>
              </div>
            </TabsContent>
            
            <TabsContent value="process">
              <div class="report-section">
                <h4 class="section-title">Process Steps</h4>
                
                <!-- Tool Results Timeline -->
                <div v-if="activeReport.toolResults && activeReport.toolResults.length > 0" class="tool-results-timeline">
                  <div v-for="(tool, index) in activeReport.toolResults" :key="index" class="timeline-item">
                    <div :class="`timeline-marker ${getToolStatusClass(tool.status)}`"></div>
                    <div class="timeline-content">
                      <h5 class="timeline-title">{{ tool.name }}</h5>
                      <p class="timeline-description">{{ getToolSummary(tool.summary) }}</p>
                      <Badge :variant="getToolStatusVariant(tool.status)">{{ tool.status }}</Badge>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-timeline">
                  <p>No process steps recorded for this report.</p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button variant="outline" @click="closeReportDetails">
            Close
          </Button>
          <Button>
            <DownloadIcon class="h-4 w-4 mr-2"/>
            Download Report
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { 
  ActivityIcon, SearchIcon, UsersIcon, PlusIcon, ChevronDownIcon, 
  ExternalLinkIcon, RefreshCwIcon, BarChartIcon, DownloadIcon,
  MessageSquarePlusIcon
} from 'lucide-vue-next'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// Sample data - Combined ongoing reports and historical reports into a single array
const allReports = ref([
  // Ongoing reports
  {
    id: 'report-1',
    sync_type: true,
    report_type: 'Financial Analysis',
    report_name: 'Q2 Financial Analysis',
    report_description: 'Analyzing Q2 financial data and preparing comprehensive report with insights and recommendations.',
    params: {},
    status: 'running',
    progress: 65,
    tools_results: [
      {
        name: 'Data Collection',
        status: 'completed',
        summary: 'Successfully collected financial data from all departments.'
      },
      {
        name: 'Data Analysis',
        status: 'in_progress',
        summary: 'Analyzing revenue trends across regions. Identified significant growth in APAC region (+18%) compared to Q1.'
      },
      {
        name: 'Report Generation',
        status: 'pending',
        summary: null
      }
    ],
    agent_name: 'Emma Watson',
    date: '2023-04-20T14:30:00Z',
    animateIn: false
  },
  {
    id: 'report-2',
    sync_type: false,
    report_type: 'Market Research',
    report_name: 'Competitor Product Analysis',
    report_description: 'Researching recent product launches, feature comparisons, and market positioning of key competitors.',
    params: {},
    status: 'pending',
    progress: 0,
    tools_results: [],
    agent_name: 'Michael Chen',
    date: '2023-04-20T15:45:00Z',
    animateIn: false
  },
  {
    id: 'report-3',
    sync_type: true,
    report_type: 'Legal Review',
    report_name: 'Regulatory Compliance Review',
    report_description: 'Analyzing new data protection regulations and assessing current compliance status for European markets.',
    params: {},
    status: 'running',
    progress: 40,
    tools_results: [
      {
        name: 'Regulation Analysis',
        status: 'completed',
        summary: 'Completed analysis of new GDPR amendments and their implications.'
      },
      {
        name: 'Compliance Assessment',
        status: 'in_progress',
        summary: 'Evaluating current systems against new requirements. Found 3 areas needing updates.'
      }
    ],
    agent_name: 'Sarah Johnson',
    date: '2023-04-20T13:15:00Z',
    animateIn: false
  },
  {
    id: 'report-4',
    sync_type: true,
    report_type: 'Data Analysis',
    report_name: 'Customer Behavior Patterns',
    report_description: 'Analyzing customer behavior patterns to identify trends and opportunities.',
    params: {},
    status: 'completed',
    progress: 100,
    tools_results: [
      {
        name: 'Data Collection',
        status: 'completed',
        summary: 'Collected 6 months of customer interaction data.'
      },
      {
        name: 'Pattern Analysis',
        status: 'completed',
        summary: 'Identified 5 key behavior patterns across customer segments.'
      },
      {
        name: 'Report Generation',
        status: 'completed',
        summary: 'Generated comprehensive report with visualizations.'
      }
    ],
    summary: 'Analysis complete. Identified 5 key customer behavior patterns with significant impact on conversion rates. Recommendations include personalized engagement strategies and targeted promotions for high-value segments.',
    findings: [
      'Mobile users have 37% higher conversion rate when using the app vs mobile web',
      'First-time visitors spend 2.3x more time on product pages with video demos',
      'Returning customers respond best to personalized recommendations (68% engagement)',
      'Cart abandonment decreased 23% after implementing the streamlined checkout',
      'Evening shoppers (6-10pm) spend 42% more per order than morning shoppers'
    ],
    recommendations: [
      'Prioritize app feature development over mobile web',
      'Expand video content across top 50 product pages',
      'Implement advanced personalization engine for returning customers',
      'Further optimize checkout flow with A/B testing',
      'Consider time-based promotions targeting evening shoppers'
    ],
    hasChart: true,
    agent_name: 'David Kim',
    date: '2023-04-20T11:30:00Z',
    animateIn: false
  },
  {
    id: 'report-5',
    sync_type: false,
    report_type: 'Strategic Planning',
    report_name: 'Q3 Strategy Mapping',
    report_description: 'Developing strategic roadmap for Q3 based on market trends and company goals.',
    params: {},
    status: 'error',
    progress: 25,
    tools_results: [
      {
        name: 'Goal Assessment',
        status: 'completed',
        summary: 'Analyzed company goals and KPIs for Q3.'
      },
      {
        name: 'Market Analysis',
        status: 'error',
        summary: 'Error accessing market data sources. Please check API credentials.'
      }
    ],
    agent_name: 'Jessica Lee',
    date: '2023-04-20T10:15:00Z',
    animateIn: false
  },
  
  // Historical reports (now integrated into the main timeline)
  {
    id: 'hist-1',
    name: 'Q1 Financial Analysis Report',
    report_type: 'Financial Analysis',
    status: 'completed',
    date: '2023-04-15T14:30:00Z',
    updated_at: '2023-04-15T16:45:00Z',
    language: 'English',
    agent_name: 'Emma Watson',
    summary: 'Q1 financial analysis showing 12% revenue growth, with detailed breakdowns by product line and recommendations for Q2 strategy.',
    findings: [
      'Overall revenue growth of 12% compared to Q1 last year',
      'Software division outperformed with 18% growth rate',
      'Hardware division faced supply chain challenges, limiting growth to 5%',
      'New markets in APAC region contributed 22% of total revenue',
      'Operating expenses increased by 7%, below revenue growth rate'
    ],
    recommendations: [
      'Increase investment in software division to capitalize on growth momentum',
      'Diversify hardware supply chain to mitigate future disruptions',
      'Expand marketing efforts in APAC region to build on initial success',
      'Maintain cost discipline to preserve margin improvements'
    ],
    hasChart: true,
    animateIn: false
  },
  {
    id: 'hist-2',
    name: 'Market Trend Analysis',
    report_type: 'Market Research',
    status: 'completed',
    date: '2023-04-10T09:15:00Z',
    updated_at: '2023-04-10T11:30:00Z',
    language: 'English',
    agent_name: 'David Kim',
    summary: 'Analysis of sustainable products market showing 23% YoY growth, with eco-friendly packaging as the fastest-growing segment.',
    findings: [
      'Sustainable products market grew 23% year-over-year',
      'Eco-friendly packaging segment grew 35%, the fastest in the category',
      'Consumer willingness to pay premium for sustainable products increased by 15%',
      'Regulatory environment becoming more favorable for sustainable initiatives',
      'Competitor activity increased 40% in sustainable product launches'
    ],
    hasChart: true,
    animateIn: false
  },
  {
    id: 'hist-3',
    name: 'EU Regulatory Compliance Review',
    report_type: 'Legal Review',
    status: 'completed',
    date: '2023-04-05T13:45:00Z',
    updated_at: '2023-04-05T15:20:00Z',
    language: 'English',
    agent_name: 'Sarah Johnson',
    summary: 'Comprehensive review of compliance with new data protection regulations in European markets. All systems now fully compliant.',
    findings: [
      'All systems are now fully compliant with GDPR and DORA regulations',
      'Data retention policies updated across all European operations',
      'Privacy notices updated on all customer-facing platforms',
      'Staff training completed for 98% of European employees',
      'Third-party vendor compliance verified for 100% of critical vendors'
    ],
    animateIn: false
  },
  {
    id: 'hist-4',
    name: 'Customer Segmentation Analysis',
    report_type: 'Data Analysis',
    status: 'completed',
    date: '2023-03-28T10:00:00Z',
    updated_at: '2023-03-28T12:15:00Z',
    language: 'English',
    agent_name: 'David Kim',
    summary: 'Detailed analysis of customer segments based on behavior, demographics, and purchasing patterns. Identified 5 key segments.',
    findings: [
      'Identified 5 distinct customer segments with unique behavior patterns',
      'Premium segment (12% of customers) generates 35% of total revenue',
      'Value-conscious segment (28% of customers) most responsive to promotions',
      'Early adopters (15% of customers) critical for new product launches',
      'Loyalty program members spend 2.4x more than non-members'
    ],
    hasChart: true,
    animateIn: false
  },
  {
    id: 'hist-5',
    name: 'Q2 Strategic Planning',
    report_type: 'Strategic Planning',
    status: 'completed',
    date: '2023-03-20T09:30:00Z',
    updated_at: '2023-03-20T11:45:00Z',
    language: 'English',
    agent_name: 'Jessica Lee',
    summary: 'Strategic roadmap for Q2 with focus areas, KPIs, and action items aligned with company goals and market opportunities.',
    findings: [
      'Identified 3 key strategic priorities for Q2',
      'Established measurable KPIs for each department',
      'Allocated resources based on Q1 performance and Q2 priorities',
      'Identified potential risks and mitigation strategies',
      'Aligned Q2 goals with annual company objectives'
    ],
    animateIn: false
  }
])

// Agent data
const agents = ref([
  {
    name: 'Emma Watson',
    role: 'Financial Analyst',
    status: 'working',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    currentTask: 'Q2 Financial Analysis'
  },
  {
    name: 'Michael Chen',
    role: 'Market Researcher',
    status: 'pending',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    currentTask: 'Competitor Product Analysis'
  },
  {
    name: 'Sarah Johnson',
    role: 'Legal Specialist',
    status: 'working',
    avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
    currentTask: 'Regulatory Compliance Review'
  },
  {
    name: 'David Kim',
    role: 'Data Scientist',
    status: 'completed',
    avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
    currentTask: 'Customer Behavior Patterns'
  },
  {
    name: 'Jessica Lee',
    role: 'Strategic Analyst',
    status: 'error',
    avatar: 'https://randomuser.me/api/portraits/women/22.jpg',
    currentTask: 'Q3 Strategy Mapping'
  },
  {
    name: 'Robert Taylor',
    role: 'Marketing Specialist',
    status: 'idle',
    avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
    currentTask: null
  }
])

// UI state
const selectedAgent = ref(null)
const selectedReportType = ref(null)
const showReportModal = ref(false)
const agentSearch = ref('')
const activeReport = ref({
  id: '',
  title: '',
  agentName: '',
  agentAvatar: '',
  reportType: '',
  completedAt: '',
  summary: '',
  content: '',
  findings: [],
  recommendations: [],
  toolResults: [],
  hasChart: false
})

// Computed properties
const filteredAgents = computed(() => {
  if (!agentSearch.value) {return agents.value}
  const search = agentSearch.value.toLowerCase()
  return agents.value.filter(agent => 
    agent.name.toLowerCase().includes(search) || 
    agent.role.toLowerCase().includes(search)
  )
})

const activeAgentsCount = computed(() => {
  return agents.value.filter(agent => agent.status === 'working' || agent.status === 'pending').length
})

const filteredReports = computed(() => {
  let reports = allReports.value

  if (selectedAgent.value) {
    reports = reports.filter(report => report.agent_name === selectedAgent.value)
  }

  if (selectedReportType.value) {
    reports = reports.filter(report => report.report_type === selectedReportType.value)
  }

  // Sort by date, newest first
  return reports.sort((a, b) => new Date(b.date) - new Date(a.date))
})

// Group reports by date for the conversation feed
const groupedByDate = computed(() => {
  const grouped = {}
  
  filteredReports.value.forEach(report => {
    const date = new Date(report.date).toLocaleDateString()
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(report)
  })
  
  return grouped
})

// Methods
const filterByAgent = (agentName) => {
  selectedAgent.value = agentName
}

const getAgentAvatar = (agentName) => {
  const agent = agents.value.find(a => a.name === agentName)
  return agent ? agent.avatar : 'https://randomuser.me/api/portraits/lego/1.jpg'
}

const getInitials = (name) => {
  return name.split(' ').map(n => n[0]).join('')
}

const getStatusClass = (status) => {
  switch (status) {
  case 'pending': return 'pending'
  case 'running': return 'running'
  case 'completed': return 'completed'
  case 'error': return 'error'
  default: return ''
  }
}

const getStatusVariant = (status) => {
  switch (status) {
  case 'pending': return 'warning'
  case 'running': return 'info'
  case 'completed': return 'success'
  case 'error': return 'destructive'
  default: return 'default'
  }
}

const getStatusDotClass = (status) => {
  switch (status) {
  case 'pending': 
  case 'running': return 'working'
  case 'completed': return 'completed'
  case 'error': return 'error'
  default: return ''
  }
}

const getStatusLabel = (status) => {
  switch (status) {
  case 'pending': return 'Starting'
  case 'running': return 'In Progress'
  case 'completed': return 'Completed'
  case 'error': return 'Error'
  default: return status
  }
}

const getAgentStatusClass = (status) => {
  switch (status) {
  case 'working': return 'working'
  case 'pending': return 'pending'
  case 'completed': return 'completed'
  case 'error': return 'error'
  case 'idle': return 'idle'
  default: return ''
  }
}

const getAgentStatusVariant = (status) => {
  switch (status) {
  case 'working': return 'info'
  case 'pending': return 'warning'
  case 'completed': return 'success'
  case 'error': return 'destructive'
  case 'idle': return 'outline'
  default: return 'default'
  }
}

const getToolStatusClass = (status) => {
  switch (status) {
  case 'completed': return 'completed'
  case 'in_progress': return 'in-progress'
  case 'pending': return 'pending'
  case 'error': return 'error'
  default: return ''
  }
}

const getToolStatusVariant = (status) => {
  switch (status) {
  case 'completed': return 'success'
  case 'in_progress': return 'info'
  case 'pending': return 'warning'
  case 'error': return 'destructive'
  default: return 'default'
  }
}

const getProgressMessage = (report) => {
  if (report.tools_results && report.tools_results.length > 0) {
    const inProgressTool = report.tools_results.find(tool => tool.status === 'in_progress')
    if (inProgressTool && inProgressTool.summary) {
      return inProgressTool.summary
    }
  }
  return report.report_description
}

const getToolSummary = (summary) => {
  if (typeof summary === 'string') {
    return summary
  } if (summary && typeof summary === 'object') {
    return Object.values(summary).join('. ')
  }
  return 'No details available'
}

const getFormattedTime = (report) => {
  const date = new Date(report.date)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const formatDateHeader = (dateString) => {
  const today = new Date().toLocaleDateString()
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  const yesterdayString = yesterday.toLocaleDateString()
  
  if (dateString === today) {
    return 'Today'
  } if (dateString === yesterdayString) {
    return 'Yesterday'
  } 
  return new Date(dateString).toLocaleDateString('en-US', { 
    weekday: 'long', 
    month: 'long', 
    day: 'numeric' 
  })
  
}

const openReportDetails = (report) => {
  activeReport.value = {
    id: report.id,
    title: report.report_name || report.name,
    agentName: report.agent_name,
    agentAvatar: getAgentAvatar(report.agent_name),
    reportType: report.report_type,
    completedAt: report.date ? new Date(report.date).toLocaleString() : 'N/A',
    summary: report.summary || 'No summary available',
    findings: report.findings || [],
    recommendations: report.recommendations || [],
    toolResults: report.tools_results || [],
    hasChart: report.hasChart || false,
    content: generateReportContent(report)
  }
  showReportModal.value = true
}

const closeReportDetails = () => {
  showReportModal.value = false
}

const generateReportContent = (report) => {
  // In a real app, this would be actual report content
  return `<p>This is the detailed content of the ${report.report_name || report.name} report.</p>
          <p>It would include comprehensive analysis, charts, and recommendations based on the findings.</p>
          <p>The report would also contain detailed data tables, methodology explanations, and references to source data.</p>`
}

// Simulate real-time updates
onMounted(() => {
  // Add animation class to existing reports
  allReports.value.forEach(report => {
    report.animateIn = true
  })

  // Simulate a new report coming in after 5 seconds
  setTimeout(() => {
    const newReport = {
      id: 'report-6',
      sync_type: true,
      report_type: 'Marketing Analysis',
      report_name: 'Social Media Campaign Performance',
      report_description: 'Analyzing performance metrics of recent social media campaigns across platforms.',
      params: {},
      status: 'pending',
      progress: 0,
      tools_results: [],
      agent_name: 'Robert Taylor',
      date: new Date().toISOString(),
      animateIn: true
    }
    
    allReports.value.push(newReport)
    
    // Update agent status
    const agentIndex = agents.value.findIndex(a => a.name === 'Robert Taylor')
    if (agentIndex !== -1) {
      agents.value[agentIndex].status = 'pending'
      agents.value[agentIndex].currentTask = 'Social Media Campaign Performance'
    }
  }, 5000)
})
</script>

<style>
/* Base Styles */
:root {
  --primary: hsl(262, 80%, 50%);
  --primary-light: hsla(262, 80%, 50%, 0.1);
  --primary-foreground: hsl(0, 0%, 100%);
  
  --secondary: hsl(220, 14%, 96%);
  --secondary-foreground: hsl(220, 9%, 46%);
  
  --accent: hsl(262, 83%, 58%);
  --accent-light: hsla(262, 83%, 58%, 0.1);
  
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(224, 71%, 4%);
  
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(224, 71%, 4%);
  
  --muted: hsl(220, 14%, 96%);
  --muted-foreground: hsl(220, 9%, 46%);
  
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --ring: hsl(224, 71%, 4%);
  
  --radius: 0.5rem;
  
  /* Custom colors */
  --glass-bg: hsla(0, 0%, 100%, 0.8);
  --glass-border: hsla(220, 13%, 91%, 0.5);
  --glass-highlight: hsla(0, 0%, 100%, 0.95);
  
  /* Status colors */
  --success: hsl(142, 71%, 45%);
  --success-light: hsla(142, 71%, 45%, 0.1);
  --info: hsl(221, 83%, 53%);
  --info-light: hsla(221, 83%, 53%, 0.1);
  --warning: hsl(38, 92%, 50%);
  --warning-light: hsla(38, 92%, 50%, 0.1);
  --error: hsl(0, 84%, 60%);
  --error-light: hsla(0, 84%, 60%, 0.1);
  
  /* Report type colors */
  --financial: hsl(142, 71%, 45%);
  --financial-light: hsla(142, 71%, 45%, 0.1);
  --legal: hsl(262, 80%, 50%);
  --legal-light: hsla(262, 80%, 50%, 0.1);
  --data: hsl(262, 83%, 58%);
  --data-light: hsla(262, 83%, 58%, 0.1);
  --market: hsl(38, 92%, 50%);
  --market-light: hsla(38, 92%, 50%, 0.1);
  --strategy: hsl(326, 73%, 59%);
  --strategy-light: hsla(326, 73%, 59%, 0.1);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-size: 14px;
  line-height: 1.5;
}

/* Layout */
.dashboard-container {
  position: relative;
  min-height: 100vh;
  overflow-x: hidden;
  background-image: 
    radial-gradient(circle at 10% 20%, hsla(262, 80%, 50%, 0.03) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, hsla(262, 83%, 58%, 0.03) 0%, transparent 20%),
    linear-gradient(to bottom right, hsla(262, 80%, 50%, 0.01) 0%, transparent 70%);
}

.dashboard-layout {
  display: grid;
  grid-template-columns: 1fr 280px;
  min-height: 100vh;
  position: relative;
  z-index: 1;
}

/* Main Content */
.main-content {
  padding: 1.5rem 2rem;
  overflow-y: auto;
  background: var(--background);
  display: flex;
  flex-direction: column;
}

.content-header {
  margin-bottom: 1.5rem;
}

.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--foreground);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.filter-button:hover {
  background: var(--glass-highlight);
  border-color: var(--glass-border);
}

/* Card Container */
.card-container {
  flex: 1;
  margin-bottom: 1.5rem;
}

.card {
  background: var(--card);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Conversation Feed */
.conversation-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  height: calc(100% - 60px);
}

.conversation-feed {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Date Separator */
.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  position: relative;
}

.date-separator::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 1px;
  background-color: var(--border);
  z-index: 0;
}

.date-separator span {
  background-color: var(--background);
  padding: 0 1rem;
  font-size: 0.75rem;
  color: var(--muted-foreground);
  position: relative;
  z-index: 1;
}

/* Message Styles */
.message-wrapper {
  display: flex;
  gap: 1rem;
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.3s ease-in-out forwards;
}

.message-wrapper.animate-in {
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
  position: relative;
}

.status-dot {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid var(--background);
}

.status-dot.completed {
  background: var(--success);
}

.status-dot.working {
  background: var(--info);
}

.status-dot.error {
  background: var(--destructive);
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: calc(100% - 60px);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.agent-name {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.125rem;
}

.agent-role {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.message-time {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.message-bubble {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: 1.25rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.message-bubble:hover {
  box-shadow: var(--shadow-md);
}

.message-bubble.completed {
  background: linear-gradient(to bottom right, var(--glass-highlight), var(--glass-bg));
  border: 1px solid var(--success-light);
  cursor: pointer;
}

.message-bubble.completed:hover {
  border-color: var(--success);
  transform: translateY(-2px);
}

.message-bubble.running {
  background: linear-gradient(to bottom right, var(--glass-highlight), var(--glass-bg));
  border: 1px solid var(--info-light);
}

.message-bubble.pending {
  background: linear-gradient(to bottom right, var(--glass-highlight), var(--glass-bg));
  border: 1px solid var(--warning-light);
}

.message-bubble.error {
  background: linear-gradient(to bottom right, var(--glass-highlight), var(--glass-bg));
  border: 1px solid var(--error-light);
}

.message-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.report-id {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.message-text {
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Loading Animation */
.loading-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--warning);
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Tool Results */
.tool-results {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.tool-result {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.75rem;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.tool-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.tool-summary {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Progress Percentage */
.progress-percentage {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  text-align: right;
  margin-top: 0.25rem;
}

/* Completed Report */
.completed-report {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.report-preview {
  background: var(--secondary);
  border-radius: var(--radius);
  padding: 1rem;
  margin: 0.5rem 0;
}

.preview-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.findings-list {
  list-style-type: disc;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.findings-list li {
  margin-bottom: 0.25rem;
}

.chart-preview {
  margin-top: 1rem;
  border-top: 1px solid var(--border);
  padding-top: 1rem;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: var(--muted);
  border-radius: var(--radius);
  height: 120px;
}

.view-report-button {
  align-self: flex-end;
  margin-top: 0.5rem;
}

.retry-button {
  align-self: flex-end;
  margin-top: 0.5rem;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-state-icon {
  margin-bottom: 1.5rem;
  color: var(--muted-foreground);
}

.empty-state-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.empty-state-description {
  color: var(--muted-foreground);
  margin-bottom: 1.5rem;
  max-width: 400px;
}

/* Right Sidebar */
</style>