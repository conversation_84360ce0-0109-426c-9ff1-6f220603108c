<template>
  <div :class="$style['two-column-layout']">
    <div tour-id="information-news"
         :class="$style['left-column']"
         
         ref="leftColumnRef"
         
         v-loading="isDailyNewsLoading"
         :element-loading-text="t('login.newsLoading')">
      <SwiperButtonGroup
        v-bind="{
          ...slideGroupButtonsProps,
        }"/>
      <template v-if="dailyNews?.length">
        <Swiper
          :modules="swiperModules"
          :observer="true"
          :slidesPerView="1"
          :autoplay="{
            delay: AUTOPLAY_DELAY,
            disableOnInteraction: false,
          }"
          :mousewheel="mouseWheelOptions"
          :speed="1500"
          :direction="'vertical'"
          :loop="true"
          class="h-full w-full main-swiper"
          :noSwiping="true"
          @swiper="onSwiperInit"
          @slideChange="handleSlideChange">
          <!-- 新闻轮播部分 -->
          <SwiperSlide
            v-for="item in dailyNews"
            :key="item.id"
            class="relative">
            <SlideContentWrapper>
              <NewsContentSlide
                class="swiper-no-swiping swiper-controls"
                :news="item"
                :show-long-summary="true"
                v-bind="slideWrapperEvents"/>
            </SlideContentWrapper>
          </SwiperSlide>
        </Swiper>
      </template>
      
    </div>

    <div :class="$style['right-column']" tour-id="information-news-list">
      <NewsList @click-news="handleNewsClick"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import 'swiper/css'
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  type Component,
  type ComputedRef,
} from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, FreeMode, Mousewheel } from 'swiper/modules'
import { useFullscreen, useLocalStorage, useTimeoutFn } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { isNil } from 'lodash-es'
import { useI18n } from 'vue-i18n'
// 组件导入
import NewsList from './components/NewsList.vue'
import NewsContentSlide from './components/NewsContentSlide.vue'
import SlideContentWrapper from './SwiperSlideItem/SlideContentWrapper.vue'
import SwiperButtonGroup from './SwiperSlideItem/SwiperButtonGroup.vue'

// Store 导入
import { useNewsStore } from './store'
import { useUserStore } from '@/stores/user'

// Composables 导入
import { useSwiper } from './composable/useSwiper'
import { useChartComposable } from './composable/useChartComposable'
import { useTrack } from '@/hooks/useTrack'

const store = useNewsStore()
const userStore = useUserStore()
const { trackEvent } = useTrack()
// Store 状态
const { locale, t } = useI18n()
const { userInfo } = storeToRefs(userStore)
const { isDailyNewsLoading, dailyNews } =
  storeToRefs(store)


// // 图表相关逻辑
// const {
//   fetchAllChartData,
// } = useChartComposable()

// Swiper 配置和状态
const leftColumnRef = ref(null)
const isHideChartSwiper = useLocalStorage(
  `isHideChartSwiper-${userInfo.value?.id}`,
  true
)
const swiperModules = [Autoplay, FreeMode, Mousewheel]

const formattedPausedTime = (time:number) => {
  return Math.floor(time / 1000)
}

const getPausedText = (time:number) => {
  // mouse -> cn :鼠标移出{actualTime}后 us:The swiper is paused, mouse out and {actualTime} later will autoplay
  // click -> cn :点击后{actualTime}秒后 us:The swiper is paused, {actualTime} later will autoplay
  const actualTime = formattedPausedTime(time)
  const TEXT_MAP = {
    chinese:{
      mouse:`鼠标移出${actualTime}秒后`,
      click:`${actualTime}秒后`
    },
    english:{
      mouse:`The swiper is paused, mouse out and ${actualTime} later will autoplay`,
      click:`The swiper is paused, ${actualTime} later will autoplay`
    },
    japanese:{
      mouse:`スライダーはマウスを出して${actualTime}秒後に自動再生します`,
      click:`スライダーはマウスを出して${actualTime}秒後に自動再生します`
    }
  } as const
  return TEXT_MAP[locale.value as 'chinese' | 'english' | 'japanese'][pauseReason.value as 'mouse' | 'click']
}

const fullScreenFeedback = (e: MouseEvent)=>{
  toggleFullscreen()
  handleContentMouseLeave()
}
const slideGroupButtonsProps = computed(() => ({
  // pausedTime: pauseReason.value === 'mouse' ? AUTOPLAY_DELAY : PAUSE_DURATION + AUTOPLAY_DELAY,
  pausedText: getPausedText(pauseReason.value === 'mouse' ? AUTOPLAY_DELAY : PAUSE_DURATION + AUTOPLAY_DELAY),
  isPaused: isPaused.value,
  isFullscreen: isFullscreen.value,
  isHideChartSwiper: isHideChartSwiper.value,
  autoplayTimeLeft: autoplayTimeLeft.value,
  autoplayPercentage: autoplayPercentage.value,
  isManualControl: isManualControl.value,

  
  // 封装事件处理器
  onToggleFullscreen: fullScreenFeedback,
  // onToggleChartSwiper: toggleChartSwiper,
  onRefresh: refreshSwiper,
  onToggleManualControl: toggleManualControl,
}))

const mouseWheelOptions = {
  forceToAxis: true,
  sensitivity: 1,
  releaseOnEdges: true,
  thresholdDelta: 50,
  thresholdTime: 100,
}

// 使用改进后的 useSwiper
const {
  isPaused,
  PAUSE_DURATION,
  pauseReason,
  AUTOPLAY_DELAY,
  autoplayTimeLeft,
  autoplayPercentage,
  isManualControl,
  
  onSwiperInit,
  slideToIndex,
  handleContentMouseEnter,
  handleContentMouseLeave,
  handleSlideChange,
  resetAutoplay,
  pauseAutoplay,
  startPauseTimeout,

  toggleManualControl,
} = useSwiper({
  onSlideChange: (index) => {
    // 在这里可以添加幻灯片变化时的额外逻辑
    // store.updateSlideChangeTime()

  },
})

const slideWrapperEvents = {
  onMouseenter: handleContentMouseEnter,
  onMouseleave: handleContentMouseLeave,
}
// 图表组件配置
interface ChartComponent {
  id: string;
  component: Component;
  hidden: ComputedRef<boolean>;
  loading: ComputedRef<boolean>;
  data: ComputedRef<any>;
}

// computed
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen(leftColumnRef)

// 方法定义
const refreshSwiper = async () => {
  try {
    await store.refreshDailyNews()
    // 刷新后重置自动播放
    resetAutoplay()
  } catch (error) {
    console.error('Failed to refresh swiper:', error)
  }
}

const handleNewsClick = (index: number) => {
  trackEvent('click_for_details', {
    news_title: dailyNews.value?.[index]?.title
  })
  slideToIndex(index)
  pauseAutoplay()
  pauseReason.value = 'click'
  // 启动10秒计时器
  startPauseTimeout()
}

</script>

<style scoped lang="scss" module>
.two-column-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 100%;
}

.left-column {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  grow: 1;
  background-color: #eef3f6;
}

.right-column {
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

// 其余样式保持不变...
</style>
