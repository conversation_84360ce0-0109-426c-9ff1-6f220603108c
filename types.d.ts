/// <reference types="vite-plugin-vue-layouts/client" />
/// <reference types="unplugin-vue-router/client" />

import type { ProductSchemeValue } from '@/stores/productRightStore'
import 'dayjs'
import type { RouteNamedMap } from 'vue-router/auto-routes'

declare module 'dayjs' {
    interface Dayjs {
      militaryTime: (enable?: boolean) => boolean;
      $militaryTime: boolean;
      $locale: () => any;
      getLocalOrgTimeZoneOffset: () => number;
      moveToOrgTimeZone: () => Dayjs;
      moveToLocalTimeZone: () => Dayjs;
      getTime: () => number;
    }
    export function militaryTime(enable?: boolean): boolean;
    export function forOrgTimeZone(...args: any[]): Dayjs;
    export let $militaryTime: boolean
    export let orgTimeZone: string
}

export interface AccessParams {
  permissions?: string[]
  roles?: string[]
  features?: string[]
  rightSchemeType?: string
  userTypes?: UserType[]
  fallback?: AccessParams[]
}  
declare module 'vue-router/auto' {
  interface RouteMeta {
    requiresAuth?: boolean
    requiresGuest?: boolean
    tryAuth?: boolean
    redirectIfUnauthorized?: string
    redirectIfNoAccess?: string
    keepAlive?: boolean
    access?: AccessParams
  }
}
export type RoutePath = RouteNamedMap[keyof RouteNamedMap]['path']
export type RouteName = RouteNamedMap[keyof RouteNamedMap]['name']
declare module 'mitt' {
  interface Emitter<Events extends Record<EventType, unknown>> {
    once<Key extends keyof Events>(type: Key, handler: Handler<Events[Key]>): void;
  }
}