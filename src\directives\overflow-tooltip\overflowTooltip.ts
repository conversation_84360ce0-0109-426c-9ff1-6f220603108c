import { useOverflow } from '@/hooks/useOverflow'
import { useTooltip, type TooltipProps } from '@/hooks/useTooltip'
import { useEventListener } from '@vueuse/core'

export type TooltipOptions = TooltipProps & {
  content?: string;
} | boolean | undefined

export default class OverflowTooltip {
  private stopOverflow: (() => void) | undefined
  private stopLeave: (() => void) | undefined
  private content: string = ''
  private useTooltipInstance: ReturnType<typeof useTooltip>
  private delayTimer: number | undefined
  private delay: number = 500
  constructor(
    private el: HTMLElement,
    private options?: TooltipOptions,
  ) {
    this.useTooltipInstance = useTooltip()
    this.update(options)
  }

  private showTooltip() {
    if (!this.content || this.options === false) {
      return
    }
    if (this.delayTimer) {
      clearTimeout(this.delayTimer)
    }
    const point = this.el.getBoundingClientRect()
    const options = this.options === true ? {} : this.options
    if (this.delay > 0) {
      this.delayTimer = window.setTimeout(() => {
        this.useTooltipInstance.showTooltip(point, this.content, options)
      }, this.delay)
      return
    }
    this.useTooltipInstance.showTooltip(point, this.content, options)
  }

  private createOverflow() {
    const { stop, isOverflowing } = useOverflow(this.el, {
      onEnter: () => {
        if (isOverflowing.value) {
          this.showTooltip()
        }
      },
      onChangeWithEnter: (overflow) => {
        if (overflow) {
          this.showTooltip()
          return
        }
        this.useTooltipInstance.hideTooltip()
      }
    })
    this.stopOverflow = stop
  }
  private createLeave() {
    this.stopLeave = useEventListener(this.el, 'mouseleave', () => {
      clearTimeout(this.delayTimer)
      this.useTooltipInstance.hideTooltip()
    })
  }

  update(options?: TooltipOptions) {
    this.delay = typeof options === 'object' ? options.showAfter || 500 : 500
    this.options = options
    if (options === false) {
      this.stopOverflow?.()
      this.stopOverflow = undefined
      return
    }
    this.content = (options === true ? '' : options?.content) || this.el.innerText || this.el.textContent || ''
    if (!this.stopLeave) {
      this.createLeave()
    }
    if (!this.stopOverflow) {
      this.createOverflow()
    }
  }

  destroy() {
    this.stopOverflow?.()
    this.stopLeave?.()
    this.useTooltipInstance.hideTooltip()
    if (this.delayTimer) {
      clearTimeout(this.delayTimer)
    }

    this.stopOverflow = undefined
    this.stopLeave = undefined
  }
}
