<template>
  <div
    v-if="modelValue"
    ref="filterDropdownRef"
    class="absolute right-0 top-full mt-1 w-72 bg-white shadow-lg rounded-md border border-slate-200 p-4 z-50 space-y-3">
    <!-- 国家筛选 -->
    <div class="filter-item">
      <label class="text-xs font-medium text-slate-700 block mb-1">{{ t('agent.nation_label') }}</label>
      <ElSelectV2Normal
        v-model="internalSelectedNations"
        :options="nationOptionsForFilter"
        multiple
        filterable
        collapse-tags
        collapse-tags-tooltip
        :placeholder="t('agent.nation_placeholder')"
        :loading="isLoadingFilterNations"
        class="filterSelect w-full"/>
    </div>

    <div class="flex items-center justify-between gap-2 mt-3">
      <Button variant="ghost"
              size="sm"
              @click="handleResetFilters"
              class="text-xs primary-color hover:bg-blue-50 flex-1">
        {{ t('common.reset_filters') }}
      </Button>
      <Button variant="default"
              size="sm"
              @click="handleApplyFilters"
              class="text-xs primary-color hover:bg-blue-700 text-white flex-1">
        {{ t('button.confirm') }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { useI18n } from 'vue-i18n'
import ElSelectV2Normal from '@/components/ElSelectV2Normal.vue'
import { Button } from '@/components/ui/button'

const props = defineProps<{
  modelValue: boolean // For v-model:showFilters
  triggerElement: HTMLElement | null
  nationOptionsForFilter: Array<{ label: string; value: string }>
  isLoadingFilterNations: boolean
  initialNations?: string[]
}>()

const emit = defineEmits(['update:modelValue', 'apply-filters', 'reset-filters-and-fetch'])

const { t } = useI18n()

const filterDropdownRef = ref<HTMLElement | null>(null)

const internalSelectedNations = ref<string[]>([])

// Create a computed ref for the trigger element to be used in ignore
const triggerElementRef = computed(() => props.triggerElement)

function syncInitialValues() {
  internalSelectedNations.value = props.initialNations ? [...props.initialNations] : []
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    syncInitialValues()
  }
})

onMounted(() => {
  if (props.modelValue) {
    syncInitialValues()
  }
})

onClickOutside(
  filterDropdownRef,
  (event) => {
    const targetElement = event.target as HTMLElement
    if (
      targetElement.closest('.el-select-dropdown') ||
      targetElement.closest('.el-popper') ||
      targetElement.closest('.el-cascader-menu')
    ) {
      return
    }
    // Also check if the click was on the trigger element itself to prevent immediate re-closing if the trigger toggles visibility
    if (props.triggerElement && props.triggerElement.contains(targetElement)) {
      return
    }
    emit('update:modelValue', false)
  },
  { ignore: [triggerElementRef] } // Use the computed ref here
)

function handleApplyFilters() {
  const filtersToEmit: Record<string, any> = {}
  if (internalSelectedNations.value.length > 0) {
    filtersToEmit.nations = internalSelectedNations.value
  }

  emit('apply-filters', filtersToEmit)
  emit('update:modelValue', false)
}

function handleResetFilters() {
  internalSelectedNations.value = []
  emit('reset-filters-and-fetch')
  emit('update:modelValue', false)
}

</script>

<style scoped>
.filterSelect {
  width: 100%;
}

.filterKeywordsInput {
  width: 100%;
}

/* 确保下拉菜单固定在合适位置 */
:deep(.el-select-dropdown) {
  z-index: 100;
}

:deep(.el-popper) {
  z-index: 100;
}
</style> 