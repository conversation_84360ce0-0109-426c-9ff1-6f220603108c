<template>
  <div 
    ref="searchResultsListRef"
    class="pdf-exclude" 
    :class="$style.container">
    <div :class="$style.header" @click="toggleExpand">
      <h4 :class="$style.title">{{ $t(title) }}</h4>
      <el-button
        type="text"
        :class="$style.toggle"
        @click.stop="toggleExpand">
        {{ isExpanded ? $t("global.collapse") : $t("global.expand") }}
      </el-button>
    </div>
    <div :class="[
      $style.wrapper,
      { [$style.expanded]: isExpanded }
    ]">
      <div 
        :class="[
          $style.content,
          { [$style.slideIn]: isExpanded },
          { [$style.slideOut]: !isExpanded }
        ]">
        <el-scrollbar 
          :class="[
            $style.innerContent,
            wrapClass
          ]"
          always>
          <div
            v-for="(result, index) in results"
            :key="index"
            :class="$style.item"
            :data-reference-id="result.id">
            <div :class="$style.itemHeader">
              <div :class="$style.titleWrapper">
                <el-image
                  v-if="result.favicon"
                  :src="result.favicon"
                  :class="$style.favicon"
                  @error="handleImageError"/>
                <a :href="result.url" target="_blank" :class="$style.title">
                  {{ result.title || result.text?.slice(0, 50) }}
                </a>
              </div>
              <div :class="$style.scores">
                <el-tooltip
                  v-if="result.credibility_score !== undefined"
                  :content="$t('global.credibilityScore')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.credibility]">
                    <Icon icon="mdi-shield-check"/>
                    {{ formatScore(result.credibility_score) }}
                  </div>
                </el-tooltip>
                <el-tooltip
                  v-if="result.importance_score !== undefined"
                  :content="$t('global.importanceScore')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.importance]">
                    <Icon icon="mdi-star"/>
                    {{ formatScore(result.importance_score) }}
                  </div>
                </el-tooltip>
                <el-tooltip
                  v-if="result.relevance_score !== undefined"
                  :content="$t('global.relevanceScore')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.relevance]">
                    <Icon icon="mdi-magnify"/>
                    {{ formatScore(result.relevance_score) }}
                  </div>
                </el-tooltip>
                <el-tooltip
                  v-if="result.average_score !== undefined"
                  :content="$t('global.averageScore')"
                  placement="top"
                  :show-after="1000">
                  <div :class="[$style.scoreChip, $style.average]">
                    <Icon icon="mdi-chart-line"/>
                    {{ formatScore(result.average_score) }}
                  </div>
                </el-tooltip>
              </div>
            </div>
            <p v-if="showSummary" :class="$style.summary">{{ removeMarkdown(result.summary || '') }}</p>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'
import { removeMarkdown } from '@/utils/markdown'
import type { url } from '@/pages/format-report/type'
import { Icon } from '@iconify/vue'

defineProps({
  results: {
    type: Array as PropType<url[]>,
    default: () => [],
  },
  showSummary: {
    type: Boolean,
    default: true,
  },
  wrapClass: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: 'global.searchResults',
  },
})

const isExpanded = ref(true)
defineExpose({ isExpanded })

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const handleImageError = (event: Event) => {
  console.log('event', event)
  const imgElement = event.target as HTMLImageElement
  imgElement.remove()
}

const formatScore = (score: number) => {
  return score.toFixed(1)
}
</script>

<style lang="scss" module>
.container {
  margin: 20px 0px;
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.wrapper {
  position: relative;
  overflow: hidden;
  transition: max-height 0.3s ease;
  max-height: 0;
  display: flex;

  &.expanded {
    max-height: 500px;
  }
}

.content {
  width: 100%;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 12px;
  opacity: 0;
  transform: translateY(-20px);
}

.slideIn {
  animation: slideDown 0.3s ease-out forwards;
}

.slideOut {
  animation: slideUp 0.3s ease-in forwards;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  cursor: pointer;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 0 8px;
}

.title {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.item {
  padding: 12px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

}

@keyframes highlight {
  0%, 100% {
    background-color: transparent;
  }
  20%, 80% {
    background-color: var(--el-color-primary-light-9);
  }
}

.itemHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 8px;
}

.titleWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
  flex: 1;
}

.scores {
  display: flex;
  gap: 8px;
  shrink: 0;
}

.scoreChip {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  
  svg {
    width: 14px;
    height: 14px;
  }
}

.credibility {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.importance {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.average {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.favicon {
  width: 16px;
  height: 16px;
}

.title {
  color: var(--primary-color);
  text-decoration: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    text-decoration: underline;
  }
}

.summary {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.innerContent {
  max-height: 500px;
  display: flex;
  flex-direction: column;
  :global(.el-scrollbar__wrap) {
    max-height: inherit;
  }
  
}
.relevance{
  background-color: var(--el-color-warning-light-9);
  color: var(--primary-color);
}
</style>
<style lang="scss">
// 全局样式，确保动画效果在任何地方都能生效
.highlight-search-result {
  animation: breathing-highlight 2s ease-in-out infinite !important;
}

@keyframes breathing-highlight {
  0% {
    background-color: transparent;
    box-shadow: none;
  }
  50% {
    background-color: var(--el-color-primary-light-9);
    box-shadow: 0 0 10px var(--el-color-primary-light-7);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}
</style>
