import router from '@/router'
import { useUserStore } from '@/stores/user'
import { redirectToPath } from '@/utils/router'

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  console.log('to.meta', to.meta, userStore.userInfo)
  if (to.meta.requiresAuth || to.meta.requiresGuest || to.meta.tryAuth) {
    const isAuthenticated = await userStore.validateToken()
    // 处理需要认证的路由
    if (to.meta.requiresAuth && !isAuthenticated) {
      const redirectPath = to.meta.redirectIfUnauthorized as string || '/landing'
      window.console.warn(`visited ${to.path}, but not authorized, redirect to ${redirectPath}`)
      redirectToPath(redirectPath, to, from, next, { addFromQuery: true, addSSORedirectQuery: true })
      return
    }
    // 处理仅限游客访问的路由
    if (to.meta.requiresGuest && isAuthenticated) {
      const redirectPath = to.meta.redirectIfUnauthorized as string || '/'
      window.console.warn(`visited ${to.path}, but already authorized, redirect to ${redirectPath}`)
      redirectToPath(redirectPath, to, from, next, { replaceRedirectFromQuery: true })
      return
    }
  }
  next()
})

