<template>
  <el-dropdown
    v-if="hasMultipleChildren"
    trigger="hover"
    :class="$style.menu"
    :popper-class="$style.menuPopper"
    @command="onCommand">
    <div
      :class="[$style.trigger, { [$style.selected]: item.selected }]"
      @click="onCommand(item.children![0].key)">
      <div class="text-[20px] flex items-center whitespace-nowrap">
        {{ item.label }}
      </div>
      <Icon icon="mdi:arrow-down" class="ml-1 text-[16px]"/>
    </div>

    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="child in item.children"
          :key="child.key"
          :command="child.key"
          :class="{ [$style.active]: isActive(child.key) }">
          <div class="w-full flex justify-center items-center">
            <div class="w-full text-center">{{ child.label }}</div>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>

  <div
    v-else
    :class="[$style.menu, { [$style.selected]: item.selected }]"
    class="h-full w-full p-[10px] cursor-pointer"
    @click="onClick(getSingleItemPath)">
    <span class="text-[20px]" style="color: #000000">
      {{ getSingleItemLabel }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router/auto'

interface MenuItem {
  key: string;
  label: string;
  selected: boolean;
  children?: Array<{
    key: string;
    label: string;
  }>;
}

const props = defineProps<{
  item: MenuItem;
}>()

const emit = defineEmits<{
  navigate: [path: string];
}>()

const route = useRoute()
const hasMultipleChildren = computed(() => 
  Boolean(props.item.children?.length && props.item.children?.length > 1)
)

const getSingleItemPath = computed(() => {
  if (props.item.children?.length === 1) {
    return props.item.children[0].key
  }
  return props.item.key
})

const getSingleItemLabel = computed(() => {
  if (props.item.children?.length === 1) {
    return props.item.children[0].label
  }
  return props.item.label
})

const isActive = (path: string) => {
  return route.path.includes(path)
}

const onClick = (path: string) => {
  emit('navigate', path)
}

const onCommand = (command: string) => {
  emit('navigate', command)
}
</script>

<style lang="scss" module>
.menu {
  width: fit-content;
}

.trigger {
  padding: 10px;
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  width: fit-content;
  min-width: fit-content;
}

.menuPopper {
  padding: 4px 0;
  min-width: fit-content !important;
  width: fit-content !important;

  :global(.el-dropdown-menu__item) {
    font-size: 14px;
    white-space: nowrap;
    padding: 8px 16px;
    box-sizing: border-box;
    margin-top: 4px !important;
    width: fit-content;

    > div {
      width: fit-content;
      min-width: 100%;
      display: flex;
      justify-content: center;
    }

    &:hover {
      > div {
        background-color: #f5f7fa;
        color: #4466bc;
      }
    }

    &.active {
      > div {
        color: #4466bc !important;
        font-weight: bold;
      }
      background-color: #f5f7fa !important;
    }
  }
}

.selected {
  border-bottom: 2px solid #4466bc;
}

.active {
  color: #4466bc !important;
  font-weight: bold;
  background-color: #f5f7fa !important;
}
</style>
