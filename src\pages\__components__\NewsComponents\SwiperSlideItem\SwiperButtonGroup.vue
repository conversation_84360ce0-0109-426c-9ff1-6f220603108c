<template>
  <div
    class="flex items-center gap-4 z-1000 w-full justify-end py-[5px] pr-[5px] swiper-controls"
    :class="$style.buttonGroup">
    <!-- Status Messages -->
    <span
      v-if="isPaused && !isManualControl"
      class="text-sm text-gray-600 animate-fade-in">
      {{ $t("intelligence.swiper.paused", { text: pausedText }) }}
    </span>
    <span
      v-if="remainingTime && remainingTime > 0"
      class="text-sm text-gray-600 animate-fade-in">
      {{ $t("intelligence.swiper.refreshRemainingTime", { time: formattedRemainingTime }) }}
    </span>

    <!-- Control Buttons -->
    <div tour-id="information-controls" class="flex items-center gap-4">
      <template v-for="button in controlButtons" :key="button.tooltip">
        <control-button
          v-if="button.show"
          :tooltip="$t(button.tooltip)"
          :icon="button.icon"
          :fill="button.fill"
          @click="button.action"/>
      </template>
    </div>


    <!-- Progress Circle -->
    <span
      v-if="!isPaused && !isManualControl && autoplayTimeLeft > 0"
      class="text-sm text-gray-600 flex items-center gap-2">
      <div class="relative inline-flex items-center justify-center">
        <el-progress
          type="circle"
          :percentage="Math.round(autoplayPercentage)"
          :width="24"
          :stroke-width="3"
          :show-text="false"
          :color="'#4466BC'"/>
        <span class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs leading-none">
          {{ Math.ceil(autoplayTimeLeft / 1000) }}
        </span>
      </div>
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import ControlButton from './ControlButton.vue'

interface Props {
  pausedText?: string;
  isPaused: boolean;
  isFullscreen: boolean;
  isHideChartSwiper: boolean;
  remainingTime?: number;
  autoplayTimeLeft: number;
  autoplayPercentage: number;
  isManualControl: boolean;
}

const { locale } = useI18n()
const props = defineProps<Props>()

const formattedRemainingTime = computed(() => {
  if (!props.remainingTime) {return}
  const seconds = Math.ceil(props.remainingTime / 1000)
  const minutes = Math.floor(seconds / 60)
  const minuteText = locale.value === 'chinese' ? '分钟' : 'min'
  const secondText = locale.value === 'chinese' ? '秒' : 's'
  return minutes >= 1 ? `${minutes}${minuteText}` : `${seconds}${secondText}`
})

const emit = defineEmits<{
  'toggle-fullscreen': [];
  'toggle-chart-swiper': [];
  refresh: [];
  'toggle-manual-control': [];
}>()

const controlButtons = computed(() => [
  {
    tooltip: 'intelligence.swiper.refresh',
    icon: 'mdi:refresh',
    fill: '#333',
    action: () => emit('refresh'),
    show: true
  },
  {
    tooltip: 'intelligence.swiper.toggleChartSwiper',
    icon: 'mdi:map-marker',
    fill: props.isHideChartSwiper ? '#333' : '#4466BC',
    action: () => emit('toggle-chart-swiper'),
    show: false
  },
  {
    tooltip: 'intelligence.swiper.fullscreen',
    icon: 'mdi:fullscreen',
    action: () => emit('toggle-fullscreen'),
    show: !props.isFullscreen
  },
  {
    tooltip: 'intelligence.swiper.exitFullscreen',
    icon: 'mdi:fullscreen-exit',
    action: () => emit('toggle-fullscreen'),
    show: props.isFullscreen
  },
  {
    tooltip: 'intelligence.swiper.togglePlay',
    icon: props.isManualControl ? 'mdi:play' : 'mdi:pause',
    fill: '#4466BC',
    action: () => emit('toggle-manual-control'),
    show: true
  }
])
</script>

<style lang="scss" module>
.buttonGroup {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 100%;
    height: 20px;
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.2);
  }
}
</style>
