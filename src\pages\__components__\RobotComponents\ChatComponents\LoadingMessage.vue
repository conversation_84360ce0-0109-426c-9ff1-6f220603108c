<template>
  <div class="flex items-start justify-start w-full h-full mr-auto ">
    <el-avatar :src="avatar" class="bg-white mr-[20px]" :size="60"></el-avatar>
    <el-skeleton :class="$style.content"
                 class="bg-[#fff] p-4 rounded-lg shadow-lg text-[16px] markdown-body prose w-full max-w-none"
                 :rows="5"
                 animated/>
  </div>
</template>
  
<script lang="ts" setup>
import { ElAvatar } from 'element-plus'

defineProps<{
  avatar: string;
}>()
</script>

<style lang="scss" module>
.loadingMessage {
  width: 100%;
  height: 100%;
}
.content {
  padding: 12px 12px 20px;
  width: 100%;
  max-width: none;
}
</style>

