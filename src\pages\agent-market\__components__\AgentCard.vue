<script setup lang="ts">
import { ref, onMounted, reactive, computed, watch, onUnmounted } from 'vue'
import GlowingStarsBackground from './GlowingStarsBackground.vue'
import Badge from '@/components/ui/badge/Badge.vue'

interface Props {
  id: string;
  description: string;
  disabled?: boolean;
  text?: string;
  className?: string;
  categoryTag?: string;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  text: '',
  className: '',
  categoryTag: 'N/A'
})

const emit = defineEmits(['click'])

// 从description中提取report_name (格式通常是"report_name: report_description")
const report_name = computed(() => {
  const colonIndex = props.description.indexOf(':')
  return colonIndex > 0 ? props.description.substring(0, colonIndex).trim() : ''
})

// 从description中提取真正的描述部分
const report_description = computed(() => {
  const colonIndex = props.description.indexOf(':')
  return colonIndex > 0 ? props.description.substring(colonIndex + 1).trim() : props.description
})

// 卡片中心显示的文本应该是report_name
const displayText = computed(() => report_name.value)

const mousePosition = reactive({ x: 0, y: 0 })
const randomString = ref('')
const cardRef = ref<HTMLElement | null>(null)
const isHovered = ref(false)

// Handle mouse movement
const onMouseMove = (event: MouseEvent) => {
  if (!cardRef.value) {
    return
  }
  
  const { left, top } = cardRef.value.getBoundingClientRect()
  mousePosition.x = event.clientX - left
  mousePosition.y = event.clientY - top
  
  // Generate new random string on mouse move
  randomString.value = generateRandomString(1500)
}

// Generate random string of characters
const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
const generateRandomString = (length: number) => {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}

// Compute mask image based on mouse position for hover effect
const maskImage = computed(() => {
  return `radial-gradient(250px at ${mousePosition.x}px ${mousePosition.y}px, white, transparent)`
})

const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

// Generate initial random string on mount
onMounted(() => {
  randomString.value = generateRandomString(1500)
})

const handleClick = () => {
  if (!props.disabled) {
    emit('click')
  }
}
</script>

<template>
  <div 
    class="border border-black/[0.2] dark:border-white/[0.2] flex flex-col items-start w-full mx-auto p-4 relative h-[400px] cursor-pointer transition-transform duration-200 hover:shadow-lg"
    :class="{ 'opacity-60 cursor-not-allowed': props.disabled }"
    @click="handleClick">
    <!-- Corner Icons -->
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      class="absolute h-6 w-6 -top-3 -left-3 dark:text-white text-black">
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6"/>
    </svg>
    
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      class="absolute h-6 w-6 -bottom-3 -left-3 dark:text-white text-black">
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6"/>
    </svg>
    
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      class="absolute h-6 w-6 -top-3 -right-3 dark:text-white text-black">
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6"/>
    </svg>
    
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      class="absolute h-6 w-6 -bottom-3 -right-3 dark:text-white text-black">
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m6-6H6"/>
    </svg>

    <!-- 主要内容区域，包含卡片的所有视觉效果 -->
    <div 
      class="group/card w-full h-[240px] relative overflow-hidden rounded-xl flex items-center justify-center"
      ref="cardRef"
      @mousemove="onMouseMove"
      @mouseenter="handleMouseEnter" 
      @mouseleave="handleMouseLeave">
      
      <!-- Card Pattern from card.vue -->
      <div class="pointer-events-none">
        <div class="absolute inset-0 rounded-xl [mask-image:linear-gradient(white,transparent)] group-hover/card:opacity-50 transition duration-500"></div>
        
        <!-- Gradient background with mask -->
        <div
          class="absolute inset-0 rounded-xl bg-gradient-to-r from-green-500 to-blue-700 opacity-0 group-hover/card:opacity-100 backdrop-blur-xl transition duration-500"
          :style="{
            maskImage: maskImage,
            WebkitMaskImage: maskImage
          }">
        </div>
        
        <!-- Text overlay with mask -->
        <div
          class="absolute inset-0 rounded-xl opacity-0 mix-blend-overlay group-hover/card:opacity-100 transition duration-500"
          :style="{
            maskImage: maskImage,
            WebkitMaskImage: maskImage
          }">
          <p class="absolute inset-x-0 text-xs h-full break-words whitespace-pre-wrap text-white font-mono font-bold">
            {{ randomString }}
          </p>
        </div>
      </div>
      
      <!-- 中心标题区 - 始终居中显示, 使用flex布局蒙层 -->
      <div class="relative z-10 flex items-center justify-center px-4">
        <div class="relative w-[280px] h-[160px] flex items-center justify-center"> <!-- Container for blur and text -->
          <!-- Blurred Background (not absolute, appears on hover) -->
          <div class="absolute inset-0 bg-white/[0.8] dark:bg-black/[0.8] blur-sm rounded-full opacity-0 group-hover/card:opacity-100 transition-opacity duration-300 ease-in-out"></div>
          <!-- Text (absolute, centered over the background) -->
          <div class="relative flex items-center justify-center p-4">
            <span class="dark:text-white text-black text-center text-lg font-medium leading-tight line-clamp-4 word-break-normal">
              {{ displayText }}
            </span>
          </div>
        </div>
      </div>
      <GlowingStarsBackground/>
    </div>
    
    <!-- 报告描述 -->
    <p class="dark:text-white/70 text-black/70 mt-3 text-sm font-light flex-grow overflow-auto w-full">
      {{ report_description }}
    </p>

    
    <!-- 状态标签 -->
    <div class="w-full flex justify-between mt-auto mb-1">
      <!-- Category Tag Badge -->
      <div class="w-full flex justify-start mt-2 mb-2 px-1">
        <Badge variant="outline" v-if="props.categoryTag && props.categoryTag !== 'N/A'">
          {{ props.categoryTag }}
        </Badge>
      </div>
      <div class="w-full flex justify-end mt-2 mb-2 px-1">
        <span 
          class="text-sm border font-light rounded-full px-3 py-1"
          :class="props.disabled ? 
            'dark:border-red-400/50 border-red-400/50 dark:text-red-400 text-red-500' : 
            'dark:border-green-400/50 border-green-400/50 dark:text-green-400 text-green-500'">
          {{ !props.disabled ? $t('agent.start') : $t('agent.loading') }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.word-break-normal {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 确保长英文单词能够正确换行 */
.text-center {
  text-align: center;
  word-spacing: normal;
}

/* 响应式字体大小调整 */
@media (max-width: 640px) {
  .text-lg {
    font-size: 1rem;
    line-height: 1.4;
  }
}
</style>
