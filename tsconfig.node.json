{"extends": "@tsconfig/node20/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "vite-plugins/**/*", "build.region.env.ts", "src/plugins/**/*", "./vite-plugins/*.ts", "*.d.ts", "src/vue-plugins/cdnFallback.ts", "src/vue-plugins/vite-plugin-cdn2.ts", "src/vue-plugins/cdnFallback.ts"], "compilerOptions": {"allowSyntheticDefaultImports": true, "composite": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "preserveValueImports": false}}