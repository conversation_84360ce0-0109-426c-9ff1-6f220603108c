import dayjs from 'dayjs'
/**
 * 日历格式化
 * @param date
 * @param formats Object @see https://dayjs.fenxianglu.cn/category/plugin.html#%E6%97%A5%E5%8E%86
 * [yesterday] [today] [tomorrow]
 * @returns {string|string}
 */
export function formatCalendarDate(date:number, formats = {}) {
  // #if BUILD_REG == 'INTL'
  formats = { sameDay: '[today]', nextDay: '[tomorrow]', lastDay: '[yesterday]', nextWeek: 'MMM D', lastWeek: 'MMM D', sameYear: 'MMM D', sameElse: 'MMM D, YYYY', ...formats }
  // #else
  formats = { sameDay: '[today]', nextDay: '[tomorrow]', lastDay: '[yesterday]', nextWeek: 'MM-DD', lastWeek: 'MM-DD', sameYear: 'MM-DD', sameElse: 'YYYY-MM-DD', ...formats }
  // #endif
  
  // 使用本地时区处理时间
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  return dayjs(date).tz(timezone).isValid() ? dayjs(date).tz(timezone).calendar(undefined, formats) : ''
}

/**
 * 格式化日历日期时间：时分秒
 * @param date
 * @param showSecond 是否显示秒,默认false
 * @param formats Object
 * ([yesterday] | [today] | [tomorrow]) HH:mm
 */
export function formatCalendarDateTime(date: number, { showSecond = false, formats }: {showSecond?:boolean, formats?:any} = {}) {
  const timeFormat = showSecond ? 'HH:mm:ss' : 'HH:mm'
  // #if BUILD_REG == 'INTL'
  formats = { sameDay: `[today] ${timeFormat}`, nextDay: `[tomorrow] ${timeFormat}`, lastDay: `[yesterday] ${timeFormat}`, nextWeek: `MMM D ${timeFormat}`, lastWeek: `MMM D ${timeFormat}`, sameYear: `MMM D ${timeFormat}`, sameElse: `MMM D, YYYY ${timeFormat}`, ...formats }
  // #else
  formats = { sameDay: `[today] ${timeFormat}`, nextDay: `[tomorrow] ${timeFormat}`, lastDay: `[yesterday] ${timeFormat}`, nextWeek: `MM-DD ${timeFormat}`, lastWeek: `MM-DD ${timeFormat}`, sameYear: `MM-DD ${timeFormat}`, sameElse: `YYYY-MM-DD ${timeFormat}`, ...formats }
  // #endif
  return formatCalendarDate(date, formats)
}
