<template>
  <div class="flex justify-center items-center h-full w-full bg-gray-50 p-5">
    <div class="max-w-3xl w-full mx-auto py-6 px-4 sm:px-6 lg:px-8 bg-white rounded-lg shadow-xs flex flex-col h-full" v-loading="fieldsLoading || isLoading || isSubmitting">
      <!-- 标题和快捷键提示 -->
      <div class="mb-6 text-center">
        <h1 class="text-2xl font-bold text-gray-900">{{ $t('poll.title') }}</h1>
        <p class="text-sm text-gray-500 mt-2">{{ $t('poll.escHint') }}</p>
      </div>

      <!-- 一屏展示所有问题 - 带滚动容器 (自适应高度) -->
      <div class="space-y-8 overflow-y-auto pr-2 py-2 flex-1">
        <!-- 问题1: 行业选择 -->
        <div class="form-section" ref="industrySection">
          <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $t('poll.sector') }}</h2>
          <div class="mb-5">
            <input
              ref="industryInput"
              type="text"
              v-model="industryValue"
              class="w-full h-12 px-4 border border-gray-300 rounded-lg focus:outline-hidden"
              :placeholder="$t('poll.sectorPlaceholder')">
          </div>
          <div class="flex flex-wrap gap-2 mb-6">
            <button
              v-for="(option, index) in profileFields.industry"
              :key="`industry-${index}`"
              class="px-4 py-2 bg-white border rounded-full text-sm font-medium transition-colors duration-200"
              :class="industrySelected.includes(option) ? 'border-blue-600 bg-blue-50 text-blue-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
              @click="handleIndustryPick(option)">
              {{ option }}
            </button>
          </div>
        </div>

        <!-- 问题2: 区域选择 -->
        <div class="form-section" ref="areaSection">
          <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $t('poll.region') }}</h2>
          <div class="mb-5">
            <input
              ref="areaInput"
              type="text"
              v-model="areaValue"
              class="w-full h-12 px-4 border border-gray-300 rounded-lg focus:outline-hidden"
              :placeholder="$t('poll.regionPlaceholder')">
          </div>
          <div class="flex flex-wrap gap-2 mb-6">
            <button
              v-for="(option, index) in profileFields.area"
              :key="`area-${index}`"
              class="px-4 py-2 bg-white border rounded-full text-sm font-medium transition-colors duration-200"
              :class="areaSelected.includes(option) ? 'border-blue-600 bg-blue-50 text-blue-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
              @click="handleAreaPick(option)">
              {{ option }}
            </button>
          </div>
        </div>

        <!-- 问题3: 风格选择 -->
        <div class="form-section" ref="styleSection">
          <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $t('poll.style') }}</h2>
          <div class="mb-5">
            <input
              ref="styleInput"
              type="text"
              v-model="styleValue"
              class="w-full h-12 px-4 border border-gray-300 rounded-lg focus:outline-hidden"
              :placeholder="$t('poll.stylePlaceholder')">
          </div>
          <div class="flex flex-wrap gap-2 mb-6">
            <button
              v-for="(option, index) in profileFields.style"
              :key="`style-${index}`"
              class="px-4 py-2 bg-white border rounded-full text-sm font-medium transition-colors duration-200"
              :class="styleSelected.includes(option) ? 'border-blue-600 bg-blue-50 text-blue-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
              @click="handleStylePick(option)">
              {{ option }}
            </button>
          </div>
        </div>

        <!-- 问题4: 海外选择 -->
        <div class="form-section" ref="overseasSection">
          <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $t('poll.overseas') }}</h2>
          <div class="mb-5">
            <input
              ref="overseasInput"
              type="text"
              v-model="overseasValue"
              class="w-full h-12 px-4 border border-gray-300 rounded-lg focus:outline-hidden"
              :placeholder="$t('poll.overseasPlaceholder')">
          </div>
          <div class="flex flex-wrap gap-2 mb-6">
            <button
              v-for="(option, index) in profileFields.overseas"
              :key="`overseas-${index}`"
              class="px-4 py-2 bg-white border rounded-full text-sm font-medium transition-colors duration-200"
              :class="overseasSelected.includes(option) ? 'border-blue-600 bg-blue-50 text-blue-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
              @click="handleOverseasPick(option)">
              {{ option }}
            </button>
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="mt-8 flex justify-center space-x-4">
        <button
          class="px-5 py-2 bg-white border border-gray-300 rounded-md text-gray-700 font-medium hover:bg-gray-50 transition-colors duration-200"
          @click="handleSubmit(true)"
          :disabled="isSubmitting">
          {{ $t('step.skip') }}
        </button>
        <button
          class="px-5 py-2 bg-blue-600 rounded-md text-white font-medium hover:bg-blue-700 transition-colors duration-200"
          @click="handleSubmit(false)"
          :disabled="isSubmitDisabled || isSubmitting">
          {{ $t('poll.start') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, toRaw, nextTick } from 'vue'
import router from '@/router'
import { usePollStepStore } from './store'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { onKeyStroke } from '@vueuse/core'
import { definePage } from 'vue-router/auto'

definePage({
  meta: {
    layout:false,
    requiresAuth: true,
  },
})
const { t } = useI18n()
const store = usePollStepStore()
const isSubmitting = ref(false)
const {
  profileFields,
  stepOnePostData,
  stepTwoPostData,
  stepThreePostData,
  stepFourPostData,
  isLoading,
  fieldsLoading
} = storeToRefs(store)

// 问题区域的引用
const industrySection = ref<HTMLElement | null>(null)
const areaSection = ref<HTMLElement | null>(null)
const styleSection = ref<HTMLElement | null>(null)
const overseasSection = ref<HTMLElement | null>(null)

// 行业选择
const industrySelected = ref<string[]>([])
const industryValue = ref<string>('')
const industryInput = ref<HTMLInputElement | null>(null)

// 区域选择
const areaSelected = ref<string[]>([])
const areaValue = ref<string>('')
const areaInput = ref<HTMLInputElement | null>(null)

// 风格选择
const styleSelected = ref<string[]>([])
const styleValue = ref<string>('')
const styleInput = ref<HTMLInputElement | null>(null)

// 海外选择
const overseasSelected = ref<string[]>([])
const overseasValue = ref<string>('')
const overseasInput = ref<HTMLInputElement | null>(null)

// 处理选择
const handleIndustryPick = (option: string) => {
  if (industrySelected.value.includes(option)) {
    industrySelected.value = industrySelected.value.filter(item => item !== option)
  } else {
    industrySelected.value.push(option)
  }
}

const handleAreaPick = (option: string) => {
  if (areaSelected.value.includes(option)) {
    areaSelected.value = areaSelected.value.filter(item => item !== option)
  } else {
    areaSelected.value.push(option)
  }
}

const handleStylePick = (option: string) => {
  if (styleSelected.value.includes(option)) {
    styleSelected.value = styleSelected.value.filter(item => item !== option)
  } else {
    styleSelected.value.push(option)
  }
}

const handleOverseasPick = (option: string) => {
  if (overseasSelected.value.includes(option)) {
    overseasSelected.value = overseasSelected.value.filter(item => item !== option)
  } else {
    overseasSelected.value.push(option)
  }
}

// 检查当前步骤是否为空
const isStepEmpty = (step: number) => {
  switch (step) {
  case 0:
    return industrySelected.value.length === 0 && !industryValue.value
  case 1:
    return areaSelected.value.length === 0 && !areaValue.value
  case 2:
    return styleSelected.value.length === 0 && !styleValue.value
  case 3:
    return overseasSelected.value.length === 0 && !overseasValue.value
  default:
    return false
  }
}

// 使用 VueUse 的 onKeyStroke 处理全局 Enter 和 Escape 键
// 传入 window 参数使其在全局捕获键盘事件
onKeyStroke('Enter', (e) => {
  // 如果焦点在输入框（例如用户正在输入文本），不触发校验
  // 但如果用户按下了修饰键（如 Ctrl+Enter），仍然触发校验
  const isModifierKey = e.ctrlKey || e.metaKey || e.altKey || e.shiftKey
  const isInputElement = e.target instanceof HTMLInputElement && 
                         document.activeElement === e.target && 
                         !isModifierKey
                         
  if (!isInputElement) {
    e.preventDefault()
    validateAndScroll()
  }
}, { target: window })

// 逃脱键也在全局捕获
onKeyStroke('Escape', () => {
  handleSubmit(true)
}, { target: window })

// 验证并滚动到第一个空问题
const validateAndScroll = () => {
  // 寻找第一个未回答的问题
  const emptyStepIndex = [0, 1, 2, 3].find(step => isStepEmpty(step))
  if (emptyStepIndex !== undefined) {
    // 获取对应的问题区域引用
    const sectionRefs = [industrySection, areaSection, styleSection, overseasSection]
    const sectionEl = sectionRefs[emptyStepIndex].value
    
    if (sectionEl) {
      // 滚动到问题位置
      sectionEl.scrollIntoView({ behavior: 'smooth' })
      
      // // 根据问题索引显示不同的提示信息
      // const questionMessages = [
      //   t('poll.pleaseAnswerIndustry') || '请回答行业问题',
      //   t('poll.pleaseAnswerArea') || '请回答区域问题',
      //   t('poll.pleaseAnswerStyle') || '请回答风格问题', 
      //   t('poll.pleaseAnswerOverseas') || '请回答海外问题'
      // ]
      
      ElMessage.warning(t('poll.pollValidation'))
    }
  } else {
    // 所有问题都已填写，提交表单
    handleSubmit(false)
  }
}

const isSubmitDisabled = computed(() => {
  return (
    (industrySelected.value.length === 0 && !industryValue.value) ||
    (areaSelected.value.length === 0 && !areaValue.value) ||
    (styleSelected.value.length === 0 && !styleValue.value) ||
    (overseasSelected.value.length === 0 && !overseasValue.value)
  )
})

// 提交所有问卷数据
const handleSubmit = async (isSkip = false) => {
  if (isSubmitDisabled.value && !isSkip) {
    ElMessage.warning(t('poll.pleaseAnswer'))
    return
  }
  
  isSubmitting.value = true
  
  try {
    // 准备数据（即使是空的也提交）
    stepOnePostData.value.user_profile.industry = industrySelected.value
    stepOnePostData.value.user_profile.industry_value = industryValue.value
    
    stepTwoPostData.value.user_profile.area = areaSelected.value
    stepTwoPostData.value.user_profile.area_value = areaValue.value
    
    stepThreePostData.value.user_profile.style = styleSelected.value
    stepThreePostData.value.user_profile.style_value = styleValue.value
    
    stepFourPostData.value.user_profile.overseas = overseasSelected.value
    stepFourPostData.value.user_profile.overseas_value = overseasValue.value
    
    // 合并所有数据
    const combinedUserProfile = {
      step: 5, // 直接设为5表示完成
      user_profile: {
        industry: industrySelected.value,
        industry_value: industryValue.value,
        area: areaSelected.value,
        area_value: areaValue.value,
        style: styleSelected.value,
        style_value: styleValue.value,
        overseas: overseasSelected.value,
        overseas_value: overseasValue.value
      }
    }
    
    // 提交数据
    await store.submitRefreshUserAccount(toRaw(combinedUserProfile))
    
    // 成功提示
    
    // 完成后跳转
    setTimeout(() => {
      router.push('/agent_market')
    }, 800)
  } catch (error) {
    console.error('Failed to submit profile data:', error)
    ElMessage.error(t('poll.submitError'))
  } finally {
    isSubmitting.value = false
  }
}

onMounted(async () => {
  await store.getFields()
  
  // 初始化聚焦第一个输入框
  nextTick(() => {
    if (industryInput.value) {
      industryInput.value.focus()
    }
  })
})
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.-z-0 {
  z-index: 0;
}

.-z-1 {
  z-index: -1;
}

.form-section {
  animation: fadeIn 0.3s ease-in-out;
  scroll-margin-top: 1rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条样式 */
.space-y-8::-webkit-scrollbar {
  width: 5px;
}

.space-y-8::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.space-y-8::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 10px;
}

.space-y-8::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* 禁用浏览器默认focus样式 */
input:focus {
  outline: none;
  box-shadow: none;
}
</style>
