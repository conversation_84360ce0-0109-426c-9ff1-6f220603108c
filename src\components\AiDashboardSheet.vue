<template>
  <Sheet v-model:open="isOpen">
    <SheetContent side="right" class="w-full sm:max-w-4xl p-0 bg-white">
      <SheetHeader class="p-4 border-b border-slate-200">
        <SheetTitle class="flex items-center gap-2">
          <Icon icon="mdi:brain" class="w-5 h-5 text-blue-600"/>
          <span>AI 深度分析</span>
          <Badge v-if="projectTitle" variant="outline" class="ml-2 max-w-xs truncate">
            {{ projectTitle }}
          </Badge>
        </SheetTitle>
        <SheetDescription v-if="projectTitle" class="text-sm text-slate-600">
          正在为「{{ projectTitle }}」进行智能分析处理
        </SheetDescription>
      </SheetHeader>
  
      <!-- 使用 IframeWrapper 统一处理iframe逻辑 -->
      <IframeWrapper
        v-if="isOpen && iframeSrc"
        :src="iframeSrc"
        :token="authToken"
        title="AI Dashboard"
        :height="sheetHeight"
        :min-height="sheetHeight"
        :max-height="sheetHeight"
        :auto-height="false"
        :enable-communication="true"
        :debug="isDevelopment"
        :sandbox="sandboxPermissions"
        :allowed-origins="allowedOrigins"
        :retry-attempts="3"
        :load-timeout="30000"
        wrapper-class="flex-1 h-full"
        container-class="h-full w-full"
        loading-title="正在加载 AI Dashboard"
        loading-icon="mdi:dashboard"
        loading-hint="💡 等待iframe身份验证完成后才会显示内容"
        :show-loading-url="true"
        :show-cancel-button="true"
        error-title="AI Dashboard 加载失败"
        :show-error-details="true"
        :error-details="errorDetails"
        :show-retry-button="true"
        :show-go-back-button="true"
        :show-close-button="true"
        :messages-config="iframeMessagesConfig"
        @loaded="handleIframeLoaded"
        @error="handleIframeError"
        @message="handleIframeMessage"
        @retry="handleIframeRetry"
        @cancel="handleCancelLoading"
        @close="handleClosePage"
        @go-back="handleGoBack"/>
    </SheetContent>
  </Sheet>
</template>
  
<script setup lang="ts">
import { computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { Badge } from '@/components/ui/badge'
import { 
  Sheet, 
  SheetContent, 
  SheetHeader, 
  SheetTitle, 
  SheetDescription 
} from '@/components/ui/sheet'
import IframeWrapper from '@/components/IframeWrapper.vue'
import { getToken } from '@/hooks/useToken'
import { useWindowSize } from '@vueuse/core'
import { AI_DASHBOARD_CONFIG, type IframeMessage } from '@/composables/useIframeMessages'
import { adaptIframeError, getErrorMessage, isDetailedError, type IframeError } from '@/utils/iframeErrorAdapter'
  
// Props 定义
interface Props {
  open: boolean
  projectId?: string
  projectTitle?: string
}
  
const props = withDefaults(defineProps<Props>(), {
  open: false,
  projectId: '',
  projectTitle: ''
})
  
// Emits 定义
const emit = defineEmits<{
    (e: 'update:open', value: boolean): void
    (e: 'message', message: IframeMessage): void
  }>()
  
// Composables
const { height: windowHeight } = useWindowSize()
  
// 计算属性
const isOpen = computed({
  get: () => props.open,
  set: (value: boolean) => emit('update:open', value)
})
  
const sheetHeight = computed(() => windowHeight.value - 100) // 减去顶部空间
  
const currentEnv = computed(() => import.meta.env.VITE_APP_ENV || 'development')
const isDevelopment = computed(() => import.meta.env.DEV)
const authToken = computed(() => getToken())
  
// iframe消息处理配置
const iframeMessagesConfig = computed(() => ({
  ...AI_DASHBOARD_CONFIG,
  handlers: {
    ...AI_DASHBOARD_CONFIG.handlers,
    onSuccess: async (message: IframeMessage) => {
      console.log('✅ AiDashboardSheet: 收到成功消息', message)
      emit('message', message)
    },
    onError: async (message: IframeMessage) => {
      console.error('🚨 AiDashboardSheet: 收到错误消息', message)
      emit('message', message)
    }
  }
}))
  
// 错误详情
const errorDetails = computed(() => {
  return [
    `项目ID: ${props.projectId || '未提供'}`,
    `环境: ${currentEnv.value}`,
    `时间: ${new Date().toLocaleString()}`
  ].join('\n')
})
  
// 根据环境构建URL
const baseUrl = computed(() => {
  const env = currentEnv.value.toLowerCase()
    
  // 环境URL映射
  const envUrls: Record<string, string> = {
    development: 'http://localhost:3000',
    dev: 'https://dev.goglobalsp.com',
    test: 'https://test.goglobalsp.com', 
    production: 'https://www.goglobalsp.com',
    prod: 'https://www.goglobalsp.com'
  }
    
  return envUrls[env] || envUrls.development
})
  
const iframeSrc = computed(() => {
  if (!isOpen.value || !props.projectId) {return ''}
    
  try {
    const url = new URL('/embed/dashboard', baseUrl.value)
      
    // 添加认证token
    if (authToken.value) {
      url.searchParams.set('token', authToken.value)
    }
      
    // 添加项目信息
    url.searchParams.set('projectId', props.projectId)
      
    if (props.projectTitle) {
      url.searchParams.set('title', props.projectTitle)
    }
      
    // 添加环境标识
    url.searchParams.set('env', currentEnv.value)
      
    // 添加来源标识
    url.searchParams.set('source', 'sheet-view')
      
    // 添加时间戳防止缓存
    url.searchParams.set('t', Date.now().toString())
      
    console.log('🔗 AiDashboardSheet: Generated iframe URL:', url.toString())
    return url.toString()
  } catch (error) {
    console.error('🔗 AiDashboardSheet: Error generating iframe URL:', error)
    return ''
  }
})
  
// 安全配置
const sandboxPermissions = computed(() => [
  'allow-scripts',
  'allow-same-origin',
  'allow-forms',
  'allow-popups',
  'allow-orientation-lock',
  'allow-pointer-lock',
  'allow-presentation'
])
  
const allowedOrigins = computed(() => {
  const origins = [
    'http://localhost:3000',
    'https://dev.goglobalsp.com',
    'https://test.goglobalsp.com',
    'https://www.goglobalsp.com'
  ]
    
  // 确保当前环境的URL在白名单中
  const currentOrigin = new URL(baseUrl.value).origin
  if (!origins.includes(currentOrigin)) {
    origins.push(currentOrigin)
  }
    
  return origins
})
  
// 事件处理
const handleIframeLoaded = () => {
  console.log('✅ AiDashboardSheet: Iframe 加载完成')
}
  
const handleIframeError = (error: IframeError | string) => {
  const errorObject = adaptIframeError(error)
  console.error('🚨 AiDashboardSheet: Iframe 错误', errorObject)
  
  // 根据错误类型进行特殊处理
  if (isDetailedError(error)) {
    switch (error.type) {
    case 'AUTH_ERROR':
      console.error('🔐 认证错误，可能需要重新登录')
      break
    case 'NETWORK_ERROR':
      console.error('🌐 网络连接错误，请检查网络状态')
      break
    case 'TIMEOUT_ERROR':
      console.error('⏰ 请求超时，服务器响应缓慢')
      break
    }
  }
}
  
const handleIframeMessage = (message: IframeMessage) => {
  console.log('📨 AiDashboardSheet: 收到iframe消息', message)
  emit('message', message)
}
  
const handleIframeRetry = () => {
  console.log('🔄 AiDashboardSheet: 重试加载')
}
  
const handleCancelLoading = () => {
  console.log('❌ AiDashboardSheet: 取消加载')
  isOpen.value = false
}
  
const handleGoBack = () => {
  console.log('⬅️ AiDashboardSheet: 返回上一页')
  if (window.history.length > 1) {
    window.history.back()
  } else {
    isOpen.value = false
  }
}
  
const handleClosePage = () => {
  console.log('🚪 AiDashboardSheet: 关闭面板')
  isOpen.value = false
}
  
// 监听状态变化
watch(isOpen, (newOpen) => {
  console.log('🚀 AiDashboardSheet: isOpen changed:', { 
    newOpen, 
    projectId: props.projectId, 
    projectTitle: props.projectTitle 
  })
}, { immediate: true })
  
watch(() => props.projectId, (newProjectId, oldProjectId) => {
  console.log('🎯 AiDashboardSheet: Project ID changed:', { 
    newProjectId, 
    oldProjectId,
    projectTitle: props.projectTitle,
    isOpen: isOpen.value 
  })
}, { immediate: true })
</script>
  
  <style lang="scss" scoped>
  /* 确保 Sheet 内容全高度 */
  :deep(.sheet-content) {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .debug-info {
      max-width: 200px;
      font-size: 0.625rem;
    }
  }
  </style> 