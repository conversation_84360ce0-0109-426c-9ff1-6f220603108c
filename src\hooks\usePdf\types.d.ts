import type { Style, TDocumentDefinitions, PageSize, Margins } from 'pdfmake/interfaces'
import type { ChartImageOptions, ProcessedImage } from '../useChartImage/types'

// Header 配置接口
interface HeaderConfig {
  left?: string;
  center?: string;
  right?: string;
}

// Footer 配置接口
interface FooterConfig {
  copyright?: string;
  confidential?: string;
}

export interface PdfOptions {
  pageSize?: PageSize
  pageMargins?: Margins | number[]
  pageOrientation?: 'portrait' | 'landscape'
  styles?: StyleDictionary
  headerConfig: HeaderConfig
  footerConfig: FooterConfig
  titleSection: TitleConfig // 添加 titleSection 到 PdfOptions
  imageOptions?: ChartImageOptions
}
export interface FontConfig {
  normal: string
  bold: string
  italics: string
  bolditalics: string
}

export interface Fonts {
  [key: string]: FontConfig
}

export interface FontCacheData {
  version: string
  fonts: any
} 
export interface TitleConfig {
  title?: string;
  subtitle: string;
  contact: {
    company: string;
    website: string;
    email: string;
  };
}

// 新增内容类型定义
export interface ContentItem {
  text?: string;
  style?: string;
  stack?: ContentItem[];
  ul?: ContentItem[];
  ol?: ContentItem[];
  className?: string;
  [key: string]: any;
}

export interface ProcessedItem extends ContentItem {
  unbreakable: boolean;
  tocItem?: boolean;
  id?: string;
}