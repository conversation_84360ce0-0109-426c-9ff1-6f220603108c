import qs from 'qs'
import { addUrlParams, isWebUrl } from './index'
import { type LocationQueryValueRaw, type NavigationGuardNext, type RouteLocationNormalized, type RouteRecordRaw, type Router } from 'vue-router/auto'
import { SSO_REDIRECT_KEY } from '@/const'
import { omit } from 'lodash-es'

export type RedirectOption = {
  addSSORedirectQuery?: boolean
  addFromQuery?: boolean
  replaceRedirectFromQuery?: boolean
  redirectPath?: string
}
export type IntoSystemOption = {
  url?: string
  query?: Record<string, LocationQueryValueRaw>
}
export const redirectTo = (to: RouteLocationNormalized, from: RouteLocationNormalized, options: RedirectOption = {}) => {
  const query = {
    ...to.query,
  } as Record<string, LocationQueryValueRaw>
  let redirectPath = options.redirectPath || to.path
  if (options.addFromQuery) {
    if (from.path !== '/') {
      query.from = from.path
    }
    const fromQuery = qs.stringify(omit(from.query, [SSO_REDIRECT_KEY, 'from', 'fromQuery']))
    if (fromQuery) {
      query.fromQuery = fromQuery
    }
  }
  if (options.addSSORedirectQuery && to.path !== '/') {
    query[SSO_REDIRECT_KEY] = to.path
  }

  if (options.replaceRedirectFromQuery && typeof query[SSO_REDIRECT_KEY] === 'string' && query[SSO_REDIRECT_KEY]) {
    redirectPath = query[SSO_REDIRECT_KEY]
    Reflect.deleteProperty(query, SSO_REDIRECT_KEY)
  }

  if (isWebUrl(redirectPath)) {
    window.location.href = addUrlParams(redirectPath, query)
    return null
  }
  return {
    path: redirectPath,
    query
  }
}
export function redirectToPath(redirectPath: string, to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext, options: RedirectOption = {}) {
  const result = redirectTo(to, from, {
    redirectPath,
    ...options,
  })
  if (!result) {
    next(false)
    return
  }

  next(result)
}

export const intoSystem = (router: Router, options: IntoSystemOption = {}) => {
  const currentRoute = router.currentRoute.value
  const url = options.url || currentRoute.query[SSO_REDIRECT_KEY] as string || '/'
  const query = omit({
    ...currentRoute.query,
    ...options.query,
  }, [SSO_REDIRECT_KEY])
  if (isWebUrl(url)) {
    window.location.href = addUrlParams(url, query)
    return
  }
  return router.push({
    path: url,
    query
  })
}

// 修复目录下没有index.vue的情况(父级则不是路由)
export const fixRouteIndex = (route: RouteRecordRaw): RouteRecordRaw => {
  if (route.children?.[0].path === '' && route.children[0].children?.length && route.component) {
    return {
      ...omit(route, 'component'),
      children: route.children?.[0].children
    }
  }
  return route
}
