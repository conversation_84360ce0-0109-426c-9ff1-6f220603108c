export default {
  zh: {
    tour: {
      information: {
        news: {
          title: '新闻轮播区',
          desc: '这里展示最新的新闻信息'
        },
        controls: {
          title: '控制按钮',
          desc: '这里可以控制新闻轮播的播放'
        },
        list: {
          title: '新闻列表',
          desc: '这里展示所有新闻列表'
        }
      },
      daily_report: {
        project_list: {
          title: '项目列表',
          desc: '用户关注的所有项目列表'
        },
        detail: {
          title: '项目详情',
          desc: '对应项目下的AI总结主题详情'
        },
        news_list: {
          title: '新闻列表',
          desc: '用户关注对应项目相关的新闻列表'
        }
      },
      format_report: {
        list: {
          title: '制式报告列表',
          desc: '用户创建的制式报告列表'
        },
        floating_ball: {
          title: '任务列表',
          desc: '用户创建的制式报告任务列表'
        },
        template: {
          title: '制式报告模板',
          desc: '用户创建的制式报告模板列表'
        }
      }
    }
  },
  en: {
    tour: {
      information: {
        news: {
          title: 'News Carousel',
          desc: 'Display the latest news information here'
        },
        controls: {
          title: 'Control Buttons',
          desc: 'Control the news carousel playback here'
        },
        list: {
          title: 'News List',
          desc: 'Display all news list here'
        }
      },
      daily_report: {
        project_list: {
          title: 'Project List',
          desc: 'List of all projects followed by users'
        },
        detail: {
          title: 'Project Details',
          desc: 'AI summary theme details under the corresponding project'
        },
        news_list: {
          title: 'News List',
          desc: 'News list related to the project followed by users'
        }
      },
      format_report: {
        list: {
          title: 'User Report List',
          desc: 'User created report list'
        },
        floating_ball: {
          title: 'Task List',
          desc: 'User created report task list'
        },
        template: {
          title: 'Report Template',
          desc: 'User created report template list'
        }
      }
    }
  },
  ja: {
    tour: {
      information: {
        news: {
          title: 'ニュースカルーセル',
          desc: '最新のニュース情報を表示する'
        },
        controls: {
          title: 'コントロールボタン',
          desc: 'ニュースカルーセルの再生を制御する'
        },
        list: {
          title: 'ニュースリスト',
          desc: 'すべてのニュースリストを表示する'
        }
      },
      daily_report: {
        project_list: {
          title: 'プロジェクトリスト',
          desc: 'ユーザーが追跡しているすべてのプロジェクトリスト'
        },
        detail: {
          title: 'プロジェクト詳細',
          desc: '対応プロジェクトのAIサマリーテーマ詳細'
        },
        news_list: {
          title: 'ニュースリスト',
          desc: 'ユーザーが追跡しているプロジェクトに関連するニュースリスト'
        }
      },
      format_report: {
        list: {
          title: 'ユーザー報告リスト',
          desc: 'ユーザーが作成した報告リスト'
        },
        floating_ball: {
          title: 'タスクリスト',
          desc: 'ユーザーが作成した報告タスクリスト'
        },
        template: {
          title: '報告テンプレート',
          desc: 'ユーザーが作成した報告テンプレートリスト'
        }
      }
    }
  }
} 