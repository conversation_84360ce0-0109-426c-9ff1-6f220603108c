<template>
  <el-avatar class="mr-[10px] cursor-pointer" :shape="'square'" style="background-color: var(--primary-color);font-weight: 700;">{{ avatarName }}</el-avatar>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const { userInfo, avatarName, userId } = storeToRefs(userStore)
</script>

<style lang="scss" module>
 .container {
 }
</style>
