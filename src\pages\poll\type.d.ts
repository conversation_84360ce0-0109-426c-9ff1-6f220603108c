export interface ProfileFields {
  industry: string[];
  area: string[];
  style: string[];
  overseas: string[];
}

export interface PostData {
  step?: number;
  user_profile: Userprofile;
}

interface Userprofile {
  industry?: string[];
  industry_value?: string;
  area?: string[];
  area_value?: string;
  style?: string[];
  style_value?: string;
  overseas?: string[];
  overseas_value?: string;
}
export interface Recommend {
  created_at: string;
  updated_at: string;
  description: string;
  industry: string;
  industry_cn: string;
  nation: string;
  nation_cn: string;
  status: string;
  others: Others;
  id: string;
}

interface Others {
}