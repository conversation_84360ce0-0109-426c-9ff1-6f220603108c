<template>
  <div class="relative flex-1 min-h-0">
    <!-- Loading 状态 -->
    <div v-if="detailLoadingState === 'loading'" class="h-full flex flex-col">
      <!-- 保持顶部导航结构 -->
      <div class="flex-shrink-0 p-6 border-b">
        <div class="flex items-center justify-between w-full mb-4">
          <div class="flex items-center gap-3 flex-1 min-w-0">
            <Button variant="ghost"
                    size="sm"
                    @click="handleCloseDetail"
                    class="h-8 w-8 p-0 flex-shrink-0">
              <ArrowLeftIcon class="h-4 w-4"/>
            </Button>
            
            <h2 class="text-xl font-bold flex-1 min-w-0 truncate">{{ selectedItemBasicInfo?.title || t('strategic.loading_detail') }}</h2>
          </div>
          
          <!-- 右侧导航控件 - 在加载时也显示 -->
          <div v-if="allItems && allItems.length > 1" class="flex items-center gap-1 bg-white rounded-lg border border-gray-200 shadow-sm p-1 flex-shrink-0">
            <!-- 上一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigatePrevious"
              :disabled="currentIndex === 0"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronLeftIcon class="h-3.5 w-3.5"/>
            </Button>
            
            <!-- 列表选择器带icon -->
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" class="h-7 px-2 text-xs gap-1 hover:bg-gray-100">
                  <ListIcon class="h-3.5 w-3.5"/>
                  <span>{{ (currentIndex ?? 0) + 1 }}/{{ allItems.length }}</span>
                  <ChevronDownIcon class="h-3 w-3"/>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-80 max-h-60 overflow-y-auto" align="end">
                <div class="p-2">
                  <div class="flex items-center gap-2 text-xs font-medium text-gray-500 mb-2">
                    <ListIcon class="h-3.5 w-3.5"/>
                    <span>{{ t('strategic.navigation.quick_nav_title', { count: allItems.length }) }}</span>
                  </div>
                  <div class="space-y-1">
                    <div 
                      v-for="(item, index) in allItems" 
                      :key="item.id"
                      @click="handleNavigateToItem(index)"
                      :class="[
                        'flex items-center justify-between p-2 rounded-md cursor-pointer text-xs hover:bg-gray-50 transition-colors',
                        currentIndex === index ? 'bg-blue-50 border border-blue-200' : ''
                      ]">
                      <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">
                          {{ getItemTitle(item) }}
                        </div>
                        <div class="text-gray-500 mt-0.5 flex items-center gap-1">
                          <MapPinIcon class="h-3 w-3"/>
                          {{ item.country || '-' }}
                        </div>
                      </div>
                      <div class="flex-shrink-0 ml-2">
                        <Badge v-if="item.news_type" variant="outline" class="text-xs">
                          {{ item.news_type }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <!-- 下一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigateNext"
              :disabled="currentIndex === (allItems.length - 1)"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronRightIcon class="h-3.5 w-3.5"/>
            </Button>
          </div>
        </div>
      </div>
      
      <!-- 加载内容区域 -->
      <div class="flex-1 flex flex-col items-center justify-center p-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
        <h3 class="text-lg font-medium text-slate-700 mb-2">{{ t('strategic.loading_detail') }}</h3>
        <p class="text-sm text-slate-500 text-center max-w-md">
          {{ selectedItemBasicInfo?.title || t('strategic.loading_detail_description') }}
        </p>
        <div class="mt-4 flex items-center gap-2 text-xs text-slate-400">
          <span>{{ t('strategic.loading_tip') }}</span>
        </div>
      </div>
    </div>
    
    <!-- Error 状态 -->
    <div v-else-if="detailLoadingState === 'error'" class="h-full flex flex-col">
      <!-- 保持顶部导航结构 -->
      <div class="flex-shrink-0 p-6 border-b">
        <div class="flex items-center justify-between w-full mb-4">
          <div class="flex items-center gap-3 flex-1 min-w-0">
            <Button variant="ghost"
                    size="sm"
                    @click="handleCloseDetail"
                    class="h-8 w-8 p-0 flex-shrink-0">
              <ArrowLeftIcon class="h-4 w-4"/>
            </Button>
            
            <h2 class="text-xl font-bold flex-1 min-w-0 truncate text-red-700">{{ t('strategic.detail_load_error') }}</h2>
          </div>
          
          <!-- 右侧导航控件 - 在错误时也显示 -->
          <div v-if="allItems && allItems.length > 1" class="flex items-center gap-1 bg-white rounded-lg border border-gray-200 shadow-sm p-1 flex-shrink-0">
            <!-- 上一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigatePrevious"
              :disabled="currentIndex === 0"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronLeftIcon class="h-3.5 w-3.5"/>
            </Button>
            
            <!-- 列表选择器带icon -->
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" class="h-7 px-2 text-xs gap-1 hover:bg-gray-100">
                  <ListIcon class="h-3.5 w-3.5"/>
                  <span>{{ (currentIndex ?? 0) + 1 }}/{{ allItems.length }}</span>
                  <ChevronDownIcon class="h-3 w-3"/>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent class="w-80 max-h-60 overflow-y-auto" align="end">
                <div class="p-2">
                  <div class="flex items-center gap-2 text-xs font-medium text-gray-500 mb-2">
                    <ListIcon class="h-3.5 w-3.5"/>
                    <span>{{ t('strategic.navigation.quick_nav_title', { count: allItems.length }) }}</span>
                  </div>
                  <div class="space-y-1">
                    <div 
                      v-for="(item, index) in allItems" 
                      :key="item.id"
                      @click="handleNavigateToItem(index)"
                      :class="[
                        'flex items-center justify-between p-2 rounded-md cursor-pointer text-xs hover:bg-gray-50 transition-colors',
                        currentIndex === index ? 'bg-blue-50 border border-blue-200' : ''
                      ]">
                      <div class="flex-1 min-w-0">
                        <div class="font-medium truncate">
                          {{ getItemTitle(item) }}
                        </div>
                        <div class="text-gray-500 mt-0.5 flex items-center gap-1">
                          <MapPinIcon class="h-3 w-3"/>
                          {{ item.country || '-' }}
                        </div>
                      </div>
                      <div class="flex-shrink-0 ml-2">
                        <Badge v-if="item.news_type" variant="outline" class="text-xs">
                          {{ item.news_type }}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <!-- 下一个按钮 -->
            <Button 
              variant="ghost" 
              size="sm" 
              @click="handleNavigateNext"
              :disabled="currentIndex === (allItems.length - 1)"
              class="h-7 w-7 p-0 hover:bg-gray-100">
              <ChevronRightIcon class="h-3.5 w-3.5"/>
            </Button>
          </div>
        </div>
      </div>
      
      <!-- 错误内容区域 -->
      <div class="flex-1 flex flex-col items-center justify-center p-8">
        <AlertCircleIcon class="h-12 w-12 text-red-400 mb-4"/>
        <h3 class="text-lg font-medium text-red-700 mb-2">{{ t('strategic.detail_load_error') }}</h3>
        <p class="text-sm text-red-500 text-center max-w-md mb-4">
          {{ t('strategic.detail_load_error_description') }}
        </p>
        <p class="text-xs text-slate-500">{{ t('strategic.auto_return_message') }}</p>
        <Button 
          variant="outline" 
          size="sm" 
          @click="handleCloseDetail" 
          class="mt-4">
          {{ t('strategic.return_to_list') }}
        </Button>
      </div>
    </div>
    
    <!-- 正常详情内容 -->
    <Card v-else-if="selectedItem" class="p-6 max-w-full h-full flex flex-col">
      <CardHeader>
        <div class="flex flex-col gap-3">
          <!-- 第一行：返回按钮、标题、导航控件 -->
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center gap-3 flex-1 min-w-0">
              <Button variant="ghost"
                      size="sm"
                      @click="handleCloseDetail"
                      class="h-8 w-8 p-0 flex-shrink-0">
                <ArrowLeftIcon class="h-4 w-4"/>
              </Button>
              
              <h2 class="text-xl font-bold flex-1 min-w-0 truncate">{{ sItemTitle || t('strategic.detail_title_fallback') }}</h2>
            </div>
            
            <!-- 右侧导航控件 -->
            <div v-if="allItems && allItems.length > 1" class="flex items-center gap-1 bg-white rounded-lg border border-gray-200 shadow-sm p-1 flex-shrink-0">
              <!-- 上一个按钮 -->
              <Button 
                variant="ghost" 
                size="sm" 
                @click="handleNavigatePrevious"
                :disabled="currentIndex === 0"
                class="h-7 w-7 p-0 hover:bg-gray-100">
                <ChevronLeftIcon class="h-3.5 w-3.5"/>
              </Button>
              
              <!-- 列表选择器带icon -->
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" class="h-7 px-2 text-xs gap-1 hover:bg-gray-100">
                    <ListIcon class="h-3.5 w-3.5"/>
                    <span>{{ (currentIndex ?? 0) + 1 }}/{{ allItems.length }}</span>
                    <ChevronDownIcon class="h-3 w-3"/>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent class="w-80 max-h-60 overflow-y-auto" align="end">
                  <div class="p-2">
                    <div class="flex items-center gap-2 text-xs font-medium text-gray-500 mb-2">
                      <ListIcon class="h-3.5 w-3.5"/>
                      <span>{{ t('strategic.navigation.quick_nav_title', { count: allItems.length }) }}</span>
                    </div>
                    <div class="space-y-1">
                      <div 
                        v-for="(item, index) in allItems" 
                        :key="item.id"
                        @click="handleNavigateToItem(index)"
                        :class="[
                          'flex items-center justify-between p-2 rounded-md cursor-pointer text-xs hover:bg-gray-50 transition-colors',
                          currentIndex === index ? 'bg-blue-50 border border-blue-200' : ''
                        ]">
                        <div class="flex-1 min-w-0">
                          <div class="font-medium truncate">
                            {{ getItemTitle(item) }}
                          </div>
                          <div class="text-gray-500 mt-0.5 flex items-center gap-1">
                            <MapPinIcon class="h-3 w-3"/>
                            {{ item.country || '-' }}
                          </div>
                        </div>
                        <div class="flex-shrink-0 ml-2">
                          <Badge v-if="item.news_type" variant="outline" class="text-xs">
                            {{ item.news_type }}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <!-- 下一个按钮 -->
              <Button 
                variant="ghost" 
                size="sm" 
                @click="handleNavigateNext"
                :disabled="currentIndex === (allItems.length - 1)"
                class="h-7 w-7 p-0 hover:bg-gray-100">
                <ChevronRightIcon class="h-3.5 w-3.5"/>
              </Button>
            </div>
          </div>
          
          <!-- 第二行：推荐理由（如果有） -->
          <div v-if="selectedItem?.recommend_reason" class="w-full">
            <div class="recommendation-reason-card">
              <div class="flex items-start gap-3">
                <div class="flex-1 min-w-0">
                  <div class="recommendation-reason-title">{{ t('strategic.recommend_reason') }}：</div>
                  <div class="recommendation-reason-content">
                    {{ selectedItem.recommend_reason }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 第三行：快捷键提示和标签徽章 -->
          <div class="w-full flex justify-between items-center gap-3">
            <!-- 左侧：快捷键提示 -->
            <div class="flex items-center gap-2 text-xs text-gray-500 flex-shrink-0">
              <span>{{ t('strategic.shortcuts.label') }}：</span>
              <kbd class="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">←</kbd>
              <kbd class="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">→</kbd>
              <span>{{ t('strategic.shortcuts.switch') }}</span>
              <kbd class="px-1.5 py-0.5 bg-gray-100 border border-gray-300 rounded text-xs font-mono">ESC</kbd>
              <span>{{ t('strategic.shortcuts.back') }}</span>
            </div>
            
            <!-- 右侧：时间徽章和标签 -->
            <div class="flex items-center gap-2 flex-shrink-0">
              <Badge v-if="selectedItem.country" variant="outline" class="text-xs bg-violet-50 text-violet-700 border-violet-200">
                {{ selectedItem.country }}
              </Badge>
              <Badge v-if="selectedItem.first_level_industry" variant="outline" class="text-xs bg-cyan-50 text-cyan-700 border-cyan-200">
                {{ selectedItem.first_level_industry }}
              </Badge>
              <Badge v-if="selectedItem.news_type" variant="outline" class="text-xs bg-amber-50 text-amber-700 border-amber-200">
                {{ selectedItem.news_type }}
              </Badge>
              <Badge variant="outline" class="text-xs">
                {{ formatDateString(selectedItem?.updated_at || selectedItem?.update_at) }}
              </Badge>
              
              <!-- AI 深度分析按钮 with DropdownMenu -->
              <DropdownMenu v-model:open="showAiConfirm">
                <DropdownMenuTrigger as-child>
                  <Button 
                    variant="outline" 
                    size="sm"
                    class="h-7 px-3 text-xs gap-1.5 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-purple-100 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md">
                    <BrainIcon class="w-3.5 h-3.5"/>
                    <span>AI 分析</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent class="w-80 p-0" align="end">
                  <div class="p-4 space-y-3">
                    <div class="flex items-start gap-3">
                      <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <BrainIcon class="w-4 h-4 text-blue-600"/>
                      </div>
                      <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-900 text-sm">AI 深度分析</h4>
                        <p class="text-xs text-gray-600 mt-1">
                          为「{{ selectedItem?.title || selectedItem?.summary || '此项目' }}」进行智能分析
                        </p>
                      </div>
                    </div>
                    <div class="bg-blue-50 rounded-md p-3">
                      <div class="text-xs text-blue-800 space-y-1">
                        <div class="flex items-center gap-1">
                          <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
                          <span>战略机会评估</span>
                        </div>
                        <div class="flex items-center gap-1">
                          <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
                          <span>市场进入策略</span>
                        </div>
                        <div class="flex items-center gap-1">
                          <div class="w-1 h-1 bg-blue-600 rounded-full"></div>
                          <span>风险评估报告</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex gap-2 pt-1">
                      <Button 
                        @click="handleConfirmAiProcessing"
                        class="flex-1 h-8 text-xs"
                        size="sm">
                        开始分析
                      </Button>
                      <Button 
                        @click="handleCancelAiProcessing"
                        variant="outline" 
                        class="flex-1 h-8 text-xs"
                        size="sm">
                        取消
                      </Button>
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent class="flex-1 min-h-0 overflow-y-auto scrollbar-thin">
        <!-- 不使用 tabs，直接展示所有信息 -->
        <div class="space-y-6">
          <!-- 文章概述 Summary -->
          <div v-if="selectedItem?.summary" class="space-y-3">
            <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
              <ActivityIcon class="h-5 w-5 text-purple-600"/>
              {{ t('strategic.summary') }}
            </h3>
            <div class="bg-gradient-to-r from-purple-50 to-white p-4 rounded-lg border border-purple-100">
              <p class="text-sm text-slate-700 leading-relaxed whitespace-pre-wrap">
                {{ selectedItem.summary }}
              </p>
            </div>
          </div>

          <!-- 机会详情 -->
          <div v-if="selectedItem?.opportunity_details" class="space-y-3">
            <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
              <LightbulbIcon class="h-5 w-5 text-amber-500"/>
              {{ t('strategic.opportunity_details') }}
            </h3>
            <div class="bg-white/90 p-4 rounded-lg border border-slate-100">
              <p class="text-sm text-slate-700 leading-relaxed whitespace-pre-wrap">
                {{ selectedItem.opportunity_details }}
              </p>
            </div>
          </div>

          <!-- 进入策略 -->
          <div v-if="selectedItem?.entry_strategy" class="space-y-3">
            <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
              <ArrowRightCircleIcon class="h-5 w-5 text-green-500"/>
              {{ t('strategic.entry_strategy') }}
            </h3>
            <div class="bg-white/90 p-4 rounded-lg border border-slate-100">
              <p class="text-sm text-slate-700 leading-relaxed whitespace-pre-wrap">
                {{ selectedItem.entry_strategy }}
              </p>
            </div>
          </div>

          <!-- 关键机会 (标签列表) -->
          <div v-if="selectedItem?.key_opportunities && selectedItem.key_opportunities.length > 0" class="space-y-3">
            <h3 class="text-lg font-semibold text-slate-800 flex items-center gap-2">
              <StarIcon class="h-5 w-5 text-blue-500"/>
              {{ t('strategic.key_opportunities') }}
            </h3>
            <div class="flex flex-wrap gap-2">
              <Badge 
                v-for="(opportunity, opIndex) in selectedItem.key_opportunities" 
                :key="opIndex"
                variant="outline" 
                class="bg-blue-50 text-blue-700 border-blue-200 shadow-sm py-1 text-sm">
                {{ opportunity }}
              </Badge>
            </div>
          </div>

          <!-- 时间线和原始链接信息 -->
          <div class="pt-4 mt-4 border-t border-slate-100 space-y-3">
            <!-- 时间线 -->
            <div v-if="selectedItem?.timeline" class="flex items-center gap-2 text-sm text-slate-600">
              <CalendarIcon class="h-4 w-4 text-slate-500"/>
              <span class="font-medium">{{ t('strategic.timeline') }}:</span>
              <span>{{ selectedItem.timeline }}</span>
            </div>

            <!-- 新闻原链接 -->
            <div v-if="selectedItem?.url" class="flex items-center gap-2">
              <ExternalLinkIcon class="h-4 w-4 text-blue-500"/>
              <span class="text-sm font-medium text-slate-600">{{ t('strategic.source_link') }}:</span>
              <a 
                :href="selectedItem.url" 
                target="_blank" 
                rel="noopener noreferrer" 
                class="text-sm text-blue-600 hover:text-blue-800 transition-colors hover:underline">
                {{ selectedItem.url }}
              </a>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, onMounted, onUnmounted, ref } from 'vue'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  ArrowLeftIcon, 
  ActivityIcon, 
  ChevronLeftIcon, 
  ChevronDownIcon, 
  ChevronRightIcon, 
  ListIcon, 
  MapPinIcon, 
  AlertCircleIcon,
  LightbulbIcon,
  StarIcon, 
  ArrowRightCircleIcon,
  ExternalLinkIcon,
  CalendarIcon,
  BrainIcon
} from 'lucide-vue-next'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useRouter } from 'vue-router'

// 定义 Strategic 数据项类型
interface StrategicDataItem extends Record<string, any> {
  id: string | number
  country?: string
  first_level_industry?: string
  news_type?: string
  summary?: string
  opportunity_details?: string
  entry_strategy?: string
  key_opportunities?: string[]
  timeline?: string
  url?: string
  recommend_reason?: string
  updated_at?: string
  update_at?: string
  title?: string
}

const props = defineProps<{
  selectedItem: StrategicDataItem | null
  selectedItemBasicInfo?: StrategicDataItem | null
  detailLoadingState?: 'idle' | 'loading' | 'success' | 'error'
  allItems?: StrategicDataItem[]
  currentIndex?: number
  formatDateString:(dateInput: any) => string
  t: (key: string, ...args: any[]) => string // i18n function
}>()

const emit = defineEmits(['close-detail', 'navigate-to-item', 'navigate-previous', 'navigate-next'])

// 添加 router
const router = useRouter()

const handleCloseDetail = () => {
  emit('close-detail')
}

// 导航事件处理函数
const handleNavigatePrevious = () => {
  emit('navigate-previous')
}

const handleNavigateNext = () => {
  emit('navigate-next')
}

const handleNavigateToItem = (index: number) => {
  emit('navigate-to-item', index)
}

// 获取项目标题的辅助函数
const getItemTitle = (item: StrategicDataItem | null): string => {
  if (!item) {
    return props.t('strategic.unknown_title')
  }
  return item.title || item.summary || item.opportunity_details || props.t('strategic.unknown_title')
}

// 计算属性
const sItemTitle = computed(() => {
  if (!props.selectedItem) {
    return props.t('strategic.detail_title_fallback')
  }
  return getItemTitle(props.selectedItem)
})

// 键盘快捷键处理
const isHandlingKeyEvent = ref(false) // 添加防抖标记

const handleKeydown = (event: KeyboardEvent) => {
  // 如果正在处理键盘事件，忽略后续事件
  if (isHandlingKeyEvent.value) {
    return
  }

  // 检查是否在输入框或可编辑元素中
  const target = event.target as HTMLElement
  if (target && (
    target.tagName === 'INPUT' ||
    target.tagName === 'TEXTAREA' ||
    target.contentEditable === 'true' ||
    target.classList.contains('el-input__inner') // Element Plus 输入框
  )) {
    return
  }

  // 确保只有在有多个项目时才处理导航快捷键
  if (!props.allItems || props.allItems.length <= 1) {
    // 即使只有一个项目，ESC键仍然可以返回
    if (event.key === 'Escape') {
      event.preventDefault()
      event.stopPropagation()
      handleCloseDetail()
    }
    return
  }

  // 设置防抖标记
  isHandlingKeyEvent.value = true

  let handled = false

  switch (event.key) {
  case 'ArrowLeft':
    if (props.currentIndex !== undefined && props.currentIndex > 0) {
      event.preventDefault()
      event.stopPropagation()
      handleNavigatePrevious()
      handled = true
    }
    break
    
  case 'ArrowRight':
    if (props.currentIndex !== undefined && props.currentIndex < props.allItems.length - 1) {
      event.preventDefault()
      event.stopPropagation()
      handleNavigateNext()
      handled = true
    }
    break
    
  case 'Escape':
    event.preventDefault()
    event.stopPropagation()
    handleCloseDetail()
    handled = true
    break
  }

  // 如果事件被处理了，添加短暂延迟防止重复触发
  if (handled) {
    setTimeout(() => {
      isHandlingKeyEvent.value = false
    }, 100) // 100ms 防抖延迟
  } else {
    isHandlingKeyEvent.value = false
  }
}

// 添加和移除键盘事件监听器
const keyEventListenerAdded = ref(false)

onMounted(() => {
  // 确保只添加一次事件监听器
  if (!keyEventListenerAdded.value) {
    document.addEventListener('keydown', handleKeydown, { passive: false })
    keyEventListenerAdded.value = true
    console.log('StrategicDetailView: 键盘事件监听器已添加')
  }
})

onUnmounted(() => {
  // 组件卸载时移除事件监听器，避免内存泄漏
  if (keyEventListenerAdded.value) {
    document.removeEventListener('keydown', handleKeydown)
    keyEventListenerAdded.value = false
    isHandlingKeyEvent.value = false // 重置防抖标记
    console.log('StrategicDetailView: 键盘事件监听器已移除')
  }
})

// AI 深度分析处理逻辑
const showAiConfirm = ref(false)

const handleConfirmAiProcessing = () => {
  if (!props.selectedItem) {
    return
  }
  
  // 关闭确认框
  showAiConfirm.value = false
  
  // 构建带参数的URL，传递当前项目信息
  const params = new URLSearchParams({
    projectId: props.selectedItem.id.toString(),
    title: props.selectedItem?.title || props.selectedItem?.summary || 'Strategic Project',
    source: 'strategic-detail-view'
  })
  
  // 跳转到 AI Dashboard
  router.push(`/next/dashboard?${params.toString()}`)
}

const handleCancelAiProcessing = () => {
  showAiConfirm.value = false
}
</script>

<style scoped>
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
}
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}

/* 推荐理由卡片样式 - 使用系统主题色 */
.recommendation-reason-card {
  background: var(--brand-color-1);
  border: 1px solid var(--brand-color-2);
  border-radius: var(--radius-default);
  padding: var(--m-default);
}

.recommendation-reason-indicator {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  margin-top: 2px;
}

.recommendation-reason-title {
  font-size: var(--size-font-8);
  font-weight: var(--weight-font-Semiblod);
  color: var(--primary-color);
  margin-bottom: var(--m-mini);
}

.recommendation-reason-content {
  font-size: var(--size-font-9);
  color: var(--primary-color);
  line-height: 1.5;
}
</style> 