import type { UserType } from '@/stores/user'

export interface NewsDetailResponse {
  items: NewsDetail[];
  meta: Meta;
}

interface Meta {
  page: number;
  page_size: number;
  max_page: number;
  total: number;
}

export interface NewsDetail {
  nation: string;
  language: string;
  timezone: string;
  icon: string;
  id: string;
  title: string;
  publish_time: number;
  url: string;
  web_site: string;
  brief_summary: string;
  long_summary: string;
}

export interface TopBtn {
    china: string;
    japan: string;
    usa: string;
    korea: string;
    vietnam: string;
  }
