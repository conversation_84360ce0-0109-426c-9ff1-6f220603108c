import { ref, computed, onMounted, onUnmounted, type Ref, watch } from 'vue'
import { useGet, usePost } from '@/hooks/useHttp'
import { useReportProgress, SPECIAL_AGENT_TYPES, AVATAR_MAP, type ReportData as RawSSEData, type ToolR<PERSON>ult } from '@/composables/useReportProgress'
import type { CompletedAgent } from '@/pages/dashboard/type' 
import type { ReportList as StandardReport } from '@/pages/format-report/type' 
import type { Report, RunningAgent, AgentStep } from '@/pages/workspace/types'
import { useI18n } from 'vue-i18n'
import { formatCalendarDate, formatCalendarDateTime } from '@/utils/filter'
import { usePermissions } from '@/composables/usePermissions'
import dayjs from 'dayjs'

// Type Guards
function isMonitorAgent(report: Report | undefined | null): report is CompletedAgent {
  return !!report && typeof report === 'object' && 'report_type' in report && SPECIAL_AGENT_TYPES.includes((report as CompletedAgent).report_type || '') && 'frequency' in report
}

function isRunningAgent(report: Report | undefined | null): report is RunningAgent {
  return !!report && typeof report === 'object' && 'steps' in report && 'progress' in report && !('created_at' in report) && !('language' in report)
}

function isStandardReport(report: Report | undefined | null): report is StandardReport {
  return !!report && typeof report === 'object' && 'language' in report && !('steps' in report) && 'summary' in report
}

// 定义useReports的选项接口
interface UseReportsOptions {
  onInitialized?: () => void; // 初始化完成后的回调函数
  onConnected?: () => void; // SSE连接成功后的回调函数Connected?: () => void; // SSE连接成功后的回调函数
}

// Exported function - Each call creates a new instance
export function useReports(options?: UseReportsOptions) {
  const { t } = useI18n()
  const { withPermission, hasPermission } = usePermissions()

  // --- Instance-specific state ---
  const standardReports = ref<StandardReport[]>([])
  const monitorAgents = ref<CompletedAgent[]>([])
  const runningAgents = ref<RunningAgent[]>([])
  const selectedReportType = ref<string | null>(null)
  const refreshDebounceTimeout = ref<number | null>(null)
  const isInitialized = ref(false) // Instance-level initialization flag

  // --- API hooks (instance-specific) ---
  const { execute: getStandardReports, isLoading: isStandardReportsLoading } = useGet<StandardReport[]>(
    '/formatted_report/standard_report_list',
    {},
    { immediate: false }
  )

  const { execute: getMonitorAgents, isLoading: isMonitorAgentsLoading } = usePost<CompletedAgent[]>(
    '/formatted_report/report_list',
    {},
    {},
    { immediate: false }
  )
  
  // --- SSE hook (instance-specific) ---
  const { 
    dashboardData, 
    open: openSSE, 
    close: closeSSE 
  } = useReportProgress('/api/formatted_report/report_progress', {
    onConnected: () => {
      console.log('useReports (instance): SSE connection opened.')
      if (options?.onConnected) {
        options.onConnected()
      }
    }
  })
  
  // --- Loading state (instance-specific) ---
  const isLoading = computed(() => isStandardReportsLoading.value || isMonitorAgentsLoading.value)
  
  // --- 带权限检查的API调用包装函数 ---
  const loadStandardReports = withPermission('api.getStandardReports', async () => {
    console.log('useReports: Loading standard reports with permission check...')
    return await getStandardReports()
  })

  const loadMonitorAgents = withPermission('api.getMonitorAgents', async () => {
    console.log('useReports: Loading monitor agents with permission check...')
    return await getMonitorAgents()
  })
  
  // --- Instance-specific Initialization ---
  async function initialize() {
    // Prevent re-initialization for the *same* instance if called multiple times somehow
    if (isInitialized.value) {
      console.log('useReports (instance): Already initialized, skipping.')
      return
    }

    console.log('useReports (instance): Initializing...')
    try {
      console.log('useReports (instance): Loading initial data based on permissions...')
      
      // 根据权限有选择地加载数据
      const loadPromises: Promise<any>[] = []
      
      if (hasPermission('api.getStandardReports')) {
        console.log('useReports: User has permission to load standard reports')
        loadPromises.push(loadStandardReports())
      } else {
        console.log('useReports: User does not have permission to load standard reports')
        loadPromises.push(Promise.resolve(null))
      }
      
      if (hasPermission('api.getMonitorAgents')) {
        console.log('useReports: User has permission to load monitor agents')
        loadPromises.push(loadMonitorAgents())
      } else {
        console.log('useReports: User does not have permission to load monitor agents')
        loadPromises.push(Promise.resolve(null))
      }
      
      const [standardReportsData, monitorAgentsData] = await Promise.all(loadPromises)
      
      updateReportsData(standardReportsData, monitorAgentsData)
      console.log('useReports (instance): Initial data load complete.')
      
      openSSE() // Open SSE connection for this instance
      console.log('useReports (instance): SSE connection opened.')
      
      // Initial processing AFTER opening SSE
      processRunningTasks() 
      console.log('useReports (instance): Initial processRunningTasks called.')
      
      isInitialized.value = true // Mark this instance as initialized
      console.log('useReports (instance): Initialization complete.')
      
      // 调用初始化完成回调（如果提供了的话）
      if (options?.onInitialized) {
        console.log('useReports (instance): Calling onInitialized callback...')
        options.onInitialized()
      }
    } catch (error) {
      console.error('useReports (instance): Error during initialization:', error)
      isInitialized.value = false // Ensure it can be retried if needed
    }
  }
  
  // --- Instance-specific Cleanup ---
  function cleanup() {
    closeSSE() // Close SSE for this instance
    if (refreshDebounceTimeout.value) {
      clearTimeout(refreshDebounceTimeout.value)
      refreshDebounceTimeout.value = null
    }
    isInitialized.value = false // Reset initialization state on cleanup
    console.log('useReports (instance): Resources cleaned up.')
  }
  
  // --- Data Update Function (instance-specific) ---
  function updateReportsData(
    newStandardReports: StandardReport[] | null | undefined, 
    newMonitorAgents: CompletedAgent[] | null | undefined
  ) {
    if (newStandardReports) {
      standardReports.value = newStandardReports
    }
    
    if (newMonitorAgents) {
      monitorAgents.value = (newMonitorAgents || []).filter(agent => 
        SPECIAL_AGENT_TYPES.includes(agent.report_type || '')
      )
    }
  }
  
  // --- Process Running Tasks (instance-specific) ---
  function processRunningTasks() {
    console.log('useReports (instance): processRunningTasks called. dashboardData:', JSON.stringify(dashboardData.value)) // Debug log

    if (!dashboardData.value) {
      runningAgents.value = []
      return
    }

    // Directly filter and map from dashboardData
    runningAgents.value = dashboardData.value
      .filter(item => item.status === 'running' || item.status === 'pending') // Filter for active tasks
      .map(rawData => {
        const status = mapSSETaskStatus(rawData.status)
        const progress = Math.ceil(rawData.progress || 0)
        
        // Determine description based on current/last tool
        const processingTool = rawData.tools_results?.find(tool => mapSSEToolStatus(tool.status) === 'PROCESSING')
        const lastCompletedTool = rawData.tools_results?.slice().reverse().find(tool => {
          const mappedStatus = mapSSEToolStatus(tool.status)
          return mappedStatus === 'COMPLETED' || mappedStatus === 'ERROR'
        })
        let currentStepDescription = t('agent.init') 
        if (processingTool) {
          currentStepDescription = SPECIAL_AGENT_TYPES.includes(rawData.report_type) ? processingTool.name : t(`tools.${processingTool.name}`)
        } else if (lastCompletedTool) {
          currentStepDescription = SPECIAL_AGENT_TYPES.includes(rawData.report_type) ? lastCompletedTool.name : t(`tools.${lastCompletedTool.name}`)
        }

        return {
          id: rawData.id,
          name: rawData.agent_name || t('agent.no_agent_name', { name: rawData.report_name }),
          report_name: rawData.report_name,
          duty: rawData.report_description, 
          status: status,
          avatar: AVATAR_MAP[rawData.report_type as keyof typeof AVATAR_MAP],
          steps: (rawData.tools_results || []).map((tool: ToolResult) => ({
            title: SPECIAL_AGENT_TYPES.includes(rawData.report_type) ? tool.name : t(`tools.${tool.name}`),
            description: SPECIAL_AGENT_TYPES.includes(rawData.report_type) ? tool.name : t(`tools.${tool.name}Desc`), // Assuming description is available
            status: mapSSEToolStatus(tool.status)
          })),
          progress: progress,
          agent_name: rawData.agent_name,
          report_type: rawData.report_type,
          date: new Date().toISOString(), 
          description: currentStepDescription,
        }
      })
    console.log('useReports (instance): runningAgents updated:', JSON.stringify(runningAgents.value)) // Debug log
  }

  // --- Watcher for dashboardData (instance-specific) ---
  watch(dashboardData, (newData, oldData) => {
    console.log('useReports (instance): dashboardData watcher triggered.')
    
    // 1. Always update the runningAgents state based on the latest SSE data
    processRunningTasks() 

    // 2. Check if any task JUST finished or errored or disappeared to trigger API refresh
    let shouldRefreshApi = false
    
    // Ensure both old and new data exist for comparison, handle initial load case
    if (oldData) { 
      // Create maps for efficient lookup
      const oldStatusMap = new Map(oldData.map(item => [item.id, item.status]))
      const newIds = new Set(newData?.map(item => item.id) || []) // Handle newData potentially being null/undefined initially

      // Check for tasks that DISAPPEARED while running/pending
      for (const oldItem of oldData) {
        if ((oldItem.status === 'running' || oldItem.status === 'pending') && !newIds.has(oldItem.id)) {
          console.log(`useReports (instance): Detected running/pending task ${oldItem.id} disappeared. Triggering API refresh.`)
          shouldRefreshApi = true
          break // Found one, no need to check further in this loop
        }
      }

      // Check for tasks still present but status changed to completed/error (only if not already decided to refresh)
      if (!shouldRefreshApi && newData) {
        for (const newItem of newData) {
          const oldStatus = oldStatusMap.get(newItem.id)
          const newStatus = newItem.status
          if (newStatus && (newStatus === 'completed' || newStatus === 'error')) {
            if (oldStatus && (oldStatus === 'running' || oldStatus === 'pending')) {
              console.log(`useReports (instance): Detected task ${newItem.id} status changed to finished/errored. Triggering API refresh.`)
              shouldRefreshApi = true
              break // Found one, no need to check further
            }
          }
        }
      }
    } else if (newData?.some(item => item.status === 'completed' || item.status === 'error')) {
      // Handle case where initial data already contains completed tasks (maybe unlikely with current init)
      // Or if oldData was null/undefined for some reason
      console.log('useReports (instance): Detected completed/errored tasks in initial/new data without old data for comparison. Triggering API refresh.')
      shouldRefreshApi = true
    }

    // 3. Trigger the smart refresh if needed
    if (shouldRefreshApi) {
      triggerSmartRefresh('sse_task_finished_or_disappeared')
    }

  }, { deep: true }) // Deep watch is crucial
  
  // --- Helper functions (remain unchanged) ---
  function mapSSEToolStatus(sseStatus: ToolResult['status'] | undefined): AgentStep['status'] {
    switch (sseStatus?.toLowerCase()) {
    case 'pending': return 'PENDING'
    case 'processing': case 'running': return 'PROCESSING'
    case 'completed': case 'success': return 'COMPLETED'
    case 'error': return 'ERROR'
    default: return 'PENDING'
    }
  }
  
  function mapSSETaskStatus(sseStatus: RawSSEData['status'] | undefined): RunningAgent['status'] {
    switch (sseStatus?.toLowerCase()) {
    case 'pending': return 'PENDING'
    case 'running': return 'RUNNING'
    case 'completed': return 'COMPLETED'
    case 'error': return 'ERROR'
    default: return 'PENDING'
    }
  }
  
  function getReportType(report: Report): string {
    let typeKey = ''
    if (isMonitorAgent(report)) { typeKey = report.report_type || 'monitor_agent_type' }
    else if (isRunningAgent(report)) { typeKey = report.report_type || 'running_task_type' }
    else if (isStandardReport(report)) { typeKey = report.report_type || 'standard_report_type' }
    return t(`report.${typeKey}`, typeKey || t('common.unknown_type'))
  }
  
  // --- Computed Properties (instance-specific) ---
  const allReports = computed<Report[]>(() => {
    // 1. 获取所有报告源
    const currentMonitorAgents = monitorAgents.value || []
    const currentRunningAgents = runningAgents.value || []
    const currentStandardReports = standardReports.value || []

    // 2. 创建运行中任务的 ID 集合，以便快速查找
    const runningAgentIds = new Set(currentRunningAgents.map(agent => agent.id))

    // 3. 合并时优先保留 runningAgents 的版本
    const combinedMonitorAndRunning = [
      // 先加入所有运行中的 Agent (包含 steps 和实时 status)
      ...currentRunningAgents.map(r => ({ ...r, date: formatCalendarDateTime(dayjs(r.date || new Date(0)).valueOf()) })),
      // 再加入 API 获取的 Monitor Agent，但要排除掉那些ID已在运行中列表里的报告
      ...currentMonitorAgents
        .filter(agent => !runningAgentIds.has(agent.id)) // 关键：排除已在 runningAgents 中的 ID
        .map(r => ({ ...r, date: formatCalendarDateTime(dayjs(r.updated_at || r.created_at || new Date(0)).valueOf()) }))
    ]

    // 4. 最后合并标准报告
    const finalReports = [
      ...combinedMonitorAndRunning,
      ...currentStandardReports.map(r => ({ ...r, date: formatCalendarDateTime(dayjs(r.updated_at || new Date(0)).valueOf()) }))
    ]

    // console.log('Running Agent IDs:', runningAgentIds); // 调试日志
    // console.log('Combined Reports:', finalReports); // 调试日志
    return finalReports
  })
  
  const sortedReports = computed(() => {
    // 过滤并排序
    return [...allReports.value]
      .filter(report => !selectedReportType.value || getReportType(report) === selectedReportType.value)
      .sort((a, b) => {
        const dateA = new Date((a as any).date || 0).getTime()
        const dateB = new Date((b as any).date || 0).getTime()
        return dateB - dateA
      })
  })
  
  const groupedReports = computed(() => {
    // 按日期分组
    const grouped: Record<string, Report[]> = {}
    for (const report of sortedReports.value) {
      // Ensure date is valid before converting
      const reportDate = (report as any).date
      let dateStr = new Date(0).toLocaleDateString() // Default to epoch if date is invalid/missing
      if (reportDate) {
        try {
          const dateObj = new Date(reportDate)
          if (!isNaN(dateObj.getTime())) { // Check if date is valid
            dateStr = dateObj.toLocaleDateString()
          } else {
            console.warn('Invalid date encountered for report:', report)
          }
        } catch (e) {
          console.warn('Error parsing date for report:', report, e)
        }
      }

      if (!grouped[dateStr]) {
        grouped[dateStr] = []
      }
      grouped[dateStr].push(report)
    }
    return grouped
  })
  
  const reportTypes = computed(() => {
    // 收集所有报告类型
    const types = new Set<string>()
    allReports.value.forEach(report => {
      const type = getReportType(report)
      if (type) {types.add(type)}
    })
    return Array.from(types)
  })
  
  // --- Smart Refresh Function (instance-specific) ---
  function triggerSmartRefresh(reason: string) {
    if (isLoading.value) {
      console.log(`useReports (instance): Refresh already in progress, skipping (${reason})`)
      return
    }
    if (refreshDebounceTimeout.value) {
      clearTimeout(refreshDebounceTimeout.value)
    }
    refreshDebounceTimeout.value = window.setTimeout(async () => {
      console.log(`useReports (instance): Smart refresh triggered, reason: ${reason}`)
      try {
        // 根据权限有选择地刷新数据
        const refreshPromises: Promise<any>[] = []
        
        if (hasPermission('api.getStandardReports')) {
          console.log('useReports: Refreshing standard reports with permission')
          refreshPromises.push(loadStandardReports())
        } else {
          console.log('useReports: Skipping standard reports refresh (no permission)')
          refreshPromises.push(Promise.resolve(null))
        }
        
        if (hasPermission('api.getMonitorAgents')) {
          console.log('useReports: Refreshing monitor agents with permission')
          refreshPromises.push(loadMonitorAgents())
        } else {
          console.log('useReports: Skipping monitor agents refresh (no permission)')
          refreshPromises.push(Promise.resolve(null))
        }
        
        const [newStandardReports, newMonitorAgents] = await Promise.all(refreshPromises)
        updateReportsData(newStandardReports, newMonitorAgents) // Update instance data
        console.log('useReports (instance): Smart refresh API data fetched and updated.')
      } catch (error) {
        console.error('useReports (instance): Smart refresh error:', error)
      } finally {
        refreshDebounceTimeout.value = null
      }
    }, 300)
  }
  
  // --- Exposed Refresh Function (instance-specific) ---
  function refreshReports(reason = 'manualRefresh') {
    console.log(`useReports (instance): Refresh requested, reason: ${reason}`)
    triggerSmartRefresh(reason)
    if (reason === 'agentDeleted' || reason === 'timeIntervalChanged') {
      setTimeout(() => {
        triggerSmartRefresh(`${reason}-delayed`)
      }, 500)
    }
  }

  // --- 手动重新初始化函数 ---
  function reinitialize() {
    if (isInitialized.value) {
      console.log('useReports (instance): Manually triggering reinitialization...')
      // 简单地重置初始化标志并重新调用initialize
      isInitialized.value = false
      initialize()
    } else {
      console.log('useReports (instance): Not initialized, calling initialize directly...')
      initialize()
    }
  }

  // --- Lifecycle Hooks for the instance ---
  onMounted(() => {
    initialize() // Initialize when the component using this instance mounts
  })

  onUnmounted(() => {
    cleanup() // Cleanup when the component using this instance unmounts
  })

  // --- Return instance-specific state and methods ---
  return {
    // Data
    groupedReports,
    reportTypes,
    selectedReportType,
    allReports, // Expose allReports if needed by consumers
    monitorAgents, // Expose raw agent lists if needed
    runningAgents, // Expose raw agent lists if needed
    isLoading,
    isInitialized, // Expose instance initialization state if useful
    dashboardData,
    // Functions
    refreshReports,
    reinitialize, // 公开重新初始化函数
    // cleanup,    // Not typically needed to be exposed
  }
} 