<template>
  <Teleport to="body">
    <Transition name="dialog">
      <div v-if="show" 
           class="fixed inset-0 bg-black/20 backdrop-blur-xs z-50 flex items-center justify-center"
           @click.self="emit('close')">
        <div class="bg-white rounded-xl w-[90vw] max-w-2xl h-[80vh] flex flex-col shadow-lg border border-gray-100">
          <!-- 头部 -->
          <div class="flex justify-between items-center px-6 py-4 border-b border-gray-100">
            <h3 class="text-lg font-medium text-gray-800">{{ t("customerService.title") }}</h3>
            <button 
              @click="emit('close')" 
              class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 active:bg-gray-200 transition-colors">
              <Icon icon="mdi:close" class="text-[24px] text-gray-500"/>
            </button>
          </div>
          
          <!-- 聊天区域 -->
          <div class="flex-1 overflow-y-auto px-6 py-5" ref="chatContainer">
            <CustomerServiceChat :messages="messages"/>
          </div>

          <!-- 输入区域 -->
          <div class="border-t border-gray-100 p-5">
            <div class="flex items-center relative">
              <input
                v-model="inputMessage"
                type="text"
                :placeholder="t('customerService.inputPlaceholder')"
                class="w-full px-4 py-2.5 pr-12 bg-gray-50 border border-gray-200 rounded-lg focus:outline-hidden focus:ring-2 focus:ring-primary/20 focus:border-primary/30 transition-all"
                @keyup.enter="sendMessage">
              <div class="absolute right-3">
                <Icon 
                  icon="mdi:send" 
                  class="text-[24px] cursor-pointer hover:opacity-80 transition-opacity" 
                  :class="[
                    inputMessage.trim() ? 'text-[#4466BC]' : 'text-gray-300 cursor-not-allowed'
                  ]"
                  @click="inputMessage.trim() && sendMessage()"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import type { CustomerServiceMessage } from '@/pages/robot/type'
import CustomerServiceChat from './CustomerServiceChat.vue'

const { t } = useI18n()

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'close': []
  'send': [message: string]
}>()

const inputMessage = ref('')
const messages = ref<CustomerServiceMessage[]>([])
const chatContainer = ref<HTMLElement | null>(null)

const initMessages = () => {
  messages.value = [{
    content: t('customerService.welcomeMessage'),
    timestamp: Date.now(),
    sender: 'system'
  }]
}

watch(() => props.show, (newVal) => {
  if (newVal) {
    initMessages()
  }
}, { immediate: true })

const sendMessage = () => {
  if (!inputMessage.value.trim()) {return}
  
  messages.value = [...messages.value, {
    content: inputMessage.value,
    timestamp: Date.now(),
    sender: 'user'
  }]
  
  emit('send', inputMessage.value)
  inputMessage.value = ''
}

const scrollToBottom = () => {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
    }
  })
}

watch(() => messages.value.length, () => {
  scrollToBottom()
})
</script>

<style lang="scss" module>
.dialog-enter-active,
.dialog-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-enter-from,
.dialog-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.dialog-enter-to,
.dialog-leave-from {
  opacity: 1;
  transform: scale(1) translateY(0);
}
</style> 