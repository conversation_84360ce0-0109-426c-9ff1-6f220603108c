<template>
  <div class="w-full">
    <div class="container mx-auto">
      <div class="flex gap-8 py-20 lg:py-40 items-center justify-center flex-col">
        <div class="flex gap-4 flex-col">
          <h1 class="text-5xl md:text-7xl max-w-2xl tracking-tighter text-center font-regular font-[PingFang SC, HarmonyOS_Sans, Microsoft YaHei, sans-serif]">
            <span class="text-primary">SpecificAI</span>
            <div class="relative flex w-full justify-center items-center overflow-hidden text-center md:pb-4 md:pt-1" style="min-height: 150px;">
              <Transition name="slide-fade" mode="out-in">
                <span
                  :key="currentTitle"
                  class="font-semibold block"
                  style="min-width: 1em">
                  {{ currentTitle }}
                </span>
              </Transition>
            </div>
          </h1>
          <p class="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl text-center font-[PingFang SC, HarmonyOS_Sans, Microsoft YaHei, sans-serif]">
            {{ t('landing.magicText.description') }}
          </p>
        </div>
        <!-- <div class="flex flex-row gap-3">
          <Button size="lg" class="gap-4">
            免费体验SpecificAI <MoveRight class="w-4 h-4"/>
          </Button>
        </div> -->
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import { MoveRight } from 'lucide-vue-next'

const { t, locale } = useI18n()
  
// 动态切换关键词（主题语） - 使用国际化键
const titleKeys = [
  'landing.magicText.titles.collection',
  'landing.magicText.titles.coverage',
  'landing.magicText.titles.organization',
  'landing.magicText.titles.monitoring',
  'landing.magicText.titles.insights',
  'landing.magicText.titles.analysis',
  'landing.magicText.titles.platform'
]
const titleNumber = ref(0)
const currentTitle = computed(() => t(titleKeys[titleNumber.value]))
  
onMounted(() => {
  setInterval(() => {
    titleNumber.value = (titleNumber.value + 1) % titleKeys.length
  }, 2000)
})
</script>
  
  <style scoped>
  /* 推荐中文字体主题，增加 font-family（在 h1、p、.font-regular 等地方可添加） */
  .font-regular {
    font-family: "PingFang SC", "HarmonyOS_Sans", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
  }
  
  /* 动画和样式与之前一致 */
  .slide-fade-enter-active, .slide-fade-leave-active {
    transition: all 0.6s cubic-bezier(0.6, 0, 0.4, 1);
  }
  .slide-fade-enter-from {
    opacity: 0;
    transform: translateY(40px);
  }
  .slide-fade-leave-to {
    opacity: 0;
    transform: translateY(-40px);
  }
  </style>
  