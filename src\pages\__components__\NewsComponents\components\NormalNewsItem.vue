<template>
  <div
    :class="[
      $style['container'],
      { [$style['flashing']]: isFlashing }
    ]"
    class="w-full flex flex-col justify-start items-start gap-[15px] my-[10px]">
    <NewsItemHeader :news="news"/>
    <div class="w-full text-black text-[16px]">
      <MarkdownContainer :class="$style['text-container']" :content="news.brief_summary || ''"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type PropType } from 'vue'
import type { NewsDetail } from '@/types'
import NewsItemHeader from './NewsItemHeader.vue'
import MarkdownContainer from '@/components/MarkdownComponents/MarkdownContainer.vue'

const props = defineProps({
  news: {
    type: Object as PropType<NewsDetail>,
    required: true,
  },
  showLongSummary: {
    type: Boolean,
    default: false,
  },
  isFlashing: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" module>
.container {
  position: relative;
  border-radius: var(--radius-default);
  transition: background-color 0.3s ease;

  &.flashing {
    animation: breathe-animation 3s ease-in-out;
    animation-iteration-count: 1;
  }

  .text-container {
    word-break: normal;
    overflow-wrap: break-word;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-size: 15px;
    line-height: 200%;
    > p {
      font-size: 16px;
      color: black;
    }
    padding: 0 !important;
  }
  
  :global(.el-divider) {
    margin: 6px 0;
  }
}

@keyframes breathe-animation {
  0% {
    background-color: transparent;
  }
  20% {
    background-color: var(--brand-transparent-active-2); // 使用已定义的透明变量
  }
  40% {
    background-color: var(--brand-transparent-active-1);
  }
  60% {
    background-color: var(--brand-transparent-active-2);
  }
  80% {
    background-color: var(--brand-transparent-hover);
  }
  100% {
    background-color: transparent;
  }
}
</style>
