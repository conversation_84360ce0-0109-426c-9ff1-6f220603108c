<template>
  <div class="w-[65%] h-full flex flex-col space-y-4 py-8 pl-4 pr-8 mt-4">
    <el-scrollbar :view-class="$style.scrollbar" ref="scrollContainer">
      <div v-for="message in messages" :key="message.id" class="flex w-full">
        <component
          :is="getMessageComponent(message.chatType)"
          :content="message.content"
          :avatar="message.avatar"/>
      </div>
      <LoadingMessage 
        v-if="isLoading" 
        :avatar="messages[0]?.avatar || ''"/>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, onMounted, nextTick, watch } from 'vue'
import { ElScrollbar } from 'element-plus'
import type { Message } from '@/pages/robot/type'
import UserMessage from './UserMessage.vue'
import LoadingMessage from './LoadingMessage.vue'
import BotMessage from './BotMessage.vue'

const props = defineProps<{
  messages: Message[];
  isLoading: boolean;
}>()

const scrollContainer = ref<typeof ElScrollbar>()
onMounted(() => {
  scrollToBottom()
})
const scrollToBottom = () => {
  nextTick(() => {
    if (
      scrollContainer.value &&
      scrollContainer.value?.wrapRef?.scrollHeight >
        scrollContainer.value?.wrapRef?.clientHeight
    ) {
      scrollContainer.value.wrapRef.scrollTop =
        scrollContainer.value.wrapRef.scrollHeight
    }
  })
}

watch(props.messages, scrollToBottom)

const getMessageComponent = (chatType: string) => {
  switch (chatType) {
  case 'userInput':
    return UserMessage
  default:
    return BotMessage
  }
}
</script>

<style lang="scss" module>
.scrollbar {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 1rem;
}
</style>