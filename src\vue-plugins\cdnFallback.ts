/**
 * CDN降级插件
 * 
 * 用于自动处理CDN加载失败的情况，当一个CDN源失败时会自动尝试其他可用的CDN源
 */
import type { Plugin } from 'vite'

// CDN源池
const CDN_POOL = [
  'https://cdn.jsdelivr.net/npm/',
  'https://unpkg.com/',
  'https://fastly.jsdelivr.net/npm/',
  'https://gcore.jsdelivr.net/npm/'
]

// 插件配置
interface CDNFallbackOptions {
  /** 是否启用调试日志 */
  enableDebugLog?: boolean
  /** 自定义CDN池 */
  cdnPool?: string[]
  /** 额外需要预加载的包 */
  preloadPackages?: Array<{
    name: string
    version: string
    path: string
  }>
}

/**
 * 创建CDN降级插件
 */
export function cdnFallbackPlugin(options: CDNFallbackOptions = {}): Plugin {
  const enableDebugLog = options.enableDebugLog ?? true
  const cdnPool = options.cdnPool ?? CDN_POOL
  const preloadPackages = options.preloadPackages ?? []

  return {
    name: 'vite-plugin-cdn-fallback',
    apply: 'build', // 默认仅在build模式下应用
    configResolved(config) {
      // 检查环境
      if (config.command === 'serve' && config.mode === 'development') {
        console.log('[CDN Fallback] 开发环境已启用CDN降级功能')
      }
    },
    transformIndexHtml(html) {
      // 构建CDN降级脚本内容
      const scriptContent = `
      // CDN降级处理脚本
      ;(function() {
        // 详细的调试日志开关
        const ENABLE_DEBUG_LOG = ${enableDebugLog};
        
        // CDN 池配置
        const CDN_POOL = ${JSON.stringify(cdnPool)};
        
        // 用于跟踪已经尝试降级的资源
        const attemptedFallbacks = new Set();
        
        // 调试日志
        function debugLog(...args) {
          if (ENABLE_DEBUG_LOG) {
            console.log('[CDN Fallback]', ...args);
          }
        }
        
        // CDN 降级加载函数
        window.loadScriptWithFallback = function(packageName, version, path, index = 0) {
          if (index >= CDN_POOL.length) {
            console.error('[CDN Fallback] 所有 CDN 源加载失败:', packageName);
            return;
          }
          
          const cdn = CDN_POOL[index];
          const script = document.createElement('script');
          script.crossOrigin = 'anonymous';
          script.referrerPolicy = 'no-referrer';
          const fullPath = \`\${cdn}\${packageName}@\${version}/\${path}\`;
          script.src = fullPath;
          
          debugLog(\`尝试从 \${cdn} 加载 \${packageName}@\${version}/\${path}\`);
          
          // 使用资源计时API来测量加载时间
          const startTime = performance.now();
          
          script.onload = function() {
            const loadTime = performance.now() - startTime;
            debugLog(\`成功加载 \${fullPath}，耗时 \${loadTime.toFixed(2)}ms\`);
          };
          
          script.onerror = function(e) {
            const elapsedTime = performance.now() - startTime;
            console.warn(\`[CDN Fallback] \${cdn} 加载失败 (\${elapsedTime.toFixed(2)}ms)，尝试下一个源...\`);
            
            // 防止重复尝试相同的资源
            attemptedFallbacks.add(fullPath);
            
            // 尝试下一个CDN
            loadScriptWithFallback(packageName, version, path, index + 1);
          };
          
          document.head.appendChild(script);
          return script;
        };
      
        // 预加载所有CDN池中的资源
        function preloadResource(cdn) {
          // 创建预加载链接
          const link = document.createElement('link');
          link.rel = 'preconnect';
          link.href = cdn;
          link.crossOrigin = 'anonymous';
          document.head.appendChild(link);
          
          debugLog(\`预连接到 \${cdn}\`);
        }
        
        // 预连接到所有CDN
        CDN_POOL.forEach(cdn => {
          preloadResource(cdn);
        });

        // 预加载指定的包
        ${JSON.stringify(preloadPackages)}.forEach(pkg => {
          debugLog(\`预加载包: \${pkg.name}@\${pkg.version}/\${pkg.path}\`);
          loadScriptWithFallback(pkg.name, pkg.version, pkg.path);
        });
        
        // 全局错误处理，捕获所有CDN资源加载错误并尝试降级
        window.addEventListener('error', function(e) {
          // 检查是否是脚本或样式表加载错误
          if (e.target && (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') && (e.target.src || e.target.href)) {
            const src = e.target.src || e.target.href;
            
            // 如果已经尝试过这个资源，跳过
            if (attemptedFallbacks.has(src)) {
              debugLog(\`已经尝试过降级: \${src}\`);
              return;
            }
            
            // 检查是否包含在CDN池中的域名
            const matchedCdnIndex = CDN_POOL.findIndex(cdn => src.includes(cdn.replace(/\\/$/, '')));
            
            if (matchedCdnIndex !== -1) {
              debugLog(\`检测到CDN资源加载错误: \${src}\`);
              
              // 从URL中提取包信息
              try {
                const urlObj = new URL(src);
                const pathParts = urlObj.pathname.split('/');
                
                // 尝试找到包含@的部分作为包名和版本
                let packageWithVersion = null;
                for (let i = 0; i < pathParts.length; i++) {
                  if (pathParts[i].includes('@')) {
                    packageWithVersion = pathParts[i];
                    break;
                  }
                }
                
                if (packageWithVersion && packageWithVersion.includes('@')) {
                  const [packageName, version] = packageWithVersion.split('@');
                  
                  // 构建剩余路径
                  const packageIndex = urlObj.pathname.indexOf(packageWithVersion);
                  const remainingPath = urlObj.pathname.substring(packageIndex + packageWithVersion.length + 1);
                  
                  debugLog(\`解析资源: 包名=\${packageName}, 版本=\${version}, 路径=\${remainingPath}\`);
                  
                  // 使用 CDN 降级加载 - 从下一个CDN开始
                  console.warn(\`[CDN Fallback] 资源 \${src} 加载失败，尝试使用备用CDN加载\`);
                  e.preventDefault(); // 阻止默认错误处理
                  
                  // 标记为已尝试
                  attemptedFallbacks.add(src);
                  
                  // 从下一个CDN开始尝试
                  loadScriptWithFallback(packageName, version, remainingPath, matchedCdnIndex + 1);
                  return;
                }
              } catch (err) {
                console.error('[CDN Fallback] 解析URL失败:', err, src);
              }
            }
          }
        }, true); // 使用捕获阶段
        
        // 也捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
          const reason = e.reason;
          
          // 检查是否是网络请求相关的错误
          if (reason && 
              (reason.message && reason.message.includes('NetworkError') || 
               reason.message && reason.message.includes('Failed to fetch') ||
               reason.name === 'TypeError' && reason.message.includes('Failed to load'))) {
            debugLog('检测到未处理的Promise错误，可能是CDN资源加载失败:', reason);
          }
        });
        
        // DOMContentLoaded事件，检查所有已加载的脚本是否正常工作
        document.addEventListener('DOMContentLoaded', function() {
          debugLog('页面加载完成，检查CDN资源');
          
          // 在这里可以添加检查关键库是否成功加载的逻辑
          setTimeout(function() {
            if (typeof Vue === 'undefined' && document.querySelector('script[src*="vue"]')) {
              console.warn('[CDN Fallback] Vue 库似乎未成功加载，尝试重新加载');
              loadScriptWithFallback('vue', '3.3.4', 'dist/vue.global.prod.js');
            }
            
            if (typeof ElementPlus === 'undefined' && document.querySelector('script[src*="element-plus"]')) {
              console.warn('[CDN Fallback] ElementPlus 库似乎未成功加载，尝试重新加载');
              loadScriptWithFallback('element-plus', '2.7.4', 'dist/index.full.min.js');
            }
            
            if (typeof pdfMake === 'undefined' && document.querySelector('script[src*="pdfmake"]')) {
              console.warn('[CDN Fallback] pdfMake 库似乎未成功加载，尝试重新加载');
              loadScriptWithFallback('pdfmake', '0.2.14', 'build/pdfmake.min.js');
            }
          }, 1000);
        });
      })();
      `
      
      // 将脚本注入到HTML的头部
      return {
        html,
        tags: [
          {
            tag: 'script',
            attrs: {
              type: 'text/javascript'
            },
            children: scriptContent,
            injectTo: 'head-prepend'
          }
        ]
      }
    }
  }
} 