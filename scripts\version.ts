import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const packagePath = path.resolve(__dirname, '../package.json')
const versionPath = path.resolve(__dirname, '../public/version.json')

// 读取 package.json
const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'))

// 解析当前版本号
const [major, minor, patch] = pkg.version.split('.').map(Number)

// 增加补丁版本号
const newVersion = `${major}.${minor}.${patch + 1}`

// 更新 package.json
pkg.version = newVersion
fs.writeFileSync(packagePath, JSON.stringify(pkg, null, 2))

// 更新 version.json
fs.writeFileSync(versionPath, JSON.stringify({ version: newVersion }, null, 2))

console.log(`Version bumped to ${newVersion}`) 