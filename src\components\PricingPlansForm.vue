<template>
  <div :class="$style['container']">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center">
        <h2 class="text-4xl font-extrabold bg-linear-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
          {{ $t("setting.plans.title") }}
        </h2>
        <p class="mt-6 text-xl text-gray-600 max-w-2xl mx-auto">
          {{
            $t("setting.plans.currentPlanInfo", {
              plan: currentPlan?.toUpperCase(),
            })
          }}
          <span
            class="text-blue-600 hover:text-blue-500 cursor-pointer font-medium ml-1 transition-colors duration-300"
            @click="showEmailInput">
            {{ $t("setting.plans.contactUs") }}
          </span>
        </p>

        <!-- Global Billing Period Toggle -->
        <div class="mt-8 flex justify-center items-center space-x-4">
          <span :class="['text-sm font-medium transition-colors duration-300', 
                         selectedBillingPeriod === 'monthly' ? 'text-blue-600' : 'text-gray-500']">
            {{ $t("setting.plans.billingPeriod.monthlyType") }}
          </span>
          <button 
            class="relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-blue-600 focus:ring-offset-2"
            :class="selectedBillingPeriod === 'annually' ? 'bg-blue-600' : 'bg-gray-200'"
            @click="toggleBillingPeriod"
            role="switch"
            :aria-checked="selectedBillingPeriod === 'annually'">
            <span 
              class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out"
              :class="selectedBillingPeriod === 'annually' ? 'translate-x-5' : 'translate-x-0'"/>
          </button>
          <span :class="['text-sm font-medium transition-colors duration-300', 
                         selectedBillingPeriod === 'annually' ? 'text-blue-600' : 'text-gray-500']">
            {{ $t("setting.plans.billingPeriod.annuallyType") }}
          </span>
        </div>

        <!-- Email Input Section -->
        <div v-if="isCollectingEmail" class="mt-8 max-w-xl mx-auto">
          <ContactUsEmailInput
            :email="email"
            :loading="isSubmitting"
            :placeholder="$t('setting.plans.emailPlaceholder')"
            @update:email="email = $event"
            @submit="handleEmailSubmit"
            @cancel="isCollectingEmail = false"/>
        </div>
      </div>

      <!-- Plans Grid -->
      <div class="mt-16 grid grid-cols-1 gap-10 sm:grid-cols-1 lg:grid-cols-3" :class="$style['plans-grid']">
        <PlanCard
          v-for="plan in plans"
          :key="plan.type"
          :plan="plan"
          :current-plan="currentPlan"
          :selected-billing-period="selectedBillingPeriod"
          @select="handlePlanSelect"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { PlanType, PlanConfig } from '@/pages/settings/plans/type'
import ContactUsEmailInput from '@/components/ContactUsEmailInput.vue'
import { usePost } from '@/hooks/useHttp'
import PlanCard from '@/components/PlanCard.vue'

const { execute: addContact, isLoading:isSubmitting } = usePost('/contact/add', {}, {}, {
  immediate: false,
})
const { t } = useI18n()

const props = defineProps<{
  currentPlan: string;
  plans: PlanConfig[];
}>()

const emit = defineEmits<{
  'update:plan': [newPlan: { type: PlanType; billingPeriod: 'monthly' | 'yearly' }];
}>()

const selectedBillingPeriod = ref<'monthly' | 'annually'>('monthly')
const isCollectingEmail = ref(false)
const email = ref('')

const toggleBillingPeriod = () => {
  selectedBillingPeriod.value = selectedBillingPeriod.value === 'monthly' ? 'annually' : 'monthly'
  // Update all plans' billing period
  props.plans.forEach(plan => {
    if (plan.type !== 'free') {
      plan.billingPeriod = selectedBillingPeriod.value as 'monthly' | 'yearly'
    }
  })
}

const showEmailInput = () => {
  isCollectingEmail.value = true
}

const handleEmailSubmit = async (submittedEmail: string) => {
  try {
    await addContact(0, { contact: submittedEmail })
    ElMessage.success(t('global.contactUsSuccess'))
    isCollectingEmail.value = false
    email.value = ''
  } catch (error) {
    console.error('Submission error:', error)
  } 
}

const handlePlanSelect = (plan: PlanConfig) => {
  emit('update:plan', {
    type: plan.type as PlanType,
    billingPeriod: selectedBillingPeriod.value as 'monthly' | 'yearly',
  })
}

</script>

<style lang="scss" module>
.container {
}

.corner-ribbon {
  position: absolute; 
  color: white; 
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  background: linear-gradient(135deg, #4466BC, #60A5FA);
  padding: 8px 40px;
  right: -35px;
  top: 20px;
  transform: rotate(45deg);
  transform-origin: center;
  text-align: center;
  width: 140px;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  overflow: hidden;
  z-index: 1;
  
  &::before {
    display: none;
  }
}

.plans-grid > div {
  overflow: hidden;
  position: relative;
}
</style>

