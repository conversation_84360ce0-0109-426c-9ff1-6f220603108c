# ---------- 构建阶段 ----------
FROM node:20-alpine AS build

WORKDIR /app

# 分开 COPY，提高缓存命中率
COPY package.json pnpm-lock.yaml ./
COPY patches ./patches

# 安装 pnpm 和依赖（提前缓存依赖层）
RUN npm install -g pnpm && pnpm install

# 再拷贝源码（不会影响上一步缓存）
COPY . .

# 构建参数（dev 或 pd）
ARG BUILD_ENV=dev

# 注入动态环境变量（优化见前说明）
RUN if [ "$BUILD_ENV" = "pd" ]; then \
      sed -i 's|^SERVER_DOMAIN=.*|SERVER_DOMAIN=specific-ai.com|' .env && \
      sed -i 's|^VITE_APP_ENV=.*|VITE_APP_ENV=production|' .env ; \
    else \
      sed -i 's|^SERVER_DOMAIN=.*|SERVER_DOMAIN=dev-specific-ai.com|' .env && \
      sed -i 's|^VITE_APP_ENV=.*|VITE_APP_ENV=development|' .env ; \
    fi && \
    sed -i 's|^VITE_API_URL=.*|VITE_API_URL=https://${SERVER_DOMAIN}|' .env && \
    sed -i 's|^VITE_SERVER_API_URL=.*|VITE_SERVER_API_URL=https://${SERVER_DOMAIN}/api|' .env && \
    sed -i 's|^VITE_WS_SERVER_URL=.*|VITE_WS_SERVER_URL=wss://${SERVER_DOMAIN}|' .env && \
    sed -i 's|^VITE_APP_CDN_URL=.*|VITE_APP_CDN_URL=https://${SERVER_DOMAIN}/dist|' .env

# 启用最大内存限制，加速构建
ENV NODE_OPTIONS=--max-old-space-size=4096

# ⚡ 构建产物
RUN pnpm run build-only

# ---------- 生产镜像 ----------
FROM nginx:alpine AS production

COPY --from=build /app/dist /usr/share/nginx/html

EXPOSE 10086
CMD ["nginx", "-g", "daemon off;"]
