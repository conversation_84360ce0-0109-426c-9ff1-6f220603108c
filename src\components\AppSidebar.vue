<script setup lang="ts">
import type { SidebarProps } from '@/components/ui/sidebar'

import NavMain from '@/components/NavMain.vue'
import NavUser from '@/components/NavUser.vue'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  useSidebar,
  SidebarTrigger
} from '@/components/ui/sidebar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import {
  SquareKanban,
  BookOpen,
  Bot,
  Radar,
  File,
  Settings2,
  Users,
  Building,
  User,
  Languages,
  ScanEye 
} from 'lucide-vue-next'
import { Icon } from '@iconify/vue'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { computed, watch, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router/auto'
// import { USER_TYPE_MAP, UserType } from '@/stores/user' // 不再需要，权限管理已统一
import { usePermissions } from '@/composables/usePermissions'
import logo from '@/assets/logo.svg'
import smallLogo from '@/assets/images/sso/small-logo.svg'
import { menuItems, type MenuItem } from '@/config/menu.config'
import { SUPPORTED_LANGUAGES } from '@/types/language'
import type { Language } from '@/types/language'
import { useLanguageSwitch } from '@/composables/useLanguageSwitch'
import { useNetwork } from '@vueuse/core'

const { isOnline, downlink } = useNetwork()

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
})

const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const sidebar = useSidebar()
const { currentLocale, switchLanguage } = useLanguageSwitch()
const { hasPermission: hasUnifiedPermission, canAccess, getAvailableMenuItems, getSettingsMenuItems } = usePermissions()

// 语言选项配置，包含图标
const languageOptions = [
  {
    value: 'chinese' as Language,
    label: '简体中文',
    icon: 'emojione:flag-for-china'
  },
  {
    value: 'english' as Language,
    label: 'English',
    icon: 'emojione:flag-for-united-states'
  },
  {
    value: 'japanese' as Language,
    label: '日本語',
    icon: 'emojione:flag-for-japan'
  }
]

// 当前选中的语言
const currentLanguage = computed<Language>(() => 
  userStore.userInfo?.language || currentLocale.value as Language
)

// 获取当前语言的图标
const getCurrentLanguageIcon = computed(() => {
  return languageOptions.find(lang => lang.value === currentLanguage.value)?.icon
})

// 切换语言
const changeLanguage = async (lang: Language) => {
  if (lang === currentLanguage.value) {return}
  
  try {
    await switchLanguage(lang, async (language) => {
      await userStore.changeUserLanguage(language)
    })
  } catch (error) {
    console.error('Language switch failed:', error)
  }
}

// 监听侧边栏状态变化
const isCollapsed = computed(() => sidebar.state.value === 'collapsed')

// 检查当前路径是否匹配某个菜单项
const isActive = (path: string, fullPath: boolean = true) => {
  if (fullPath) {
    console.log('fullPath', route.path, path, route.path === path)
    return route.path === path
  }
  return route.path.startsWith(path + '/')
}

// 注意：权限检查现在统一由 usePermissions composable 管理

// Logo点击事件
const onClickLogo = () => {
  router.push('/')
}

// 导航到特定页面
const navigateTo = (url: string) => {
  if (url.startsWith('#')) { return }
  router.push(url)
}

// 创建settings菜单项 - 使用统一权限管理
const settingsItems = computed(() => {
  const unifiedItems = getSettingsMenuItems()

  // 将统一权限配置转换为组件需要的格式
  return unifiedItems.map(item => ({
    title: item.title,
    url: item.url,
    icon: item.url === '/settings' ? Users :
      item.url === '/settings/company-info' ? Building : User,
    isActive: isActive(item.url),
  }))
})
const MENU_ICON_MAP = {
  '/daily-report': BookOpen,
  '/format-report': File,
  '/robot': Bot,
  '/dashboard': SquareKanban,
  '/procurement': Radar,    
  '/information': Bot,
  '/workspace': ScanEye,
}
// 创建header菜单项 (一级菜单)
const headerMenuItems = computed(() => {
  // 使用统一权限管理过滤菜单项
  const availableMenuItems = getAvailableMenuItems(menuItems)

  const transformedItems = availableMenuItems
    .map(item => {
      return {
        title: t(item.labelKey),
        url: item.route,
        icon: MENU_ICON_MAP[item.route as keyof typeof MENU_ICON_MAP],
        isActive: route.path === item.route || route.path.startsWith(`${item.route}/`),
        priority: item.priority === undefined ? Infinity : item.priority
      }
    })
    .sort((a, b) => a.priority - b.priority)

  // 添加机器人市场菜单项
  const agentMarketItem = {
    title: t('global.agentMarket'),
    url: '/agent-market',
    icon: Bot,
    isActive: isActive('/agent-market'),
  }

  return [...transformedItems, agentMarketItem]
})

// settings菜单项 (有子菜单)
const settingsMenu = computed(() => {
  return {
    title: t('setting.menuTitle'),
    url: '/settings/personal-info',
    icon: Settings2,
    isActive: route.path.startsWith('/settings'),
    items: settingsItems.value,
  }
})

// 用户数据
const user = {
  name: userStore.userInfo?.user_name || 'User',
  email: userStore.userInfo?.email || '<EMAIL>',
  avatar: '/avatars/user.jpg',
}

const getCurrentLanguageLabel = () => {
  const lang = languageOptions.find(l => l.value === currentLanguage.value)
  return lang?.label || currentLanguage.value
}

// 网速状态计算
const networkStatus = computed(() => {
  if (!isOnline.value) {
    return {
      status: 'offline',
      label: t('global.networkOffline'),
      color: 'bg-red-500',
      dotClass: 'bg-red-500'
    }
  }
  
  const speed = downlink.value
  
  if (speed === undefined || speed < 1) {
    return {
      status: 'slow',
      label: t('global.networkSlow'),
      color: 'bg-orange-500',
      dotClass: 'bg-orange-500'
    }
  }
  
  if (speed >= 1 && speed < 10) {
    return {
      status: 'medium',
      label: t('global.networkMedium'),
      color: 'bg-yellow-500',
      dotClass: 'bg-yellow-500'
    }
  }
  
  return {
    status: 'fast',
    label: t('global.networkFast'),
    color: 'bg-green-500',
    dotClass: 'bg-green-500'
  }
})

// 获取网速数值显示
const getNetworkSpeedText = computed(() => {
  if (!isOnline.value) {
    return t('global.networkOffline')
  }
  
  if (downlink.value === undefined) {
    return t('global.networkUnknown')
  }
  
  return `${downlink.value.toFixed(1)} Mbps`
})
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <div class="flex items-center cursor-pointer justify-start" :class="{'p-2':!isCollapsed}" @click="onClickLogo">
        <img v-if="isCollapsed" 
             :src="smallLogo" 
             class="w-[30px] h-[30px]" 
             alt="Small Logo"/>
        <img v-else 
             :src="logo" 
             class="w-[150px] h-[30px]" 
             alt="Logo"/>
      </div>
    </SidebarHeader>
    <SidebarContent>
      <!-- Header菜单项 - 作为一级菜单直接渲染 -->
      <SidebarGroup>
        <SidebarGroupLabel>{{$t('global.mainMenu')}}</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem v-for="item in headerMenuItems" :key="item.title">
            <SidebarMenuButton @click="navigateTo(item.url)"
                               :data-active="item.isActive"
                               :class="$style.menuButton"
                               class="cursor-pointer">
              <component :is="item.icon"/>
              <span>{{ item.title }}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>
      
      <!-- Settings菜单 - 使用NavMain来渲染，因为它有子菜单 -->
      <NavMain :items="[settingsMenu]" :title="$t('global.settingMenu')"/>
    </SidebarContent>
    <SidebarFooter>
      <!-- 网速状态显示 -->
      <SidebarMenu class="mb-2">
        <SidebarMenuItem>
          <SidebarMenuButton 
            class="w-full pointer-events-none"
            :class="$style.networkStatusButton">
            <div class="flex items-center">
              <!-- 网速状态圆点 -->
              <div 
                class="w-2 h-2 rounded-full mr-2 flex-shrink-0"
                :class="networkStatus.dotClass">
              </div>
              <!-- 网速状态文字 -->
              <div v-if="!isCollapsed" class="flex">
                <!-- <span class="text-xs font-medium">{{ networkStatus.label }}</span> -->
                <span class="text-xs text-muted-foreground">{{ getNetworkSpeedText }}</span>
              </div>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
      
      <!-- 语言切换菜单 -->
      <SidebarMenu class="mb-2">
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger as-child class="cursor-pointer">
              <SidebarMenuButton class="w-full hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                <div class="flex items-center">
                  <Icon v-if="getCurrentLanguageIcon" :icon="getCurrentLanguageIcon" class="w-4 h-4 mr-2"/>
                  <Languages v-else class="w-4 h-4 mr-2"/>
                  <span v-if="!isCollapsed">{{ getCurrentLanguageLabel() }}</span>
                </div>
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" class="w-[--reka-dropdown-menu-trigger-width]">
              <DropdownMenuItem 
                v-for="lang in languageOptions" 
                :key="lang.value"
                @click="changeLanguage(lang.value)"
                :class="{ [$style.activeLanguage]: lang.value === currentLanguage }">
                <div class="flex items-center cursor-pointer">
                  <Icon :icon="lang.icon" class="mr-2"/>
                  {{ lang.label }}
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
      
      <!-- 用户菜单 -->
      <NavUser :user="user"/>
    </SidebarFooter>
    <SidebarRail/>
  </Sidebar>
</template>

<style lang="scss" module>
.menuButton {
  &[data-active="true"] {
    color: var(--primary-color) !important;
    background-color: var(--brand-color-1) !important;
  }
}

.activeLanguage {
  color: var(--primary-color) !important;
  font-weight: 500;
  background-color: var(--brand-color-1) !important;
}

.networkStatusButton {
  background-color: var(--brand-color-1) !important;
  opacity: 0.8;
  cursor: default;
  
  &:hover {
    background-color: var(--brand-color-1) !important;
    opacity: 1;
  }
}
</style>
