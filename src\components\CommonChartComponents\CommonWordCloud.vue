<template>
  <EchartsComponent :params="getChartOption" :auto-size="true" data-pdf-chart/>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import EchartsComponent from '@/components/EchartsComponent.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface WordCloudData {
  name: string
  value: number
  textStyle?: Record<string, any>
}

interface Props {
  data: WordCloudData[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ([])
})

// 颜色配置
const COLORS = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B']

// 图表配置
const getChartOption = computed(() => {
  const data = props.data
  return {
    title: {
      text: t('charts.worldCloud.title'),
      left: 'left',
      top: 0,
      textStyle: {
        color: '#374151',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      appendToBody: true,
      formatter: (params: any) => {
        return `${params.name}: ${params.value}`
      }
    },
    series: [{
      type: 'wordCloud',
      shape: 'circle',
      left: 'center',
      top: 'center',
      width: '70%',
      height: '80%',
      right: null,
      bottom: null,
      sizeRange: [12, 60],
      rotationRange: [-90, 90],
      rotationStep: 45,
      gridSize: 8,
      drawOutOfBound: false,
      layoutAnimation: true,
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        color: () => {
          return COLORS[Math.floor(Math.random() * COLORS.length)] + 'CC'
        }
      },
      emphasis: {
        focus: 'self',
        textStyle: {
          textShadowBlur: 10,
          textShadowColor: '#333'
        }
      },
      data: data.sort((a, b) => b.value - a.value)
    }]
  }
})
</script> 