import dayjs from 'dayjs'
// #if BUILD_REG === 'INTL'
import 'dayjs/locale/en'
dayjs.locale('en')
// #else
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
// #endif
import isToday from 'dayjs/plugin/isToday'
import isYesterday from 'dayjs/plugin/isYesterday'
import calendar from 'dayjs/plugin/calendar'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import updateLocale from 'dayjs/plugin/updateLocale'
import isBetween from 'dayjs/plugin/isBetween'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { getTimezoneOffset } from 'date-fns-tz'

dayjs.extend(isYesterday)
dayjs.extend(isToday)
dayjs.extend(calendar)
dayjs.extend(localizedFormat)
dayjs.extend(updateLocale)
dayjs.extend(isBetween)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(quarterOfYear)
dayjs.extend(weekOfYear)
dayjs.extend((option, c, d) => {
  const { calendar } = c.prototype
  if (!calendar) {
    return
  }
  c.prototype.calendar = function (referenceTime, formats) {
    const { calendar: locale = {}, name: language } = this.$locale()
    const isEn = language === 'en'
    const f = {
      lastDay: isEn ? '[yesterday]' : '[yesterday] HH:mm',
      sameDay: isEn ? '[today]' : '[today] HH:mm',
      nextDay: isEn ? '[tomorrow]' : '[tomorrow] HH:mm',
      nextWeek: '[sameElse]',
      lastWeek: '[sameElse]',
      sameYear: isEn ? 'MMM D' : 'MM-DD HH:mm',
      sameElse: isEn ? 'MMM D YYYY' : 'YYYY-MM-DD HH:mm',
      ...locale,
      ...formats,
    }
    const ff = {
      ...f,
      lastDay: f.lastDay.replace('[yesterday]', `[${locale.yesterday || '昨天'}]`),
      sameDay: f.sameDay.replace('[today]', `[${locale.today || '今天'}]`),
      nextDay: f.nextDay.replace('[tomorrow]', `[${locale.tomorrow || '明天'}]`),
      sameElse: '[sameElse]',
    }
    const text = Reflect.apply(calendar, this, [referenceTime, ff])

    if (text === 'sameElse') {
      const currentFormat = this.year() === d().year() ? f.sameYear : f.sameElse || 'YYYY-MM-DD HH:mm'
      if (typeof currentFormat === 'function') {
        return currentFormat.call(this, d())
      }

      return this.format(currentFormat)
    }
    return text
  }
})
dayjs.extend((option, c, d) => {
  const { format } = c.prototype
  if (!format) {
    return
  }
  d.militaryTime = (enable?: boolean) => {
    if (typeof enable === 'boolean') {d.$militaryTime = enable}
    if (typeof d.$militaryTime === 'boolean') {return d.$militaryTime}
    return true
  }
  c.prototype.militaryTime = function (enable) {
    if (typeof enable === 'boolean') {
      this.$militaryTime = enable
    }
    if (typeof this.$militaryTime === 'boolean') {
      return this.$militaryTime
    }
    return d.militaryTime(enable)
  }
  const getFormat = (formatStr: string, isMilitaryTime: boolean) => {
    // 把LTS替换为24小时制
    if (/LTS?/g.test(formatStr) && isMilitaryTime) {
      const fm = {
        LT: 'H:mm',
        LTS: 'H:mm:ss',
      }
      return formatStr.replace(/(\[[^\]]+])|(LTS?)/g, (_, a, b) => a || fm[b as keyof typeof fm] || b)
    }
    // hh:mm:ss 替换
    if (/[hH]{1,2}:m{1,2}(:s{1,2})?(.*([aA]))?/g.test(formatStr)) {
      return formatStr.replace(/(\[[^\]]+])|([hH]{1,2}:m{1,2}(:s{1,2})?(.*([aA]))?)/g, (_, a, b) => {
        if (a)
        {return a}

        if (isMilitaryTime) {
          b = b.replace(/(\[[^\]]+])|(\s[aA])/g, (_: string, a: string) => a || '')
          return b.replace(/(\[[^\]]+])|(h{1,2})/g, (_: string, a: string, b: string) => a || b.toUpperCase())
        }
        b = b.replace(/(\[[^\]]+])|(H{1,2})/g, (_: string, a: string) => a || 'h')
        return `${b} a`
      })
    }
    // hh 替换
    if (/[hH]{1,2}/g.test(formatStr))
    {return formatStr.replace(/(\[[^\]]+])|([hH]{1,2})/g, (_, a, b) => a || (isMilitaryTime ? b.toUpperCase() : b.toLowerCase()))}

    return formatStr
  }
  c.prototype.format = function (formatStr) {
    if (formatStr) {
      const isMilitaryTime = this.militaryTime()
      formatStr = getFormat(formatStr, isMilitaryTime)
    }
    return format.call(this, formatStr)
  }
})

dayjs.updateLocale('zh-cn', {
  weekStart: 1,
  formats: {
    LT: 'h:mm A',
    LTS: 'h:mm:ss A',
    L: 'YYYY/MM/DD',
    LL: 'YYYY年M月D日',
    LLL: 'YYYY年M月D日Ah点mm分 A',
    LLLL: 'YYYY年M月D日ddddAh点mm分 A',
    l: 'YYYY/M/D',
    ll: 'YYYY年M月D日',
    lll: 'YYYY年M月D日 HH:mm A',
    llll: 'YYYY年M月D日dddd HH:mm A',
  },
})

dayjs.updateLocale('en', {
  calendar: {
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
  },
})

const cache_localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
const getLocalTimeZone = (): string => cache_localTimezone
const cache_format = dayjs.prototype.format

/**
 * 日期格式化模板过滤
 * 注：为了统一适配dayjs和element-ui的日期时间组件以及date-fns的 格式化占位符，在这里做一个转化
 */
const templateReplaceSet = {
  yyyy: 'YYYY',
  yy: 'YY',
  ddd: 'ddd',
  dd: 'DD',
  d: 'D',
  EEE: 'ddd',
  EEEE: 'dddd',
  Today: 'Today',
  Yesterday: 'Yesterday',
  Tomorrow: 'Tomorrow',
}
function templateFormat(date: string) {
  /* res = /(yyyy|yy|ddd|dd|d|EEE|EEEE|(,-))/g */
  const reg = new RegExp(`(${Object.keys(templateReplaceSet).map(i => i).join('|')}|(,-))`, 'g')
  return date.replace(reg, item => templateReplaceSet[item as keyof typeof templateReplaceSet])
}

dayjs.extend((option, dayjsClass, dayjsFactory) => {
  let cache_local_org_timezone_offset: number | null = null

  dayjsClass.prototype.format = function (template) {
    const toFormat = cache_format.bind(this)
    return toFormat(templateFormat(template || ''))
  }

  dayjsClass.prototype.getLocalOrgTimeZoneOffset = function () {
    // 因为组织时区返回了 GTM+8:00，getTimezoneOffset 里面的参数需要是 Asia/Shanghai 或者 +8:00 这种
    // 注意 同样的，目前假设用户不会在使用网站的过程中切换时区和修改组织时区
    if (cache_local_org_timezone_offset === null)
    {cache_local_org_timezone_offset = -getTimezoneOffset(getLocalTimeZone(), this.toDate()) + getTimezoneOffset(dayjsFactory.orgTimeZone.replace('GMT', ''), this.toDate())}

    return cache_local_org_timezone_offset
  }

  dayjsClass.prototype.moveToOrgTimeZone = function () {
    return dayjsFactory(this.getTime() - this.getLocalOrgTimeZoneOffset())
  }

  dayjsClass.prototype.moveToLocalTimeZone = function () {
    return dayjsFactory(this.getTime() + this.getLocalOrgTimeZoneOffset())
  }

  dayjsClass.prototype.getTime = function () {
    return this.valueOf()
  }

  dayjsFactory.forOrgTimeZone = function (...args: any[]) {
    return dayjsFactory(...args).tz(dayjsFactory.orgTimeZone)
  }

  dayjsFactory.orgTimeZone = 'Asia/Shanghai'
})
