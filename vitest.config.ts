import { fileURLToPath } from 'node:url'
import { mergeConfig, defineConfig, configDefaults } from 'vitest/config'
import viteConfig from './vite.config'

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./tests/setup.ts'],
      include: ['src/**/*.{test,spec}.{js,ts}'],
      coverage: {
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: './coverage',
        include: ['src/**/*.{vue,js,ts}'],
        exclude: [
          'node_modules/',
          '**/*.d.ts',
          '**/types/**',
          '**/*.config.{js,ts}',
          '**/mock/**',
          '**/assets/**',
          'src/vue-plugins/sentry/**',
          '**/__tests__/**',
          '**/*.{test,spec}.{js,ts}'
        ]
      }
    }
  })
)
