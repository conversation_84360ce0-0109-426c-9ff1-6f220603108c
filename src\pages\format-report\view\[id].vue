<template>
  <FormatReportDetail
    :is-loading="isReportLoading"
    :report-data="outlineData"
    :status="status"
    :is-reconnecting="false"
    :retry-count="0"
    :highlighted-section="highlightedSection || ''"
    :search-results-list="reportDetail?.search_result || []"
    :show-regenerate-button="false"
    :timeline-data="timelineData"
    :legal-timeline-data="legalTimelineData!"
    :charts="charts"
    @back="clickBack"
    @section-change="handleSectionChange">
    <div :class="$style['report-content']">
      <template v-for="section in reportSections" :key="section.title">
        <MarkdownDisplay
          :content="section.content"
          :title="section.title"
          :type="section.title"
          :hide-divider="false"/>
      </template>
    </div>
  </FormatReportDetail>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router/auto'
import { definePage } from 'vue-router/auto'
import type { RouteParams } from 'vue-router/auto'
import MarkdownDisplay from '@/components/MarkdownComponents/MarkdownDisplay.vue'
import FormatReportDetail from '@/components/FormatReportDetail.vue'
import { usePost } from '@/hooks/useHttp'
import type { CreatedReport } from '../type'
import type { SSEMessage } from '@/hooks/useEnhancedEventSource'

type RouteParamsWithId = RouteParams<'/format-report/view/[id]'>;

definePage({
  meta: {
    layout: false,
    requiresAuth: true,
  },
})

const { state: reportDetail, execute: refreshReport, isLoading: isReportLoading } = usePost<CreatedReport>(
  '/formatted_report/output_standard_report',
  {},
  {},
  {
    immediate: false,
  }
)


// 基础状态
const route = useRoute()
const router = useRouter()

const status = ref('CLOSED')
const highlightedSection = ref<string | null>(null)
const timelineData = computed(()=> reportDetail.value?.time_line || [])
// const normalSearchResults = computed(()=> reportDetail.value?.search_result?.news_data || null)
const legalTimelineData = computed(()=> reportDetail.value?.legal_data || null)
const charts = computed(() => reportDetail.value?.chart_data?.map((item, index) => ({
  id: `chart-${index}`,
  type: item.type as SSEMessage['chart_type'],
  gridSpan: (['bar', 'word_could'].includes(item.type) ? 3 : 1) as 1 | 2 | 3 | undefined,
  data: item.type === 'line' ? item.data.data : item.data
})) || [])
const handleSectionChange = (sectionKey: string) => {
  highlightedSection.value = sectionKey
}

interface SectionData {
  title: string
  content: string
}

// 处理报告章节
const reportSections = computed<SectionData[]>(() => {
  if (!reportDetail.value) {
    return []
  }

  const reportOutline = outlineData.value?.report_outline
  const sortedReportOutline = Object.keys(reportOutline)

  return sortedReportOutline.map((item) => ({
    title: item,
    content: reportDetail.value?.[item] || '',
  }))
})

// 基础导航
const clickBack = () => {
  sessionStorage.removeItem(`created-report-${(route.params as RouteParamsWithId).id}`)
  const to = router.resolve('/format-report')
  // const to = router.resolve('/dashboard')
  router.push(to)
}


const outlineData = computed(() => {
  const reportId = (route.params as RouteParamsWithId).id
  const report = reportDetail.value
  
  if (!report?.report_outline) {
    return {
      report_id: reportId,
      report_title: report?.report_name ?? '',
      report_outline: {},
      report_type: report?.report_type,
      company_name: report?.company_name
    }
  }

  const outlineKeys = Object.keys(report.report_outline)
  return {
    report_id: reportId,
    report_title: report.report_name,
    report_outline: outlineKeys.reduce<Record<string, any>>((acc, key) => {
      if (key in report) {
        acc[key] = report[key as keyof typeof report]
      }
      return acc
    }, {}),
    report_type: report?.report_type,
    company_name: report?.company_name
  }
})

onMounted(async () => {
  await refreshReport(0, {
    id: (route.params as RouteParamsWithId).id,
  })
})

</script>

<style lang="scss" module>
.report-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  :global(.markdown-content) {
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-all;

    :global(.markdown-body) {
      max-width: 100%;
      line-height: 1.6;
      font-size: 16px;

      p,
      div {
        white-space: pre-wrap;
        max-width: 100%;
      }
    }
  }
}
</style>
