<template>
  <!-- 简化的扁平结构 -->
  <div class="h-full flex flex-col">
    <!-- 头部 -->
    <div class="border-b border-slate-100 flex-shrink-0 pb-2 px-2">
      <div class="flex items-center justify-end">
        <!-- <div class="flex items-center gap-2">
          <ActivityIcon class="h-4 w-4 text-violet-500"/>
          <h2 class="text-lg font-semibold text-slate-800">{{ t('page.report_chat.feed_title') }}</h2>
        </div> -->
        <div class="flex items-center gap-2 justify-end">
          <!-- 排序切换按钮 -->
          <Button
            variant="outline"
            size="sm"
            @click="toggleSortOrder"
            class="bg-white/80 shadow-sm text-slate-600 border-slate-200 hover:bg-slate-50 data-[state=active]:bg-slate-100 transition-all gap-1.5 min-w-[100px] px-2 py-1 h-7 rounded-full text-xs font-medium">
            <span>{{ getSortLabel() }}</span>
            <component 
              :is="sortOrder === 'desc' ? ArrowDownIcon : ArrowUpIcon" 
              class="h-3 w-3"/>
          </Button>
          
          <Badge variant="outline" class="bg-white/80 shadow-sm text-slate-600 border-slate-200">
            <!-- TODO: Calculate report count based on groupedReports -->
            {{ t('page.report_chat.report_count', { count: totalReportCount }) }}
          </Badge>
          <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4 flex-shrink-0">
            <span 
              class="flex items-center gap-2 text-sm text-red-600 hover:underline cursor-pointer"
              @click="emit('show-risk-companies-drawer')">
              <Icon icon="mdi:alert-circle-outline" class="w-4 h-4"/>
              <span>{{ t("agent.view_all_risk_companies") }}</span>
              <!-- <Icon icon="mdi:arrow-right" class="w-4 h-4"/> -->
            </span>
          </div> 
        </div>
      </div>
    </div>
            
    <!-- 内容区域 - 简化布局 -->
    <div class="flex-1 min-h-0">
      <!-- 空状态 -->
      <div v-if="totalReportCount === 0" class="flex flex-col items-center justify-center py-20 text-center px-4 h-full">
        <div class="bg-gradient-to-br from-slate-50 to-white p-6 rounded-full mb-4 shadow-md">
          <div class="relative">
            <MessageSquarePlusIcon class="h-12 w-12 text-violet-400"/>
            <BellRingIcon class="h-6 w-6 text-amber-400 absolute -top-1 -right-1"/>
          </div>
        </div>
        <h2 class="text-lg font-semibold mb-2 text-slate-800">{{ t('common.no_report') }}</h2>
        <p class="text-sm text-slate-500 mb-4 max-w-md">
          {{ t('common.no_reports_desc') }}
        </p>
        <Button @click="toMarket" class="flex gap-2 items-center bg-gradient-to-r from-violet-600 to-cyan-500 text-white shadow-md hover:shadow-lg transition-all">
          <PlusIcon class="h-4 w-4"/>
          {{ t('common.assign_new_task') }}
        </Button>
      </div>
                  
      <!-- DynamicScroller - 最简化实现 -->
      <DynamicScroller
        v-else
        ref="scrollerRef"
        :items="flatReportList"
        :min-item-size="120"
        key-field="id"
        class="h-full overflow-y-auto scrollbar-thin"
        v-slot="{ item, index, active }">
        <DynamicScrollerItem
          :item="item"
          :active="active"
          :data-index="index"
          :size-dependencies="[
            item, // Re-measure if the item object changes
            shouldShowDateSeparator(index, item) // Re-measure if separator appears/disappears
          ]"
          class="px-4 py-2">
          <template v-if="shouldShowDateSeparator(index, item)">
            <div class="relative flex items-center pb-4 pt-2"> 
              <div class="flex-grow border-t border-slate-100"></div>
              <span class="flex-shrink mx-4 text-sm font-medium text-slate-500 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full shadow-sm">
                {{ formatDateHeader(getReportDateString(item)) }}
              </span>
              <div class="flex-grow border-t border-slate-100"></div>
            </div>
          </template>

          <ReportItem
            :report="item"
            :officer-type="officerType"
            :officer-name="officerName"
            :agent-report-name="findDescriptionForReport(item).report_name"
            :agent-description="findDescriptionForReport(item).description"
            @view-report="emit('view-report', item)"
            @tagClick="handleTagClick"
            @delete-agent="emit('delete-agent', $event)"
            @refresh-agent="emit('refresh-agent', $event)"
            @request-change-time="emit('request-change-time', item)"/>
        </DynamicScrollerItem>
      </DynamicScroller>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router/auto'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import { 
  ActivityIcon, 
  MessageSquarePlusIcon, 
  BellRingIcon,
  PlusIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import ReportItem from './ReportItem.vue'
import type { Report, RunningAgent } from '../types'
import { isMonitorAgent, isRunningAgent, isStandardReport } from '../types'
import type { CompletedAgent } from '@/pages/dashboard/type'
import type { ReportList as StandardReport } from '@/pages/format-report/type'
import { SPECIAL_AGENT_TYPES } from '@/composables/useReportProgress'
import { Icon } from '@iconify/vue'
import { useStorage } from '@vueuse/core'

// --- Template Ref for Scroller ---
const scrollerRef = ref<InstanceType<typeof DynamicScroller> | null>(null)

// --- Simple Debounce Utility ---
function debounce(func: Function, wait: number) {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  return function(this: any, ...args: any[]) {
    const context = this
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      timeoutId = null
      func.apply(context, args)
    }, wait)
  }
}

// --- 滚动处理方法 ---
const scrollToTop = () => {
  console.log('Scrolling to top, scroller ref:', scrollerRef.value)
  if (scrollerRef.value) {
    scrollerRef.value.scrollToItem(0)
  }
}

const scrollToBottom = () => {
  console.log('Scrolling to bottom, scroller ref:', scrollerRef.value)
  if (scrollerRef.value) {
    scrollerRef.value.scrollToBottom()
  }
}

// --- Debounced Scroll Function ---
const debouncedScrollToBottom = debounce(() => {
  console.log('Attempting to scroll to bottom (debounced). Scroller ref:', scrollerRef.value)
  if (scrollerRef.value) {
    scrollerRef.value.scrollToBottom()
  }
}, 500)

// --- Props ---
const props = defineProps<{
  groupedReports: Record<string, Report[]>;
  // 情报官类型和名称
  officerType?: 'notify' | 'normal' | 'binding';
  officerName?: string;
  // 新增：用于存储 agent 描述信息的数组
  agentDescriptions?: Array<{
    report_type: string;
    description: string;
    report_name: string;
  }>;
}>()

// --- Router ---
const router = useRouter()
const toMarket = ()=>{
  router.push('/agent-market')
}
// --- Emits ---
const emit = defineEmits<{
  (e: 'view-report', report: Report): void;
  (e: 'tagClick', tagData: { report: Report, tag: string }): void;
  (e: 'show-risk-companies-drawer'): void;
  (e: 'request-change-time', report: Report): void;
  (e: 'delete-agent', report: Report): void;
  (e: 'refresh-agent', report: Report): void;
}>()

// --- i18n ---
const { t } = useI18n()

// --- 排序状态管理 ---
type SortOrder = 'asc' | 'desc'
const sortOrder = useStorage<SortOrder>('report-feed-sort-order', 'desc')

// --- 排序切换方法 ---
const toggleSortOrder = () => {
  const newOrder = sortOrder.value === 'desc' ? 'asc' : 'desc'
  sortOrder.value = newOrder
  console.log('Sort order changed to:', newOrder) // 调试日志
}

// --- 排序相关辅助函数 ---
const getSortLabel = (): string => {
  return sortOrder.value === 'desc' ? t('common.newest_first') : t('common.oldest_first')
}

// --- Virtual List Setup ---

// 1. Flatten the grouped reports into a single list
const flatReportList = computed<Report[]>(() => {
  const reports = Object.values(props.groupedReports).flat()
  
  // 应用排序逻辑
  return reports.sort((a, b) => {
    // 特殊处理：Running 任务应该优先显示
    const isRunningA = isRunningAgent(a)
    const isRunningB = isRunningAgent(b)
    
    // 如果都是 running 或都不是 running，按正常时间排序
    if (isRunningA === isRunningB) {
      const dateA = getReportDateValue(a)
      const dateB = getReportDateValue(b)
      
      // 处理无效日期
      if (!dateA && !dateB) {
        return 0
      }
      if (!dateA) {
        return sortOrder.value === 'desc' ? 1 : -1
      }
      if (!dateB) {
        return sortOrder.value === 'desc' ? -1 : 1
      }
      
      try {
        const timeA = new Date(dateA).getTime()
        const timeB = new Date(dateB).getTime()
        
        // 检查日期是否有效
        if (isNaN(timeA) && isNaN(timeB)) {
          return 0
        }
        if (isNaN(timeA)) {
          return sortOrder.value === 'desc' ? 1 : -1
        }
        if (isNaN(timeB)) {
          return sortOrder.value === 'desc' ? -1 : 1
        }
        
        // 应用排序顺序
        if (sortOrder.value === 'desc') {
          return timeB - timeA // 降序：最新的在前
        }
        return timeA - timeB // 升序：最旧的在前
      } catch (error) {
        console.warn('Date comparison error:', error)
        return 0
      }
    }
    
    // Running 任务的优先级处理
    if (sortOrder.value === 'desc') {
      // 降序：Running 任务应该在最前面
      return isRunningA ? -1 : 1
    }
    // 升序：Running 任务应该在最后面（因为它们是"最新"的）
    return isRunningA ? 1 : -1
  })
})

// 2. Helper to get date string for comparison - using type guards
const getReportDateString = (report: Report | undefined | null): string => {
  if (!report) {
    return ''
  }

  let dateVal: string | number | Date | undefined | null = undefined

  if (isRunningAgent(report)) {
    // 对于 running 任务，如果没有有效的 date，使用当前时间
    dateVal = (report as RunningAgent).date || new Date().toISOString()
  } else if (isMonitorAgent(report) || isStandardReport(report)) {
    dateVal = (report as any).updated_at
  }
  
  // Fallback to current time for running tasks, 0 for others
  const finalDateVal = dateVal || (isRunningAgent(report) ? new Date().toISOString() : 0)

  try {
    if (finalDateVal === 0) {
      return ''
    }
    
    const date = new Date(finalDateVal)
    if (isNaN(date.getTime())) {
      // 对于 running 任务，如果日期无效，使用今天的日期
      if (isRunningAgent(report)) {
        return new Date().toLocaleDateString()
      }
      return ''
    }
    
    return date.toLocaleDateString()
  } catch (error) {
    console.warn('Date parsing error in getReportDateString:', error)
    // 对于 running 任务，发生错误时使用今天的日期
    if (isRunningAgent(report)) {
      return new Date().toLocaleDateString()
    }
    return ''
  }
}

// 新增：获取报告的实际日期值用于排序
const getReportDateValue = (report: Report | undefined | null): string => {
  if (!report) {
    return ''
  }

  let dateVal: string | number | Date | undefined | null = undefined

  if (isRunningAgent(report)) {
    // 对于 running 任务，如果没有有效的 date，使用当前时间
    dateVal = (report as RunningAgent).date || new Date().toISOString()
  } else if (isMonitorAgent(report) || isStandardReport(report)) {
    dateVal = (report as any).updated_at
  }

  // 返回标准化的 ISO 字符串用于排序
  const finalDateVal = dateVal || (isRunningAgent(report) ? new Date().toISOString() : 0)
  try {
    if (finalDateVal === 0 || isNaN(new Date(finalDateVal).getTime())) {
      return ''
    }
    return new Date(finalDateVal).toISOString()
  } catch {
    return ''
  }
}

// --- Computed Properties ---
// Keep totalReportCount based on the original flat list
const totalReportCount = computed(() => flatReportList.value.length)

// --- 处理标签点击事件 ---
const handleTagClick = (tagData: { report: Report, tag: string }) => {
  // 这里我们直接转发事件，让ReportItem处理跳转逻辑
  emit('tagClick', tagData)
}

// --- Helper Functions ---

function formatDateHeader(dateString: string): string {
  if (!dateString) {
    return t('common.date_today') // 如果没有日期字符串，默认显示今天
  }
  
  const today = new Date().toLocaleDateString()
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  const yesterdayString = yesterday.toLocaleDateString()
    
  if (dateString === today) { return t('common.date_today') }
  if (dateString === yesterdayString) { return t('common.date_yesterday') }
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) { 
      // 如果日期解析失败，尝试按照当前日期处理
      return t('common.date_today')
    }
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' }
    return date.toLocaleDateString(t('locale_code', 'zh-CN'), options)
  } catch (e) { 
    console.warn('Date formatting error:', e)
    return t('common.date_today') // 发生错误时，默认显示今天
  }
}

// Helper to determine if date separator is needed - make more robust
const shouldShowDateSeparator = (index: number, item: Report | undefined | null): boolean => {
  if (!item) {
    return false
  }
  
  if (index === 0) {
    return true
  }

  const prevItem = flatReportList.value?.[index - 1]
  if (!prevItem) {
    return true
  }

  const currentDateString = getReportDateString(item)
  const prevDateString = getReportDateString(prevItem)

  return currentDateString !== prevDateString
}

// --- 优雅的滚动控制 ---
const performSmoothScroll = (direction: 'top' | 'bottom', delay = 50) => {
  console.log(`Performing smooth scroll to ${direction} with delay ${delay}ms`)
  setTimeout(() => {
    nextTick(() => {
      if (direction === 'top') {
        scrollToTop()
      } else {
        scrollToBottom()
      }
    })
  }, delay)
}

// --- 监听排序变化 ---
watch(sortOrder, (newOrder) => {
  console.log(`Sort order changed to: ${newOrder}`)
  // 立即触发平滑滚动
  if (newOrder === 'desc') {
    performSmoothScroll('top', 50)
  } else {
    performSmoothScroll('bottom', 50)
  }
}, { flush: 'post' })

// --- 监听列表长度变化 ---
watch(() => flatReportList.value.length, (newLength, oldLength) => {
  // 只有当列表增长时才滚动
  if (newLength > (oldLength || 0)) {
    if (sortOrder.value === 'desc') {
      performSmoothScroll('top', 200)
    } else {
      performSmoothScroll('bottom', 200)
    }
  }
}, { flush: 'post' })

// --- 根据 report_type 查找对应的描述信息 ---
const findDescriptionForReport = (report: Report) => {
  if (!props.agentDescriptions || props.agentDescriptions.length === 0) {
    return { report_name: '', description: '' }
  }
  
  // 从 report 中获取 report_type
  const reportType = (report as any)?.report_type
  if (!reportType) {
    return { report_name: '', description: '' }
  }
  
  // 查找对应的描述信息
  const descInfo = props.agentDescriptions.find(desc => desc.report_type === reportType)
  return descInfo || { report_name: '', description: '' }
}

</script>

<style scoped>
/* Styles specific to ReportFeed can go here */
/* Ensure DynamicScroller has height */
.vue-recycle-scroller { /* Default class for the scroller */
  height: 100%;
}
</style> 