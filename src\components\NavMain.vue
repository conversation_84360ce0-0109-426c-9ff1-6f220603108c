<script setup lang="ts">
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import { ChevronRight, type LucideIcon } from 'lucide-vue-next'
import { useRouter, useRoute } from 'vue-router/auto'
import { computed } from 'vue'

const router = useRouter()
const route = useRoute()

const props = defineProps<{
  title:string,
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      isActive: boolean
    }[]
  }[]
}>()

const navigateTo = (url: string) => {
  if (url.startsWith('#')) { return }
  router.push(url)
}

// 检查子菜单项是否处于激活状态
const isSubItemActive = (url: string) => {
  return route.path === url || route.path.startsWith(`${url}/`)
}
</script>

<template>
  <SidebarGroup>
    <SidebarGroupLabel>{{title}}</SidebarGroupLabel>
    <SidebarMenu>
      <Collapsible
        v-for="item in items"
        :key="item.title"
        as-child
        :default-open="true"
        class="group/collapsible">
        <SidebarMenuItem>
          <CollapsibleTrigger as-child>
            <SidebarMenuButton 
              :tooltip="item.title" 
              :data-active="item.isActive" 
              :class="$style.menuButton">
              <component :is="item.icon" v-if="item.icon"/>
              <span>{{ item.title }}</span>
              <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"/>
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              <SidebarMenuSubItem v-for="subItem in item.items" :key="subItem.title">
                <SidebarMenuSubButton as-child>
                  <a 
                    @click.prevent="navigateTo(subItem.url)" 
                    href="javascript:void(0)" 
                    :class="[$style.subMenuItem, { [$style.activeSubMenuItem]: subItem.isActive }]">
                    <span>{{ subItem.title }}</span>
                  </a>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    </SidebarMenu>
  </SidebarGroup>
</template>

<style lang="scss" module>
.menuButton {
  &[data-active="true"] {
    color: var(--primary-color) !important;
    background-color: var(--brand-color-1) !important;
  }
}

.subMenuItem {
  &.activeSubMenuItem {
    color: var(--primary-color) !important;
    font-weight: 500;
  }
}
</style>
