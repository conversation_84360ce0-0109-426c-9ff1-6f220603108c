<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :class="$style.dialog"
    @open="openDialog"
    @close="closeDialog"
    v-loading="recommendLoading || isLoadingIndustries || isLoadingNations">
    <div v-if="reportIntroduce" :class="$style.introduction">
      <MarkdownContainer :content="reportIntroduce"/>
    </div>
    
    <!-- Excel Upload Section -->
    <div v-if="isExcelMode" :class="$style.uploadSection">
      <h3 :class="$style.sectionTitle">{{ t('agent.upload_excel') }}</h3>
      <ExcelUploader
        v-model="fileValidation"
        @validation-change="handleFileValidationChange"/>
    </div>

    <!-- Form Section -->
    <el-form
      v-if="!isExcelMode"
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      :validate-on-rule-change="false"
      :class="$style.form">
      <el-form-item
        v-for="(label, key) in userKeywords"
        :key="key"
        :label="label"
        :prop="`userInfo.${key}`">
        <!-- Revert to single dynamic component rendering for ALL types -->
        <component
          :is="getFieldComponent(key)"
          v-model="formData.userInfo[key]"
          v-bind="getFieldProps(key)"
          :class="$style[getFieldStyleClass(key)]">
          <!-- Specific slots for COMPANY or MULTI_COMPANY -->
          <template
            v-if="getFieldType(key) === FieldType.COMPANY || getFieldType(key) === FieldType.MULTI_COMPANY"
            #default="scope">
            <!-- Assuming MultiCompanySelector might also use a default slot similarly -->
            <div v-if="getFieldType(key) === FieldType.COMPANY" class="company-suggestion">{{ scope.item.value }}</div>
            <!-- Add specific slot content for MULTI_COMPANY if needed -->
          </template>
        </component>
      </el-form-item>
    </el-form>

    <!-- Binding Agent Specific Fields - 对于所有绑定类型都显示 -->
    <div v-if="showBindingFields" :class="$style.bindingFieldsSection">
      <el-form
        ref="bindingFormRef"
        :model="bindingFormData"
        label-position="top"
        :class="$style.form">
        <!-- 行业选择 - 所有绑定类型都显示，但TENDER类型除外 -->
        <el-form-item v-if="!isTenderType" :label="t('agent.industry_label')" prop="industries">
          <ElSelectV2Group
            v-model="selectedIndustries"
            :options="industries"
            multiple
            filterable
            groupDivider
            :placeholder="t('agent.industry_placeholder')"
            :loading="isLoadingIndustries"
            style="width: 100%"
            loading-text="Loading industries..."
            collapse-tags
            collapse-tags-tooltip>
            <template #group="{ item }">
              <el-checkbox
                :model-value="item.checked"
                :disabled="item.disabled"
                :indeterminate="item.indeterminate"
                :label="item.label"
                @click.prevent="item.checked = !item.checked"/>
            </template>
            <template #item="{ item }">
              <span>{{ item.label }}</span>
            </template>
          </ElSelectV2Group>
        </el-form-item>
        
        <!-- 国家选择 - 所有绑定类型都显示 -->
        <el-form-item :label="t('agent.nation_label')" prop="nations">
          <ElSelectV2Normal
            v-model="selectedNations"
            :options="nations"
            multiple
            filterable
            :placeholder="t('agent.nation_placeholder')"
            :loading="isLoadingNations"
            style="width: 100%"
            loading-text="Loading nations..."/>
        </el-form-item>
        
        <!-- 关键词输入 - 仅非战略洞察类型且非TENDER类型显示 -->
        <el-form-item v-if="!isStrategicType && !isTenderType" :label="t('agent.keywords_label')" prop="keywords">
          <TagsInput v-model="keywords" style="width: 100%">
            <TagsInputItem v-for="item in keywords" :key="item" :value="item">
              <TagsInputItemText/>
              <TagsInputItemDelete/>
            </TagsInputItem>
            <TagsInputInput :placeholder="t('agent.keywords_placeholder')"/>
          </TagsInput>
        </el-form-item>
        
        <!-- 新闻类型选择 - 仅战略洞察类型显示 -->
        <el-form-item v-if="isStrategicType" :label="t('agent.news_type')" prop="newsType">
          <ElSelectV2Normal
            v-model="selectedNewsTypes"
            :options="uniqueNewsTypes"
            multiple
            filterable
            :placeholder="t('agent.select_news_type')"
            :loading="isLoadingNewsTypes"
            style="width: 100%"
            loading-text="Loading news types..."/>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div :class="$style.footer">
        <el-button @click="closeDialog">{{ t('button.cancel') }}</el-button>
        <el-button
          type="primary"
          :loading="isLoadingConfirm"
          @click="confirmInput">
          {{ t('button.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import MarkdownContainer from '@/components/MarkdownComponents/MarkdownContainer.vue'
import { ref, watch, defineEmits, defineProps, nextTick, watchEffect, onMounted, computed, reactive, type Ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { ElInput, ElDatePicker, ElAutocomplete, ElMessage, ElSelect, ElOption, ElCheckbox, ElInputTag } from 'element-plus'
import { useDebounceFn } from '@vueuse/core'
import ExcelUploader from '@/pages/__components__/DashboardComponents/ExcelUploader.vue'
import { TagsInput, TagsInputItem, TagsInputItemText, TagsInputItemDelete, TagsInputInput } from '@/components/ui/tags-input'
import ElSelectV2Group from '@/components/ElSelectV2Group.vue'
import ElSelectV2Normal from '@/components/ElSelectV2Normal.vue'
import { usePost } from '@/hooks/useHttp'
import MultiCompanySelector from './MultiCompanySelector.vue'

// Field type definitions
enum FieldType {
  DATE = 'date',
  TEXT = 'text',
  COMPANY = 'company',
  INPUT = 'input',
  MULTI_COMPANY = 'multi_company',
}

interface IndustryOption {
  label: string;
  value: string;
}

interface IndustryGroup {
  label: string;
  options: IndustryOption[];
}

interface Nation {
  label: string;
  value: string; // Or number
}

interface Props {
  visible: boolean
  userKeywords: Record<string, string>
  title: string
  reportIntroduce?: string
  reportId?: string
  isExcelMode: boolean
  modelValue?: File | null
  showBindingFields?: boolean
  industries?: IndustryGroup[]
  nations?: Nation[]
  isLoadingIndustries?: boolean
  isLoadingNations?: boolean
  isStrategicType: boolean
  newsTypes: string[]
  isLoadingNewsTypes: boolean
  initialSelectedNations?: string[];
  initialSelectedSecondLevelIndustries?: string[];
  initialSelectedKeywords?: string[];
  isLoadingConfirm?: boolean
}

// Helper function to compare string arrays (order-insensitive)
function stringArraysEqual(arr1: string[] | undefined, arr2: string[] | undefined): boolean {
  if (arr1 === undefined && arr2 === undefined) {
    return true
  }
  if (arr1 === undefined || arr2 === undefined) {
    return false
  }
  if (arr1.length !== arr2.length) {
    return false
  }
  
  const sortedArr1 = [...arr1].sort()
  const sortedArr2 = [...arr2].sort()
  
  for (let i = 0; i < sortedArr1.length; i++) {
    if (sortedArr1[i] !== sortedArr2[i]) {
      return false
    }
  }
  return true
}

// Helper function to update a ref if its value (as a string array) has changed
function updateStringArrayRefIfChanged(targetRef: Ref<string[]>, newValue: string[] | undefined) {
  if (!stringArraysEqual(targetRef.value, newValue)) {
    targetRef.value = newValue ? [...newValue] : []
  }
}

// Use withDefaults for better prop definition
const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userKeywords: () => ({}),
  title: '',
  reportIntroduce: '',
  reportId: '',
  isExcelMode: false,
  modelValue: null,
  showBindingFields: false,
  industries: () => [],
  nations: () => [],
  isLoadingIndustries: false,
  isLoadingNations: false,
  isStrategicType: false,
  newsTypes: () => [],
  isLoadingNewsTypes: false,
  initialSelectedNations: undefined,
  initialSelectedSecondLevelIndustries: undefined,
  initialSelectedKeywords: undefined,
})

const uniqueNewsTypes = computed(() => {
  return props.newsTypes.map(type =>{
    return {
      label: type,
      value: type
    }
  })
})

// 判断是否为TENDER类型，TENDER类型只显示nation选项
const isTenderType = computed(() => {
  return props.reportId === 'TENDER'
})
const emit = defineEmits(['update:visible', 'confirm'])

const dialogVisible = useVModel(props, 'visible', emit)
const fileValidation = ref<any>(undefined)

const { t } = useI18n()
const formRef = ref<FormInstance>()

// Initialize formData with a structure derived from userKeywords
const formData = ref<{ 
  userInfo: Record<string, any>; 
  excelFile: File | null 
}>({ 
  userInfo: {}, // Initial empty object
  excelFile: null 
})

const rules = ref<FormRules>({}) 

// --- 新增状态和逻辑 ---
const bindingFormRef = ref<FormInstance>()
const selectedIndustries = ref<string[]>([])
const selectedNations = ref<string[]>([])
const keywords = ref<string[]>([])
const selectedNewsTypes = ref<string[]>([])

// Reactive object for binding form data (needed for rules)
const bindingFormData = reactive({
  keywords
})

// const bindingRules = reactive<FormRules>({
//   keywords: [
//     // Try function form for message
//     { required: true, message: () => t('required.keywords'), trigger: 'change', type: 'array', min: 1 }
//   ]
// })
// -------------------

const { execute: fetchCompanyList, isLoading:recommendLoading, state:recommendMatchCompanyList } = usePost<{id:string, company_name:string}[]>(
  '/formatted_report/search_company',
  undefined,
  {},
  { immediate: false }
)

// 创建查询结果缓存对象
const companyQueryCache = ref<Record<string, {value: string, id: string}[]>>({})

// 优化公司搜索逻辑，增加防抖时间，添加最小输入长度和缓存机制
const queryCompanyAsync = useDebounceFn(async (
  query: string,
  callback: (suggestions: {value: string}[]) => void
) => {
  // 去除查询字符串前后空格
  const trimmedQuery = query.trim()
  
  // 如果查询为空或长度小于2，直接返回空结果
  if (!trimmedQuery || trimmedQuery.length < 2) {
    callback([])
    return
  }
  
  // 检查缓存中是否已有结果
  if (companyQueryCache.value[trimmedQuery]) {
    console.log('使用缓存的公司查询结果:', trimmedQuery)
    callback(companyQueryCache.value[trimmedQuery])
    return
  }
  
  // 执行API查询
  try {
    await fetchCompanyList(0, {
      keyword: trimmedQuery
    })
    const companies = recommendMatchCompanyList.value || []
    const suggestions = companies
      .map(company => ({
        value: company.company_name,
        id: company.id
      }))
    
    // 将结果存入缓存
    companyQueryCache.value[trimmedQuery] = suggestions
    
    callback(suggestions)
  } catch (error) {
    console.error('获取公司列表失败:', error)
    callback([])
  }
}, 1000)

// Component mapping configuration
const FIELD_CONFIG = {
  [FieldType.DATE]: {
    component: ElDatePicker,
    props: {
      type: 'date',
      valueFormat: 'YYYY-MM-DD',
    },
    styleClass: 'datePicker',
    trigger: 'change',
  },
  [FieldType.TEXT]: {
    component: ElInput,
    props: {
      type: 'textarea',
      rows: 3,
      resize: 'none',
    },
    styleClass: 'textarea',
    trigger: 'blur-sm',
  },
  [FieldType.COMPANY]: {
    component: ElAutocomplete,
    props: {
      fetchSuggestions: queryCompanyAsync,
      triggerOnFocus: false,
      clearable: true,
      loading: recommendLoading.value,
    },
    styleClass: 'company',
    trigger: 'blur-sm',
  },
  [FieldType.INPUT]: {
    component: ElInputTag,
    props: { 
      // Props will be handled in getFieldProps or component defaults
      max:5
    },
    styleClass: 'input',
    trigger: 'change',
  },
  [FieldType.MULTI_COMPANY]: {
    component: MultiCompanySelector,
    props: {
      // fetchSuggestions is passed dynamically in getFieldProps based on FIELD_CONFIG
      // loading is passed dynamically in getFieldProps
    },
    styleClass: 'multiCompany', // Define a style class if needed
    trigger: 'change', // Use 'change' as trigger for array validation
  },
} as const

// Use computed for dynamic rule generation and field types
const fieldTypes = computed(() => {
  return Object.keys(props.userKeywords).reduce((acc, key) => {
    const lowerKey = key.toLowerCase()
    const isCompanyKey = ['company_name', 'company_name_list'].includes(lowerKey)
    const isProductKey = ['product_name', 'product_name_list'].includes(lowerKey)

    if (key === 'reporting_period') {
      acc[key] = FieldType.DATE
    } else if (props.reportId === 'CompanyLegalAnalysisReport' && isCompanyKey) {
      // Use MULTI_COMPANY for CompanyLegalAnalysisReport company fields
      acc[key] = FieldType.MULTI_COMPANY
    } else if (isCompanyKey || isProductKey) {
      // For other reports, company fields remain INPUT for now (as per original logic)
      // This might change later based on user feedback for the second request.
      acc[key] = FieldType.INPUT
    } else {
      // Default to TEXT for non-date, non-company fields
      acc[key] = FieldType.TEXT
    }
    return acc
  }, {} as Record<string, FieldType>)
})

const generatedRules = computed(() => {
  return Object.keys(props.userKeywords).reduce((acc, key) => {
    const type = fieldTypes.value[key]
    const config = FIELD_CONFIG[type]
    let rule: any = {
      required: true,
      message: t('required.input', { label: props.userKeywords[key] }),
      trigger: config.trigger || 'blur',
    }

    if (type === FieldType.MULTI_COMPANY) {
      rule = {
        ...rule,
        type: 'array',
        min: 1,
        message: t('required.input', { label: props.userKeywords[key] }),
      }
    } else if (type === FieldType.INPUT) {
      rule = {
        ...rule,
        type: 'array',
        min: 1,
        message: t('required.input', { label: props.userKeywords[key] }),
        trigger: 'change'
      }
    }

    acc[`userInfo.${key}`] = [rule]
    return acc
  }, {} as FormRules)
})

watchEffect(() => {
  rules.value = generatedRules.value
})

// Function definitions remain largely the same, adjusted for computed types
const getFieldType = (key: string): FieldType => {
  return fieldTypes.value[key]
}

const getFieldComponent = (key: string) => {
  const type = fieldTypes.value[key]
  return FIELD_CONFIG[type].component
}

const getFieldProps = (key: string) => {
  const type = fieldTypes.value[key]
  const config = FIELD_CONFIG[type]
  const baseProps: Record<string, any> = {
    placeholder: t('required.input', { label: props.userKeywords[key] }),
    ...config.props,
  }

  if (type === FieldType.COMPANY) {
    baseProps.loading = recommendLoading.value
    baseProps.fetchSuggestions = queryCompanyAsync
  } else if (type === FieldType.MULTI_COMPANY) {
    baseProps.loading = recommendLoading.value
    baseProps.fetchSuggestions = queryCompanyAsync
  }

  if (type === FieldType.INPUT) {
    baseProps['data-input-tag'] = 'true'
  }

  return baseProps
}

const getFieldStyleClass = (key: string): string => {
  const type = fieldTypes.value[key]
  return FIELD_CONFIG[type].styleClass
}

const handleFileValidationChange = (validation: { isValid: boolean; file: File | null }) => {
  fileValidation.value = validation
}

// Watch for initial data and available options to ensure pre-selection
watch(
  [() => props.initialSelectedNations, () => props.nations, () => props.visible],
  ([initialNationsVal, nationsOptions, isVisible]) => {
    // 只有在对话框可见且有选项数据时才设置初始值
    if (isVisible && nationsOptions && nationsOptions.length > 0) {
      console.log('Setting initial nations:', initialNationsVal)
      // 使用 nextTick 确保在 openDialog 重置后设置
      nextTick(() => {
        updateStringArrayRefIfChanged(selectedNations, initialNationsVal)
      })
    }
  },
  { immediate: true, deep: true }
)

watch(
  [() => props.initialSelectedSecondLevelIndustries, () => props.industries, () => props.visible],
  ([initialIndustriesVal, industriesOptions, isVisible]) => {
    if (isVisible && industriesOptions && industriesOptions.length > 0) {
      console.log('Setting initial industries:', initialIndustriesVal)
      nextTick(() => {
        updateStringArrayRefIfChanged(selectedIndustries, initialIndustriesVal)
      })
    }
  },
  { immediate: true, deep: true }
)

watch(
  [() => props.initialSelectedKeywords, () => props.visible],
  ([initialKeywordsVal, isVisible]) => {
    if (isVisible) { // Keywords don't depend on an options list being loaded
      console.log('Setting initial keywords:', initialKeywordsVal)
      nextTick(() => {
        updateStringArrayRefIfChanged(keywords, initialKeywordsVal)
      })
    }
  },
  { immediate: true, deep: true }
)

const openDialog = async () => {
  await nextTick()

  const initialUserInfo: Record<string, any> = {}
  if (props.userKeywords) {
    for (const key in props.userKeywords) {
      if (Object.prototype.hasOwnProperty.call(props.userKeywords, key)) {
        const fieldType = fieldTypes.value[key]
        if (fieldType === FieldType.MULTI_COMPANY) {
          initialUserInfo[key] = []
        } else if (fieldType === FieldType.INPUT) {
          initialUserInfo[key] = []
        } else {
          initialUserInfo[key] = ''
        }
      }
    }
  }
  
  formData.value = {
    userInfo: initialUserInfo,
    excelFile: null
  }
  
  fileValidation.value = undefined
  
  // 总是重置绑定字段的状态，让 watch 逻辑来处理初始值的设置
  // 这样可以确保组件状态的一致性和响应式的正确工作
  selectedIndustries.value = []
  selectedNations.value = []
  keywords.value = []
  selectedNewsTypes.value = []

  // The watchers above handle the initial population.

  if (formRef.value) {
    formRef.value.clearValidate()
  }
  if (bindingFormRef.value) {
    bindingFormRef.value.clearValidate()
  }

  // if (Object.values(fieldTypes.value).includes(FieldType.COMPANY)) {
  //   fetchCompanyList()
  // }
}

const closeDialog = () => {
  dialogVisible.value = false
}

const confirmInput = async () => {
  let isValid = true
  let bindingIsValid = true

  if (props.isExcelMode) {
    if (!fileValidation.value?.isValid) {
      ElMessage.error(t('required.excel'))
      isValid = false
    }
  } else if (formRef.value) {
    try {
      await formRef.value.validate()
    } catch (error) {
      isValid = false
      console.error('Form validation error:', error)
    }
  }

  if (props.showBindingFields && bindingFormRef.value && !props.isStrategicType && !isTenderType.value) {
    try {
      await bindingFormRef.value.validate()
    } catch (error) {
      bindingIsValid = false
      console.error('Binding form validation error:', error)
    }
  }

  if (isValid && bindingIsValid) {
    const emitData: any = {
      userInfo: formData.value.userInfo,
      excelFile: fileValidation.value?.file,
    }
    
    // 根据类型添加不同的数据
    if (props.isStrategicType) {
      // 战略分析类型，添加industries、nations和newsType
      emitData.selectedIndustries = selectedIndustries.value
      emitData.selectedNations = selectedNations.value
      emitData.newsTypes = selectedNewsTypes.value
    } else if (isTenderType.value) {
      // TENDER类型，只添加nations数据
      emitData.selectedNations = selectedNations.value
      // 不添加industries和keywords - TENDER模式下不使用这些字段
      emitData.userInfo.nations = selectedNations.value
    } else if (props.showBindingFields) {
      // 绑定类型 (非战略洞察且非TENDER)
      const userSelectedSecondLevel: string[] = selectedIndustries.value || []

      // These are directly used by the parent (agent-market) for structuring the API call
      emitData.selectedIndustries = userSelectedSecondLevel // API usually expects `industries` as the list of second-level names
      emitData.selectedNations = selectedNations.value
      emitData.keywords = keywords.value

      // Logic to derive first-level industries based on "full match" of its second-level children
      const firstLevelIndustriesToAdd = new Set<string>()
      if (userSelectedSecondLevel.length > 0 && props.industries) {
        const userSelectedSet = new Set(userSelectedSecondLevel)

        for (const group of props.industries) { // group is { label: string (first-level), options: {value: string}[] (second-level) }
          const definedSecondLevelForGroup = group.options.map(opt => opt.value)

          // If a first-level group has no defined second-level industries, it cannot be matched.
          if (definedSecondLevelForGroup.length === 0) {
            continue
          }

          // Check if all defined second-level industries for this group are present in the user's selection
          let allDefinedInGroupAreSelectedByUser = true
          for (const definedSecond of definedSecondLevelForGroup) {
            if (!userSelectedSet.has(definedSecond)) {
              allDefinedInGroupAreSelectedByUser = false
              break
            }
          }

          if (allDefinedInGroupAreSelectedByUser) {
            firstLevelIndustriesToAdd.add(group.label)
          }
        }
      }
      const industry_first_array = Array.from(firstLevelIndustriesToAdd)

      // 合并到userInfo for completeness and potential backend use
      emitData.userInfo.industry_first = industry_first_array
      emitData.userInfo.industry_second = userSelectedSecondLevel // User's full selection of second-level industries
      // Also add nations and keywords to userInfo, though parent might use direct fields from emitData
      emitData.userInfo.nations = selectedNations.value
      emitData.userInfo.keywords = keywords.value
    }
    emit('confirm', emitData)
  } else {
    ElMessage.warning(t('form.validation.failed'))
  }
}

const handleEnterKey = (event: KeyboardEvent) => {
  const target = event.target as HTMLElement

  if (target.closest('[data-input-tag="true"]') || target.closest('[data-multi-selector="true"]')) {
    return
  }

  // 处理 Textarea (允许 Shift+Enter 换行)
  if (target instanceof HTMLTextAreaElement && event.shiftKey) {
    return
  }

  // 对于其他所有情况 (例如在 ElDatePicker, ElInput(非textarea) 等组件上按 Enter), 提交表单
  // .prevent 修饰符会自动处理 event.preventDefault()
  confirmInput()
}

onMounted(() => {
  // Initial setup if needed
})
</script>

<style lang="scss" module>
.dialog {
  --el-dialog-width: 45%;
  min-width: 400px;
  max-width: 700px;

  .el-dialog__body {
    padding: 20px 25px;
  }
  .el-dialog__footer {
    padding: 15px 25px;
    border-top: 1px solid var(--border-color-light);
  }
}

.introduction {
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-color-soft);
  border-radius: var(--radius-base);
  border: 1px solid var(--border-color-lighter);
  max-height: 150px;
  overflow-y: auto;
}

.uploadSection {
  margin-bottom: 20px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--font-color-1);
}

.form {
  .el-form-item {
    margin-bottom: 18px;
  }
  .el-form-item__label {
    padding-bottom: 6px;
    line-height: 1.4;
  }
}

.datePicker {
  width: 100%;
}
.textarea {
  // textarea specific styles if any
}
.company {
  width: 100%;
}
.multiCompany {
  width: 100%;
}

.bindingFieldsSection {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color-light);
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.el-autocomplete-suggestion {
  .company-suggestion {
    // Styles for the suggestion item
  }
}
</style>

<style lang="scss">
.el-autocomplete-suggestion {
  // min-width: 250px !important;
}
</style>
