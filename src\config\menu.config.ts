import { type RouteLocationNormalized } from 'vue-router'

export interface MenuItem {
  key: string
  route: string
  menuId: string
  labelKey: string
  priority?: number
  children?: MenuItem[]
}

export const menuItems: MenuItem[] = [
  {
    key: 'formatReport',
    route: '/format-report',
    menuId: 'format_report',
    labelKey: 'menu.formatReport',
    priority: 3
  },
  {
    key: 'information',
    route: '/',
    menuId: 'information',
    labelKey: 'menu.topInfoMenu',
    priority: 1
  },
  {
    key: 'dailyReport',
    route: '/intelligence',
    menuId: 'daily_report',
    labelKey: 'menu.dailyReport',
    priority: 4
  },
  {
    key: 'chatBot',
    route: '/robot',
    menuId: 'chat_bot',
    labelKey: 'menu.topRobotMenu',
    priority: 6
  },
  {
    key: 'procurement',
    route: '/procurement',
    menuId: 'procurement',
    labelKey: 'menu.topProcurementMenu',
    priority: 5
  },
  // {
  //   key: 'dashboard',
  //   route: '/dashboard',
  //   menuId: 'dashboard',
  //   labelKey: 'menu.topDashboardMenu',
  //   priority: 2
  // },
  {
    key: 'workspace',
    route: '/workspace',
    menuId: 'dashboard',
    labelKey: 'menu.topWorkspaceMenu',
    priority: 2
  }
]

export const getMenuByRoute = (route: string): MenuItem | undefined => {
  const findInItems = (items: MenuItem[]): MenuItem | undefined => {
    for (const item of items) {
      if (item.route === route) { return item }
      if (item.children) {
        const found = findInItems(item.children)
        if (found) { return found }
      }
    }
    return undefined
  }
  return findInItems(menuItems)
}

export const isRouteInMenu = (route: RouteLocationNormalized): boolean => {
  const path = route.path
  return menuItems.some(item => {
    if (path === item.route || path.startsWith(`${item.route}/`)) { return true }
    if (item.children) {
      return item.children.some(child => 
        path === child.route || path.startsWith(`${child.route}/`)
      )
    }
    return false
  })
}

export const getRouteToMenuMap = (): Record<string, string> => {
  const map: Record<string, string> = {}
  const addToMap = (items: MenuItem[]) => {
    items.forEach(item => {
      map[item.route] = item.menuId
      if (item.children) {
        addToMap(item.children)
      }
    })
  }
  addToMap(menuItems)
  return map
} 