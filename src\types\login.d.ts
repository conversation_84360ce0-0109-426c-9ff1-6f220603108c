import type { PlanType } from '@/pages/settings/plans/type'

export interface Register {
    user: User;
    token: string;
    default_project: null;
  }
  
  interface Others {
  }

export interface LoginResult {
    user: User;
    token: string;
    default_project: null;
  }
export interface TourConfigItem {
    id: string
    tour_completed: boolean
  }
export interface User {
    created_at: string;
    updated_at: string;
    user_name: string;
    nickname: string;
    email: string;
    user_type: 1 | 2 | 3;
    language: 'chinese' | 'english' | 'japanese';
    projects: string[];
    nation: string[];
    industry: string[];
    default_project: Defaultproject;
    company: Company;
    step: number;
    user_profile: Userprofile2;
    others: Others;
    tour_config: TourConfigItem[];
    id: string;
  }
  
  interface Others {
    password: string;
  }
  
  interface Userprofile2 {
    industry: any[];
    industry_value: string;
    area: any[];
    area_value: string;
    style: any[];
    style_value: string;
    overseas: any[];
    overseas_value: string;
    project_recommendation: any[];
    industry_recommendation: string[];
    target_market_recommendation: null[];
  }
  
  interface Company {
    created_at: string;
    updated_at: string;
    name: string;
    nations: any[];
    industries: any[];
    invite_code: string;
    language: string;
    exp: string;
    employees_limit: number;
    token_limit: number;
    is_delete: boolean;
    config: Config;
    step: number;
    user_profile: Userprofile;
    others: Userprofile;
    id: string;
  }
  
  interface Userprofile {
  }
  
  interface Config {
    members: number;
    tokens: number | 'unlimited';
    information_data: string;
    pdf_report: string;
    permissions: Permissions;
    plan: PlanType;
  }
  
  interface Permissions {
    pdf_setting: boolean;
    pdf_review: boolean;
    pdf_download: boolean;
  }
  
  interface Defaultproject {
    id: string;
  }

export type LoginMode = 'login' | 'register'
export type PageMode = 'init' | 'step'
