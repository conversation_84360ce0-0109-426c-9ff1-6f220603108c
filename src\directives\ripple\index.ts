import {
  DEFAULT_PLUGIN_OPTIONS,
  type IRippleDirectiveOptions,
  type IRippleDirectiveOptionWithBinding
} from './options'
import { ripple } from './ripple'
const optionMap = new WeakMap<HTMLElement, Partial<IRippleDirectiveOptions> | false>()
const globalOptions = { ...DEFAULT_PLUGIN_OPTIONS }
export default {
  name: 'ripple',
  mounted(el: HTMLElement, binding: IRippleDirectiveOptionWithBinding): void {
    optionMap.set(el, binding.value ?? {})

    el.addEventListener('pointerdown', (event) => {
      const options = optionMap.get(el)
      // 必须确保disabled 属性存在 否则指令终止报错
      if (binding.value && binding.value.disabled) {return}

      if (options === false) {return}

      ripple(event, el, {
        ...globalOptions,
        ...options
      })
    })
  },
  updated(el: HTMLElement, binding: IRippleDirectiveOptionWithBinding): void {
    optionMap.set(el, binding.value ?? {})
  }
}
