import type { ModuleTourConfig } from './types'

export const THEME = {
  primaryColor: 'var(--primary-color)',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

// 简化后的配置
export const tourConfig: Record<string, ModuleTourConfig> = {
  // 信息模块引导
  information: {
    moduleId: 'information',
    steps: [
      {
        id: 'info-step-1',
        target: '[tour-id="information-news"]',
        title: 'tour.information.news.title',
        description: 'tour.information.news.desc',
        placement: 'right'
      },
      {
        id: 'info-step-2',
        target: '[tour-id="information-controls"]',
        title: 'tour.information.controls.title',
        description: 'tour.information.controls.desc'
      },
      {
        id: 'info-step-3',
        target: '[tour-id="information-news-list"]',
        title: 'tour.information.list.title',
        description: 'tour.information.list.desc',
        placement: 'left'
      }
    ],
    theme: {
      primaryColor: 'var(--primary-color)',
      maskColor: 'rgba(0, 0, 0, 0.5)'
    }
  },
  
  // 日报模块引导
  daily_report: {
    moduleId: 'daily_report',
    steps: [
      {
        id: 'daily-step-1',
        target: '[tour-id="daily_report-project-list"]',
        title: 'tour.daily_report.project_list.title',
        description: 'tour.daily_report.project_list.desc',
        placement: 'right'
      },
      {
        id: 'daily-step-2',
        target: '[tour-id="daily_report-detail"]',
        title: 'tour.daily_report.detail.title',
        description: 'tour.daily_report.detail.desc',
        placement: 'left'
      },
      {
        id: 'daily-step-3',
        target: '[tour-id="daily_report-news-list"]',
        title: 'tour.daily_report.news_list.title',
        description: 'tour.daily_report.news_list.desc',
        placement: 'left'
      }
    ]
  },
  // 制式报告模块引导
  format_report: {
    moduleId: 'format_report',
    steps: [
      {
        id: 'format-step-1',
        target: '[tour-id="format_report-list"]',
        title: 'tour.format_report.list.title',
        description: 'tour.format_report.list.desc',
        placement: 'left'
      },
      {
        id: 'format-step-2',
        target: '[tour-id="format_report-floating-ball"]',
        title: 'tour.format_report.floating_ball.title',
        description: 'tour.format_report.floating_ball.desc',
        placement: 'left'
      },
      {
        id: 'format-step-3',
        target: '[tour-id="format_report-template"]',
        title: 'tour.format_report.template.title',
        description: 'tour.format_report.template.desc',
        placement: 'left'
      }
    ]
  }
} 