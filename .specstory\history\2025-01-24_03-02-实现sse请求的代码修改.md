<!-- Generated by SpecStory -->

# 实现SSE请求的代码修改 (2025-01-24 11:02:39)

_**User**_

在 @index.vue 中，我希望你能模仿 @format-report 中请求SSE的接口，使用useEnhancedEventSource，去请求/api/directed_search/search接口，传参search_text以及token、language

---

_**Assistant**_

```thinking
Let me analyze this request step by step:

1. First, let me understand what the user wants:
- Need to modify the search/index.vue file
- Need to implement SSE (Server-Sent Events) request similar to format-report
- Should use useEnhancedEventSource hook
- Target endpoint is /api/directed_search/search
- Need to pass search_text, token and language parameters

2. Let me check what I need:
- I need to see how format-report implements SSE
- I need to understand the current search implementation
- I need to know the useEnhancedEventSource interface

3. Looking at the current search/index.vue:
- It's using useSearchStream custom hook
- Has search query input and streaming UI
- Already handles loading states and errors
- Has suggestion cards feature

4. Key changes needed:
- Replace useSearchStream with useEnhancedEventSource
- Modify the search function to use SSE
- Keep existing UI and error handling
- Ensure proper parameter passing

5. Let me search for format-report implementation...

Let me search for the format-report implementation to see how it uses useEnhancedEventSource
```

---

<details>
            <summary>Searched codebase "useEnhancedEventSource in format-report" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/pages/format-report/[id].vue` | L172-210 |
| `src/pages/format-report/[id].vue` | L272-281 |
| `src/pages/format-report/[id].vue` | L97-128 |
| `src/pages/format-report/[id].vue` | L129-171 |
| `src/pages/format-report/__components__/SearchSource.vue` | L87-147 |
| `src/pages/format-report/[id].vue` | L1-34 |
| `src/pages/format-report/view/[id].vue` | L148-161 |
| `src/pages/format-report/view/[id].vue` | L1-30 |
| `src/pages/format-report/index.vue` | L210-264 |
| `src/pages/format-report/index.vue` | L1-40 |
| `src/pages/format-report/index.vue` | L69-111 |
| `src/pages/format-report/[id].vue` | L35-67 |
| `src/pages/format-report/index.vue` | L41-68 |
| `src/pages/format-report/view/[id].vue` | L31-70 |
| `src/pages/format-report/__components__/SearchSource.vue` | L1-25 |
| `src/pages/format-report/view/[id].vue` | L71-100 |
| `src/pages/format-report/type.d.ts` | L1-73 |
| `src/pages/format-report/__components__/ReportSidebar.vue` | L140-192 |
| `src/pages/format-report/type.d.ts` | L75-78 |
| `src/pages/format-report/index.vue` | L112-155 |
| `src/pages/format-report/[id].vue` | L68-96 |
| `src/pages/format-report/__components__/FormatReportTemplateItem.vue` | L1-43 |
| `src/pages/format-report/__components__/ReportSidebar.vue` | L43-92 |
| `src/pages/format-report/__components__/SearchSource.vue` | L26-86 |
| `src/pages/format-report/index.vue` | L156-209 |

</details>

---



```thinking
Let me search for useEnhancedEventSource implementation to understand how to use it properly...
```

---

<details>
            <summary>Searched codebase "useEnhancedEventSource implementation or definition" • **0** results</summary>
        
No results found
</details>

---



```thinking
Let me check the current useSearchStream implementation to understand what I need to replace...
```

---

<details>
            <summary>Searched codebase "useSearchStream implementation" • **7** results</summary>
        
| File | Lines |
|------|-------|
| `src/pages/search/useSearchStream.ts` | L1-66 |
| `src/pages/search/index.vue` | L69-133 |
| `src/pages/search/index.vue` | L35-68 |
| `src/pages/search/index.vue` | L1-34 |
| `src/pages/search/index.vue` | L134-193 |
| `src/pages/search/question.vue` | L1-24 |
| `src/pages/search/index.vue` | L194-212 |

</details>

---



```thinking
Based on my research, I need to:

1. Create a new composable similar to useSearchStream but for directed search
2. Modify the search implementation to use useEnhancedEventSource with the new endpoint
3. Pass the required parameters (search_text, token, language)

Let me create a new composable first...
```

---

```diff
  import { ref } from 'vue'
  import { useEnhancedEventSource } from '@/hooks/useEnhancedEventSource'
+ import { getToken } from '@/hooks/useToken'
+ import { useI18n } from 'vue-i18n'
  
  export interface SearchStreamMessage {
```

```diff
    const streamError = ref<Error | null>(null)
    const eventSource = ref<EventSource | null>(null)
+   const { locale } = useI18n()
  
    const { status, error, close, open } = useEnhancedEventSource(
-     '/api/search/stream',
+     (query: string) => {
+       const url = new URL('/api/directed_search/search', window.location.origin)
+       const token = getToken()
+       url.searchParams.append('search_text', query)
+       url.searchParams.append('token', token)
+       url.searchParams.append('language', locale.value)
+       return url
+     },
      ['message'] as const,
      {
```

```diff
          backoffFactor: 1.5,
        },
+       onMessage: (event) => {
+         try {
+           const data = JSON.parse(event.data) as SearchStreamMessage
+           streamContent.value += data.content
+         } catch (err) {
+           console.error('Failed to parse message:', err)
+         }
+       },
        onError: (err) => {
          console.error('Search stream error:', err)
```

```diff
  
    const startSearchStream = (query: string) => {
-     const url = new URL('/api/search/stream', window.location.origin)
-     url.searchParams.append('q', query)
- 
      // 重置状态
      streamContent.value = ''
      streamError.value = null
  
-     open()
+     open(query)
    }
  
```

---



```thinking
I see the linter errors. The issue is with the type definition of useEnhancedEventSource. Let me check its implementation to understand the correct way to use it...
```

---

<details>
            <summary>Searched codebase "useEnhancedEventSource type definition or interface" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `src/hooks/useEnhancedEventSource.ts` | L1-44 |
| `src/hooks/useEnhancedEventSource.ts` | L168-212 |
| `src/hooks/useEnhancedEventSource.ts` | L46-101 |
| `src/hooks/useEnhancedEventSource.ts` | L103-166 |
| `src/hooks/useSafeWebsocket.ts` | L68-91 |
| `src/hooks/useHttpPostStream.ts` | L1-29 |
| `src/hooks/useSafeWebsocket.ts` | L1-9 |
| `src/hooks/useSafeWebsocket.ts` | L11-66 |
| `src/hooks/useHttpPostStream.ts` | L211-211 |
| `src/hooks/useAIStream.ts` | L1-46 |
| `src/hooks/useHttpPostStream.ts` | L168-209 |
| `src/hooks/useTrack.ts` | L1-53 |
| `src/hooks/useWebSocket.ts` | L1-43 |
| `src/hooks/useNewsNotification.ts` | L90-101 |
| `src/hooks/useNewsNotification.ts` | L1-8 |
| `src/hooks/useWebSocket.ts` | L390-402 |
| `src/hooks/useTrack.ts` | L53-108 |
| `src/hooks/useAIStream.ts` | L341-361 |
| `src/hooks/useAIStream.ts` | L225-236 |
| `src/hooks/usePdf/types.d.ts` | L63-67 |
| `src/hooks/useWebSocket.ts` | L238-320 |
| `src/hooks/useChartImage/types.ts` | L1-22 |
| `src/hooks/useHttpPostStream.ts` | L120-166 |
| `src/hooks/useOverflowTooltip.ts` | L1-51 |
| `src/hooks/useHttp.ts` | L1-35 |

</details>

---







```thinking
Based on the useEnhancedEventSource implementation, I see that the URL parameter should be a MaybeRefOrGetter<string | URL>. Let me modify the implementation to match this type...
```

---

```diff
  
    const { status, error, close, open } = useEnhancedEventSource(
-     (query: string) => {
+     () => {
        const url = new URL('/api/directed_search/search', window.location.origin)
        const token = getToken()
-       url.searchParams.append('search_text', query)
        url.searchParams.append('token', token)
        url.searchParams.append('language', locale.value)
-       return url
+       return url.toString()
      },
      ['message'] as const,
```

```diff
      streamError.value = null
  
-     open(query)
+     const url = new URL('/api/directed_search/search', window.location.origin)
+     url.searchParams.append('search_text', query)
+     url.searchParams.append('token', getToken())
+     url.searchParams.append('language', locale.value)
+     open()
    }
  
```

---







```thinking
The implementation still has issues. The URL needs to be updated with the search query when starting the stream. Let me modify it again...
```

---

```diff
    const eventSource = ref<EventSource | null>(null)
    const { locale } = useI18n()
+   const currentQuery = ref('')
  
    const { status, error, close, open } = useEnhancedEventSource(
      () => {
        const url = new URL('/api/directed_search/search', window.location.origin)
        const token = getToken()
+       url.searchParams.append('search_text', currentQuery.value)
        url.searchParams.append('token', token)
        url.searchParams.append('language', locale.value)
```

```diff
      streamContent.value = ''
      streamError.value = null
- 
-     const url = new URL('/api/directed_search/search', window.location.origin)
-     url.searchParams.append('search_text', query)
-     url.searchParams.append('token', getToken())
-     url.searchParams.append('language', locale.value)
+     currentQuery.value = query
      open()
    }
```

---







```thinking
Now that I've updated the useSearchStream implementation to use the directed search endpoint with proper SSE handling, let me explain the changes:

1. The implementation now uses useEnhancedEventSource to handle SSE connections
2. It properly handles the search query by storing it in a reactive ref and using it in the URL
3. It includes all required parameters:
   - search_text: The search query
   - token: For authentication
   - language: From i18n locale

The implementation should now work as expected. Let me explain this to the user in both English and Chinese.
```

I've updated the search implementation to use Server-Sent Events (SSE) with the directed search endpoint. Here are the key changes:

1. Now using `/api/directed_search/search` endpoint
2. Added required parameters:
   - search_text: Your search query
   - token: For authentication
   - language: Current UI language
3. Implemented proper SSE handling with automatic reconnection
4. Maintained all existing UI feedback (loading states, error handling)

The search functionality should work the same as before, but now using the directed search endpoint.

我已经更新了搜索实现，使用服务器发送事件（SSE）和定向搜索端点。以下是主要更改：

1. 现在使用 `/api/directed_search/search` 端点
2. 添加了必需的参数：
   - search_text：您的搜索查询
   - token：用于身份验证
   - language：当前界面语言
3. 实现了适当的 SSE 处理，包括自动重连
4. 保持了所有现有的 UI 反馈（加载状态、错误处理）

搜索功能的使用方式与之前相同，但现在使用定向搜索端点。

---

_**User**_

相应的 @index.vue 需要去调用 @useSearchStream.ts 

---

_**Assistant**_

```thinking
Let me analyze what needs to be done:

1. The index.vue file needs to:
- Import and use the useSearchStream composable
- Keep the existing UI components and layout
- Wire up the search functionality to use the new implementation
- Handle loading states and errors properly

2. Looking at the current implementation:
- Already has search input with textarea
- Has loading states and error handling
- Has suggestion cards
- Uses Element Plus components

3. Key changes needed:
- Import useSearchStream
- Use the returned methods and states
- Keep existing UI feedback
- Maintain the same user experience

Let me check the current index.vue implementation...
```

---

Read file: src/pages/search/index.vue

---







```thinking
I see that the index.vue file is already properly set up to use useSearchStream! It has:

1. Imported and used the useSearchStream composable
2. Destructured the necessary methods and states
3. Proper error handling and loading states
4. Clean UI with search input and suggestions
5. Proper cleanup on component unmount

Since the useSearchStream implementation has been updated to use the directed search endpoint, and the index.vue is already properly using it, no changes are needed to index.vue.

Let me explain this to the user in both English and Chinese.
```

Actually, the `index.vue` file is already correctly set up to use the updated `useSearchStream`! No changes are needed because:

1. It already imports and uses the `useSearchStream` composable
2. All necessary states and methods are properly destructured:
   ```ts
   const {
     streamContent,
     isStreaming,
     streamError,
     startSearchStream,
     stopSearchStream
   } = useSearchStream()
   ```
3. Error handling and loading states are properly implemented
4. The UI components are already wired up correctly
5. Proper cleanup is done on component unmount

The component will automatically use the new directed search endpoint because we updated the implementation in `useSearchStream.ts`.

实际上，`index.vue` 文件已经正确设置为使用更新后的 `useSearchStream`！不需要做任何更改，因为：

1. 已经导入并使用了 `useSearchStream` 组合式函数
2. 所有必需的状态和方法都已正确解构：
   ```ts
   const {
     streamContent,
     isStreaming,
     streamError,
     startSearchStream,
     stopSearchStream
   } = useSearchStream()
   ```
3. 已实现了错误处理和加载状态
4. UI 组件已经正确连接
5. 在组件卸载时进行了适当的清理

由于我们更新了 `useSearchStream.ts` 中的实现，该组件将自动使用新的定向搜索端点。

---

_**User**_

在 @index.vue 中的搜索中，返回的数据如 @我该如何到日本出海经商？_20250124_091941.json 所示，这种结构的返回，适合复用 @FormatReportDetail.vue 组件吗？如果不合适，请告诉应该使用什么数据结构，因为我是全栈，我可以更改结构，适配前端

---

_**Assistant**_

```thinking
Let me analyze this situation:

1. Current Data Structure in JSON:
```json
[
  {
    "event_type": "search_started",
    "iteration": 0,
    "content": "{'query': '我该如何到日本出海经商？'}"
  },
  {
    "event_type": "keywords_extracted",
    "content": "['日本 出海经商', '日本 商业环境'...]"
  },
  {
    "event_type": "results_analyzed",
    "content": "main_findings=[...] completeness_score=0.8 next_focus=[...]"
  }
]
```

2. FormatReportDetail.vue Requirements:
- Expects a structured report with:
  - report_title
  - report_outline
  - searchResultsList
  - timelineData
  - charts
  - legalTimelineData

3. Key Differences:
- Current data is event-based stream
- FormatReportDetail expects a structured report object
- Missing key sections like outline and charts

4. Potential Solutions:
- Either modify the backend to match FormatReportDetail structure
- Or create a simpler component for search results

Let me analyze which approach would be better...
```

Based on my analysis, I recommend the following:

1. The current event-based stream structure is not ideal for FormatReportDetail.vue because:
   - FormatReportDetail is designed for comprehensive reports with multiple sections
   - It has features like PDF generation, mindmaps, and charts that aren't relevant for search results
   - It's too heavy for simple search results

2. Recommended Data Structure for Search Results:
```typescript
interface SearchResponse {
  query: string;
  keywords: string[];  // From keywords_extracted
  results: {
    main_findings: string[];  // Key findings
    next_steps?: string[];   // Optional next steps/focus areas
    sources?: {             // Optional reference sources
      title: string;
      url: string;
      summary: string;
    }[];
    metadata: {
      completeness_score: number;
      timestamp: string;
    };
  };
}
```

3. Backend Changes Needed:
```python
# Example Python structure
def format_search_response(events):
    return {
        "query": extract_query(events),
        "keywords": extract_keywords(events),
        "results": {
            "main_findings": extract_findings(events),
            "next_steps": extract_next_focus(events),
            "metadata": {
                "completeness_score": extract_score(events),
                "timestamp": get_timestamp()
            }
        }
    }
```

基于我的分析，我建议以下方案：

1. 当前基于事件的流式结构不适合使用 FormatReportDetail.vue，因为：
   - FormatReportDetail 是为包含多个章节的综合报告设计的
   - 它包含 PDF 生成、思维导图和图表等对搜索结果来说不必要的功能
   - 对于简单的搜索结果来说过于重量级

2. 建议的搜索结果数据结构：
```typescript
interface SearchResponse {
  query: string;
  keywords: string[];  // 来自 keywords_extracted
  results: {
    main_findings: string[];  // 主要发现
    next_steps?: string[];   // 可选的下一步/关注点
    sources?: {             // 可选的参考来源
      title: string;
      url: string;
      summary: string;
    }[];
    metadata: {
      completeness_score: number;
      timestamp: string;
    };
  };
}
```

3. 后端需要的改动：
```python
# Python 示例结构
def format_search_response(events):
    return {
        "query": extract_query(events),
        "keywords": extract_keywords(events),
        "results": {
            "main_findings": extract_findings(events),
            "next_steps": extract_next_focus(events),
            "metadata": {
                "completeness_score": extract_score(events),
                "timestamp": get_timestamp()
            }
        }
    }
```

建议创建一个更简单的搜索结果组件，而不是复用 FormatReportDetail.vue。这样可以：
1. 提供更好的用户体验
2. 减少不必要的复杂性
3. 提高性能
4. 更容易维护和扩展

---

_**User**_

那请你根据当前后端返回的SSE事件的数据结构，创建一个搜索结果组件，具体的展现形式可以，参考 @[id].vue 这种单独页面

---

_**Assistant**_

```thinking
Let me analyze this request:

1. Current SSE Event Structure:
```json
[
  {
    "event_type": "search_started",
    "content": "{'query': '...'}"
  },
  {
    "event_type": "keywords_extracted", 
    "content": "[keywords...]"
  },
  {
    "event_type": "results_analyzed",
    "content": "main_findings=[...] completeness_score=0.8 next_focus=[...]"
  }
]
```

2. Key Components Needed:
- Main container similar to FormatReportDetail
- Progress indicator for different search stages
- Keywords display section
- Main findings section
- Next steps/focus areas section
- Loading states and animations

3. UI Elements to Reuse:
- SearchSource component for showing search progress
- FilteringAnimation for loading states
- MarkdownDisplay for content rendering

4. New Structure:
- Create a new SearchResultDetail component
- Handle SSE events and state management
- Show search progress and results in a clean layout

Let me create this new component...
```

---

```diff
- 
+ <template>
+   <div class="min-h-screen flex flex-col" :class="$style.root">
+     <!-- Header -->
+     <Header :is-show-menu="false"/>
+ 
+     <!-- Main Content -->
+     <div class="flex-1 flex">
+       <!-- Left Sidebar -->
+       <div class="w-[15%] p-4 flex flex-col" :class="$style['left-sidebar']">
+         <div :class="$style['nav-actions']">
+           <el-button @click="$router.back()" class="w-full">
+             <Icon icon="mdi:arrow-left" class="mr-2"/> {{ t("global.back") }}
+           </el-button>
+ 
+           <el-button
+             v-if="status === 'OPEN'"
+             @click="emit('stop')"
+             class="w-full"
+             type="danger">
+             <Icon icon="mdi:stop" class="mr-2"/> {{ t("global.stop") }}
+           </el-button>
+         </div>
+       </div>
+ 
+       <!-- Main Content Area -->
+       <div class="flex-1 flex flex-col overflow-y-auto h-full">
+         <!-- Title Area -->
+         <div class="py-3 text-center w-full flex justify-center flex-col items-center gap-[10px] h-[100px]">
+           <div class="flex items-center justify-center">
+             <span class="text-[20px] font-bold">{{ searchQuery }}</span>
+           </div>
+         </div>
+ 
+         <!-- Content Area -->
+         <div ref="containerRef" class="flex-1 overflow-y-auto px-4 h-full" :class="$style['search-container']">
+           <!-- Search Progress -->
+           <div v-if="status === 'OPEN'" class="mb-8">
+             <SearchSource
+               :show="true"
+               icon="mdi:magnify"
+               :text="t('search.searching')"/>
+             
+             <div v-if="keywords.length > 0" class="mt-4">
+               <h3 class="text-lg font-semibold mb-2">{{ t('search.keywords') }}</h3>
+               <div class="flex flex-wrap gap-2">
+                 <el-tag 
+                   v-for="keyword in keywords" 
+                   :key="keyword"
+                   size="large"
+                   effect="plain">
+                   {{ keyword }}
+                 </el-tag>
+               </div>
+             </div>
+           </div>
+ 
+           <!-- Search Results -->
+           <div v-if="mainFindings.length > 0" class="space-y-6">
+             <!-- Main Findings -->
+             <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
+               <h2 class="text-xl font-semibold mb-4">{{ t('search.mainFindings') }}</h2>
+               <ul class="list-disc list-inside space-y-2">
+                 <li v-for="(finding, index) in mainFindings" :key="index" class="text-gray-700">
+                   {{ finding }}
+                 </li>
+               </ul>
+             </div>
+ 
+             <!-- Next Steps -->
+             <div v-if="nextSteps.length > 0" class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
+               <h2 class="text-xl font-semibold mb-4">{{ t('search.nextSteps') }}</h2>
+               <ul class="list-disc list-inside space-y-2">
+                 <li v-for="(step, index) in nextSteps" :key="index" class="text-gray-700">
+                   {{ step }}
+                 </li>
+               </ul>
+             </div>
+ 
+             <!-- Completeness Score -->
+             <div v-if="completenessScore !== null" class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
+               <h2 class="text-xl font-semibold mb-4">{{ t('search.completeness') }}</h2>
+               <el-progress 
+                 :percentage="completenessScore * 100" 
+                 :format="format => \`\${format}%\`"
+                 :stroke-width="20"
+                 status="success"/>
+             </div>
+           </div>
+         </div>
+       </div>
+ 
+       <!-- Right Sidebar -->
+       <div class="w-[20%] p-4">
+         <!-- Progress Timeline -->
+         <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
+           <h3 class="font-semibold mb-4">{{ t('search.progress') }}</h3>
+           <el-timeline>
+             <el-timeline-item
+               v-for="event in searchEvents"
+               :key="event.timestamp"
+               :timestamp="formatTime(event.timestamp)"
+               :type="getEventType(event.event_type)">
+               {{ t(\`search.events.\${event.event_type}\`) }}
+             </el-timeline-item>
+           </el-timeline>
+         </div>
+       </div>
+     </div>
+   </div>
+ </template>
+ 
+ <script setup lang="ts">
+ import { ref, computed } from 'vue'
+ import { useI18n } from 'vue-i18n'
+ import { Icon } from '@iconify/vue'
+ import dayjs from 'dayjs'
+ import Header from '@/components/LayoutComponents/Header.vue'
+ import SearchSource from '@/pages/format-report/__components__/SearchSource.vue'
+ 
+ interface SearchEvent {
+   event_type: string;
+   iteration: number;
+   content: string;
+   timestamp: string;
+ }
+ 
+ const props = defineProps<{
+   searchQuery: string;
+   searchEvents: SearchEvent[];
+   status: 'OPEN' | 'CLOSED';
+ }>()
+ 
+ const emit = defineEmits<{
+   stop: [];
+ }>()
+ 
+ const { t } = useI18n()
+ const containerRef = ref<HTMLElement | null>(null)
+ 
+ // Extract keywords from events
+ const keywords = computed(() => {
+   const keywordEvent = props.searchEvents.find(e => e.event_type === 'keywords_extracted')
+   if (!keywordEvent) return []
+   try {
+     return JSON.parse(keywordEvent.content.replace(/'/g, '"'))
+   } catch {
+     return []
+   }
+ })
+ 
+ // Extract main findings and next steps
+ const { mainFindings, nextSteps, completenessScore } = computed(() => {
+   const resultsEvent = props.searchEvents.find(e => e.event_type === 'results_analyzed')
+   if (!resultsEvent) return { mainFindings: [], nextSteps: [], completenessScore: null }
+ 
+   try {
+     const content = resultsEvent.content
+     const mainFindingsMatch = content.match(/main_findings=\[(.*?)\]/)
+     const nextFocusMatch = content.match(/next_focus=\[(.*?)\]/)
+     const scoreMatch = content.match(/completeness_score=([\d.]+)/)
+ 
+     return {
+       mainFindings: mainFindingsMatch ? 
+         JSON.parse(`[${mainFindingsMatch[1]}]`.replace(/'/g, '"')) : [],
+       nextSteps: nextFocusMatch ? 
+         JSON.parse(`[${nextFocusMatch[1]}]`.replace(/'/g, '"')) : [],
+       completenessScore: scoreMatch ? parseFloat(scoreMatch[1]) : null
+     }
+   } catch {
+     return { mainFindings: [], nextSteps: [], completenessScore: null }
+   }
+ }).value
+ 
+ // Format timestamp
+ const formatTime = (timestamp: string) => {
+   return dayjs(timestamp).format('HH:mm:ss')
+ }
+ 
+ // Get event type for timeline
+ const getEventType = (eventType: string): '' | 'primary' | 'success' | 'warning' | 'danger' => {
+   switch (eventType) {
+     case 'search_started':
+       return 'primary'
+     case 'keywords_extracted':
+       return 'warning'
+     case 'results_analyzed':
+       return 'success'
+     default:
+       return ''
+   }
+ }
+ </script>
+ 
+ <style lang="scss" module>
+ .root {
+   background-color: var(--bg-color);
+ 
+   .nav-actions {
+     display: flex;
+     flex-direction: column;
+     gap: 10px;
+     margin-top: 10px;
+     :global(.el-button + .el-button) {
+       margin-left: 0;
+     }
+   }
+ 
+   .left-sidebar {
+     border-right: 1px solid #eee;
+   }
+ 
+   .search-container {
+     max-width: 1000px;
+     margin: 0 auto;
+     padding: 20px;
+   }
+ }
+ </style> 
```

---



Now let me update the index.vue to use this new component:

---

```diff
  <template>
    <div :class="$style.pageWrapper">
-     <div :class="$style.container">
+     <div v-if="!isSearching" :class="$style.container">
        <h1 class="text-3xl font-semibold text-gray-800 mb-8">您想知道什么？</h1>
  
```

```diff
          </el-input>
        </div>
- 
-       <!-- 流式输出结果区域 -->
-       <div v-if="streamContent || isStreaming" :class="$style.resultContainer">
-         <div :class="$style.resultContent">
-           <div v-if="streamError" :class="$style.errorMessage">
-             {{ streamError.message }}
-           </div>
-           <div v-else>
-             {{ streamContent }}<span v-if="isStreaming" class="animate-pulse">▋</span>
-           </div>
-         </div>
-         <div v-if="isStreaming" :class="$style.streamingIndicator">
-           <Icon icon="mdi:loading" class="animate-spin"></Icon>
-           正在生成回答...
-         </div>
-       </div>
  
        <!-- 建议卡片 -->
-       <div v-if="!streamContent && !isStreaming" class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
+       <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
          <div
            v-for="(suggestion, index) in suggestions"
```

```diff
        </div>
      </div>
+ 
+     <!-- 搜索结果页面 -->
+     <SearchResultDetail
+       v-else
+       :search-query="searchQuery"
+       :search-events="searchEvents"
+       :status="status"
+       @stop="stopSearchStream"/>
    </div>
  </template>
```

```diff
  import { useSearchStream } from '@/pages/search/useSearchStream'
  import { ElMessage } from 'element-plus'
+ import SearchResultDetail from './SearchResultDetail.vue'
  
  const searchQuery = ref('')
+ const isSearching = ref(false)
+ const searchEvents = ref<any[]>([])
+ 
  const {
    streamContent,
    isStreaming,
    streamError,
+   status,
    startSearchStream,
    stopSearchStream
```

```diff
  ]
  
- const handleSearch = () => {
+ const handleSearch = async () => {
    if (!searchQuery.value.trim()) {
      ElMessage.warning('请输入搜索内容')
```

```diff
  
    try {
-     startSearchStream(searchQuery.value.trim())
+     isSearching.value = true
+     searchEvents.value = []
+     await startSearchStream(searchQuery.value.trim())
    } catch (error) {
      ElMessage.error('搜索请求失败，请稍后重试')
```

```diff
    }
  })
+ 
+ // 监听SSE消息
+ watch(streamContent, (content) => {
+   if (content) {
+     try {
+       const event = JSON.parse(content)
+       searchEvents.value.push(event)
+     } catch (err) {
+       console.error('Failed to parse SSE message:', err)
+     }
+   }
+ })
  
  // 组件卸载时清理
```

```diff
    }
  }
- 
- .resultContainer {
-   @apply mt-8 bg-gray-50 rounded-xl p-6;
- }
- 
- .resultContent {
-   @apply text-gray-800 leading-relaxed whitespace-pre-wrap;
-   font-size: 15px;
- }
- 
- .streamingIndicator {
-   @apply flex items-center gap-2 text-gray-500 mt-4;
-   font-size: 14px;
- }
- 
- .errorMessage {
-   @apply text-red-500 mb-4;
- }
  
  .suggestionCard {
```

---



Let me also add the necessary translations:

---

```diff
      }
  
+   },
+   search: {
+     searching: '正在搜索相关信息...',
+     keywords: '关键词',
+     mainFindings: '主要发现',
+     nextSteps: '下一步建议',
+     completeness: '完整度',
+     progress: '搜索进度',
+     events: {
+       search_started: '开始搜索',
+       keywords_extracted: '提取关键词',
+       search_executed: '执行搜索',
+       results_analyzed: '分析结果',
+       search_completed: '搜索完成'
+     }
    }
  }
```

---







```thinking
Let me explain the new implementation:

1. Created a new SearchResultDetail component that:
- Has a similar layout to FormatReportDetail but simplified
- Shows search progress with timeline
- Displays keywords as they are extracted
- Shows main findings and next steps in a clean layout
- Includes completeness score with progress bar

2. Modified index.vue to:
- Switch between search input and results view
- Pass search events to SearchResultDetail
- Handle SSE messages and update events array
- Keep the same search suggestions feature

3. Added translations for all new UI elements

The implementation maintains the same functionality but with a cleaner, more focused UI for search results.
```

I've created a new implementation for the search results display that matches your current SSE event structure. Here are the key features:

1. New SearchResultDetail Component:
   - Similar layout to FormatReportDetail but optimized for search
   - Left sidebar with back/stop actions
   - Main content area showing search results
   - Right sidebar with search progress timeline
   - Real-time updates as search progresses

2. Key Features:
   - Shows extracted keywords as tags
   - Displays main findings in a clean list
   - Shows next steps/focus areas
   - Includes completeness score with progress bar
   - Timeline showing search progress

3. Integration with Current Code:
   - Uses the same SSE connection
   - Parses and displays events in real-time
   - Maintains all existing functionality

我已经创建了一个新的搜索结果显示实现，与您当前的 SSE 事件结构匹配。以下是主要特点：

1. 新的 SearchResultDetail 组件：
   - 布局类似于 FormatReportDetail 但针对搜索进行了优化
   - 左侧边栏带有返回/停止操作
   - 主要内容区域显示搜索结果
   - 右侧边栏显示搜索进度时间线
   - 搜索过程中实时更新

2. 主要功能：
   - 以标签形式显示提取的关键词
   - 以清晰的列表形式显示主要发现
   - 显示下一步/关注点
   - 包含带进度条的完整度评分
   - 显示搜索进度的时间线

3. 与现有代码的集成：
   - 使用相同的 SSE 连接
   - 实时解析和显示事件
   - 保持所有现有功能

Would you like me to make any adjustments to this implementation?
您希望我对这个实现进行任何调整吗？

---

_**User**_

我认为你这个组件的样式需要更改，系统设计主题文件参考： @var.scss 

---

_**Assistant**_



```thinking
Let me analyze the current design and how to improve it using the system theme variables:

1. Current Issues:
- Using hardcoded colors (gray-100, gray-700 etc.)
- Not using system variables for spacing, borders, shadows
- Not consistent with the system's design language

2. Key Theme Variables to Use:
- Colors:
  - --bg-color for background
  - --primary-color for primary elements
  - --font-color-1/2/3 for text
  - --brand-color-* for brand elements
  - --gray-* for gray scales

- Spacing:
  - --m-* for margins
  - --p-* for padding
  - --radius-* for border radius

- Shadows:
  - --shadow-normal
  - --shadow-hover
  - --shadow-dialog

3. Components to Update:
- Cards
- Tags
- Progress bar
- Timeline
- Buttons
- Search input

Let me update the styles...
```

---

```diff
            <div v-if="mainFindings.length > 0" class="space-y-6">
              <!-- Main Findings -->
-             <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
-               <h2 class="text-xl font-semibold mb-4">{{ t('search.mainFindings') }}</h2>
-               <ul class="list-disc list-inside space-y-2">
-                 <li v-for="(finding, index) in mainFindings" :key="index" class="text-gray-700">
+             <div :class="$style['result-card']">
+               <h2 :class="$style['card-title']">{{ t('search.mainFindings') }}</h2>
+               <ul class="list-disc list-inside" :class="$style['card-content']">
+                 <li v-for="(finding, index) in mainFindings" :key="index">
                    {{ finding }}
                  </li>
```

```diff
  
              <!-- Next Steps -->
-             <div v-if="nextSteps.length > 0" class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
-               <h2 class="text-xl font-semibold mb-4">{{ t('search.nextSteps') }}</h2>
-               <ul class="list-disc list-inside space-y-2">
-                 <li v-for="(step, index) in nextSteps" :key="index" class="text-gray-700">
+             <div v-if="nextSteps.length > 0" :class="$style['result-card']">
+               <h2 :class="$style['card-title']">{{ t('search.nextSteps') }}</h2>
+               <ul class="list-disc list-inside" :class="$style['card-content']">
+                 <li v-for="(step, index) in nextSteps" :key="index">
                    {{ step }}
                  </li>
```

```diff
  
              <!-- Completeness Score -->
-             <div v-if="completenessScore !== null" class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
-               <h2 class="text-xl font-semibold mb-4">{{ t('search.completeness') }}</h2>
+             <div v-if="completenessScore !== null" :class="$style['result-card']">
+               <h2 :class="$style['card-title']">{{ t('search.completeness') }}</h2>
                <el-progress 
                  :percentage="completenessScore * 100" 
-                 :format="format => `${format}%`"
+                 :format="(format: number) => \`\${format}%\`"
                  :stroke-width="20"
                  status="success"/>
```

```diff
      display: flex;
      flex-direction: column;
-     gap: 10px;
-     margin-top: 10px;
+     gap: var(--m-small);
+     margin-top: var(--m-small);
      :global(.el-button + .el-button) {
        margin-left: 0;
```

```diff
  
    .left-sidebar {
-     border-right: 1px solid #eee;
+     border-right: var(--divider-height) solid var(--color-border-2);
    }
  
    .search-container {
      max-width: 1000px;
      margin: 0 auto;
-     padding: 20px;
+     padding: var(--p-larger);
+ 
+     :global {
+       .el-tag {
+         background-color: var(--brand-transparent-hover);
+         border-color: var(--brand-color-3);
+         color: var(--primary-color);
+         
+         &:hover {
+           background-color: var(--brand-transparent-active-1);
+         }
+       }
+ 
+       .el-progress-bar__outer {
+         background-color: var(--brand-color-2);
+       }
+ 
+       .el-progress-bar__inner {
+         background-color: var(--primary-color);
+       }
+ 
+       .el-timeline-item__node--primary {
+         background-color: var(--primary-color);
+       }
+ 
+       .el-timeline-item__node--success {
+         background-color: var(--success-color);
+       }
+ 
+       .el-timeline-item__node--warning {
+         background-color: var(--warning-color);
+       }
+     }
+ 
+     .result-card {
+       background-color: var(--main-bg-color);
+       border-radius: var(--radius-default);
+       padding: var(--p-larger);
+       border: var(--divider-height) solid var(--color-border-2);
+       box-shadow: var(--shadow-normal);
+       transition: all 0.3s ease;
+ 
+       &:hover {
+         box-shadow: var(--shadow-hover);
+       }
+ 
+       .card-title {
+         font-size: var(--size-font-5);
+         color: var(--font-color-1);
+         font-weight: var(--weight-font-Semiblod);
+         margin-bottom: var(--m-default);
+       }
+ 
+       .card-content {
+         color: var(--font-color-2);
+         font-size: var(--size-font-6);
+         line-height: 1.6;
+ 
+         li {
+           margin-bottom: var(--m-small);
+           
+           &:last-child {
+             margin-bottom: 0;
+           }
+         }
+       }
+     }
    }
  }
```

---







```thinking
Let me fix the linter errors in the format function...
```

---

```diff
                <el-progress 
                  :percentage="completenessScore * 100" 
-                 :format="(format: number) => `${format}%`"
+                 :format="formatPercentage"
                  :stroke-width="20"
                  status="success"/>
```

```diff
    }
  }
+ 
+ // Add format function
+ const formatPercentage = (percentage: number): string => {
+   return `${percentage}%`
+ }
  </script>
  
```

---







```thinking
The styles have been updated to use system theme variables, making the component more consistent with the overall design system. Here are the key changes:

1. Colors:
- Using --bg-color for backgrounds
- Using --primary-color for primary elements
- Using --font-color-* for text
- Using --brand-color-* for brand elements

2. Spacing:
- Using --m-* for margins
- Using --p-* for padding
- Using --radius-* for border radius

3. Typography:
- Using --size-font-* for font sizes
- Using --weight-font-* for font weights

4. Element UI Components:
- Customized el-tag styles
- Customized el-progress styles
- Customized el-timeline styles

5. Cards:
- Added hover effects with system shadows
- Using system border colors and widths
- Consistent spacing and typography

Let me explain these changes to the user.
```

我已经更新了组件的样式以使用系统主题变量。主要更改包括：

1. 颜色系统：
   - 使用 `--bg-color` 作为背景色
   - 使用 `--primary-color` 作为主色调
   - 使用 `--font-color-*` 作为文字颜色
   - 使用 `--brand-color-*` 作为品牌色系

2. 间距系统：
   - 使用 `--m-*` 作为外边距
   - 使用 `--p-*` 作为内边距
   - 使用 `--radius-*` 作为圆角半径

3. Element UI 组件样式：
   - 自定义了标签（el-tag）样式
   - 自定义了进度条（el-progress）样式
   - 自定义了时间线（el-timeline）样式

4. 卡片设计：
   - 添加了系统阴影效果（`--shadow-normal/hover`）
   - 使用系统边框颜色和宽度
   - 统一的间距和排版

这些更改使组件更好地融入了系统的设计语言，提供了更一致的视觉体验。您觉得还需要调整哪些地方吗？

---

_**User**_

需要 @SearchResultDetail.vue 全屏展示，就像 @[id].vue 一样

---

_**Assistant**_

```thinking
Let me analyze the requirements and current implementation:

1. Looking at @[id].vue, I notice it uses:
- layout: false in the page meta to enable full screen
- FormatReportDetail component that takes up full viewport

2. Current SearchResultDetail.vue issues:
- Uses fixed width percentages for sidebars (15% and 20%)
- Has padding that reduces available space
- Not using layout: false meta

3. Key changes needed:
- Add layout: false to page meta
- Make sidebars more responsive with flex
- Remove fixed width constraints
- Adjust padding and margins
- Update container max-width

4. Style changes needed:
- Use viewport units for height
- Make content area more flexible
- Adjust card layouts for better space usage
- Keep consistent spacing with system variables

Let me implement these changes.
```

我将修改 `SearchResultDetail.vue` 组件以实现全屏展示。主要更改包括：

1. 添加全屏布局配置
2. 优化侧边栏宽度和响应式布局
3. 调整内容区域的宽度和间距
4. 使用系统变量保持一致的样式

让我来进行这些更改：

---

```diff
      <div class="flex-1 flex">
        <!-- Left Sidebar -->
-       <div class="w-[15%] p-4 flex flex-col" :class="$style['left-sidebar']">
+       <div class="w-64 flex-shrink-0 p-4 flex flex-col" :class="$style['left-sidebar']">
          <div :class="$style['nav-actions']">
            <el-button @click="$router.back()" class="w-full">
```

```diff
        <div class="flex-1 flex flex-col overflow-y-auto h-full">
          <!-- Title Area -->
-         <div class="py-3 text-center w-full flex justify-center flex-col items-center gap-[10px] h-[100px]">
+         <div class="py-6 text-center w-full flex justify-center flex-col items-center gap-[10px]">
            <div class="flex items-center justify-center">
-             <span class="text-[20px] font-bold">{{ searchQuery }}</span>
+             <span class="text-2xl font-bold">{{ searchQuery }}</span>
            </div>
          </div>
  
          <!-- Content Area -->
-         <div ref="containerRef" class="flex-1 overflow-y-auto px-4 h-full" :class="$style['search-container']">
+         <div ref="containerRef" class="flex-1 overflow-y-auto px-8" :class="$style['search-container']">
            <!-- Search Progress -->
            <div v-if="status === 'OPEN'" class="mb-8">
```

```diff
  
        <!-- Right Sidebar -->
-       <div class="w-[20%] p-4">
+       <div class="w-80 flex-shrink-0 p-4">
          <!-- Progress Timeline -->
          <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
```

```diff
  const { t } = useI18n()
  const containerRef = ref<HTMLElement | null>(null)
+ 
+ // Add page meta configuration
+ defineOptions({
+   layout: false,
+ })
  
  // Extract keywords from events
```

```diff
  .root {
    background-color: var(--bg-color);
+   min-height: 100vh;
+   display: flex;
+   flex-direction: column;
  
    .nav-actions {
```

```diff
    .left-sidebar {
      border-right: var(--divider-height) solid var(--color-border-2);
+     background-color: var(--main-bg-color);
    }
  
    .search-container {
-     max-width: 1000px;
+     max-width: 1200px;
+     width: 100%;
      margin: 0 auto;
      padding: var(--p-larger);
```

```diff
        box-shadow: var(--shadow-normal);
        transition: all 0.3s ease;
+       margin-bottom: var(--m-larger);
  
        &:hover {
```

---



我已经更新了 `SearchResultDetail.vue` 组件以实现全屏布局。主要更改包括：

1. 布局配置：
   - 添加了 `layout: false` 配置以启用全屏显示
   - 设置了 `min-height: 100vh` 确保组件占满整个视口

2. 侧边栏优化：
   - 左侧边栏宽度从 15% 改为固定的 `w-64`（256px）
   - 右侧边栏宽度从 20% 改为固定的 `w-80`（320px）
   - 添加了 `flex-shrink-0` 防止侧边栏被压缩

3. 内容区域改进：
   - 增加了内容区域最大宽度至 1200px
   - 优化了标题区域的间距和字体大小
   - 调整了内容区域的内边距，提供更好的阅读体验

4. 样式优化：
   - 为侧边栏添加了背景色
   - 优化了卡片之间的间距
   - 使用系统变量保持样式一致性

这些更改使得组件现在能够全屏显示，并且布局更加合理。您觉得还需要调整什么地方吗？

---

_**User**_

我希望 @SearchResultDetail.vue 可以作为一个单独的路由，而不是 @index.vue 的子组件

---

_**Assistant**_

```thinking
Let me analyze what needs to be done to make SearchResultDetail a standalone route:

1. Current Structure:
- SearchResultDetail is a child component in index.vue
- State is managed in index.vue and passed as props
- Search functionality is controlled by parent

2. Changes Needed:
- Move SearchResultDetail to pages directory as a route component
- Add route configuration with path parameters
- Move search state management into the route component
- Handle search initialization and control within the component
- Update index.vue to navigate to the new route instead of showing component

3. Key Implementation Points:
- Need to pass search query as route parameter
- Should preserve all current functionality
- Need to handle search stream initialization
- Keep the full screen layout
- Maintain consistent styling

4. File Changes Required:
- Create new route file
- Modify index.vue to remove component usage
- Update router configuration
- Move relevant state management

Let me implement these changes.
```

我将把 `SearchResultDetail` 组件改造成独立路由。主要更改包括：

1. 创建新的路由组件
2. 修改现有组件的引用关系
3. 更新路由配置
4. 迁移状态管理逻辑

让我来进行这些更改：

---

```diff
- 
+ <template>
+   <div class="min-h-screen flex flex-col" :class="$style.root">
+     <!-- Header -->
+     <Header :is-show-menu="false"/>
+ 
+     <!-- Main Content -->
+     <div class="flex-1 flex">
+       <!-- Left Sidebar -->
+       <div class="w-64 flex-shrink-0 p-4 flex flex-col" :class="$style['left-sidebar']">
+         <div :class="$style['nav-actions']">
+           <el-button @click="$router.back()" class="w-full">
+             <Icon icon="mdi:arrow-left" class="mr-2"/> {{ t("global.back") }}
+           </el-button>
+ 
+           <el-button
+             v-if="status === 'OPEN'"
+             @click="stopSearchStream"
+             class="w-full"
+             type="danger">
+             <Icon icon="mdi:stop" class="mr-2"/> {{ t("global.stop") }}
+           </el-button>
+         </div>
+       </div>
+ 
+       <!-- Main Content Area -->
+       <div class="flex-1 flex flex-col overflow-y-auto h-full">
+         <!-- Title Area -->
+         <div class="py-6 text-center w-full flex justify-center flex-col items-center gap-[10px]">
+           <div class="flex items-center justify-center">
+             <span class="text-2xl font-bold">{{ decodeURIComponent(query) }}</span>
+           </div>
+         </div>
+ 
+         <!-- Content Area -->
+         <div ref="containerRef" class="flex-1 overflow-y-auto px-8" :class="$style['search-container']">
+           <!-- Search Progress -->
+           <div v-if="status === 'OPEN'" class="mb-8">
+             <SearchSource
+               :show="true"
+               icon="mdi:magnify"
+               :text="t('search.searching')"/>
+             
+             <div v-if="keywords.length > 0" class="mt-4">
+               <h3 class="text-lg font-semibold mb-2">{{ t('search.keywords') }}</h3>
+               <div class="flex flex-wrap gap-2">
+                 <el-tag 
+                   v-for="keyword in keywords" 
+                   :key="keyword"
+                   size="large"
+                   effect="plain">
+                   {{ keyword }}
+                 </el-tag>
+               </div>
+             </div>
+           </div>
+ 
+           <!-- Search Results -->
+           <div v-if="mainFindings.length > 0" class="space-y-6">
+             <!-- Main Findings -->
+             <div :class="$style['result-card']">
+               <h2 :class="$style['card-title']">{{ t('search.mainFindings') }}</h2>
+               <ul class="list-disc list-inside" :class="$style['card-content']">
+                 <li v-for="(finding, index) in mainFindings" :key="index">
+                   {{ finding }}
+                 </li>
+               </ul>
+             </div>
+ 
+             <!-- Next Steps -->
+             <div v-if="nextSteps.length > 0" :class="$style['result-card']">
+               <h2 :class="$style['card-title']">{{ t('search.nextSteps') }}</h2>
+               <ul class="list-disc list-inside" :class="$style['card-content']">
+                 <li v-for="(step, index) in nextSteps" :key="index">
+                   {{ step }}
+                 </li>
+               </ul>
+             </div>
+ 
+             <!-- Completeness Score -->
+             <div v-if="completenessScore !== null" :class="$style['result-card']">
+               <h2 :class="$style['card-title']">{{ t('search.completeness') }}</h2>
+               <el-progress 
+                 :percentage="completenessScore * 100" 
+                 :format="formatPercentage"
+                 :stroke-width="20"
+                 status="success"/>
+             </div>
+           </div>
+         </div>
+       </div>    
+ 
+       <!-- Right Sidebar -->
+       <div class="w-80 flex-shrink-0 p-4">
+         <!-- Progress Timeline -->
+         <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
+           <h3 class="font-semibold mb-4">{{ t('search.progress') }}</h3>
+           <el-timeline>
+             <el-timeline-item
+               v-for="event in searchEvents"
+               :key="event.timestamp"
+               :timestamp="formatTime(event.timestamp)"
+               :type="getEventType(event.event_type)">
+               {{ t(`search.events.${event.event_type}`) }}
+             </el-timeline-item>
+           </el-timeline>
+         </div>
+       </div>
+     </div>
+   </div>
+ </template>
+ 
+ <script setup lang="ts">
+ import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
+ import { useI18n } from 'vue-i18n'
+ import { Icon } from '@iconify/vue'
+ import { useRoute } from 'vue-router/auto'
+ import { ElMessage } from 'element-plus'
+ import dayjs from 'dayjs'
+ import Header from '@/components/LayoutComponents/Header.vue'
+ import SearchSource from '@/pages/format-report/__components__/SearchSource.vue'
+ import { useSearchStream } from '@/pages/search/useSearchStream'
+ import type { RouteParams } from 'vue-router/auto'
+ 
+ // Page Configuration
+ defineOptions({
+   layout: false,
+ })
+ 
+ type RouteParamsWithQuery = RouteParams<'/search/result/[query]'>
+ const route = useRoute()
+ const query = computed(() => (route.params as RouteParamsWithQuery).query)
+ 
+ interface SearchEvent {
+   event_type: string;
+   iteration: number;
+   content: string;
+   timestamp: string;
+ }
+ 
+ const { t } = useI18n()
+ const containerRef = ref<HTMLElement | null>(null)
+ const searchEvents = ref<SearchEvent[]>([])
+ 
+ const {
+   streamContent,
+   streamError,
+   status,
+   startSearchStream,
+   stopSearchStream
+ } = useSearchStream()
+ 
+ // Extract keywords from events
+ const keywords = computed(() => {
+   const keywordEvent = searchEvents.value.find(e => e.event_type === 'keywords_extracted')
+   if (!keywordEvent) {return []}
+   try {
+     return JSON.parse(keywordEvent.content.replace(/'/g, '"'))
+   } catch {
+     return []
+   }
+ })
+ 
+ // Extract main findings and next steps
+ const { mainFindings, nextSteps, completenessScore } = computed(() => {
+   const resultsEvent = searchEvents.value.find(e => e.event_type === 'results_analyzed')
+   if (!resultsEvent) {return { mainFindings: [], nextSteps: [], completenessScore: null }}
+ 
+   try {
+     const content = resultsEvent.content
+     const mainFindingsMatch = content.match(/main_findings=\[(.*?)\]/)
+     const nextFocusMatch = content.match(/next_focus=\[(.*?)\]/)
+     const scoreMatch = content.match(/completeness_score=([\d.]+)/)
+ 
+     return {
+       mainFindings: mainFindingsMatch ? 
+         JSON.parse(`[${mainFindingsMatch[1]}]`.replace(/'/g, '"')) : [],
+       nextSteps: nextFocusMatch ? 
+         JSON.parse(`[${nextFocusMatch[1]}]`.replace(/'/g, '"')) : [],
+       completenessScore: scoreMatch ? parseFloat(scoreMatch[1]) : null
+     }
+   } catch {
+     return { mainFindings: [], nextSteps: [], completenessScore: null }
+   }
+ }).value
+ 
+ // Format timestamp
+ const formatTime = (timestamp: string) => {
+   return dayjs(timestamp).format('HH:mm:ss')
+ }
+ 
+ // Get event type for timeline
+ const getEventType = (eventType: string): '' | 'primary' | 'success' | 'warning' | 'danger' => {
+   switch (eventType) {
+   case 'search_started':
+     return 'primary'
+   case 'keywords_extracted':
+     return 'warning'
+   case 'results_analyzed':
+     return 'success'
+   default:
+     return ''
+   }
+ }
+ 
+ // Add format function
+ const formatPercentage = (percentage: number): string => {
+   return `${percentage}%`
+ }
+ 
+ // Watch for stream content
+ watch(streamContent, (content) => {
+   if (content) {
+     try {
+       const event = JSON.parse(content)
+       searchEvents.value.push(event)
+     } catch (err) {
+       console.error('Failed to parse SSE message:', err)
+     }
+   }
+ })
+ 
+ // Watch for stream errors
+ watch(streamError, (error) => {
+   if (error) {
+     ElMessage.error(error.message)
+   }
+ })
+ 
+ // Start search on mount
+ onMounted(async () => {
+   try {
+     await startSearchStream(decodeURIComponent(query.value))
+   } catch (error) {
+     ElMessage.error('搜索请求失败，请稍后重试')
+   }
+ })
+ 
+ // Cleanup on unmount
+ onUnmounted(() => {
+   stopSearchStream()
+ })
+ </script>
+ 
+ <style lang="scss" module>
+ .root {
+   background-color: var(--bg-color);
+   min-height: 100vh;
+   display: flex;
+   flex-direction: column;
+ 
+   .nav-actions {
+     display: flex;
+     flex-direction: column;
+     gap: var(--m-small);
+     margin-top: var(--m-small);
+     :global(.el-button + .el-button) {
+       margin-left: 0;
+     }
+   }
+ 
+   .left-sidebar {
+     border-right: var(--divider-height) solid var(--color-border-2);
+     background-color: var(--main-bg-color);
+   }
+ 
+   .search-container {
+     max-width: 1200px;
+     width: 100%;
+     margin: 0 auto;
+     padding: var(--p-larger);
+ 
+     :global {
+       .el-tag {
+         background-color: var(--brand-transparent-hover);
+         border-color: var(--brand-color-3);
+         color: var(--primary-color);
+         
+         &:hover {
+           background-color: var(--brand-transparent-active-1);
+         }
+       }
+ 
+       .el-progress-bar__outer {
+         background-color: var(--brand-color-2);
+       }
+ 
+       .el-progress-bar__inner {
+         background-color: var(--primary-color);
+       }
+ 
+       .el-timeline-item__node--primary {
+         background-color: var(--primary-color);
+       }
+ 
+       .el-timeline-item__node--success {
+         background-color: var(--success-color);
+       }
+ 
+       .el-timeline-item__node--warning {
+         background-color: var(--warning-color);
+       }
+     }
+ 
+     .result-card {
+       background-color: var(--main-bg-color);
+       border-radius: var(--radius-default);
+       padding: var(--p-larger);
+       border: var(--divider-height) solid var(--color-border-2);
+       box-shadow: var(--shadow-normal);
+       transition: all 0.3s ease;
+       margin-bottom: var(--m-larger);
+ 
+       &:hover {
+         box-shadow: var(--shadow-hover);
+       }
+ 
+       .card-title {
+         font-size: var(--size-font-5);
+         color: var(--font-color-1);
+         font-weight: var(--weight-font-Semiblod);
+         margin-bottom: var(--m-default);
+       }
+ 
+       .card-content {
+         color: var(--font-color-2);
+         font-size: var(--size-font-6);
+         line-height: 1.6;
+ 
+         li {
+           margin-bottom: var(--m-small);
+           
+           &:last-child {
+             margin-bottom: 0;
+           }
+         }
+       }
+     }
+   }
+ }
+ </style> 
```

---

```diff
  <template>
    <div :class="$style.pageWrapper">
-     <div v-if="!isSearching" :class="$style.container">
+     <div :class="$style.container">
        <h1 class="text-3xl font-semibold text-gray-800 mb-8">您想知道什么？</h1>
  
```

```diff
                :class="$style.searchButton"
                type="primary"
-               :loading="isStreaming"
+               :loading="isLoading"
                @click="handleSearch">
-               {{ isStreaming ? '思考中...' : '搜索' }}
+               {{ isLoading ? '思考中...' : '搜索' }}
              </el-button>
            </template>
```

```diff
        </div>
      </div>
- 
-     <!-- 搜索结果页面 -->
-     <SearchResultDetail
-       v-else
-       :search-query="searchQuery"
-       :search-events="searchEvents"
-       :status="status"
-       @stop="stopSearchStream"/>
    </div>
  </template>
  
  <script setup lang="ts">
- import { onUnmounted, ref, watch } from 'vue'
+ import { ref } from 'vue'
  import { Icon } from '@iconify/vue'
- import { useSearchStream } from '@/pages/search/useSearchStream'
+ import { useRouter } from 'vue-router/auto'
  import { ElMessage } from 'element-plus'
- import SearchResultDetail from './SearchResultDetail.vue'
  import { definePage } from 'vue-router/auto'
  
```

```diff
  })
  
+ const router = useRouter()
  const searchQuery = ref('')
- const isSearching = ref(false)
- const searchEvents = ref<any[]>([])
- 
- const {
-   streamContent,
-   isStreaming,
-   streamError,
-   status,
-   startSearchStream,
-   stopSearchStream
- } = useSearchStream()
+ const isLoading = ref(false)
  
  const suggestions = [
```

```diff
  
    try {
-     isSearching.value = true
-     searchEvents.value = []
-     await startSearchStream(searchQuery.value.trim())
+     isLoading.value = true
+     await router.push(`/search/result/${encodeURIComponent(searchQuery.value.trim())}`)
    } catch (error) {
-     ElMessage.error('搜索请求失败，请稍后重试')
+     ElMessage.error('导航失败，请稍后重试')
+   } finally {
+     isLoading.value = false
    }
  }
```

```diff
    handleSearch()
  }
- 
- // 监听错误
- watch(streamError, (error) => {
-   if (error) {
-     ElMessage.error(error.message)
-   }
- })
- 
- // 监听SSE消息
- watch(streamContent, (content) => {
-   if (content) {
-     try {
-       const event = JSON.parse(content)
-       searchEvents.value.push(event)
-     } catch (err) {
-       console.error('Failed to parse SSE message:', err)
-     }
-   }
- })
- 
- // 组件卸载时清理
- onUnmounted(() => {
-   stopSearchStream()
- })
  </script>
  
```

---



我已经将搜索结果页面改造成独立路由。主要更改包括：

1. 创建新的路由组件：
   - 在 `src/pages/search/result/[query].vue` 创建新的路由组件
   - 使用 URL 参数传递搜索查询
   - 保持全屏布局和现有功能

2. 修改首页组件：
   - 移除 `SearchResultDetail` 子组件
   - 改用路由导航实现搜索跳转
   - 简化状态管理，只保留必要的逻辑

3. 路由跳转逻辑：
   - 搜索时使用 `router.push` 导航到结果页
   - 使用 `encodeURIComponent` 处理查询参数
   - 添加加载状态和错误处理

4. 状态管理优化：
   - 搜索状态现在由路由组件管理
   - 搜索流程在路由组件中初始化
   - 保持了完整的错误处理和清理逻辑

这样的改造使得代码结构更清晰，同时保持了原有的功能和用户体验。您觉得还需要调整什么地方吗？

---

