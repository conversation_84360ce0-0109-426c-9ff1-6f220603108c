<template>
  <div :class="$style.specificIndex">
    <el-container direction="horizontal" :class="$style.specificContainer">
      <el-aside :class="$style.leftAside" width="208px" :collapse="collapse">
        <el-menu
          :default-active="currentPath"
          :class="[$style.menu, $style.menuOne]"
          :router="true">
          <div :class="$style.menuTop">
            <el-button :class="$style.menuTopBtn"
                       type="primary"
                       round
                       @click="handlerRouterToHome">
              <Icon icon="mdi:arrow-left" class="text-[#fff] text-[16px]"/>
              <span>{{ $t('setting.backBtn') }}</span>
            </el-button>
            <span :class="$style.menuTopTitle">{{ $t('setting.menuTitle') }}</span>
          </div>
        </el-menu>

        <el-menu
          :class="[$style.menu, $style.menuTwo]"
          :router="true">
          <el-divider :class="$style.menuDivider"/>
          <el-menu-item
            v-for="(item) in systemMenus"
            :key="item.name"
            :index="item.name"
            :route="item.route"
            :class="[$style.menuItem, { [$style.isActive]: isActive(item) }]">
            <span>{{ item.name }}</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-container direction="vertical" :class="$style.wrap">

        <el-main :style="{ background: '#fff' }" :class="[$style.main]">
          <RouterView v-slot="{ Component }">
            <Transition mode="out-in">
              <KeepAlive :include="keepAliveNames">
                <Suspense>
                  <!-- 主要内容 -->
                  <div class="w-full h-full overflow-auto">
                    <component :is="Component"></component>
                  </div>

                  <!-- 加载中状态 -->
                  <template #fallback>
                    <div class="h-full w-full flex justify-center items-center">
                      <div class="w-[50%]">
                        <el-skeleton :rows="5" animated/>
                      </div>
                    </div>
                  </template>
                </Suspense>
              </KeepAlive>
            </Transition>
          </RouterView>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import { ref, computed, onBeforeMount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router/auto'
import { useI18n } from 'vue-i18n'
import type { RoutePath } from 'types'
import { useKeepAliveRoutes } from '@/stores/keepAliveRoutes'
import { Icon } from '@iconify/vue'
import { USER_TYPE_MAP, UserType } from '@/stores/user'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const collapse = ref(false)
const { keepAliveNames } = useKeepAliveRoutes()

type RouteConfig = {
  name: string
  path: RoutePath[]
  route: {
    path: RoutePath
  }
  permission?: keyof typeof USER_TYPE_MAP[UserType]
}
const isActive = (menu: RouteConfig) => {
  return menu.path.includes(`${currentPath.value}/` as RoutePath) || menu.path.includes(currentPath.value as RoutePath)
}

const systemMenus = computed(() => {
  const routes: RouteConfig[] = [
    {
      name: t('setting.peopleManage'),
      path: ['/settings'],
      route: {
        path: '/settings',
      },
      permission: 'canManageUsers'
    },
    // {
    //   name: t('setting.plansTitle'),
    //   path: ['/settings/plans'],
    //   route: {
    //     path: '/settings/plans',
    //   },
    //   permission: 'canAccessPlans'
    // },
    {
      name: t('setting.companyInfo'),
      path: ['/settings/company-info'],
      route: {
        path: '/settings/company-info',
      },
      permission: 'canAccessCompanyInfo'
    },
    {
      name: t('setting.personalInfo'),
      path: ['/settings/personal-info'],
      route: {
        path: '/settings/personal-info',
      },
      // 个人信息所有角色都可以访问
      permission: undefined
    },
  ]

  // 根据用户权限过滤菜单
  return routes.filter(item => {
    // 如果没有指定权限要求，允许访问
    if (!item.permission) {return true}
    // 检查用户是否有对应权限
    return userStore.hasPermission(item.permission as keyof typeof USER_TYPE_MAP[UserType])
  })
})

const currentPath = computed(() => {
  return route.path
})

const handlerRouterToHome = () => {
  router.push('/')
}

</script>

<style lang="scss" module>
$menuBg: #33344b;
$defaultColor: #b7b6cd;
$activeColor: #fff;

.specificIndex {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

}

.specificContainer {
  height: 100%;
  flex: 1;
  overflow: hidden;
}

.main {
  height: 100%;
  padding: 0 !important;
}

.wrap {
  position: relative;
}
.leftAside {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.menu {
  padding: 0 12px;
}
.menuOne {
  flex: 1 1;
}
.menuTwo {
  flex: 1 0 100%;
}
.menuDivider {
  margin: 0;
  padding: 10px 0;
}
.menuItem {
  padding: 7px 12px;
  height: 36px;
  border-radius: 20px;
  &:global(.is-active) {
    color: initial
  }
  &:hover {
    background: var(--brand-color-1, #ECE8FF);
  }
  &.isActive {
    padding: 7px 12px;
    border-radius: 20px;
    background: var(--brand-color-1, #ECE8FF);
    color: var(--primary-color);
  }
  & + & {
    margin-top: 2px;
  }
}
.menuTitle {
  padding: 0 18px 16px;
  color: var(--font-color-2, #797A85);
  font-size: 13px;
}
.menuDivider {
  display: flex;
  margin: 16px 18px;
  padding: 0;
  width: 140px;
}
.menuTop {
  padding: 24px 18px 0;
  display: flex;
  flex-direction: column;
}
.menuTopTitle {
  margin-top: 24px;
  color: var(--font-color-1, #1F2134);
  font-size: 20px;
  font-weight: 500;
}
.menuTopBtn {
  width: 110px;
  .icon {
    margin-right: 4px;
  }
}
.alert {
  height: 32px;
  line-height: 32px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 100;
  :global(.el-alert .el-alert__close-btn) {
    top: 8px;
  }
}
</style>
