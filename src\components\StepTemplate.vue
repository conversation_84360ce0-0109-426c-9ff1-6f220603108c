<template>
  <div
    class="w-full h-full flex flex-col items-center justify-center mb-20 max-w-(--breakpoint-lg) mx-auto">
    <div class="mb-4 w-full flex flex-col items-center justify-center gap-8">
      <div class="text-center text-[30px]  font-bold text-2xl">
        {{ $t('poll.title') }}
      </div>
      <StepProgress
        :steps="stepsProps"
        :active="stepNumber"
        class="w-full px-4 mb-4"/>
    </div>
    <div
      class="mb-8 w-full max-w-3xl flex flex-col justify-center items-center">
      <div class="flex flex-col w-full">
        <div
          class="flex items-center h-[70px] border border-[#E5E7EB] rounded-[12px] p-2 w-full bg-[#F9FAFB] shadow-xs mb-2">
          <input
            ref="inputRef"
            type="text"
            v-model="inputText"
            class="grow bg-transparent border-none outline-hidden px-4 text-[#111827]"
            :placeholder="description"
            @input="debounceInputEmit"
            @keyup.enter="handleNext"/>
          <button
            v-if="isSkip"
            @click="handleNext"
            :class="$style['back-button']">
            <!-- <arrow-right theme="outline" size="24" fill="#fff"/> -->
            <Icon icon="mdi:arrow-right" class="text-[#fff] text-[24px]"/>
          </button>
        </div>
      </div>
      <div class="flex justify-center flex-wrap gap-4 my-8 w-full">
        <button
          v-for="(option, index) in options"
          :key="index"
          class="border border-[#4466BC] rounded-full py-2 px-4 bg-white hover:bg-[#E6F0FF] text-main-color font-medium transition-colors duration-300"
          :style="{
            backgroundColor: selected.includes(option)
              ? 'var(--primary-color)'
              : '',
            color: selected.includes(option) ? 'white' : '',
          }"
          @click="handleClickOption(option)">
          {{ option }}
        </button>
      </div>
      <div class="flex justify-between w-full">
        <button
          class="p-2 bg-white border font-bold border-[#D1D5DB] text-main-color rounded-full w-[150px] hover:bg-gray-50 transition-colors duration-300"
          @click="handlePrevious">
          {{ preText }}
        </button>
        <button
          class="p-2 font-bold rounded-full w-[150px] transition-colors duration-300"
          @click="handleNext"
          :class="[
            disabledNext && !isSkip
              ? 'bg-[#ccc] text-black'
              : 'bg-[#4466BC] text-white hover:bg-[#1E40AF]',
          ]">
          {{ nextText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, type PropType, onMounted } from 'vue'
import { debounce } from 'lodash-es'
import { useCamelCaseAttrs } from '@/hooks/useCamelCaseAttrs'
import { Icon } from '@iconify/vue'
import StepProgress from './StepProgress.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const props = defineProps({
  description: {
    type: String,
    default: '',
  },
  options: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  stepNumber: {
    type: Number,
    default: 0,
  },
  selected: {
    type: Array as PropType<String[]>,
    default: () => [],
  },
  stepTotalNumber: {
    type: Number,
    required: true,
  },
  nextText: {
    type: String,
    default: '提交',
  },
  preText: {
    type: String,
    default: '上一步',
  },
  steps: {
    type: Array as PropType<{
      title: string;
      description?: string;
      icon?: string;
      status?: 'wait' | 'process' | 'finish' | 'error' | 'success';
    }[]>,
    default: () => [

    ]
  }
})

const stepsProps = computed(() => {
  return [
    { title: t('poll.sector') },
    { title: t('poll.region') },
    { title: t('poll.style') },
    { title: t('poll.overseas') }
  ]
})
const { attrs, camelCaseAttrs } = useCamelCaseAttrs()

const isSkip = computed(() =>
  attrs.isSkip === '' || !!attrs.isSkip ? true : false
)
const emit = defineEmits<{
  previous: [];
  next: [];
  skip: [];
  pick: [value: string];
  cancel: [value: string];
  change: [value: string];
}>()

const debounceInputEmit = debounce(() => {
  emit('change', inputText.value)
}, 100)
const handlePrevious = () => {
  emit('previous')
}

const inputText = ref<string>('')
const disabledNext = computed(
  () => props.selected.length === 0 && inputText.value?.length === 0
)
const handleNext = () => {
  if (isSkip.value || !disabledNext.value) {
    emit('next')
  }
}

const handleClickOption = (option: string) => {
  if (props.selected.includes(option)) {
    emit('cancel', option)
    return
  }
  emit('pick', option)
}

const inputRef = ref<HTMLInputElement | null>(null)

onMounted(() => {
  // 在组件挂载后自动聚焦input
  inputRef.value?.focus()
})
</script>
<style lang="scss" module>
.back-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.back-button:hover {
  background-color: #003d82;
}

.back-button:active {
  background-color: var(--primary-color);
}

/* 确保图标颜色与背景形成对比 */
:deep(svg) {
  fill: #ffffff;
}
</style>
