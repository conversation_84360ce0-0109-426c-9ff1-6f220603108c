<template>
  <template v-for="field in fields" :key="field.key">
    <el-form-item :label="$t(field.label)">
      <el-select
        v-if="field.type === 'select'"
        :model-value="getFieldValue(field.key)"
        @update:model-value="updateFieldValue(field.key, $event)">
        <el-option
          v-for="option in field.options"
          :key="option.value"
          :label="$t(option.label)"
          :value="option.value"/>
      </el-select>
      <el-input
        v-else
        :model-value="getFieldValue(field.key)"
        @update:model-value="updateFieldValue(field.key, $event)"
        :type="inputType"
        :rows="rows"
        :placeholder="field.placeholder ? $t(field.placeholder) : ''"/>
    </el-form-item>
  </template>
</template>

<script setup lang="ts">
interface FormField {
  key: string
  label: string
  type?: 'select' | 'input' | 'textarea'
  options?: Array<{
    label: string
    value: string
  }>
  placeholder?: string
}

interface Props {
  fields: FormField[]
  form: Record<string, any>
  inputType?: string
  rows?: number
}

const props = defineProps<Props>()

// 处理嵌套属性的获取
const getFieldValue = (key: string) => {
  const parts = key.split('.')
  let value: any = props.form
  
  for (const part of parts) {
    if (value === undefined || value === null) {return ''}
    value = value[part]
  }
  
  return value ?? ''
}

// 处理嵌套属性的更新
const updateFieldValue = (key: string, value: any) => {
  const parts = key.split('.')
  const lastPart = parts.pop()!
  let current = props.form
  
  // 构建嵌套路径
  for (const part of parts) {
    if (!(part in current)) {
      current[part] = {}
    }
    current = current[part]
  }
  
  // 更新值
  current[lastPart] = value
}
</script>
