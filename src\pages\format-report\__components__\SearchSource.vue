<template>
  <Transition
    enter-active-class="transition duration-300 ease-out"
    enter-from-class="transform opacity-0 scale-95"
    enter-to-class="transform opacity-100 scale-100"
    leave-active-class="transition duration-300 ease-in"
    leave-from-class="transform opacity-100 scale-100"
    leave-to-class="transform opacity-0 scale-95">
    <div class="flex items-center justify-center z-50" v-if="show">
      <div class="w-full max-w-[1150px] bg-white/90 dark:bg-gray-800/90 backdrop-blur-xs rounded-lg p-6 my-4" :class="$style['highlight-source']">
        <div class="relative flex flex-col items-center gap-4">
          <!-- Dynamic Icon -->
          <div class="relative">
            <Icon
              :icon="icon"
              class="text-4xl text-primary-600 dark:text-primary-400"
              :class="$style['pulse-icon']"/>
            
            <!-- Data Particles -->
            <div class="absolute top-0 left-0 w-full h-full">
              <div 
                v-for="i in 6" 
                :key="i"
                class="absolute w-1.5 h-1.5 bg-primary-500/60 rounded-full"
                :class="[
                  $style[`data-particle-${i}`],
                  $style['data-flow']
                ]"/>
            </div>
          </div>
          
          <!-- Display Text -->
          <p class="text-main-color dark:text-gray-200 font-medium" :class="$style['typing-text']">
            {{ text }}
          </p>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import type { url } from '../type'
interface Props {
  icon: string
  text: string
  show: boolean
  searchResults?: url[]
}

defineProps<Props>()
</script>

<style lang="scss" module>
.data-flow {
  animation: dataFlow 2s infinite;
}

.pulse-icon {
  animation: pulse 2s ease-in-out infinite;
}

.highlight-source {
  border: 1px solid transparent;
  animation: source-highlight 3s ease-in-out infinite;
}

.typing-text {
  animation: typing 0.5s ease-out;
}

.data-particle-1 {
  animation-delay: 0.2s;
  left: 20%;
}

.data-particle-2 {
  animation-delay: 0.4s;
  left: 40%;
}

.data-particle-3 {
  animation-delay: 0.6s;
  left: 60%;
}

.data-particle-4 {
  animation-delay: 0.8s;
  left: 80%;
}

.data-particle-5 {
  animation-delay: 1s;
  left: 100%;
}

.data-particle-6 {
  animation-delay: 1.2s;
  left: 120%;
}

@keyframes dataFlow {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  
  100% {
    transform: translateY(-15px) scale(0.5);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  
  50% {
    transform: scale(1.1);
  }
}

@keyframes source-highlight {
  0%, 100% {
    border-color: transparent;
    box-shadow: none;
  }
  
  50% {
    border-color: var(--el-color-primary-light-8);
    box-shadow: 0 0 20px var(--el-color-primary-light-6);
  }
}

@keyframes typing {
  from {
    opacity: 0;
  }
  
  to {
    opacity: 1;
  }
}
</style>
