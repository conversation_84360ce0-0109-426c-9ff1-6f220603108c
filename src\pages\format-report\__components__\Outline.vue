<template>
  <div :class="$style.outline">
    <h3 :class="$style.title">{{ $t('intelligence.outline.title') }}</h3>
    <ul :class="$style.sectionList">
      <li
        v-for="(section, key) in sections"
        :key="key"
        :class="[
          $style.sectionItem, 
          { [$style.active]: highlightedSection === key }
        ]"
        @click="handleSectionClick(key as string)">
        {{ key }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'

interface Section {
  description: string
  keywords: string[]
}

interface Sections {
  [key: string]: Section
}

const emit = defineEmits<{
    'section-change': [sectionKey: string, shouldScroll: boolean]
}>()

const props = defineProps({
  sections: {
    type: Object as PropType<Sections>,
    required: true
  },
  highlightedSection: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    required: true
  },
  startedSections: {
    type: Array as PropType<string[]>,
  }
})

const handleSectionClick = (sectionKey: string) => {
  if (props.startedSections && !props.startedSections.includes(sectionKey)) {
    return
  }
  emit('section-change', sectionKey, true)
}
</script>

<style lang="scss" module>
.outline {
  padding:0 16px;
  border-left: 1px solid #eee;
}

.title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.sectionList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sectionItem {
  color: #6b7280;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin-bottom: 4px;
  
  &:hover {
    color: #374151;
  }

  &.active {
    color: black;
    font-weight: 500;
    background-color: #f3f4f6;
  }
  
  &.pending {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>
