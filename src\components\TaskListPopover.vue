<template>
  <el-popover
    v-model:visible="showPopover"
    :width="380"
    trigger="click"
    placement="bottom-end" 
    popper-class="task-popover" 
    :show-arrow="false"
    :offset="12"
    :teleported="true" 
    @show="handlePopoverShow">
    <template #reference>
      <el-badge :value="tasks.length"
                :hidden="tasks.length === 0"
                class="item cursor-pointer"
                type="primary">
        <el-button 
          type="primary" 
          circle 
          v-loading="loading">
          <!-- Use ListTodo icon -->
          <ListTodo class="w-5 h-5"/> 
        </el-button>
      </el-badge>
    </template>
    <!-- Task List Content -->
    <div class="task-list">
      <div class="flex justify-between items-center p-4 border-b border-border">
        <h3 class="text-card-foreground text-base font-semibold">Task List</h3>
        <button
          class="w-6 h-6 rounded-full text-muted-foreground hover:bg-accent/20 hover:text-accent-foreground transition-colors flex items-center justify-center"
          @click="showPopover = false">
          <svg viewBox="0 0 24 24"
               class="w-3.5 h-3.5"
               fill="none"
               stroke="currentColor"
               stroke-width="2">
            <path d="M18 6L6 18M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <div class="max-h-[400px] overflow-y-auto p-4">
        <div
          v-for="task in tasks"
          :key="task.id"
          class="task-item bg-card rounded-lg p-4 mb-3 border border-border transition-all hover:shadow-md">
          <div class="flex justify-between items-center mb-3">
            <span class="text-card-foreground text-sm">{{ task.report_name }}</span>
          </div>
          <div class="progress-container">
            <div class="flex justify-between items-center mb-1">
              <span class="text-xs text-muted-foreground">{{ formatStatus(task.status) }}</span>
              <span class="text-xs font-medium text-card-foreground">{{ task.progress }}%</span>
            </div>
            <div class="h-1 bg-muted rounded-sm overflow-hidden">
              <div
                class="h-full transition-all duration-300 relative"
                :style="{
                  width: `${task.progress}%`,
                  background: getProgressBackground(task)
                }">
                <div class="absolute inset-0 shine-effect"></div>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-if="tasks.length === 0" description="No running tasks" :image-size="60"/>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElPopover, ElBadge, ElButton, ElEmpty } from 'element-plus'
import { useGet } from '@/hooks/useHttp'
import emitter from '@/utils/events'
import { ListTodo } from 'lucide-vue-next' // Import the new icon

// Task Interface
interface Task {
  id: number
  report_name: string
  status: string
  progress: number
  sync_type: boolean
}

// Refs and State
const tasks = ref<Task[]>([])
const showPopover = ref(false)
const { execute: fetchProgress, isLoading: loading } = useGet<Task[]>('/formatted_report/check_report_list', 
  { showError: false }, 
  { immediate: false }
)

// Functions
const fetchProgressReports = async () => {
  try {
    const data = await fetchProgress()
    if (Array.isArray(data)) {
      // Filter only running tasks that are not sync_type
      tasks.value = data.filter(task => !task.sync_type && task.status === 'running')
    } else {
      tasks.value = [] // Ensure tasks is an array if API returns non-array
    }
  } catch (error) {
    console.error('Failed to fetch progress reports:', error)
    tasks.value = []
  }
}

const handlePopoverShow = () => {
  fetchProgressReports() // Refresh data when popover shows
}

const formatStatus = (status: string): string => {
  if (!status) { return 'Pending' }
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const getProgressBackground = (task: Task): string => {
  if (task.status === 'failed') {
    return 'linear-gradient(to right, hsl(var(--destructive) / 0.5), hsl(var(--destructive) / 0.8))'
  }
  return 'linear-gradient(to right, hsl(var(--primary) / 0.5), hsl(var(--primary) / 0.8))'
}

// Lifecycle Hooks and Event Listeners
onMounted(() => {
  fetchProgressReports() // Initial fetch
  emitter.on('async:update', fetchProgressReports)
})

onUnmounted(() => {
  emitter.off('async:update', fetchProgressReports)
})
</script>

<style lang="scss">
/* Global styles moved from default.vue */
.task-popover {
  padding: 0 !important; 
  border-radius: 8px !important;
  box-shadow: var(--shadow-lg, var(--el-box-shadow-light));
}

.task-list {
  /* Add specific styles for the task list elements if needed */
}

.progress-container {
  /* Styles from FloatingBall.vue if needed */
}

.shine-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  .dark & {
     background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(0, 0, 0, 0) 100%
      );
  }
  animation: shine 2.5s infinite linear;
  opacity: 0.6;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style> 